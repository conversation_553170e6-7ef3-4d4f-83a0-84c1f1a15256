<%--
    SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
    Copyright (C) 2013-2025 SteVe Community Team
    All Rights Reserved.

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.
--%>
<%@ include file="../00-header.jsp" %>
<%@ include file="../00-op-bind-errors.jsp" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!-- 不再依赖任何外部JS库 -->
<script type="text/javascript">
    $(document).ready(function() {
        // 实现简单的选项卡功能
        $("#tab-links a").on("click", function(e) {
            e.preventDefault();

            // 隐藏所有选项卡内容
            $(".tab-content").hide();

            // 显示当前选项卡内容
            var targetTab = $(this).attr("href");
            $(targetTab).show();

            // 更新选中样式
            $("#tab-links li").removeClass("active");
            $(this).parent().addClass("active");
        });

        // 默认显示第一个选项卡
        $("#tab-links li:first a").click();

        // 简单的下拉列表显示/隐藏功能
        $(".multi-select-header").on("click", function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log("下拉菜单被点击");

            // 先关闭所有其他下拉菜单
            $(".multi-select-menu").not($(this).next(".multi-select-menu")).hide();

            var menu = $(this).next(".multi-select-menu");
            console.log("找到菜单元素:", menu.length);

            if (menu.is(":visible")) {
                menu.hide();
            } else {
                menu.show();
            }
            console.log("菜单显示状态:", menu.is(":visible"));
        });
        
        // 点击文档其他地方关闭下拉列表
        $(document).on("click", function(e) {
            if (!$(e.target).closest(".multi-select-container").length) {
                $(".multi-select-menu").hide();
            }
        });
        
        // 阻止点击下拉列表内部时关闭菜单
        $(".multi-select-menu").on("click", function(e) {
            e.stopPropagation();
        });
        
        // 下拉列表项点击事件
        $(".dropdown-item").on("click", function() {
            var chargeBoxId = $(this).data("value");
            var menu = $(this).closest(".multi-select-menu");
            var type = menu.attr("id").replace("MultiSelect", "");
            var checkbox = $("." + type + "Checkbox[value='" + chargeBoxId + "']");
            
            // 切换选中状态
            checkbox.prop("checked", !checkbox.prop("checked"));
            updateSelections();
        });
        
        // 删除已选项目
        $(document).on("click", ".remove-item", function() {
            var chargeBoxId = $(this).parent().data("value");
            var container = $(this).closest(".selected-items-container");
            var type = container.attr("id").replace("SelectedItems", "");
            $("." + type + "Checkbox[value='" + chargeBoxId + "']").prop("checked", false);
            updateSelections();
        });
        
        // 搜索框功能
        $(".search-input").on("keyup", function() {
            var value = $(this).val().toLowerCase();
            var container = $(this).closest(".multi-select-menu").find(".dropdown-item");
            
            container.each(function() {
                $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
            });
        });
        
        // 全选/取消全选
        $("#selectAllAssigned, #selectAllUnassigned").on("change", function() {
            var type = this.id.replace("selectAll", "");
            $("." + type.toLowerCase() + "Checkbox").prop("checked", $(this).prop("checked"));
            updateSelections();
        });
        
        // 初始化选择状态
        updateSelections();
    });
    
    // 更新所有选择状态和显示
    function updateSelections() {
        // 更新已分配的充电桩选择
        updateSelectionGroup("assigned");
        
        // 更新未分配的充电桩选择
        updateSelectionGroup("unassigned");
    }
    
    // 更新指定组的选择状态
    function updateSelectionGroup(type) {
        var checkboxes = $("." + type + "Checkbox");
        var checkedBoxes = checkboxes.filter(":checked");
        var container = $("#" + type + "SelectedItems");
        
        // 更新计数
        $("#" + type + "SelectedCount").text(checkedBoxes.length);
        $("#" + type + "TotalCount").text(checkboxes.length);
        
        // 清空已选容器
        container.empty();
        
        // 如果没有选择项，显示提示信息
        if (checkedBoxes.length === 0) {
            container.html('<div class="no-selection">No charge points selected</div>');
            return;
        }
        
        // 添加选中项
        checkedBoxes.each(function() {
            var chargeBoxId = $(this).val();
            var row = $(this).closest("tr");
            var description = row.find("td:eq(1)").text();
            var lastHeartbeat = row.find("td:eq(2)").text();
            
            var itemHtml = 
                '<div class="selected-item" data-value="' + chargeBoxId + '">' +
                    '<div class="item-info">' +
                        '<div class="item-id">' + chargeBoxId + '</div>' +
                        '<div class="item-description">' + description + '</div>' +
                        '<div class="item-heartbeat">' + lastHeartbeat + '</div>' +
                    '</div>' +
                    '<span class="remove-item">&times;</span>' +
                '</div>';
            
            container.append(itemHtml);
        });
        
        // 更新下拉列表项的选中状态
        $("#" + type + "MultiSelect .dropdown-item").each(function() {
            var chargeBoxId = $(this).data("value");
            var isChecked = $("." + type + "Checkbox[value='" + chargeBoxId + "']").prop("checked");
            
            if (isChecked) {
                $(this).addClass("selected");
                $(this).find(".checkmark").show();
            } else {
                $(this).removeClass("selected");
                $(this).find(".checkmark").hide();
            }
        });
    }
</script>
<style>
    /* 自定义选项卡样式 */
    .tab-container {
        width: 100%;
        margin-bottom: 20px;
    }
    
    .tab-links {
        list-style: none;
        padding: 0;
        margin: 0;
        background: #f5f5f5;
        border: 1px solid #ddd;
        display: flex;
    }
    
    .tab-links li {
        padding: 0;
        margin: 0;
    }
    
    .tab-links li a {
        display: block;
        padding: 10px 15px;
        text-decoration: none;
        color: #333;
    }
    
    .tab-links li.active a {
        background: #fff;
        border-bottom: 2px solid #4285f4;
        font-weight: bold;
    }
    
    .tab-links li a:hover {
        background: #e9e9e9;
    }
    
    .tab-content {
        border: 1px solid #ddd;
        border-top: none;
        padding: 15px;
        display: none;
        overflow: visible !important;
    }
    
    /* 多选下拉列表样式 */
    .multi-select-container {
        position: relative;
        width: 100%;
        margin-bottom: 10px;
        overflow: visible !important;
    }
    
    .multi-select-dropdown {
        width: 100%;
        position: relative;
        overflow: visible !important;
    }
    
    .multi-select-header {
        padding: 8px 12px;
        border: 1px solid #ccc;
        background-color: #fff;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
        z-index: 1;
        pointer-events: auto;
    }
    
    .multi-select-placeholder {
        color: #888;
    }
    
    .multi-select-menu {
        position: absolute;
        width: 100%;
        max-height: 300px;
        overflow-y: auto;
        border: 1px solid #ccc;
        background-color: #fff;
        z-index: 1000;
        display: none;
    }
    
    .multi-select-search {
        padding: 8px;
        border-bottom: 1px solid #eee;
    }
    
    .multi-select-search input {
        width: 100%;
        padding: 6px;
        border: 1px solid #ddd;
    }
    
    .multi-select-items {
        max-height: 250px;
        overflow-y: auto;
    }
    
    .dropdown-item {
        padding: 8px 12px;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .dropdown-item:hover {
        background-color: #f5f5f5;
    }
    
    .dropdown-item.selected {
        background-color: #e6f7ff;
    }
    
    .checkmark {
        color: green;
        font-weight: bold;
        display: none;
    }
    
    /* 已选项目样式 */
    .selected-items-container {
        border: 1px solid #ddd;
        padding: 10px;
        margin-bottom: 10px;
        min-height: 50px;
        max-height: 200px;
        overflow-y: auto;
    }
    
    .no-selection {
        color: #888;
        font-style: italic;
    }
    
    .selected-item {
        padding: 6px 10px;
        margin-bottom: 5px;
        background-color: #f5f5f5;
        border-radius: 4px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .item-info {
        display: flex;
        flex: 1;
    }
    
    .item-id {
        font-weight: bold;
        margin-right: 10px;
        min-width: 100px;
    }
    
    .item-description {
        flex: 1;
        margin-right: 10px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    
    .item-heartbeat {
        color: #666;
        font-size: 0.9em;
    }
    
    .remove-item {
        cursor: pointer;
        font-weight: bold;
        color: #999;
        margin-left: 5px;
    }
    
    .remove-item:hover {
        color: #ff4d4f;
    }
    
    /* 表格操作区域样式 */
    .table-actions {
        display: flex;
        align-items: center;
    }
    
    .selection-info {
        margin-left: 10px;
        font-size: 0.9em;
        color: #666;
    }
    
    /* 表头样式 */
    .table-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 5px;
        background-color: #f5f5f5;
        border: 1px solid #ddd;
        margin-bottom: 5px;
    }
    
    .table-header h4 {
        margin: 0;
    }
    
    /* 隐藏原始表格，只在选择完成后显示 */
    .selected-table-container {
        margin-top: 10px;
    }
    
    /* 下拉箭头样式 */
    .dropdown-arrow {
        display: inline-block;
        width: 0;
        height: 0;
        margin-left: 4px;
        vertical-align: middle;
        border-top: 6px solid #666;
        border-right: 4px solid transparent;
        border-left: 4px solid transparent;
    }

    /* 强制确保所有可能的父容器不会裁剪下拉菜单 */
    .content,
    .content-container,
    .tab-container,
    .tab-content,
    .multi-select-container,
    .multi-select-dropdown,
    form {
        overflow: visible !important;
    }
</style>
<div class="content">
    <div class="content-container">
        <div class="page-title">
            <h2>Owner Assignment Details</h2>
        </div>
        
        <section>
            <span>Owner Assignment Details</span>
        </section>

        <c:if test="${not empty errorMessage}">
            <div class="error">
                <b>Error:</b> ${errorMessage}
            </div>
        </c:if>
        
        <%-- 调试信息部分（已注释掉）
        <c:if test="${not empty owner}">
            <div class="debug-info" style="margin-bottom: 10px; padding: 10px; background-color: #f5f5f5; border: 1px solid #ddd;">
                <h4>Debug Info:</h4>
                <p>User: ${owner.username} (PK: ${assignmentForm.userPk})</p>
                <c:set var="assignedCount" value="${assignedChargePoints == null ? 0 : assignedChargePoints.size()}" />
                <c:set var="unassignedCount" value="${unassignedChargePoints == null ? 0 : unassignedChargePoints.size()}" />
                <p>Assigned Charge Box Count: ${assignedCount}</p>
                <p>Unassigned Charge Box Count: ${unassignedCount}</p>
            </div>
        </c:if>
        --%>

        <c:if test="${owner != null}">
            <div class="user-info-section">
                <table class="userInput">
                    <thead><tr><th colspan="2">Owner Information</th></tr></thead>
                    <tbody>
                        <tr><td>Username:</td><td>${owner.username}</td></tr>
                        <tr><td>User ID:</td><td>${assignmentForm.userPk}</td></tr>
                        <tr><td>Role:</td><td>${owner.userRole}</td></tr>
                    </tbody>
                </table>
            </div>
            
            <!-- 自定义选项卡 -->
            <div class="tab-container">
                <ul id="tab-links" class="tab-links">
                    <li><a href="#assigned-tab">Assigned Charge Points (<c:out value="${fn:length(assignedChargePoints)}" />)</a></li>
                    <li><a href="#unassigned-tab">Unassigned Charge Points (<c:out value="${fn:length(unassignedChargePoints)}" />)</a></li>
                </ul>
                
                <div id="assigned-tab" class="tab-content">
                    <form action="${ctxPath}/manager/ownerAssignments/unassign" method="post">
                        <input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}" />
                        <input type="hidden" name="userPk" value="${assignmentForm.userPk}" />
                        
                        <!-- 多选下拉列表 -->
                        <div class="multi-select-container">
                            <div class="multi-select-dropdown">
                                <div id="assignedDropdownHeader" class="multi-select-header">
                                    <span class="multi-select-placeholder">Select charge points...</span>
                                    <span class="dropdown-arrow"></span>
                                </div>
                                <div id="assignedMultiSelect" class="multi-select-menu">
                                    <div class="multi-select-search">
                                        <input type="text" id="assignedSearchInput" class="search-input" placeholder="Search charge points..." />
                                    </div>
                                    <div class="multi-select-items">
                                        <c:if test="${not empty assignedChargePoints}">
                                            <c:forEach items="${assignedChargePoints}" var="cp">
                                                <div class="dropdown-item" data-value="${cp.chargeBoxId}">
                                                    <span>${cp.chargeBoxId} - ${cp.description}</span>
                                                    <span class="checkmark">&#10004;</span>
                                                </div>
                                            </c:forEach>
                                        </c:if>
                                        <c:if test="${empty assignedChargePoints}">
                                            <div class="dropdown-item disabled">No charge boxes available</div>
                                        </c:if>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 已选项目显示区域 -->
                            <div id="assignedSelectedItems" class="selected-items-container">
                                <div class="no-selection">No charge points selected</div>
                            </div>
                        </div>
                        
                        <c:if test="${not empty assignedChargePoints}">
                            <div class="table-actions">
                                <input type="submit" class="redSubmit" value="Unassign Selected" />
                                <div class="selection-info">
                                    Selected: <span id="assignedSelectedCount">0</span>/<span id="assignedTotalCount">0</span>
                                </div>
                            </div>
                        </c:if>
                        
                        <!-- 隐藏的数据表格（仅用于存储数据和状态） -->
                        <div class="selected-table-container" style="display: none;">
                            <table class="res" id="assignedTable">
                                <thead>
                                    <tr>
                                        <th>ChargeBox ID</th>
                                        <th>Description</th>
                                        <th>Last Heartbeat</th>
                                        <th>
                                            <input type="checkbox" id="selectAllAssigned" />
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                <c:if test="${not empty assignedChargePoints}">
                                    <c:forEach items="${assignedChargePoints}" var="cp">
                                        <tr>
                                            <td><a href="${ctxPath}/manager/chargepoints/details/${cp.chargeBoxPk}">${cp.chargeBoxId}</a></td>
                                            <td>${cp.description}</td>
                                            <td>${cp.lastHeartbeatTimestamp}</td>
                                            <td>
                                                <input type="checkbox" class="assignedCheckbox" name="assignedChargeBoxIds" value="${cp.chargeBoxId}" />
                                            </td>
                                        </tr>
                                    </c:forEach>
                                </c:if>
                                <c:if test="${empty assignedChargePoints}">
                                    <tr><td colspan="4">No charge boxes are assigned to this owner.</td></tr>
                                </c:if>
                                </tbody>
                            </table>
                        </div>
                    </form>
                </div>
                
                <div id="unassigned-tab" class="tab-content">
                    <form action="${ctxPath}/manager/ownerAssignments/assign" method="post">
                        <input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}" />
                        <input type="hidden" name="userPk" value="${assignmentForm.userPk}" />
                        
                        <!-- 多选下拉列表 -->
                        <div class="multi-select-container">
                            <div class="multi-select-dropdown">
                                <div id="unassignedDropdownHeader" class="multi-select-header">
                                    <span class="multi-select-placeholder">Select charge points...</span>
                                    <span class="dropdown-arrow"></span>
                                </div>
                                <div id="unassignedMultiSelect" class="multi-select-menu">
                                    <div class="multi-select-search">
                                        <input type="text" id="unassignedSearchInput" class="search-input" placeholder="Search charge points..." />
                                    </div>
                                    <div class="multi-select-items">
                                        <c:if test="${not empty unassignedChargePoints}">
                                            <c:forEach items="${unassignedChargePoints}" var="cp">
                                                <div class="dropdown-item" data-value="${cp.chargeBoxId}">
                                                    <span>${cp.chargeBoxId} - ${cp.description}</span>
                                                    <span class="checkmark">&#10004;</span>
                                                </div>
                                            </c:forEach>
                                        </c:if>
                                        <c:if test="${empty unassignedChargePoints}">
                                            <div class="dropdown-item disabled">No charge boxes available</div>
                                        </c:if>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 已选项目显示区域 -->
                            <div id="unassignedSelectedItems" class="selected-items-container">
                                <div class="no-selection">No charge points selected</div>
                            </div>
                        </div>
                        
                        <c:if test="${not empty unassignedChargePoints}">
                            <div class="table-actions">
                                <input type="submit" class="blueSubmit" value="Assign Selected" />
                                <div class="selection-info">
                                    Selected: <span id="unassignedSelectedCount">0</span>/<span id="unassignedTotalCount">0</span>
                                </div>
                            </div>
                        </c:if>
                        
                        <!-- 隐藏的数据表格（仅用于存储数据和状态） -->
                        <div class="selected-table-container" style="display: none;">
                            <table class="res" id="unassignedTable">
                                <thead>
                                    <tr>
                                        <th>ChargeBox ID</th>
                                        <th>Description</th>
                                        <th>Last Heartbeat</th>
                                        <th>
                                            <input type="checkbox" id="selectAllUnassigned" />
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                <c:if test="${not empty unassignedChargePoints}">
                                    <c:forEach items="${unassignedChargePoints}" var="cp">
                                        <tr>
                                            <td><a href="${ctxPath}/manager/chargepoints/details/${cp.chargeBoxPk}">${cp.chargeBoxId}</a></td>
                                            <td>${cp.description}</td>
                                            <td>${cp.lastHeartbeatTimestamp}</td>
                                            <td>
                                                <input type="checkbox" class="unassignedCheckbox" name="unassignedChargeBoxIds" value="${cp.chargeBoxId}" />
                                            </td>
                                        </tr>
                                    </c:forEach>
                                </c:if>
                                <c:if test="${empty unassignedChargePoints}">
                                    <tr><td colspan="4">No unassigned charge boxes are available.</td></tr>
                                </c:if>
                                </tbody>
                            </table>
                        </div>
                    </form>
                </div>
            </div>
            
            <div class="add-margin-bottom">
                <form action="${ctxPath}/manager/ownerAssignments/cancel" method="post">
                    <input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}" />
                    <input type="submit" value="Back to Overview">
                </form>
            </div>
        </c:if>
        
        <c:if test="${owner == null && empty errorMessage}">
            <div class="info">
                <span>Owner not found.</span>
                <div class="add-margin-bottom">
                    <form action="${ctxPath}/manager/ownerAssignments/cancel" method="post" style="margin-top: 10px;">
                        <input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}" />
                        <input type="submit" value="Back to Overview">
                    </form>
                </div>
            </div>
        </c:if>
    </div>
</div>
<%@ include file="../00-footer.jsp" %>