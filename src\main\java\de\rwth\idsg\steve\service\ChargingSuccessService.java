package de.rwth.idsg.steve.service;

import de.rwth.idsg.steve.repository.dto.ChargingSuccessStats;
import ocpp.cs._2015._10.ChargePointStatus;
import ocpp.cs._2015._10.MeterValue;

import java.util.List;
import java.util.Map;

/**
 * 充电成功率统计服务接口
 *
 * <AUTHOR> Assistant
 */
public interface ChargingSuccessService {
    
    /**
     * 处理状态通知消息
     * 
     * @param chargeBoxId 充电桩ID
     * @param connectorId 连接器ID
     * @param status 状态
     * @param transactionId 事务ID（可选）
     */
    void handleStatusNotification(String chargeBoxId, int connectorId, ChargePointStatus status, Integer transactionId);
    
    /**
     * 处理电表数据消息
     * 
     * @param chargeBoxId 充电桩ID
     * @param connectorId 连接器ID
     * @param meterValues 电表数据列表
     * @param transactionId 事务ID
     */
    void handleMeterValues(String chargeBoxId, int connectorId, List<MeterValue> meterValues, Integer transactionId);
    
    /**
     * 获取所有充电桩的成功率统计
     * 
     * @return 充电桩ID到成功率统计的映射
     */
    Map<String, ChargingSuccessStats> getAllSuccessStats();
    
    /**
     * 获取指定充电桩的成功率统计
     * 
     * @param chargeBoxId 充电桩ID
     * @return 成功率统计，如果不存在则返回null
     */
    ChargingSuccessStats getSuccessStats(String chargeBoxId);
    
    /**
     * 重新计算所有充电桩的成功率统计
     */
    void recalculateAllSuccessStats();
    
    /**
     * 重新计算指定充电桩的成功率统计
     * 
     * @param chargeBoxId 充电桩ID
     */
    void recalculateSuccessStats(String chargeBoxId);
    
    /**
     * 初始化充电桩的成功率统计（如果不存在）
     * 
     * @param chargeBoxId 充电桩ID
     */
    void initializeSuccessStats(String chargeBoxId);
}
