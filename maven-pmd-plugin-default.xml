<?xml version="1.0" encoding="UTF-8"?>
<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at
http://www.apache.org/licenses/LICENSE-2.0
Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->
<ruleset name="Default Maven PMD Plugin Ruleset"
  xmlns="http://pmd.sourceforge.net/ruleset/2.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://pmd.sourceforge.net/ruleset/2.0.0 http://pmd.sourceforge.net/ruleset_2_0_0.xsd">

  <description>
    The default ruleset used by the Maven PMD Plugin, when no other ruleset is specified.
    It contains the rules of the old (pre PMD 6.0.0) rulesets java-basic, java-empty, java-imports,
    java-unnecessary, java-unusedcode.

    This ruleset might be used as a starting point for an own customized ruleset [0].

    [0] https://pmd.github.io/latest/pmd_userdocs_making_rulesets.html
  </description>

  <rule ref="category/java/bestpractices.xml/AvoidUsingHardCodedIP" />
  <rule ref="category/java/bestpractices.xml/CheckResultSet" />
  <rule ref="category/java/bestpractices.xml/PrimitiveWrapperInstantiation" />
  <rule ref="category/java/bestpractices.xml/UnusedFormalParameter" />
  <rule ref="category/java/bestpractices.xml/UnusedLocalVariable" />
  <rule ref="category/java/bestpractices.xml/UnusedPrivateField" />
  <rule ref="category/java/bestpractices.xml/UnusedPrivateMethod" />

  <rule ref="category/java/codestyle.xml/EmptyControlStatement" />
  <rule ref="category/java/codestyle.xml/ExtendsObject" />
  <rule ref="category/java/codestyle.xml/ForLoopShouldBeWhileLoop" />
  <rule ref="category/java/codestyle.xml/TooManyStaticImports" />
  <rule ref="category/java/codestyle.xml/UnnecessaryFullyQualifiedName" />
  <rule ref="category/java/codestyle.xml/UnnecessaryImport" />
  <rule ref="category/java/codestyle.xml/UnnecessaryModifier" />
  <rule ref="category/java/codestyle.xml/UnnecessaryReturn" />
  <rule ref="category/java/codestyle.xml/UnnecessarySemicolon" />
  <rule ref="category/java/codestyle.xml/UselessParentheses" />
  <rule ref="category/java/codestyle.xml/UselessQualifiedThis" />

  <rule ref="category/java/design.xml/CollapsibleIfStatements" />
  <rule ref="category/java/design.xml/SimplifiedTernary" />
  <rule ref="category/java/design.xml/UselessOverridingMethod" />

  <rule ref="category/java/errorprone.xml/AvoidBranchingStatementAsLastInLoop" />
  <rule ref="category/java/errorprone.xml/AvoidDecimalLiteralsInBigDecimalConstructor" />
  <rule ref="category/java/errorprone.xml/AvoidMultipleUnaryOperators" />
  <rule ref="category/java/errorprone.xml/AvoidUsingOctalValues" />
  <rule ref="category/java/errorprone.xml/BrokenNullCheck" />
  <rule ref="category/java/errorprone.xml/CheckSkipResult" />
  <rule ref="category/java/errorprone.xml/ClassCastExceptionWithToArray" />
  <rule ref="category/java/errorprone.xml/DontUseFloatTypeForLoopIndices" />
  <rule ref="category/java/errorprone.xml/EmptyCatchBlock" />
  <rule ref="category/java/errorprone.xml/JumbledIncrementer" />
  <rule ref="category/java/errorprone.xml/MisplacedNullCheck" />
  <rule ref="category/java/errorprone.xml/OverrideBothEqualsAndHashcode" />
  <rule ref="category/java/errorprone.xml/ReturnFromFinallyBlock" />
  <rule ref="category/java/errorprone.xml/UnconditionalIfStatement" />
  <rule ref="category/java/errorprone.xml/UnnecessaryConversionTemporary" />
  <rule ref="category/java/errorprone.xml/UnusedNullCheckInEquals" />
  <rule ref="category/java/errorprone.xml/UselessOperationOnImmutable" />

  <rule ref="category/java/multithreading.xml/AvoidThreadGroup" />
  <rule ref="category/java/multithreading.xml/DontCallThreadRun" />
  <rule ref="category/java/multithreading.xml/DoubleCheckedLocking" />

  <rule ref="category/java/performance.xml/BigIntegerInstantiation" />

</ruleset>
