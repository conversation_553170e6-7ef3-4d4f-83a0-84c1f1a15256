/*
 * SteV<PERSON> - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2023 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.repository.impl;

import de.rwth.idsg.steve.repository.UpdateFirmwareLogRepository;
import de.rwth.idsg.steve.utils.DateTimeUtils;
import de.rwth.idsg.steve.web.dto.UpdateFirmwareLog;
import de.rwth.idsg.steve.web.dto.UpdateFirmwareLogQueryForm;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.Record;
import org.jooq.Record1;
import org.jooq.SelectQuery;
import org.jooq.impl.DSL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static org.jooq.impl.DSL.field;
import static org.jooq.impl.DSL.table;

/**
 * <AUTHOR>
 * @since 29.04.2023
 */
@Slf4j
@Repository
public class UpdateFirmwareLogRepositoryImpl implements UpdateFirmwareLogRepository {

    @Autowired private DSLContext ctx;

    /**
     * 判断两个固件位置是否相同
     */
    private boolean isSameLocation(String location1, String location2) {
        if (location1 == null && location2 == null) {
            return true;
        }
        if (location1 == null || location2 == null) {
            return false;
        }
        return location1.equals(location2);
    }
    
    /**
     * 判断两个检索时间是否相同（允许几秒钟的误差）
     */
    private boolean isSameRetrieveTime(DateTime time1, DateTime time2) {
        if (time1 == null && time2 == null) {
            return true;
        }
        if (time1 == null || time2 == null) {
            return false;
        }
        // 允许5秒内的误差
        return Math.abs(time1.getMillis() - time2.getMillis()) < 5000;
    }

    @Override
    public int insert(String location, Integer retries, Integer retryInterval, 
                     DateTime retrieveDatetime, String chargeBoxId, String status, String response) {
        try {
            // 先检查是否存在重复记录
            List<UpdateFirmwareLog> recentLogs = getRecentLogs(chargeBoxId, 60);
            if (recentLogs != null && !recentLogs.isEmpty()) {
                for (UpdateFirmwareLog logEntry : recentLogs) {
                    if (isSameLocation(location, logEntry.getLocation()) && 
                        isSameRetrieveTime(retrieveDatetime, logEntry.getRetrieveDatetime())) {
                        // 使用更清晰的消息
                        log.debug("Skipping duplicate firmware update record for chargeBoxId={}, location={}", 
                                chargeBoxId, location);
                        return logEntry.getLogId(); // 返回现有记录ID而不是-1
                    }
                }
            }
            
            Record record = ctx.insertInto(table("update_firmware_log"))
                    .set(field("location"), location)
                    .set(field("retries"), retries)
                    .set(field("retry_interval"), retryInterval)
                    .set(field("retrieve_datetime"), retrieveDatetime)
                    .set(field("charge_box_id"), chargeBoxId)
                    .set(field("status"), status)
                    .set(field("response"), response)
                    .returning(field("log_id", Integer.class))
                    .fetchOne();
            
            if (record == null) {
                // 修改为更明确的消息和适当的日志级别
                log.debug("No record returned when inserting update_firmware_log for chargeBoxId={}", chargeBoxId);
                return -1;
            }
            
            return record.getValue(field("log_id", Integer.class));
        } catch (Exception e) {
            log.error("Error inserting update_firmware_log: {}", e.getMessage());
            return -1;
        }
    }

    @Override
    public int getCount(UpdateFirmwareLogQueryForm form) {
        SelectQuery<Record> selectQuery = ctx.selectQuery();
        selectQuery.addFrom(table("update_firmware_log"));
        selectQuery.addSelect(DSL.count());
        addConditions(selectQuery, form);
        
        return ctx.fetchOne(selectQuery).get(0, Integer.class);
    }

    @Override
    public List<UpdateFirmwareLog> getRecords(UpdateFirmwareLogQueryForm form) {
        SelectQuery<Record> selectQuery = ctx.selectQuery();
        selectQuery.addFrom(table("update_firmware_log"));
        selectQuery.addSelect(
                field("log_id"),
                field("location"),
                field("retries"),
                field("retry_interval"),
                field("retrieve_datetime"),
                field("sending_time"),
                field("last_updated"),
                field("charge_box_id"),
                field("status"),
                field("response"),
                field("batch_id")
        );
        
        addConditions(selectQuery, form);
        
        // 添加排序
        selectQuery.addOrderBy(field("sending_time").desc());
        
        // 添加分页
        selectQuery.addLimit(form.getLimit());
        selectQuery.addOffset(form.getOffset());
        
        return ctx.fetch(selectQuery)
                .map(r -> UpdateFirmwareLog.builder()
                        .logId(r.get(field("log_id", Integer.class)))
                        .location(r.get(field("location", String.class)))
                        .retries(r.get(field("retries", Integer.class)))
                        .retryInterval(r.get(field("retry_interval", Integer.class)))
                        .retrieveDatetime(DateTimeUtils.convertLocalDateTimeValue(r.get(field("retrieve_datetime"))))
                        .sendingTime(DateTimeUtils.convertLocalDateTimeValue(r.get(field("sending_time"))))
                        .lastUpdated(DateTimeUtils.convertLocalDateTimeValue(r.get(field("last_updated"))))
                        .chargeBoxId(r.get(field("charge_box_id", String.class)))
                        .status(r.get(field("status", String.class)))
                        .response(r.get(field("response", String.class)))
                        .batchId(r.get(field("batch_id", Integer.class)))
                        .build());
    }

    @Override
    public UpdateFirmwareLog getRecord(int logId) {
        Record r = ctx.selectFrom(table("update_firmware_log"))
                .where(field("log_id").eq(logId))
                .fetchOne();
        
        if (r == null) {
            return null;
        }
        
        return UpdateFirmwareLog.builder()
                .logId(r.get(field("log_id", Integer.class)))
                .location(r.get(field("location", String.class)))
                .retries(r.get(field("retries", Integer.class)))
                .retryInterval(r.get(field("retry_interval", Integer.class)))
                .retrieveDatetime(DateTimeUtils.convertLocalDateTimeValue(r.get(field("retrieve_datetime"))))
                .sendingTime(DateTimeUtils.convertLocalDateTimeValue(r.get(field("sending_time"))))
                .lastUpdated(DateTimeUtils.convertLocalDateTimeValue(r.get(field("last_updated"))))
                .chargeBoxId(r.get(field("charge_box_id", String.class)))
                .status(r.get(field("status", String.class)))
                .response(r.get(field("response", String.class)))
                .batchId(r.get(field("batch_id", Integer.class)))
                .build();
    }
    
    /**
     * 获取指定充电站的最近日志记录
     * 
     * @param chargeBoxId 充电站ID
     * @param seconds 过去的秒数
     * @return 最近的日志记录列表
     */
    @Override
    public List<UpdateFirmwareLog> getRecentLogs(String chargeBoxId, int seconds) {
        DateTime now = DateTime.now();
        DateTime startTime = now.minusSeconds(seconds);
        
        return ctx.select()
                  .from(table("update_firmware_log"))
                  .where(field("charge_box_id").eq(chargeBoxId))
                  .and(field("sending_time").greaterOrEqual(startTime))
                  .orderBy(field("sending_time").desc())
                  .fetch()
                  .map(r -> UpdateFirmwareLog.builder()
                            .logId(r.getValue(field("log_id", Integer.class)))
                            .location(r.getValue(field("location", String.class)))
                            .retries(r.getValue(field("retries", Integer.class)))
                            .retryInterval(r.getValue(field("retry_interval", Integer.class)))
                            .retrieveDatetime(DateTimeUtils.convertLocalDateTimeValue(r.getValue(field("retrieve_datetime"))))
                            .sendingTime(DateTimeUtils.convertLocalDateTimeValue(r.getValue(field("sending_time"))))
                            .lastUpdated(DateTimeUtils.convertLocalDateTimeValue(r.getValue(field("last_updated"))))
                            .chargeBoxId(r.getValue(field("charge_box_id", String.class)))
                            .status(r.getValue(field("status", String.class)))
                            .response(r.getValue(field("response", String.class)))
                            .batchId(r.getValue(field("batch_id", Integer.class)))
                            .build());
    }
    
    private void addConditions(SelectQuery<Record> selectQuery, UpdateFirmwareLogQueryForm form) {
        if (form.getLocation() != null && !form.getLocation().isEmpty()) {
            selectQuery.addConditions(field("location").like("%" + form.getLocation() + "%"));
        }
        
        if (form.getRetries() != null) {
            selectQuery.addConditions(field("retries").eq(form.getRetries()));
        }
        
        if (form.getRetryInterval() != null) {
            selectQuery.addConditions(field("retry_interval").eq(form.getRetryInterval()));
        }
        
        if (form.getRetrieveDateFrom() != null) {
            selectQuery.addConditions(field("retrieve_datetime").ge(form.getRetrieveDateFromAsDateTime()));
        }
        
        if (form.getRetrieveDateTo() != null) {
            selectQuery.addConditions(field("retrieve_datetime").le(form.getRetrieveDateToAsDateTime()));
        }
        
        if (form.getSendingTimeFrom() != null) {
            selectQuery.addConditions(field("sending_time").ge(form.getSendingTimeFromAsDateTime()));
        }
        
        if (form.getSendingTimeTo() != null) {
            selectQuery.addConditions(field("sending_time").le(form.getSendingTimeToAsDateTime()));
        }
        
        if (form.getChargeBoxId() != null && !form.getChargeBoxId().isEmpty()) {
            selectQuery.addConditions(field("charge_box_id").eq(form.getChargeBoxId()));
        }
        
        if (form.getStatus() != null && !form.getStatus().isEmpty()) {
            selectQuery.addConditions(field("status").eq(form.getStatus()));
        }
    }

    /**
     * Map firmware status to a display string.
     */
    private String mapFirmwareStatusToString(String status) {
        if (status == null) {
            return null;
        }
        switch (status) {
            case "Installed":
                return "Upgrade succeeded";
            case "Downloading":
            case "Installing":
            case "Downloaded":
                return "Upgrading";
            case "DownloadFailed":
            case "InstallationFailed":
            case "Installation timeout":
                return "Upgrade failed";
            default:
                return null;
        }
    }

    /**
     * 更新固件日志的状态
     *
     * @param logId 日志ID
     * @param status 新状态
     * @param response 响应内容
     * @return 更新的记录数
     */
    @Override
    @Transactional
    public int updateStatus(int logId, String status, String response) {
        try {
            // First, get the charge_box_id for the given logId
            Record1<String> chargeBoxRecord = ctx.select(field("charge_box_id", String.class))
                    .from(table("update_firmware_log"))
                    .where(field("log_id").eq(logId))
                    .fetchOne();

            if (chargeBoxRecord == null) {
                log.warn("Cannot update firmware status. No log found for logId={}", logId);
                return 0;
            }

            String chargeBoxId = chargeBoxRecord.value1();

            // Update the log table
            int updatedRows = ctx.update(table("update_firmware_log"))
                    .set(field("status"), status)
                    .set(field("response"), response)
                    .set(field("last_updated"), DateTime.now())
                    .where(field("log_id").eq(logId))
                    .execute();

            if (updatedRows > 0) {
                // Now, update the charge_box table with the mapped status
                String lastUpgradeStatus = mapFirmwareStatusToString(status);
                if (lastUpgradeStatus != null) {
                    ctx.update(table("charge_box"))
                            .set(field("last_upgrade_status"), lastUpgradeStatus)
                            .where(field("charge_box_id").eq(chargeBoxId))
                            .execute();
                }
            }

            return updatedRows;
        } catch (Exception e) {
            log.error("Error updating firmware log status for logId={}: {}", logId, e.getMessage(), e);
            // Transaction will be rolled back by Spring
            throw e;
        }
    }

    @Override
    public List<UpdateFirmwareLog> getLogsWithStatus(List<String> statuses) {
        return ctx.select()
                .from(table("update_firmware_log"))
                .where(field("status").in(statuses))
                .orderBy(field("last_updated").asc()) // Process older logs first
                .fetch()
                .map(r -> UpdateFirmwareLog.builder()
                        .logId(r.getValue(field("log_id", Integer.class)))
                        .location(r.getValue(field("location", String.class)))
                        .retries(r.getValue(field("retries", Integer.class)))
                        .retryInterval(r.getValue(field("retry_interval", Integer.class)))
                        .retrieveDatetime(DateTimeUtils.convertLocalDateTimeValue(r.getValue(field("retrieve_datetime"))))
                        .sendingTime(DateTimeUtils.convertLocalDateTimeValue(r.getValue(field("sending_time"))))
                        .lastUpdated(DateTimeUtils.convertLocalDateTimeValue(r.getValue(field("last_updated"))))
                        .chargeBoxId(r.getValue(field("charge_box_id", String.class)))
                        .status(r.getValue(field("status", String.class)))
                        .response(r.getValue(field("response", String.class)))
                        .batchId(r.getValue(field("batch_id", Integer.class)))
                        .build());
    }

    @Override
    public Page<UpdateFirmwareLog> findAll(UpdateFirmwareLogQueryForm form, Pageable pageable) {
        SelectQuery<Record> dataQuery = ctx.selectQuery();
        dataQuery.addFrom(table("update_firmware_log"));
        dataQuery.addSelect(
                field("log_id"),
                field("location"),
                field("retries"),
                field("retry_interval"),
                field("retrieve_datetime"),
                field("sending_time"),
                field("last_updated"),
                field("charge_box_id"),
                field("status"),
                field("response"),
                field("batch_id")
        );
        addConditions(dataQuery, form); // Reuse existing condition logic

        // Apply sorting from Pageable
        if (pageable.getSort().isSorted()) {
            for (org.springframework.data.domain.Sort.Order order : pageable.getSort()) {
                String propertyName = order.getProperty();
                // Simple mapping for known sortable fields from DTO to DB column name
                if ("sendingTime".equalsIgnoreCase(propertyName)) {
                    propertyName = "sending_time";
                } else if ("retrieveDatetime".equalsIgnoreCase(propertyName)) {
                    propertyName = "retrieve_datetime";
                } else if ("lastUpdated".equalsIgnoreCase(propertyName)) {
                    propertyName = "last_updated";
                } else if ("chargeBoxId".equalsIgnoreCase(propertyName)) {
                    propertyName = "charge_box_id";
                } else if ("logId".equalsIgnoreCase(propertyName)) {
                    propertyName = "log_id";
                }
                // Add more mappings if other fields are sortable, e.g., location, retries, status etc.
                // For now, assuming these are the primary sortable date/ID fields.
                
                org.jooq.Field<?> sortField = field(DSL.name(propertyName)); // Use DSL.name() for safety with potentially quoted names
                dataQuery.addOrderBy(order.isAscending() ? sortField.asc() : sortField.desc());
            }
        } else {
            // Apply default sort if Pageable does not specify any sort
            dataQuery.addOrderBy(field("sending_time").desc()); // Default sort uses actual column name
        }

        dataQuery.addLimit(pageable.getPageSize());
        dataQuery.addOffset(pageable.getOffset());

        List<UpdateFirmwareLog> logs = ctx.fetch(dataQuery)
                .map(r -> UpdateFirmwareLog.builder()
                        .logId(r.get(field("log_id", Integer.class)))
                        .location(r.get(field("location", String.class)))
                        .retries(r.get(field("retries", Integer.class)))
                        .retryInterval(r.get(field("retry_interval", Integer.class)))
                        .retrieveDatetime(DateTimeUtils.convertLocalDateTimeValue(r.get(field("retrieve_datetime"))))
                        .sendingTime(DateTimeUtils.convertLocalDateTimeValue(r.get(field("sending_time"))))
                        .lastUpdated(DateTimeUtils.convertLocalDateTimeValue(r.get(field("last_updated"))))
                        .chargeBoxId(r.get(field("charge_box_id", String.class)))
                        .status(r.get(field("status", String.class)))
                        .response(r.get(field("response", String.class)))
                        .batchId(r.get(field("batch_id", Integer.class)))
                        .build());

        // For total count, create a separate query or reuse getCount if appropriate
        // For simplicity, reusing getCount here. Consider optimizing if performance is an issue.
        int totalCount = getCount(form);

        return new PageImpl<>(logs, pageable, totalCount);
    }
}