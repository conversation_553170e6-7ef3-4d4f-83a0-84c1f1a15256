/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.service.impl;

import de.rwth.idsg.steve.repository.ChargePointRepository;
import de.rwth.idsg.steve.repository.dto.ChargePoint;
import de.rwth.idsg.steve.repository.dto.ChargePointSelect;
import de.rwth.idsg.steve.repository.dto.ConnectorStatus;
import de.rwth.idsg.steve.service.ChargePointService;
import de.rwth.idsg.steve.web.dto.ChargePointForm;
import de.rwth.idsg.steve.web.dto.ChargePointQueryForm;
import de.rwth.idsg.steve.web.dto.ConnectorStatusForm;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 充电点服务实现
 */
@Slf4j
@Service
public class ChargePointServiceImpl implements ChargePointService {
    
    @Autowired
    private ChargePointRepository chargePointRepository;

    @Override
    public List<ChargePoint.Overview> getOverview(ChargePointQueryForm form) {
        return chargePointRepository.getOverview(form);
    }

    @Override
    public ChargePoint.Details getDetails(int chargeBoxPk) {
        return chargePointRepository.getDetails(chargeBoxPk);
    }

    @Override
    public List<ConnectorStatus> getChargePointConnectorStatus(ConnectorStatusForm form) {
        return chargePointRepository.getChargePointConnectorStatus(form);
    }

    @Override
    public void addChargePointList(List<String> chargeBoxIdList) {
        chargePointRepository.addChargePointList(chargeBoxIdList);
    }

    @Override
    public int addChargePoint(ChargePointForm form) {
        return chargePointRepository.addChargePoint(form);
    }

    @Override
    public void updateChargePoint(ChargePointForm form) {
        chargePointRepository.updateChargePoint(form);
    }

    @Override
    public void deleteChargePoint(int chargeBoxPk) {
        chargePointRepository.deleteChargePoint(chargeBoxPk);
    }

    @Override
    public List<ChargePointSelect> getChargePointSelectByOwner(Integer userPk) {
        if (userPk == null) {
            return Collections.emptyList();
        }
        return chargePointRepository.getChargePointSelectByOwner(userPk);
    }

    @Override
    public List<ChargePoint.Overview> getOwnerChargeBoxes(int userPk, ChargePointQueryForm form) {
        return chargePointRepository.getOwnerChargeBoxes(userPk, form);
    }
} 