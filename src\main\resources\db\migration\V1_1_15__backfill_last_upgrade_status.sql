-- This script backfills the last_upgrade_status in the charge_box table
-- based on the latest status from the update_firmware_log table.
UPDATE charge_box cb
JOIN (
    --
    -- Find the latest firmware status for each charge box.
    --
    -- There can be multiple log entries for the same charge box. We need to find
    -- the most recent one. We do this by partitioning by charge_box_id and ordering
    -- by the last_updated timestamp in descending order. The row with row_number = 1
    -- is the latest.
    --
    WITH latest_log AS (
        SELECT
            charge_box_id,
            status,
            ROW_NUMBER() OVER(PARTITION BY charge_box_id ORDER BY last_updated DESC, log_id DESC) as rn
        FROM update_firmware_log
        WHERE status IS NOT NULL AND status != ''
    )
    SELECT charge_box_id, status
    FROM latest_log
    WHERE rn = 1
) AS latest_status ON cb.charge_box_id = latest_status.charge_box_id
SET
    cb.last_upgrade_status =
        CASE
            WHEN latest_status.status = 'Installed'
                THEN 'Upgrade succeeded'
            WHEN latest_status.status IN ('Downloading', 'Installing', 'Downloaded')
                THEN 'Upgrading'
            WHEN latest_status.status IN ('DownloadFailed', 'InstallationFailed', 'Installation timeout')
                THEN 'Upgrade failed'
            ELSE NULL
        END; 