<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>充电桩管理页面修改验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .filter-demo {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            align-items: center;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .filter-item {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        .filter-item label {
            font-weight: bold;
            font-size: 12px;
            color: #666;
        }
        .filter-item input, .filter-item select {
            padding: 5px 8px;
            border: 1px solid #ccc;
            border-radius: 3px;
            font-size: 12px;
            min-width: 120px;
        }
        .removed {
            text-decoration: line-through;
            color: #999;
            background-color: #ffe6e6;
        }
        .added {
            background-color: #e6ffe6;
            font-weight: bold;
        }
        .permission-note {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }
        .code-block {
            background-color: #f4f4f4;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>充电桩管理页面修改验证</h1>
    
    <div class="section">
        <h2>1. 筛选条件修改对比</h2>
        
        <h3>修改前的筛选条件：</h3>
        <div class="filter-demo">
            <div class="filter-item">
                <label>ChargeBox ID:</label>
                <input type="text" placeholder="输入充电桩ID">
            </div>
            <div class="filter-item">
                <label>Last Heartbeat:</label>
                <select>
                    <option>All</option>
                    <option>Today</option>
                    <option>Yesterday</option>
                    <option>Earlier</option>
                </select>
            </div>
            <div class="filter-item removed">
                <label>Firmware:</label>
                <input type="text" placeholder="固件版本" disabled>
            </div>
            <div class="filter-item">
                <label>Owner:</label>
                <input type="text" placeholder="输入所有者" style="background-color: #ffe6e6;">
            </div>
            <div class="filter-item removed">
                <label>Firmware Update:</label>
                <input type="text" placeholder="YYYY-MM-DD" disabled>
            </div>
            <div class="filter-item">
                <label>Last Upgrade Status:</label>
                <input type="text" placeholder="升级状态">
            </div>
        </div>
        
        <h3>修改后的筛选条件：</h3>
        <div class="filter-demo">
            <div class="filter-item">
                <label>ChargeBox ID:</label>
                <input type="text" placeholder="输入充电桩ID">
            </div>
            <div class="filter-item">
                <label>Last Heartbeat:</label>
                <select>
                    <option>All</option>
                    <option>Today</option>
                    <option>Yesterday</option>
                    <option>Earlier</option>
                </select>
            </div>
            <div class="filter-item added">
                <label>Owner:</label>
                <select>
                    <option>-- All Owners --</option>
                    <option>admin</option>
                    <option>owner1</option>
                    <option>owner2</option>
                </select>
            </div>
            <div class="filter-item">
                <label>Last Upgrade Status:</label>
                <input type="text" placeholder="升级状态">
            </div>
        </div>
        
        <div class="permission-note">
            <strong>权限控制：</strong>Owner筛选选项仅对ADMIN和OPERATOR_FACTORY角色显示，OPERATOR_OWNER角色用户不会看到此选项。
        </div>
    </div>
    
    <div class="section">
        <h2>2. 主要修改内容</h2>
        
        <h3>前端修改 (chargepoints.jsp):</h3>
        <div class="code-block">
&lt;!-- 移除的筛选条件 --&gt;
&lt;s&gt;&lt;div class="filter-item"&gt;
    &lt;label for="firmwareVersion"&gt;Firmware:&lt;/label&gt;
    &lt;form:input path="firmwareVersion" id="firmwareVersion"/&gt;
&lt;/div&gt;&lt;/s&gt;

&lt;s&gt;&lt;div class="filter-item"&gt;
    &lt;label for="firmwareUpdateTimestamp"&gt;Firmware Update:&lt;/label&gt;
    &lt;form:input path="firmwareUpdateTimestamp" id="firmwareUpdateTimestamp"/&gt;
&lt;/div&gt;&lt;/s&gt;

&lt;!-- 新增的Owner下拉选择 --&gt;
&lt;sec:authorize access="hasAnyAuthority('ADMIN', 'OPERATOR_FACTORY')"&gt;
    &lt;div class="filter-item"&gt;
        &lt;label for="owner"&gt;Owner:&lt;/label&gt;
        &lt;form:select path="owner" id="owner"&gt;
            &lt;form:option value="" label="-- All Owners --"/&gt;
            &lt;c:forEach items="${ownerList}" var="owner"&gt;
                &lt;form:option value="${owner.username}" label="${owner.displayName}"/&gt;
            &lt;/c:forEach&gt;
        &lt;/form:select&gt;
    &lt;/div&gt;
&lt;/sec:authorize&gt;
        </div>
        
        <h3>后端修改 (ChargePointsController.java):</h3>
        <div class="code-block">
// 添加owner列表用于筛选（仅对非OPERATOR_OWNER角色显示）
if (!permissionService.isOperatorOwner()) {
    try {
        List&lt;WebUserRecord&gt; ownerUsers = webUserService.getUsersByRole(UserRole.OPERATOR_OWNER);
        List&lt;Map&lt;String, String&gt;&gt; ownerList = ownerUsers.stream()
            .map(user -&gt; {
                Map&lt;String, String&gt; ownerMap = new HashMap&lt;&gt;();
                ownerMap.put("username", user.getUsername());
                ownerMap.put("displayName", user.getUsername());
                return ownerMap;
            })
            .collect(Collectors.toList());
        model.addAttribute("ownerList", ownerList);
    } catch (Exception e) {
        log.warn("Failed to get owner list for filtering", e);
        model.addAttribute("ownerList", Collections.emptyList());
    }
}
        </div>
    </div>
    
    <div class="section">
        <h2>3. 功能验证清单</h2>
        
        <h3>✅ 已完成的修改：</h3>
        <ul>
            <li>✅ 移除了"Firmware"筛选条件</li>
            <li>✅ 移除了"Firmware Update"筛选条件</li>
            <li>✅ 将Owner从文本输入框改为下拉选择框</li>
            <li>✅ Owner下拉框显示所有OPERATOR_OWNER角色的用户</li>
            <li>✅ 添加了权限控制，OPERATOR_OWNER角色用户看不到Owner筛选选项</li>
            <li>✅ 更新了分页链接，移除了相关参数</li>
            <li>✅ 后端添加了获取owner列表的逻辑</li>
        </ul>
        
        <h3>🔍 需要测试的功能：</h3>
        <ul>
            <li>🔍 访问 http://localhost:8080/steve/manager/chargepoints</li>
            <li>🔍 验证筛选条件中不再显示Firmware和Firmware Update选项</li>
            <li>🔍 验证Owner显示为下拉选择框</li>
            <li>🔍 验证Owner下拉框包含所有OPERATOR_OWNER用户</li>
            <li>🔍 使用OPERATOR_OWNER角色登录，验证看不到Owner筛选选项</li>
            <li>🔍 测试Owner筛选功能是否正常工作</li>
            <li>🔍 测试分页功能是否正常</li>
        </ul>
    </div>
    
    <div class="section">
        <h2>4. 技术实现细节</h2>
        
        <h3>权限控制实现：</h3>
        <p>使用Spring Security的<code>&lt;sec:authorize&gt;</code>标签实现权限控制：</p>
        <div class="code-block">
&lt;sec:authorize access="hasAnyAuthority('ADMIN', 'OPERATOR_FACTORY')"&gt;
    &lt;!-- Owner筛选选项只对ADMIN和OPERATOR_FACTORY角色显示 --&gt;
&lt;/sec:authorize&gt;
        </div>
        
        <h3>数据获取逻辑：</h3>
        <p>通过WebUserService获取所有OPERATOR_OWNER角色的用户：</p>
        <div class="code-block">
List&lt;WebUserRecord&gt; ownerUsers = webUserService.getUsersByRole(UserRole.OPERATOR_OWNER);
        </div>
        
        <h3>前端显示：</h3>
        <p>使用Spring Form标签库的select组件：</p>
        <div class="code-block">
&lt;form:select path="owner" id="owner"&gt;
    &lt;form:option value="" label="-- All Owners --"/&gt;
    &lt;c:forEach items="${ownerList}" var="owner"&gt;
        &lt;form:option value="${owner.username}" label="${owner.displayName}"/&gt;
    &lt;/c:forEach&gt;
&lt;/form:select&gt;
        </div>
    </div>
</body>
</html>
