/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.web.dto;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 故障过滤表单
 */
@Getter
@Setter
@NoArgsConstructor
public class FaultForm {

    /**
     * 充电桩ID
     */
    private String chargeBoxId;
    
    /**
     * 状态过滤
     */
    private ChargerIssueStatus status;
    
    /**
     * 用户ID过滤
     */
    private Integer userId;
    
    /**
     * 开始日期过滤
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date startDate;
    
    /**
     * 结束日期过滤
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endDate;
    
    /**
     * 排序字段
     */
    private String sortField;
    
    /**
     * 排序顺序
     */
    private SortOrder sortOrder = SortOrder.DESC;
    
    /**
     * 检查是否设置了充电桩ID
     */
    public boolean isChargeBoxIdSet() {
        return chargeBoxId != null && !chargeBoxId.isEmpty();
    }
    
    /**
     * 检查是否设置了状态
     */
    public boolean isStatusSet() {
        return status != null;
    }
    
    /**
     * 检查是否设置了用户ID
     */
    public boolean isUserIdSet() {
        return userId != null;
    }
    
    /**
     * 检查是否设置了开始日期
     */
    public boolean isStartDateSet() {
        return startDate != null;
    }
    
    /**
     * 检查是否设置了结束日期
     */
    public boolean isEndDateSet() {
        return endDate != null;
    }
} 