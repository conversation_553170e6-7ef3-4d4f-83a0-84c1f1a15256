package de.rwth.idsg.steve.repository.dto;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.joda.time.DateTime;

/**
 * 固件文件信息DTO
 */
@Getter
@Setter
@ToString
@Builder
public class FirmwareFile {
    
    private Integer firmwareFilePk;
    private String filename;
    private FileType fileType;
    private DateTime uploadTime;
    private UploadStatus uploadStatus;
    private Integer uploadedByUserPk;
    private Long fileSize;
    private String description;
    
    /**
     * 文件类型枚举
     */
    public enum FileType {
        FIRMWARE,    // .bin 文件
        APPLICATION  // .apk 文件
    }
    
    /**
     * Upload status enumeration
     */
    public enum UploadStatus {
        PENDING,   // File uploaded but firmware installation not yet confirmed
        SUCCESS,   // Firmware successfully installed on charge point
        FAILED     // Upload or installation failed
    }
    
    /**
     * 根据文件名判断文件类型
     */
    public static FileType getFileTypeFromFilename(String filename) {
        if (filename == null) {
            return null;
        }
        
        String lowerFilename = filename.toLowerCase();
        if (lowerFilename.endsWith(".bin")) {
            return FileType.FIRMWARE;
        } else if (lowerFilename.endsWith(".apk")) {
            return FileType.APPLICATION;
        }
        
        return null;
    }
    
    /**
     * Get file type description
     */
    public String getFileTypeDescription() {
        if (fileType == null) {
            return "Unknown";
        }

        switch (fileType) {
            case FIRMWARE:
                return "Firmware";
            case APPLICATION:
                return "Application";
            default:
                return "Unknown";
        }
    }
    
    /**
     * Get upload status description
     */
    public String getUploadStatusDescription() {
        if (uploadStatus == null) {
            return "Unknown";
        }

        switch (uploadStatus) {
            case PENDING:
                return "Pending Installation";
            case SUCCESS:
                return "Successfully Installed";
            case FAILED:
                return "Failed";
            default:
                return "Unknown";
        }
    }
    
    /**
     * Format file size
     */
    public String getFormattedFileSize() {
        if (fileSize == null || fileSize <= 0) {
            return "Unknown";
        }
        
        if (fileSize < 1024) {
            return fileSize + " B";
        } else if (fileSize < 1024 * 1024) {
            return String.format("%.1f KB", fileSize / 1024.0);
        } else if (fileSize < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", fileSize / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", fileSize / (1024.0 * 1024.0 * 1024.0));
        }
    }
}
