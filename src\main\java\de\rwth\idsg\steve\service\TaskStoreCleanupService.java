/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.service;

import de.rwth.idsg.steve.ocpp.CommunicationTask;
import de.rwth.idsg.steve.repository.TaskStore;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * TaskStore清理服务
 * 定期清理TaskStore中已完成的任务，防止内存泄漏
 * 
 * <AUTHOR> Community Team
 * @since 2025.01
 */
@Slf4j
@Service
public class TaskStoreCleanupService {
    
    @Autowired
    private TaskStore taskStore;
    
    /**
     * TaskStore清理间隔时间（分钟）
     */
    @Value("${steve.taskstore.cleanup.interval-minutes:15}")
    private int cleanupIntervalMinutes;
    
    /**
     * 服务启动时的初始化
     */
    @PostConstruct
    public void init() {
        log.info("🗂️ TaskStoreCleanupService initialized");
        log.info("⏰ TaskStore cleanup interval: {} minutes", cleanupIntervalMinutes);
    }
    
    /**
     * 定时清理TaskStore中已完成的任务
     * 使用Spring的@Scheduled注解，根据配置的间隔时间执行
     */
    @Scheduled(fixedRateString = "#{${steve.taskstore.cleanup.interval-minutes:15} * 60 * 1000}")
    public void scheduledTaskStoreCleanup() {
        log.info("🗂️ Scheduled TaskStore cleanup triggered at {}", 
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        performTaskStoreCleanup();
    }
    
    /**
     * 执行TaskStore清理操作
     */
    private void performTaskStoreCleanup() {
        try {
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            log.info("🗂️ Starting TaskStore cleanup at {}", timestamp);
            
            // 获取清理前的任务数量
            int taskCountBefore = taskStore.getOverview().size();
            log.info("📊 TaskStore tasks before cleanup: {}", taskCountBefore);
            
            // 清理已完成的任务
            taskStore.clearFinished();
            
            // 获取清理后的任务数量
            int taskCountAfter = taskStore.getOverview().size();
            int cleanedTasks = taskCountBefore - taskCountAfter;
            
            if (cleanedTasks > 0) {
                log.info("✅ TaskStore cleanup completed: {} finished tasks removed", cleanedTasks);
            } else {
                log.info("✅ TaskStore cleanup completed: no finished tasks found");
            }
            
            log.info("📊 TaskStore tasks after cleanup: {}", taskCountAfter);
            
        } catch (Exception e) {
            log.error("❌ Error during TaskStore cleanup: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 手动触发TaskStore清理（用于测试或管理界面）
     * 
     * @return 清理的任务数量
     */
    public int manualTaskStoreCleanup() {
        log.info("🔧 Manual TaskStore cleanup triggered");
        int taskCountBefore = taskStore.getOverview().size();
        performTaskStoreCleanup();
        int taskCountAfter = taskStore.getOverview().size();
        return taskCountBefore - taskCountAfter;
    }
    
    /**
     * 获取TaskStore清理服务状态信息
     * 
     * @return 状态信息
     */
    public String getStatus() {
        int currentTaskCount = taskStore.getOverview().size();
        return String.format("TaskStore cleanup service running - interval: %d min, current tasks: %d", 
                cleanupIntervalMinutes, currentTaskCount);
    }
}
