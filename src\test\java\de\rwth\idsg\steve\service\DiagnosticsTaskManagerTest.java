/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.service;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 诊断任务管理器测试
 * 
 * <AUTHOR> Community Team
 * @since 2025.01
 */
public class DiagnosticsTaskManagerTest {
    
    private DiagnosticsTaskManager taskManager;

    @Mock
    private DiagnosticFileDownloadService downloadService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        taskManager = new DiagnosticsTaskManager();

        // 使用反射设置测试值和依赖
        try {
            var field = DiagnosticsTaskManager.class.getDeclaredField("taskExpiryMinutes");
            field.setAccessible(true);
            field.set(taskManager, 10);

            var downloadServiceField = DiagnosticsTaskManager.class.getDeclaredField("downloadService");
            downloadServiceField.setAccessible(true);
            downloadServiceField.set(taskManager, downloadService);
        } catch (Exception e) {
            // 忽略反射错误，使用默认值
        }
    }
    
    @Test
    public void testRegisterTask() {
        String chargeBoxId = "TEST_CHARGER_001";
        Integer taskId = 12345;
        
        DiagnosticsTaskInfo taskInfo = taskManager.registerTask(chargeBoxId, taskId);
        
        assertNotNull(taskInfo);
        assertEquals(taskId, taskInfo.getTaskId());
        assertEquals(chargeBoxId, taskInfo.getChargeBoxId());
        assertTrue(taskInfo.isValid());
    }
    
    @Test
    public void testGetValidTask() {
        String chargeBoxId = "TEST_CHARGER_002";
        Integer taskId = 12346;
        
        // 注册任务
        taskManager.registerTask(chargeBoxId, taskId);
        
        // 获取有效任务
        DiagnosticsTaskInfo validTask = taskManager.getValidTask(chargeBoxId);
        assertNotNull(validTask);
        assertEquals(taskId, validTask.getTaskId());
        
        // 获取不存在的任务
        DiagnosticsTaskInfo nonExistentTask = taskManager.getValidTask("NON_EXISTENT");
        assertNull(nonExistentTask);
    }
    
    @Test
    public void testTaskExpiry() throws InterruptedException {
        String chargeBoxId = "TEST_CHARGER_003";
        Integer taskId = 12347;
        
        // 创建一个很短过期时间的任务信息（用于测试）
        DiagnosticsTaskInfo taskInfo = new DiagnosticsTaskInfo(taskId, chargeBoxId, 0); // 0分钟过期
        
        // 等待一小段时间确保过期
        Thread.sleep(100);
        
        assertTrue(taskInfo.isExpired());
        assertFalse(taskInfo.isValid());
    }
    
    @Test
    public void testMarkFileReady() {
        String chargeBoxId = "TEST_CHARGER_004";
        Integer taskId = 12348;
        String fileName = "test_diagnostics.log";
        
        taskManager.registerTask(chargeBoxId, taskId);
        taskManager.markFileReady(chargeBoxId, fileName);
        
        DiagnosticsTaskInfo taskInfo = taskManager.getValidTask(chargeBoxId);
        assertNotNull(taskInfo);
        assertEquals(fileName, taskInfo.getFileName());
        assertEquals(DiagnosticsTaskInfo.TaskStatus.FILE_READY, taskInfo.getStatus());
    }
    
    @Test
    public void testCleanupTaskForChargeBox() {
        String chargeBoxId = "TEST_CHARGER_005";
        Integer taskId = 12349;

        // 设置mock行为
        when(downloadService.hasDownloadableFile(chargeBoxId)).thenReturn(false);

        taskManager.registerTask(chargeBoxId, taskId);
        assertNotNull(taskManager.getValidTask(chargeBoxId));

        taskManager.cleanupTaskForChargeBox(chargeBoxId);
        assertNull(taskManager.getValidTask(chargeBoxId));

        // 验证mock调用
        verify(downloadService).hasDownloadableFile(chargeBoxId);
    }
}
