CREATE DATABASE  IF NOT EXISTS `stevedb` /*!40100 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci */ /*!80016 DEFAULT ENCRYPTION='N' */;
USE `stevedb`;
-- MySQL dump 10.13  Distrib 8.0.41, for Win64 (x86_64)
--
-- Host: localhost    Database: stevedb
-- ------------------------------------------------------
-- Server version	8.0.41

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `address`
--

DROP TABLE IF EXISTS `address`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `address` (
  `address_pk` int NOT NULL AUTO_INCREMENT,
  `street` varchar(1000) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `house_number` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `zip_code` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `city` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `country` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`address_pk`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `address`
--

LOCK TABLES `address` WRITE;
/*!40000 ALTER TABLE `address` DISABLE KEYS */;
INSERT INTO `address` VALUES (3,NULL,NULL,NULL,NULL,'BB'),(4,NULL,NULL,NULL,NULL,'BB'),(5,'Test Street','123','12345','Test City','Test Country');
/*!40000 ALTER TABLE `address` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `charge_box`
--

DROP TABLE IF EXISTS `charge_box`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `charge_box` (
  `charge_box_pk` int NOT NULL AUTO_INCREMENT,
  `charge_box_id` varchar(255) COLLATE utf8mb3_unicode_ci NOT NULL,
  `endpoint_address` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `ocpp_protocol` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `registration_status` varchar(255) COLLATE utf8mb3_unicode_ci NOT NULL DEFAULT 'Accepted',
  `charge_point_vendor` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `charge_point_model` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `charge_point_serial_number` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `charge_box_serial_number` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `fw_version` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `fw_update_status` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `fw_update_timestamp` timestamp(6) NULL DEFAULT NULL,
  `iccid` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `imsi` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `meter_type` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `meter_serial_number` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `diagnostics_status` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `diagnostics_timestamp` timestamp(6) NULL DEFAULT NULL,
  `last_heartbeat_timestamp` timestamp(6) NULL DEFAULT NULL,
  `description` mediumtext COLLATE utf8mb3_unicode_ci,
  `note` mediumtext COLLATE utf8mb3_unicode_ci,
  `location_latitude` decimal(11,8) DEFAULT NULL,
  `location_longitude` decimal(11,8) DEFAULT NULL,
  `address_pk` int DEFAULT NULL,
  `admin_address` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `insert_connector_status_after_transaction_msg` tinyint(1) DEFAULT '1',
  `station_name` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL COMMENT '电站名称',
  `operator_name` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL COMMENT '运营商名称',
  `build_date` date DEFAULT NULL COMMENT '建站时间',
  `operation_date` date DEFAULT NULL COMMENT '正式投运时间',
  `last_session_id` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL COMMENT '最后一次连接的会话ID',
  `last_connection_time` timestamp NULL DEFAULT NULL COMMENT '最后一次连接时间',
  `reconnect_required` tinyint(1) DEFAULT '0' COMMENT '重启后是否需要强制重连',
  `in_transaction` tinyint(1) DEFAULT '0' COMMENT '是否有进行中的事务',
  PRIMARY KEY (`charge_box_pk`),
  UNIQUE KEY `chargeBoxId_UNIQUE` (`charge_box_id`),
  KEY `chargebox_op_ep_idx` (`ocpp_protocol`,`endpoint_address`),
  KEY `FK_charge_box_address_apk` (`address_pk`),
  KEY `idx_charge_box_station_name` (`station_name`),
  KEY `idx_charge_box_operator_name` (`operator_name`),
  KEY `idx_charge_box_build_date` (`build_date`),
  KEY `idx_charge_box_operation_date` (`operation_date`),
  CONSTRAINT `FK_charge_box_address_apk` FOREIGN KEY (`address_pk`) REFERENCES `address` (`address_pk`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `charge_box`
--

LOCK TABLES `charge_box` WRITE;
/*!40000 ALTER TABLE `charge_box` DISABLE KEYS */;
INSERT INTO `charge_box` VALUES (1,'CP002',NULL,'ocpp1.6J','Accepted','SETEC-POWER','DC','CP002','SN020000000000','1.6.200.250508,v181  ','InstallationFailed','2025-05-09 03:35:31.185000',NULL,NULL,'Internal Meter',NULL,NULL,NULL,'2025-05-09 03:43:23.754000',NULL,NULL,NULL,NULL,NULL,NULL,0,NULL,NULL,NULL,NULL,'a5cfaca8-5226-7ab0-bb45-906cc40d82b4','2025-05-09 03:29:24',0,0),(2,'CP003',NULL,'ocpp1.6J','Accepted','SETEC-POWER','Mixed','CP003','SN60fb00ac68b8','1.6.200.20250428,v  ','Installing','2025-05-08 07:06:26.954000',NULL,NULL,'Internal Meter',NULL,NULL,NULL,'2025-05-08 09:10:48.537000',NULL,NULL,NULL,NULL,NULL,NULL,0,NULL,NULL,NULL,NULL,'b5897b00-36e2-f67c-e430-1c5dc28904ca','2025-05-08 09:00:48',0,0),(3,'CP001',NULL,'ocpp1.6J','Accepted','SETEC-POWER','DC','CP001','SN020000000000','1.6.200.20250428,v180  ','Downloading','2025-05-09 02:49:34.723000',NULL,NULL,'Internal Meter',NULL,'Uploaded','2025-05-08 01:37:48.285000','2025-05-09 02:49:18.081000',NULL,NULL,NULL,NULL,NULL,NULL,0,NULL,NULL,NULL,NULL,'b1fc57ed-355a-c316-e185-02ae52b16924','2025-05-09 02:53:56',0,0),(4,'CP004',NULL,'ocpp1.6J','Accepted','SETEC-POWER','Mixed','CP004','SN020000000000','1.6.200.250424,v179  ',NULL,NULL,NULL,NULL,'Internal Meter',NULL,NULL,NULL,'2025-04-27 08:50:07.612000',NULL,NULL,NULL,NULL,NULL,NULL,0,NULL,NULL,NULL,NULL,NULL,NULL,0,0),(5,'CP005',NULL,'ocpp1.6J','Accepted','SETEC-POWER','Mixed','CP005','SN020000000000','1.6.200.20250428,v172  ','DownloadFailed','2025-04-29 08:14:21.741000',NULL,NULL,'Internal Meter',NULL,NULL,NULL,'2025-04-29 08:42:55.518000',NULL,NULL,NULL,NULL,NULL,NULL,0,NULL,NULL,NULL,NULL,NULL,NULL,0,0),(6,'CP006',NULL,NULL,'Accepted',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,NULL,NULL,NULL,NULL,NULL,NULL,0,0),(7,'CP007',NULL,'ocpp1.6J','Accepted','SETEC-POWER','DC','CP007','SNc43cb0a87797','1.6.200.250430,v177  ','Downloading','2025-05-08 09:03:53.607000',NULL,NULL,'Internal Meter',NULL,NULL,NULL,'2025-05-08 09:10:53.493000',NULL,NULL,NULL,NULL,NULL,NULL,0,NULL,NULL,NULL,NULL,'4419517d-8648-1e82-ded6-1d61972057b0','2025-05-08 09:00:48',0,0),(8,'CP008',NULL,'ocpp1.6J','Accepted','SETEC-POWER','Mixed','CP008','SN020000000000','1.6.200.20250428,v181  ','DownloadFailed','2025-04-30 03:24:49.382000',NULL,NULL,'Internal Meter',NULL,NULL,NULL,'2025-04-30 03:35:36.969000',NULL,NULL,NULL,NULL,NULL,NULL,0,NULL,NULL,NULL,NULL,NULL,NULL,0,0),(9,'CP009',NULL,'ocpp1.6J','Accepted','SETEC-POWER','Mixed','CP009','SN020000000000','1.6.200.20250428,v181  ',NULL,NULL,NULL,NULL,'Internal Meter',NULL,NULL,NULL,'2025-05-06 05:53:23.502000',NULL,NULL,NULL,NULL,NULL,NULL,0,NULL,NULL,NULL,NULL,'111fe14f-c729-eff7-f5d0-b3fc9f15dff5','2025-05-06 05:53:22',0,0),(10,'CP010',NULL,NULL,'Accepted',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,NULL,NULL,NULL,NULL,NULL,NULL,0,0);
/*!40000 ALTER TABLE `charge_box` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `charger_issue`
--

DROP TABLE IF EXISTS `charger_issue`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `charger_issue` (
  `issue_id` int NOT NULL AUTO_INCREMENT,
  `charge_box_pk` int NOT NULL,
  `report_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `reporter_user_pk` int DEFAULT NULL,
  `fault_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` enum('NEW','IN_PROGRESS','RESOLVED') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'NEW',
  `ocpp_error_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_auto_reported` tinyint(1) NOT NULL DEFAULT '0',
  `resolve_time` timestamp NULL DEFAULT NULL,
  `resolve_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  PRIMARY KEY (`issue_id`),
  KEY `reporter_user_pk` (`reporter_user_pk`),
  KEY `idx_charge_box_pk` (`charge_box_pk`),
  KEY `idx_status` (`status`),
  KEY `idx_report_time` (`report_time`),
  CONSTRAINT `charger_issue_ibfk_1` FOREIGN KEY (`charge_box_pk`) REFERENCES `charge_box` (`charge_box_pk`) ON DELETE CASCADE,
  CONSTRAINT `charger_issue_ibfk_2` FOREIGN KEY (`reporter_user_pk`) REFERENCES `web_user` (`web_user_pk`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=130 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `charger_issue`
--

LOCK TABLES `charger_issue` WRITE;
/*!40000 ALTER TABLE `charger_issue` DISABLE KEYS */;
INSERT INTO `charger_issue` VALUES (1,1,'2025-04-21 07:12:42',10,'充电桩显示屏无法正常显示','RESOLVED',NULL,0,'2025-04-22 03:27:51','test'),(2,2,'2025-04-21 07:12:45',NULL,'充电接口损坏，无法正常充电','RESOLVED','ConnectorError',0,'2025-04-22 00:42:36','test'),(3,3,'2025-04-21 09:36:00',1,'test','IN_PROGRESS',NULL,0,NULL,NULL),(4,6,'2025-04-22 03:00:00',1,'test','IN_PROGRESS',NULL,0,NULL,NULL),(5,5,'2025-04-22 03:27:00',1,'test1','IN_PROGRESS',NULL,0,NULL,NULL),(6,4,'2025-04-22 03:48:00',1,'test','IN_PROGRESS',NULL,0,NULL,NULL),(7,7,'2025-04-22 05:35:00',1,'test','IN_PROGRESS',NULL,0,NULL,NULL),(8,8,'2025-04-22 05:53:00',1,'test','IN_PROGRESS','test',0,NULL,NULL),(9,1,'2025-04-18 00:00:00',1,'test','IN_PROGRESS',NULL,0,NULL,NULL),(10,1,'2025-04-22 06:34:00',1,'test333','IN_PROGRESS',NULL,0,NULL,NULL),(11,3,'2025-04-22 07:44:00',1,'test1','NEW',NULL,0,NULL,NULL),(12,8,'2025-04-23 01:18:00',1,'test','NEW',NULL,0,NULL,NULL),(13,5,'2025-04-23 01:22:00',1,'you kown,just test','NEW',NULL,0,NULL,NULL),(14,3,'2025-04-23 01:26:00',1,'just test','NEW',NULL,0,NULL,NULL),(15,7,'2025-04-23 01:34:00',1,'test','RESOLVED',NULL,0,'2025-04-23 01:36:51','test7'),(16,2,'2025-04-23 08:30:45',NULL,'充电桩 CP003 连接器 0 报告故障。','NEW','OtherError',1,NULL,NULL),(17,2,'2025-04-23 08:30:45',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 0','NEW','OtherError',1,NULL,NULL),(18,2,'2025-04-23 08:30:45',NULL,'充电桩 CP003 连接器 2 报告故障。','NEW','OtherError',1,NULL,NULL),(19,2,'2025-04-23 08:30:45',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 2','NEW','OtherError',1,NULL,NULL),(20,2,'2025-04-23 08:30:45',NULL,'充电桩 CP003 连接器 1 报告故障。','NEW','OtherError',1,NULL,NULL),(21,2,'2025-04-23 08:30:45',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 1','NEW','OtherError',1,NULL,NULL),(22,2,'2025-04-23 08:30:45',NULL,'充电桩 CP003 连接器 0 报告故障。','NEW','OtherError',1,NULL,NULL),(23,2,'2025-04-23 08:30:45',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 0','NEW','OtherError',1,NULL,NULL),(24,2,'2025-04-23 08:31:01',NULL,'充电桩 CP003 连接器 2 报告故障。','NEW','OtherError',1,NULL,NULL),(25,2,'2025-04-23 08:31:01',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 2','NEW','OtherError',1,NULL,NULL),(26,2,'2025-04-23 08:31:01',NULL,'充电桩 CP003 连接器 1 报告故障。','NEW','OtherError',1,NULL,NULL),(27,2,'2025-04-23 08:31:01',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 1','NEW','OtherError',1,NULL,NULL),(28,2,'2025-04-23 08:31:01',NULL,'充电桩 CP003 连接器 0 报告故障。','NEW','OtherError',1,NULL,NULL),(29,2,'2025-04-23 08:31:01',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 0','NEW','OtherError',1,NULL,NULL),(30,2,'2025-04-24 02:03:11',NULL,'充电桩 CP003 连接器 0 报告故障。','NEW','OtherError',1,NULL,NULL),(31,2,'2025-04-24 02:03:11',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 0','NEW','OtherError',1,NULL,NULL),(32,2,'2025-04-24 02:03:11',NULL,'充电桩 CP003 连接器 2 报告故障。','NEW','OtherError',1,NULL,NULL),(33,2,'2025-04-24 02:03:11',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 2','NEW','OtherError',1,NULL,NULL),(34,2,'2025-04-24 02:03:11',NULL,'充电桩 CP003 连接器 1 报告故障。','NEW','OtherError',1,NULL,NULL),(35,2,'2025-04-24 02:03:11',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 1','NEW','OtherError',1,NULL,NULL),(36,2,'2025-04-24 02:03:11',NULL,'充电桩 CP003 连接器 0 报告故障。','NEW','OtherError',1,NULL,NULL),(37,2,'2025-04-24 02:03:11',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 0','NEW','OtherError',1,NULL,NULL),(38,2,'2025-04-24 02:32:03',NULL,'充电桩 CP003 连接器 0 报告故障。','NEW','OtherError',1,NULL,NULL),(39,2,'2025-04-24 02:32:03',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 0','NEW','OtherError',1,NULL,NULL),(40,2,'2025-04-24 02:32:03',NULL,'充电桩 CP003 连接器 1 报告故障。','NEW','OtherError',1,NULL,NULL),(41,2,'2025-04-24 02:32:03',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 1','NEW','OtherError',1,NULL,NULL),(42,2,'2025-04-24 02:32:03',NULL,'充电桩 CP003 连接器 0 报告故障。','NEW','OtherError',1,NULL,NULL),(43,2,'2025-04-24 02:32:03',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 0','NEW','OtherError',1,NULL,NULL),(44,2,'2025-04-24 02:36:40',NULL,'充电桩 CP003 连接器 0 报告故障。','NEW','OtherError',1,NULL,NULL),(45,2,'2025-04-24 02:36:40',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 0','NEW','OtherError',1,NULL,NULL),(46,2,'2025-04-24 02:36:41',NULL,'充电桩 CP003 连接器 2 报告故障。','NEW','OtherError',1,NULL,NULL),(47,2,'2025-04-24 02:36:41',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 2','NEW','OtherError',1,NULL,NULL),(48,2,'2025-04-24 02:36:41',NULL,'充电桩 CP003 连接器 2 报告故障。','NEW','OtherError',1,NULL,NULL),(49,2,'2025-04-24 02:36:41',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 2','NEW','OtherError',1,NULL,NULL),(50,2,'2025-04-24 02:36:41',NULL,'充电桩 CP003 连接器 1 报告故障。','NEW','OtherError',1,NULL,NULL),(51,2,'2025-04-24 02:36:41',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 1','NEW','OtherError',1,NULL,NULL),(52,2,'2025-04-24 02:36:41',NULL,'充电桩 CP003 连接器 1 报告故障。','NEW','OtherError',1,NULL,NULL),(53,2,'2025-04-24 02:36:41',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 1','NEW','OtherError',1,NULL,NULL),(54,2,'2025-04-24 02:36:41',NULL,'充电桩 CP003 连接器 0 报告故障。','NEW','OtherError',1,NULL,NULL),(55,2,'2025-04-24 02:36:41',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 0','NEW','OtherError',1,NULL,NULL),(56,2,'2025-04-24 02:36:41',NULL,'充电桩 CP003 连接器 0 报告故障。','NEW','OtherError',1,NULL,NULL),(57,2,'2025-04-24 02:36:42',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 0','NEW','OtherError',1,NULL,NULL),(58,2,'2025-04-24 02:37:18',NULL,'充电桩 CP003 连接器 0 报告故障。','NEW','OtherError',1,NULL,NULL),(59,2,'2025-04-24 02:37:18',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 0','NEW','OtherError',1,NULL,NULL),(60,2,'2025-04-24 02:37:18',NULL,'充电桩 CP003 连接器 2 报告故障。','NEW','OtherError',1,NULL,NULL),(61,2,'2025-04-24 02:37:18',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 2','NEW','OtherError',1,NULL,NULL),(62,2,'2025-04-24 02:37:18',NULL,'充电桩 CP003 连接器 1 报告故障。','NEW','OtherError',1,NULL,NULL),(63,2,'2025-04-24 02:37:18',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 1','NEW','OtherError',1,NULL,NULL),(64,2,'2025-04-24 02:37:18',NULL,'充电桩 CP003 连接器 0 报告故障。','NEW','OtherError',1,NULL,NULL),(65,2,'2025-04-24 02:37:18',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 0','NEW','OtherError',1,NULL,NULL),(66,2,'2025-04-24 02:59:33',NULL,'充电桩 CP003 连接器 2 报告故障。','NEW','OtherError',1,NULL,NULL),(67,2,'2025-04-24 02:59:33',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 2','NEW','OtherError',1,NULL,NULL),(68,2,'2025-04-24 02:59:33',NULL,'充电桩 CP003 连接器 1 报告故障。','NEW','OtherError',1,NULL,NULL),(69,2,'2025-04-24 02:59:33',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 1','NEW','OtherError',1,NULL,NULL),(70,2,'2025-04-24 02:59:33',NULL,'充电桩 CP003 连接器 0 报告故障。','NEW','OtherError',1,NULL,NULL),(71,2,'2025-04-24 02:59:33',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 0','NEW','OtherError',1,NULL,NULL),(72,2,'2025-04-24 02:59:33',NULL,'充电桩 CP003 连接器 2 报告故障。','NEW','OtherError',1,NULL,NULL),(73,2,'2025-04-24 02:59:33',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 2','NEW','OtherError',1,NULL,NULL),(74,2,'2025-04-24 02:59:33',NULL,'充电桩 CP003 连接器 1 报告故障。','NEW','OtherError',1,NULL,NULL),(75,2,'2025-04-24 02:59:33',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 1','NEW','OtherError',1,NULL,NULL),(76,2,'2025-04-24 02:59:33',NULL,'充电桩 CP003 连接器 0 报告故障。','NEW','OtherError',1,NULL,NULL),(77,2,'2025-04-24 02:59:33',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 0','NEW','OtherError',1,NULL,NULL),(78,2,'2025-04-24 03:02:01',NULL,'充电桩 CP003 连接器 2 报告故障。','NEW','OtherError',1,NULL,NULL),(79,2,'2025-04-24 03:02:01',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 2','NEW','OtherError',1,NULL,NULL),(80,2,'2025-04-24 03:02:01',NULL,'充电桩 CP003 连接器 1 报告故障。','NEW','OtherError',1,NULL,NULL),(81,2,'2025-04-24 03:02:01',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 1','NEW','OtherError',1,NULL,NULL),(82,2,'2025-04-24 03:02:01',NULL,'充电桩 CP003 连接器 0 报告故障。','NEW','OtherError',1,NULL,NULL),(83,2,'2025-04-24 03:02:01',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 0','NEW','OtherError',1,NULL,NULL),(84,2,'2025-04-24 03:02:35',NULL,'充电桩 CP003 连接器 2 报告故障。','NEW','OtherError',1,NULL,NULL),(85,2,'2025-04-24 03:02:35',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 2','NEW','OtherError',1,NULL,NULL),(86,2,'2025-04-24 03:02:35',NULL,'充电桩 CP003 连接器 1 报告故障。','NEW','OtherError',1,NULL,NULL),(87,2,'2025-04-24 03:02:35',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 1','NEW','OtherError',1,NULL,NULL),(88,2,'2025-04-24 03:02:35',NULL,'充电桩 CP003 连接器 0 报告故障。','NEW','OtherError',1,NULL,NULL),(89,2,'2025-04-24 03:02:35',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 0','NEW','OtherError',1,NULL,NULL),(90,2,'2025-04-24 03:03:55',NULL,'充电桩 CP003 连接器 2 报告故障。','NEW','OtherError',1,NULL,NULL),(91,2,'2025-04-24 03:03:55',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 2','NEW','OtherError',1,NULL,NULL),(92,2,'2025-04-24 03:03:55',NULL,'充电桩 CP003 连接器 1 报告故障。','NEW','OtherError',1,NULL,NULL),(93,2,'2025-04-24 03:03:55',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 1','NEW','OtherError',1,NULL,NULL),(94,2,'2025-04-24 03:03:55',NULL,'充电桩 CP003 连接器 0 报告故障。','NEW','OtherError',1,NULL,NULL),(95,2,'2025-04-24 03:03:55',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 0','NEW','OtherError',1,NULL,NULL),(96,2,'2025-04-24 03:04:52',NULL,'充电桩 CP003 连接器 2 报告故障。','NEW','OtherError',1,NULL,NULL),(97,2,'2025-04-24 03:04:52',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 2','NEW','OtherError',1,NULL,NULL),(98,2,'2025-04-24 03:04:52',NULL,'充电桩 CP003 连接器 1 报告故障。','NEW','OtherError',1,NULL,NULL),(99,2,'2025-04-24 03:04:52',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 1','NEW','OtherError',1,NULL,NULL),(100,2,'2025-04-24 03:04:52',NULL,'充电桩 CP003 连接器 0 报告故障。','NEW','OtherError',1,NULL,NULL),(101,2,'2025-04-24 03:04:52',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 0','NEW','OtherError',1,NULL,NULL),(102,2,'2025-04-24 03:12:02',NULL,'充电桩 CP003 连接器 2 报告故障。','NEW','OtherError',1,NULL,NULL),(103,2,'2025-04-24 03:12:02',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 2','NEW','OtherError',1,NULL,NULL),(104,2,'2025-04-24 03:12:02',NULL,'充电桩 CP003 连接器 1 报告故障。','NEW','OtherError',1,NULL,NULL),(105,2,'2025-04-24 03:12:02',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 1','NEW','OtherError',1,NULL,NULL),(106,2,'2025-04-24 03:12:02',NULL,'充电桩 CP003 连接器 0 报告故障。','NEW','OtherError',1,NULL,NULL),(107,2,'2025-04-24 03:12:02',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 0','NEW','OtherError',1,NULL,NULL),(108,2,'2025-04-24 03:17:32',NULL,'充电桩 CP003 连接器 2 报告故障。','NEW','OtherError',1,NULL,NULL),(109,2,'2025-04-24 03:17:32',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 2','NEW','OtherError',1,NULL,NULL),(110,2,'2025-04-24 03:17:32',NULL,'充电桩 CP003 连接器 1 报告故障。','NEW','OtherError',1,NULL,NULL),(111,2,'2025-04-24 03:17:32',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 1','NEW','OtherError',1,NULL,NULL),(112,2,'2025-04-24 03:17:32',NULL,'充电桩 CP003 连接器 0 报告故障。','NEW','OtherError',1,NULL,NULL),(113,2,'2025-04-24 03:17:32',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 0','NEW','OtherError',1,NULL,NULL),(114,2,'2025-04-24 03:24:27',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 2','NEW','OtherError',1,NULL,NULL),(115,2,'2025-04-24 03:24:27',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 1','NEW','OtherError',1,NULL,NULL),(116,2,'2025-04-24 03:24:27',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 0','NEW','OtherError',1,NULL,NULL),(117,2,'2025-04-24 03:32:20',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 2','NEW','OtherError',1,NULL,NULL),(118,2,'2025-04-24 03:32:20',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 1','NEW','OtherError',1,NULL,NULL),(119,2,'2025-04-24 03:32:20',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 0','NEW','OtherError',1,NULL,NULL),(120,2,'2025-04-24 03:38:39',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 2','NEW','OtherError',1,NULL,NULL),(121,2,'2025-04-24 03:38:39',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 1','NEW','OtherError',1,NULL,NULL),(122,2,'2025-04-24 03:38:39',NULL,'系统自动创建的故障记录。详细信息: Emergency。连接器ID: 0','NEW','OtherError',1,NULL,NULL),(123,2,'2025-04-24 03:42:20',NULL,'System auto-created fault record. Details: Emergency. Connector ID: 2','NEW','OtherError',1,NULL,NULL),(124,2,'2025-04-24 03:42:20',NULL,'System auto-created fault record. Details: Emergency. Connector ID: 1','NEW','OtherError',1,NULL,NULL),(125,2,'2025-04-24 03:42:20',NULL,'System auto-created fault record. Details: Emergency. Connector ID: 0','NEW','OtherError',1,NULL,NULL),(126,2,'2025-04-24 06:50:29',NULL,'System auto-created fault record. Details: Emergency. Connector ID: 0','NEW','OtherError',1,NULL,NULL),(127,2,'2025-04-24 06:50:29',NULL,'System auto-created fault record. Details: Emergency. Connector ID: 2','NEW','OtherError',1,NULL,NULL),(128,2,'2025-04-24 06:50:29',NULL,'System auto-created fault record. Details: Emergency. Connector ID: 1','NEW','OtherError',1,NULL,NULL),(129,2,'2025-04-24 06:50:29',NULL,'System auto-created fault record. Details: Emergency. Connector ID: 0','NEW','OtherError',1,NULL,NULL);
/*!40000 ALTER TABLE `charger_issue` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `charging_profile`
--

DROP TABLE IF EXISTS `charging_profile`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `charging_profile` (
  `charging_profile_pk` int NOT NULL AUTO_INCREMENT,
  `stack_level` int NOT NULL,
  `charging_profile_purpose` varchar(255) COLLATE utf8mb3_unicode_ci NOT NULL,
  `charging_profile_kind` varchar(255) COLLATE utf8mb3_unicode_ci NOT NULL,
  `recurrency_kind` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `valid_from` timestamp(6) NULL DEFAULT NULL,
  `valid_to` timestamp(6) NULL DEFAULT NULL,
  `duration_in_seconds` int DEFAULT NULL,
  `start_schedule` timestamp(6) NULL DEFAULT NULL,
  `charging_rate_unit` varchar(255) COLLATE utf8mb3_unicode_ci NOT NULL,
  `min_charging_rate` decimal(15,1) DEFAULT NULL,
  `description` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `note` text COLLATE utf8mb3_unicode_ci,
  PRIMARY KEY (`charging_profile_pk`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `charging_profile`
--

LOCK TABLES `charging_profile` WRITE;
/*!40000 ALTER TABLE `charging_profile` DISABLE KEYS */;
/*!40000 ALTER TABLE `charging_profile` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `charging_schedule_period`
--

DROP TABLE IF EXISTS `charging_schedule_period`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `charging_schedule_period` (
  `charging_profile_pk` int NOT NULL,
  `start_period_in_seconds` int NOT NULL,
  `power_limit` decimal(15,1) NOT NULL,
  `number_phases` int DEFAULT NULL,
  UNIQUE KEY `UQ_charging_schedule_period` (`charging_profile_pk`,`start_period_in_seconds`),
  CONSTRAINT `FK_charging_schedule_period_charging_profile_pk` FOREIGN KEY (`charging_profile_pk`) REFERENCES `charging_profile` (`charging_profile_pk`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `charging_schedule_period`
--

LOCK TABLES `charging_schedule_period` WRITE;
/*!40000 ALTER TABLE `charging_schedule_period` DISABLE KEYS */;
/*!40000 ALTER TABLE `charging_schedule_period` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `charging_station`
--

DROP TABLE IF EXISTS `charging_station`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `charging_station` (
  `charging_station_pk` int NOT NULL AUTO_INCREMENT,
  `station_name` varchar(255) NOT NULL,
  `operator_name` varchar(255) DEFAULT NULL,
  `construction_date` timestamp NULL DEFAULT NULL,
  `operation_date` timestamp NULL DEFAULT NULL,
  `created_on` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `last_updated_on` timestamp NULL DEFAULT NULL,
  `location` varchar(255) DEFAULT NULL,
  `web_user_pk` int DEFAULT NULL,
  PRIMARY KEY (`charging_station_pk`),
  KEY `fk_charging_station_web_user` (`web_user_pk`),
  CONSTRAINT `fk_charging_station_web_user` FOREIGN KEY (`web_user_pk`) REFERENCES `web_user` (`web_user_pk`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `charging_station`
--

LOCK TABLES `charging_station` WRITE;
/*!40000 ALTER TABLE `charging_station` DISABLE KEYS */;
INSERT INTO `charging_station` VALUES (1,'Test Station','Test Operator',NULL,NULL,'2025-04-27 06:10:28',NULL,'Test Location',1),(2,'Test Station 2','Test Operator 2','2024-05-15 11:11:00','2024-06-19 07:13:00','2025-04-27 06:10:34',NULL,'Test Location 2',NULL),(3,'test1111','test1111','2025-04-02 00:00:00','2025-04-16 00:00:00','2025-04-27 07:31:54',NULL,NULL,NULL);
/*!40000 ALTER TABLE `charging_station` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `charging_station_user`
--

DROP TABLE IF EXISTS `charging_station_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `charging_station_user` (
  `charging_station_user_pk` int NOT NULL AUTO_INCREMENT,
  `charging_station_pk` int NOT NULL,
  `user_pk` int NOT NULL,
  PRIMARY KEY (`charging_station_user_pk`),
  UNIQUE KEY `unique_cs_user` (`charging_station_pk`,`user_pk`),
  KEY `user_pk` (`user_pk`),
  CONSTRAINT `charging_station_user_ibfk_1` FOREIGN KEY (`charging_station_pk`) REFERENCES `charging_station` (`charging_station_pk`) ON DELETE CASCADE,
  CONSTRAINT `charging_station_user_ibfk_2` FOREIGN KEY (`user_pk`) REFERENCES `user` (`user_pk`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `charging_station_user`
--

LOCK TABLES `charging_station_user` WRITE;
/*!40000 ALTER TABLE `charging_station_user` DISABLE KEYS */;
/*!40000 ALTER TABLE `charging_station_user` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `connector`
--

DROP TABLE IF EXISTS `connector`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `connector` (
  `connector_pk` int unsigned NOT NULL AUTO_INCREMENT,
  `charge_box_id` varchar(255) COLLATE utf8mb3_unicode_ci NOT NULL,
  `connector_id` int NOT NULL,
  PRIMARY KEY (`connector_pk`),
  UNIQUE KEY `connector_pk_UNIQUE` (`connector_pk`),
  UNIQUE KEY `connector_cbid_cid_UNIQUE` (`charge_box_id`,`connector_id`),
  CONSTRAINT `FK_connector_charge_box_cbid` FOREIGN KEY (`charge_box_id`) REFERENCES `charge_box` (`charge_box_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=351 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `connector`
--

LOCK TABLES `connector` WRITE;
/*!40000 ALTER TABLE `connector` DISABLE KEYS */;
INSERT INTO `connector` VALUES (183,'CP001',0),(179,'CP001',1),(180,'CP001',2),(181,'CP001',3),(182,'CP001',4),(3,'CP002',0),(2,'CP002',1),(1,'CP002',2),(9,'CP003',0),(8,'CP003',1),(7,'CP003',2),(246,'CP003',3),(247,'CP003',4),(134,'CP004',0),(130,'CP004',1),(131,'CP004',2),(132,'CP004',3),(133,'CP004',4),(288,'CP005',0),(284,'CP005',1),(285,'CP005',2),(286,'CP005',3),(287,'CP005',4),(308,'CP007',0),(304,'CP007',1),(305,'CP007',2),(306,'CP007',3),(307,'CP007',4),(311,'CP008',0),(309,'CP008',1),(310,'CP008',2),(314,'CP009',0),(312,'CP009',1),(313,'CP009',2);
/*!40000 ALTER TABLE `connector` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `connector_charging_profile`
--

DROP TABLE IF EXISTS `connector_charging_profile`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `connector_charging_profile` (
  `connector_pk` int unsigned NOT NULL,
  `charging_profile_pk` int NOT NULL,
  UNIQUE KEY `UQ_connector_charging_profile` (`connector_pk`,`charging_profile_pk`),
  KEY `FK_connector_charging_profile_charging_profile_pk` (`charging_profile_pk`),
  CONSTRAINT `FK_connector_charging_profile_charging_profile_pk` FOREIGN KEY (`charging_profile_pk`) REFERENCES `charging_profile` (`charging_profile_pk`),
  CONSTRAINT `FK_connector_charging_profile_connector_pk` FOREIGN KEY (`connector_pk`) REFERENCES `connector` (`connector_pk`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `connector_charging_profile`
--

LOCK TABLES `connector_charging_profile` WRITE;
/*!40000 ALTER TABLE `connector_charging_profile` DISABLE KEYS */;
/*!40000 ALTER TABLE `connector_charging_profile` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `connector_meter_value`
--

DROP TABLE IF EXISTS `connector_meter_value`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `connector_meter_value` (
  `connector_pk` int unsigned NOT NULL,
  `transaction_pk` int unsigned DEFAULT NULL,
  `value_timestamp` timestamp(6) NULL DEFAULT NULL,
  `value` text COLLATE utf8mb3_unicode_ci,
  `reading_context` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `format` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `measurand` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `location` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `unit` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `phase` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  KEY `FK_cm_pk_idx` (`connector_pk`),
  KEY `FK_tid_cm_idx` (`transaction_pk`),
  KEY `cmv_value_timestamp_idx` (`value_timestamp`),
  CONSTRAINT `FK_pk_cm` FOREIGN KEY (`connector_pk`) REFERENCES `connector` (`connector_pk`) ON DELETE CASCADE,
  CONSTRAINT `FK_tid_cm` FOREIGN KEY (`transaction_pk`) REFERENCES `transaction_start` (`transaction_pk`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `connector_meter_value`
--

LOCK TABLES `connector_meter_value` WRITE;
/*!40000 ALTER TABLE `connector_meter_value` DISABLE KEYS */;
INSERT INTO `connector_meter_value` VALUES (130,1,'2025-04-27 08:21:53.094000','0.00','Transaction.Begin','Raw','Energy.Active.Import.Register',NULL,'Wh',NULL),(130,1,'2025-04-27 08:21:53.094000','99.00','Transaction.Begin','Raw','SoC',NULL,'Percent',NULL),(130,1,'2025-04-27 08:21:53.094000','297.00','Transaction.Begin','Raw','Voltage',NULL,'V',NULL),(130,1,'2025-04-27 08:21:53.094000','0.00','Transaction.Begin','Raw','Power.Active.Import',NULL,'W',NULL),(130,1,'2025-04-27 08:21:53.094000','0.00','Transaction.Begin','Raw','Current.Import',NULL,'A',NULL),(130,1,'2025-04-27 08:21:53.094000','0.92','Transaction.Begin','Raw','Power.Factor',NULL,'Percent',NULL),(130,1,'2025-04-27 08:22:36.498000','0.00','Transaction.End','Raw','Energy.Active.Import.Register',NULL,'Wh',NULL),(130,1,'2025-04-27 08:22:36.498000','99.00','Transaction.End','Raw','SoC',NULL,'Percent',NULL),(130,1,'2025-04-27 08:22:36.498000','9.40','Transaction.End','Raw','Voltage',NULL,'V',NULL),(130,1,'2025-04-27 08:22:36.498000','1.88','Transaction.End','Raw','Power.Active.Import',NULL,'W',NULL),(130,1,'2025-04-27 08:22:36.498000','0.20','Transaction.End','Raw','Current.Import',NULL,'A',NULL),(130,1,'2025-04-27 08:22:36.498000','0.92','Transaction.End','Raw','Power.Factor',NULL,'Percent',NULL),(130,2,'2025-04-27 08:23:12.945000','0.00','Transaction.Begin','Raw','Energy.Active.Import.Register',NULL,'Wh',NULL),(130,2,'2025-04-27 08:23:12.945000','99.00','Transaction.Begin','Raw','SoC',NULL,'Percent',NULL),(130,2,'2025-04-27 08:23:12.945000','286.80','Transaction.Begin','Raw','Voltage',NULL,'V',NULL),(130,2,'2025-04-27 08:23:12.945000','0.00','Transaction.Begin','Raw','Power.Active.Import',NULL,'W',NULL),(130,2,'2025-04-27 08:23:12.945000','0.00','Transaction.Begin','Raw','Current.Import',NULL,'A',NULL),(130,2,'2025-04-27 08:23:12.945000','0.92','Transaction.Begin','Raw','Power.Factor',NULL,'Percent',NULL),(130,2,'2025-04-27 08:23:42.099000','0.00','Transaction.End','Raw','Energy.Active.Import.Register',NULL,'Wh',NULL),(130,2,'2025-04-27 08:23:42.099000','99.00','Transaction.End','Raw','SoC',NULL,'Percent',NULL),(130,2,'2025-04-27 08:23:42.099000','7.80','Transaction.End','Raw','Voltage',NULL,'V',NULL),(130,2,'2025-04-27 08:23:42.099000','0.00','Transaction.End','Raw','Power.Active.Import',NULL,'W',NULL),(130,2,'2025-04-27 08:23:42.099000','0.00','Transaction.End','Raw','Current.Import',NULL,'A',NULL),(130,2,'2025-04-27 08:23:42.099000','0.92','Transaction.End','Raw','Power.Factor',NULL,'Percent',NULL),(130,3,'2025-04-27 08:46:11.117000','0.00','Transaction.Begin','Raw','Energy.Active.Import.Register',NULL,'Wh',NULL),(130,3,'2025-04-27 08:46:11.117000','99.00','Transaction.Begin','Raw','SoC',NULL,'Percent',NULL),(130,3,'2025-04-27 08:46:11.117000','298.20','Transaction.Begin','Raw','Voltage',NULL,'V',NULL),(130,3,'2025-04-27 08:46:11.117000','0.00','Transaction.Begin','Raw','Power.Active.Import',NULL,'W',NULL),(130,3,'2025-04-27 08:46:11.117000','0.00','Transaction.Begin','Raw','Current.Import',NULL,'A',NULL),(130,3,'2025-04-27 08:46:11.117000','0.92','Transaction.Begin','Raw','Power.Factor',NULL,'Percent',NULL),(130,3,'2025-04-27 08:47:41.294000','0.00','Sample.Periodic','Raw','Energy.Active.Import.Register',NULL,'Wh',NULL),(130,3,'2025-04-27 08:47:41.294000','99.00','Sample.Periodic','Raw','SoC',NULL,'Percent',NULL),(130,3,'2025-04-27 08:47:41.294000','162.80','Sample.Periodic','Raw','Voltage',NULL,'V',NULL),(130,3,'2025-04-27 08:47:41.294000','16.28','Sample.Periodic','Raw','Power.Active.Import',NULL,'W',NULL),(130,3,'2025-04-27 08:47:41.294000','0.10','Sample.Periodic','Raw','Current.Import',NULL,'A',NULL),(130,3,'2025-04-27 08:47:41.294000','0.92','Sample.Periodic','Raw','Power.Factor',NULL,'Percent',NULL),(130,3,'2025-04-27 08:47:51.347000','0.01','Sample.Periodic','Raw','Energy.Active.Import.Register',NULL,'Wh',NULL),(130,3,'2025-04-27 08:47:51.347000','99.00','Sample.Periodic','Raw','SoC',NULL,'Percent',NULL),(130,3,'2025-04-27 08:47:51.347000','7.20','Sample.Periodic','Raw','Voltage',NULL,'V',NULL),(130,3,'2025-04-27 08:47:51.347000','0.00','Sample.Periodic','Raw','Power.Active.Import',NULL,'W',NULL),(130,3,'2025-04-27 08:47:51.347000','0.00','Sample.Periodic','Raw','Current.Import',NULL,'A',NULL),(130,3,'2025-04-27 08:47:51.347000','0.92','Sample.Periodic','Raw','Power.Factor',NULL,'Percent',NULL),(130,3,'2025-04-27 08:48:01.399000','0.01','Sample.Periodic','Raw','Energy.Active.Import.Register',NULL,'Wh',NULL),(130,3,'2025-04-27 08:48:01.399000','99.00','Sample.Periodic','Raw','SoC',NULL,'Percent',NULL),(130,3,'2025-04-27 08:48:01.399000','6.70','Sample.Periodic','Raw','Voltage',NULL,'V',NULL),(130,3,'2025-04-27 08:48:01.399000','0.67','Sample.Periodic','Raw','Power.Active.Import',NULL,'W',NULL),(130,3,'2025-04-27 08:48:01.399000','0.10','Sample.Periodic','Raw','Current.Import',NULL,'A',NULL),(130,3,'2025-04-27 08:48:01.399000','0.92','Sample.Periodic','Raw','Power.Factor',NULL,'Percent',NULL),(130,3,'2025-04-27 08:48:10.125000','0.01','Transaction.End','Raw','Energy.Active.Import.Register',NULL,'Wh',NULL),(130,3,'2025-04-27 08:48:10.125000','99.00','Transaction.End','Raw','SoC',NULL,'Percent',NULL),(130,3,'2025-04-27 08:48:10.125000','6.40','Transaction.End','Raw','Voltage',NULL,'V',NULL),(130,3,'2025-04-27 08:48:10.125000','0.64','Transaction.End','Raw','Power.Active.Import',NULL,'W',NULL),(130,3,'2025-04-27 08:48:10.125000','0.10','Transaction.End','Raw','Current.Import',NULL,'A',NULL),(130,3,'2025-04-27 08:48:10.125000','0.92','Transaction.End','Raw','Power.Factor',NULL,'Percent',NULL);
/*!40000 ALTER TABLE `connector_meter_value` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `connector_status`
--

DROP TABLE IF EXISTS `connector_status`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `connector_status` (
  `connector_pk` int unsigned NOT NULL,
  `status_timestamp` timestamp(6) NULL DEFAULT NULL,
  `status` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `error_code` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `error_info` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `vendor_id` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `vendor_error_code` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  KEY `FK_cs_pk_idx` (`connector_pk`),
  KEY `connector_status_cpk_st_idx` (`connector_pk`,`status_timestamp`),
  CONSTRAINT `FK_cs_pk` FOREIGN KEY (`connector_pk`) REFERENCES `connector` (`connector_pk`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `connector_status`
--

LOCK TABLES `connector_status` WRITE;
/*!40000 ALTER TABLE `connector_status` DISABLE KEYS */;
INSERT INTO `connector_status` VALUES (1,'2025-04-16 07:10:38.183000','Available',NULL,NULL,NULL,NULL),(2,'2025-04-16 07:10:38.209000','Available',NULL,NULL,NULL,NULL),(3,'2025-04-16 07:10:38.228000','Available',NULL,NULL,NULL,NULL),(1,'2025-04-18 02:24:38.266000','Available',NULL,NULL,NULL,NULL),(2,'2025-04-18 02:24:38.311000','Available',NULL,NULL,NULL,NULL),(3,'2025-04-18 02:24:38.329000','Available',NULL,NULL,NULL,NULL),(7,'2025-04-23 08:29:17.183000','Available','NoError','NoError','SETEC-POWER','0'),(8,'2025-04-23 08:29:17.216000','Available','NoError','NoError','SETEC-POWER','0'),(9,'2025-04-23 08:29:17.239000','Available','NoError','NoError','SETEC-POWER','0'),(9,'2025-04-23 08:30:44.656000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(7,'2025-04-23 08:30:44.685000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(8,'2025-04-23 08:30:44.707000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(9,'2025-04-23 08:30:44.730000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(7,'2025-04-23 08:31:01.024000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(8,'2025-04-23 08:31:01.042000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(9,'2025-04-23 08:31:01.058000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(9,'2025-04-23 08:32:03.831000','Available','NoError','NoError','SETEC-POWER','0'),(7,'2025-04-23 08:32:03.865000','Available','NoError','NoError','SETEC-POWER','0'),(8,'2025-04-23 08:32:03.909000','Available','NoError','NoError','SETEC-POWER','0'),(7,'2025-04-23 08:33:42.721000','Available','NoError','NoError','SETEC-POWER','0'),(8,'2025-04-23 08:33:42.742000','Available','NoError','NoError','SETEC-POWER','0'),(9,'2025-04-23 08:33:42.775000','Available','NoError','NoError','SETEC-POWER','0'),(7,'2025-04-23 08:35:27.766000','Available','NoError','NoError','SETEC-POWER','0'),(8,'2025-04-23 08:35:27.888000','Available','NoError','NoError','SETEC-POWER','0'),(9,'2025-04-23 08:35:27.981000','Available','NoError','NoError','SETEC-POWER','0'),(7,'2025-04-23 08:39:44.964000','Available','NoError','NoError','SETEC-POWER','0'),(8,'2025-04-23 08:39:45.032000','Available','NoError','NoError','SETEC-POWER','0'),(9,'2025-04-23 08:39:45.137000','Available','NoError','NoError','SETEC-POWER','0'),(7,'2025-04-24 02:03:04.167000','Available','NoError','NoError','SETEC-POWER','0'),(8,'2025-04-24 02:03:04.200000','Available','NoError','NoError','SETEC-POWER','0'),(9,'2025-04-24 02:03:04.227000','Available','NoError','NoError','SETEC-POWER','0'),(9,'2025-04-24 02:03:10.523000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(7,'2025-04-24 02:03:10.542000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(8,'2025-04-24 02:03:10.565000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(9,'2025-04-24 02:03:10.590000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(9,'2025-04-24 02:32:02.236000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(8,'2025-04-24 02:32:02.309000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(9,'2025-04-24 02:32:02.376000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(9,'2025-04-24 02:36:39.364000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(7,'2025-04-24 02:36:39.392000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(7,'2025-04-24 02:36:39.431000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(8,'2025-04-24 02:36:39.468000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(8,'2025-04-24 02:36:39.493000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(9,'2025-04-24 02:36:39.514000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(9,'2025-04-24 02:36:39.538000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(9,'2025-04-24 02:37:08.096000','Available','NoError','NoError','SETEC-POWER','0'),(7,'2025-04-24 02:37:08.137000','Available','NoError','NoError','SETEC-POWER','0'),(8,'2025-04-24 02:37:08.171000','Available','NoError','NoError','SETEC-POWER','0'),(9,'2025-04-24 02:37:17.070000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(7,'2025-04-24 02:37:17.106000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(8,'2025-04-24 02:37:17.125000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(9,'2025-04-24 02:37:17.143000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(7,'2025-04-24 02:59:32.322000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(8,'2025-04-24 02:59:32.378000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(9,'2025-04-24 02:59:32.410000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(7,'2025-04-24 02:59:32.545000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(8,'2025-04-24 02:59:32.649000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(9,'2025-04-24 02:59:32.673000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(9,'2025-04-24 03:01:59.671000','Available','NoError','NoError','SETEC-POWER','0'),(7,'2025-04-24 03:01:59.698000','Available','NoError','NoError','SETEC-POWER','0'),(8,'2025-04-24 03:01:59.718000','Available','NoError','NoError','SETEC-POWER','0'),(7,'2025-04-24 03:02:00.726000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(8,'2025-04-24 03:02:00.763000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(9,'2025-04-24 03:02:00.793000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(7,'2025-04-24 03:02:34.205000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(8,'2025-04-24 03:02:34.225000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(9,'2025-04-24 03:02:34.243000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(7,'2025-04-24 03:03:54.231000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(8,'2025-04-24 03:03:54.274000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(9,'2025-04-24 03:03:54.341000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(7,'2025-04-24 03:04:51.666000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(8,'2025-04-24 03:04:51.688000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(9,'2025-04-24 03:04:51.706000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(7,'2025-04-24 03:12:01.138000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(8,'2025-04-24 03:12:01.258000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(9,'2025-04-24 03:12:01.285000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(7,'2025-04-24 03:16:40.294000','Available','NoError','NoError','SETEC-POWER','0'),(8,'2025-04-24 03:16:40.322000','Available','NoError','NoError','SETEC-POWER','0'),(9,'2025-04-24 03:16:40.339000','Available','NoError','NoError','SETEC-POWER','0'),(7,'2025-04-24 03:17:31.665000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(8,'2025-04-24 03:17:31.705000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(9,'2025-04-24 03:17:31.735000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(9,'2025-04-24 03:17:37.521000','Available','NoError','NoError','SETEC-POWER','0'),(7,'2025-04-24 03:17:37.544000','Available','NoError','NoError','SETEC-POWER','0'),(8,'2025-04-24 03:17:37.579000','Available','NoError','NoError','SETEC-POWER','0'),(7,'2025-04-24 03:24:26.811000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(8,'2025-04-24 03:24:26.850000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(9,'2025-04-24 03:24:26.867000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(9,'2025-04-24 03:24:28.322000','Available','NoError','NoError','SETEC-POWER','0'),(7,'2025-04-24 03:24:28.356000','Available','NoError','NoError','SETEC-POWER','0'),(8,'2025-04-24 03:24:28.375000','Available','NoError','NoError','SETEC-POWER','0'),(7,'2025-04-24 03:32:19.831000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(8,'2025-04-24 03:32:19.879000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(9,'2025-04-24 03:32:19.899000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(9,'2025-04-24 03:38:37.522000','Available','NoError','NoError','SETEC-POWER','0'),(7,'2025-04-24 03:38:37.559000','Available','NoError','NoError','SETEC-POWER','0'),(8,'2025-04-24 03:38:37.577000','Available','NoError','NoError','SETEC-POWER','0'),(7,'2025-04-24 03:38:38.607000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(8,'2025-04-24 03:38:38.667000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(9,'2025-04-24 03:38:38.688000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(9,'2025-04-24 03:38:45.799000','Available','NoError','NoError','SETEC-POWER','0'),(7,'2025-04-24 03:38:45.843000','Available','NoError','NoError','SETEC-POWER','0'),(8,'2025-04-24 03:38:45.871000','Available','NoError','NoError','SETEC-POWER','0'),(7,'2025-04-24 03:42:19.857000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(8,'2025-04-24 03:42:19.903000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(9,'2025-04-24 03:42:19.924000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(9,'2025-04-24 03:42:21.276000','Available','NoError','NoError','SETEC-POWER','0'),(7,'2025-04-24 03:42:21.316000','Available','NoError','NoError','SETEC-POWER','0'),(8,'2025-04-24 03:42:21.335000','Available','NoError','NoError','SETEC-POWER','0'),(7,'2025-04-24 05:56:18.297000','Available','NoError','NoError','SETEC-POWER','0'),(8,'2025-04-24 05:56:18.329000','Available','NoError','NoError','SETEC-POWER','0'),(9,'2025-04-24 05:56:18.362000','Available','NoError','NoError','SETEC-POWER','0'),(7,'2025-04-24 06:09:25.153000','Available','NoError','NoError','SETEC-POWER','0'),(8,'2025-04-24 06:09:25.193000','Available','NoError','NoError','SETEC-POWER','0'),(9,'2025-04-24 06:09:25.209000','Available','NoError','NoError','SETEC-POWER','0'),(9,'2025-04-24 06:50:29.320000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(7,'2025-04-24 06:50:29.347000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(8,'2025-04-24 06:50:29.372000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(9,'2025-04-24 06:50:29.389000','Faulted','OtherError','Emergency','SETEC-POWER','0'),(7,'2025-04-24 06:51:01.510000','Available','NoError','NoError','SETEC-POWER','0'),(8,'2025-04-24 06:51:01.537000','Available','NoError','NoError','SETEC-POWER','0'),(9,'2025-04-24 06:51:01.563000','Available','NoError','NoError','SETEC-POWER','0'),(7,'2025-04-24 06:51:20.839000','Available','NoError','NoError','SETEC-POWER','0'),(8,'2025-04-24 06:51:20.942000','Available','NoError','NoError','SETEC-POWER','0'),(9,'2025-04-24 06:51:21.014000','Available','NoError','NoError','SETEC-POWER','0'),(7,'2025-04-27 08:09:17.431000','Available','NoError','NoError','SETEC-POWER','0'),(8,'2025-04-27 08:09:17.683000','Available','NoError','NoError','SETEC-POWER','0'),(9,'2025-04-27 08:09:17.721000','Available','NoError','NoError','SETEC-POWER','0'),(8,'2025-04-27 08:12:16.201000','Preparing','NoError',NULL,NULL,NULL),(130,'2025-04-27 08:20:01.053000','Available','NoError','NoError','SETEC-POWER','0'),(131,'2025-04-27 08:20:01.111000','Available','NoError','NoError','SETEC-POWER','0'),(132,'2025-04-27 08:20:01.170000','Available','NoError','NoError','SETEC-POWER','0'),(133,'2025-04-27 08:20:01.225000','Available','NoError','NoError','SETEC-POWER','0'),(134,'2025-04-27 08:20:01.297000','Available','NoError','NoError','SETEC-POWER','0'),(130,'2025-04-27 08:20:29.190000','Preparing','NoError',NULL,NULL,NULL),(130,'2025-04-27 08:21:37.261000','Charging','NoError',NULL,NULL,NULL),(130,'2025-04-27 08:22:36.471000','Finishing','NoError',NULL,NULL,NULL),(130,'2025-04-27 08:22:53.289000','Preparing','NoError',NULL,NULL,NULL),(130,'2025-04-27 08:22:57.509000','Charging','NoError',NULL,NULL,NULL),(130,'2025-04-27 08:23:42.068000','Finishing','NoError',NULL,NULL,NULL),(130,'2025-04-27 08:26:09.606000','Preparing','NoError','NoError','SETEC-POWER','0'),(131,'2025-04-27 08:26:09.635000','Available','NoError','NoError','SETEC-POWER','0'),(132,'2025-04-27 08:26:09.663000','Available','NoError','NoError','SETEC-POWER','0'),(133,'2025-04-27 08:26:09.689000','Available','NoError','NoError','SETEC-POWER','0'),(134,'2025-04-27 08:26:09.726000','Available','NoError','NoError','SETEC-POWER','0'),(130,'2025-04-27 08:26:35.534000','Preparing','NoError','NoError','SETEC-POWER','0'),(131,'2025-04-27 08:26:35.560000','Available','NoError','NoError','SETEC-POWER','0'),(132,'2025-04-27 08:26:35.586000','Available','NoError','NoError','SETEC-POWER','0'),(133,'2025-04-27 08:26:35.609000','Available','NoError','NoError','SETEC-POWER','0'),(134,'2025-04-27 08:26:35.639000','Available','NoError','NoError','SETEC-POWER','0'),(130,'2025-04-27 08:29:22.587000','Preparing','NoError','NoError','SETEC-POWER','0'),(131,'2025-04-27 08:29:22.632000','Available','NoError','NoError','SETEC-POWER','0'),(132,'2025-04-27 08:29:22.665000','Available','NoError','NoError','SETEC-POWER','0'),(133,'2025-04-27 08:29:22.692000','Available','NoError','NoError','SETEC-POWER','0'),(134,'2025-04-27 08:29:22.715000','Available','NoError','NoError','SETEC-POWER','0'),(130,'2025-04-27 08:29:58.564000','Preparing','NoError','NoError','SETEC-POWER','0'),(131,'2025-04-27 08:29:58.583000','Available','NoError','NoError','SETEC-POWER','0'),(132,'2025-04-27 08:29:58.601000','Available','NoError','NoError','SETEC-POWER','0'),(133,'2025-04-27 08:29:58.620000','Available','NoError','NoError','SETEC-POWER','0'),(134,'2025-04-27 08:29:58.637000','Available','NoError','NoError','SETEC-POWER','0'),(130,'2025-04-27 08:40:45.634000','Available','NoError',NULL,NULL,NULL),(130,'2025-04-27 08:41:20.001000','Reserved','NoError',NULL,NULL,NULL),(130,'2025-04-27 08:43:09.032000','Available','NoError',NULL,NULL,NULL),(130,'2025-04-27 08:45:45.429000','Preparing','NoError',NULL,NULL,NULL),(130,'2025-04-27 08:45:55.089000','Charging','NoError',NULL,NULL,NULL),(130,'2025-04-27 08:48:10.099000','Finishing','NoError',NULL,NULL,NULL),(179,'2025-04-29 03:26:49.839000','Preparing','NoError','NoError','SETEC-POWER','0'),(180,'2025-04-29 03:26:49.872000','Available','NoError','NoError','SETEC-POWER','0'),(181,'2025-04-29 03:26:49.904000','Available','NoError','NoError','SETEC-POWER','0'),(182,'2025-04-29 03:26:49.939000','Available','NoError','NoError','SETEC-POWER','0'),(183,'2025-04-29 03:26:49.994000','Available','NoError','NoError','SETEC-POWER','0'),(179,'2025-04-29 06:03:48.707000','Preparing','NoError','NoError','SETEC-POWER','0'),(180,'2025-04-29 06:03:48.735000','Available','NoError','NoError','SETEC-POWER','0'),(181,'2025-04-29 06:03:48.763000','Available','NoError','NoError','SETEC-POWER','0'),(182,'2025-04-29 06:03:48.802000','Available','NoError','NoError','SETEC-POWER','0'),(183,'2025-04-29 06:03:48.831000','Available','NoError','NoError','SETEC-POWER','0'),(183,'2025-04-29 06:17:36.971000','Unavailable','NoError',NULL,NULL,NULL),(179,'2025-04-29 06:17:36.992000','Unavailable','NoError',NULL,NULL,NULL),(180,'2025-04-29 06:17:37.007000','Unavailable','NoError',NULL,NULL,NULL),(181,'2025-04-29 06:17:37.021000','Unavailable','NoError',NULL,NULL,NULL),(182,'2025-04-29 06:17:37.035000','Unavailable','NoError',NULL,NULL,NULL),(183,'2025-04-29 06:18:40.882000','Available','NoError',NULL,NULL,NULL),(179,'2025-04-29 06:18:40.918000','Available','NoError',NULL,NULL,NULL),(180,'2025-04-29 06:18:40.932000','Available','NoError',NULL,NULL,NULL),(181,'2025-04-29 06:18:40.946000','Available','NoError',NULL,NULL,NULL),(182,'2025-04-29 06:18:40.962000','Available','NoError',NULL,NULL,NULL),(183,'2025-04-29 06:20:41.877000','Unavailable','NoError',NULL,NULL,NULL),(179,'2025-04-29 06:20:41.912000','Unavailable','NoError',NULL,NULL,NULL),(180,'2025-04-29 06:20:41.925000','Unavailable','NoError',NULL,NULL,NULL),(181,'2025-04-29 06:20:41.939000','Unavailable','NoError',NULL,NULL,NULL),(182,'2025-04-29 06:20:41.953000','Unavailable','NoError',NULL,NULL,NULL),(183,'2025-04-29 06:21:45.846000','Available','NoError',NULL,NULL,NULL),(179,'2025-04-29 06:21:45.882000','Available','NoError',NULL,NULL,NULL),(180,'2025-04-29 06:21:45.896000','Available','NoError',NULL,NULL,NULL),(181,'2025-04-29 06:21:45.909000','Available','NoError',NULL,NULL,NULL),(182,'2025-04-29 06:21:45.923000','Available','NoError',NULL,NULL,NULL),(183,'2025-04-29 06:23:18.921000','Unavailable','NoError',NULL,NULL,NULL),(179,'2025-04-29 06:23:18.942000','Unavailable','NoError',NULL,NULL,NULL),(180,'2025-04-29 06:23:18.956000','Unavailable','NoError',NULL,NULL,NULL),(181,'2025-04-29 06:23:18.969000','Unavailable','NoError',NULL,NULL,NULL),(182,'2025-04-29 06:23:18.982000','Unavailable','NoError',NULL,NULL,NULL),(183,'2025-04-29 06:24:22.219000','Available','NoError',NULL,NULL,NULL),(179,'2025-04-29 06:24:22.256000','Available','NoError',NULL,NULL,NULL),(180,'2025-04-29 06:24:22.296000','Available','NoError',NULL,NULL,NULL),(181,'2025-04-29 06:24:22.310000','Available','NoError',NULL,NULL,NULL),(182,'2025-04-29 06:24:22.324000','Available','NoError',NULL,NULL,NULL),(183,'2025-04-29 06:28:13.063000','Unavailable','NoError',NULL,NULL,NULL),(179,'2025-04-29 06:28:13.095000','Unavailable','NoError',NULL,NULL,NULL),(180,'2025-04-29 06:28:13.108000','Unavailable','NoError',NULL,NULL,NULL),(181,'2025-04-29 06:28:13.122000','Unavailable','NoError',NULL,NULL,NULL),(182,'2025-04-29 06:28:13.137000','Unavailable','NoError',NULL,NULL,NULL),(183,'2025-04-29 06:29:29.061000','Available','NoError',NULL,NULL,NULL),(179,'2025-04-29 06:29:29.083000','Available','NoError',NULL,NULL,NULL),(180,'2025-04-29 06:29:29.106000','Available','NoError',NULL,NULL,NULL),(181,'2025-04-29 06:29:29.126000','Available','NoError',NULL,NULL,NULL),(182,'2025-04-29 06:29:29.145000','Available','NoError',NULL,NULL,NULL),(179,'2025-04-29 07:25:36.245000','Preparing','NoError','NoError','SETEC-POWER','0'),(180,'2025-04-29 07:25:36.270000','Available','NoError','NoError','SETEC-POWER','0'),(181,'2025-04-29 07:25:36.293000','Available','NoError','NoError','SETEC-POWER','0'),(182,'2025-04-29 07:25:36.316000','Available','NoError','NoError','SETEC-POWER','0'),(183,'2025-04-29 07:25:36.336000','Available','NoError','NoError','SETEC-POWER','0'),(179,'2025-04-29 07:25:47.007000','Preparing','NoError','NoError','SETEC-POWER','0'),(180,'2025-04-29 07:25:47.055000','Available','NoError','NoError','SETEC-POWER','0'),(181,'2025-04-29 07:25:47.083000','Available','NoError','NoError','SETEC-POWER','0'),(182,'2025-04-29 07:25:47.109000','Available','NoError','NoError','SETEC-POWER','0'),(183,'2025-04-29 07:25:47.129000','Available','NoError','NoError','SETEC-POWER','0'),(179,'2025-04-29 07:25:51.021000','Preparing','NoError','NoError','SETEC-POWER','0'),(180,'2025-04-29 07:25:51.072000','Available','NoError','NoError','SETEC-POWER','0'),(181,'2025-04-29 07:25:51.093000','Available','NoError','NoError','SETEC-POWER','0'),(182,'2025-04-29 07:25:51.121000','Available','NoError','NoError','SETEC-POWER','0'),(183,'2025-04-29 07:25:51.145000','Available','NoError','NoError','SETEC-POWER','0'),(8,'2025-04-29 07:43:55.471000','Preparing','NoError','NoError','SETEC-POWER','0'),(7,'2025-04-29 07:43:55.496000','Available','NoError','NoError','SETEC-POWER','0'),(246,'2025-04-29 07:43:55.521000','Available','NoError','NoError','SETEC-POWER','0'),(247,'2025-04-29 07:43:55.548000','Available','NoError','NoError','SETEC-POWER','0'),(9,'2025-04-29 07:43:55.580000','Available','NoError','NoError','SETEC-POWER','0'),(8,'2025-04-29 07:44:07.920000','Preparing','NoError','NoError','SETEC-POWER','0'),(7,'2025-04-29 07:44:07.970000','Available','NoError','NoError','SETEC-POWER','0'),(246,'2025-04-29 07:44:07.992000','Available','NoError','NoError','SETEC-POWER','0'),(247,'2025-04-29 07:44:08.013000','Available','NoError','NoError','SETEC-POWER','0'),(9,'2025-04-29 07:44:08.035000','Available','NoError','NoError','SETEC-POWER','0'),(9,'2025-04-29 07:44:38.065000','Unavailable','NoError',NULL,NULL,NULL),(8,'2025-04-29 07:44:38.092000','Unavailable','NoError',NULL,NULL,NULL),(7,'2025-04-29 07:44:38.114000','Unavailable','NoError',NULL,NULL,NULL),(246,'2025-04-29 07:44:38.140000','Unavailable','NoError',NULL,NULL,NULL),(247,'2025-04-29 07:44:38.159000','Unavailable','NoError',NULL,NULL,NULL),(9,'2025-04-29 07:45:56.055000','Available','NoError',NULL,NULL,NULL),(8,'2025-04-29 07:45:56.078000','Available','NoError',NULL,NULL,NULL),(7,'2025-04-29 07:45:56.100000','Available','NoError',NULL,NULL,NULL),(246,'2025-04-29 07:45:56.117000','Available','NoError',NULL,NULL,NULL),(247,'2025-04-29 07:45:56.137000','Available','NoError',NULL,NULL,NULL),(8,'2025-04-29 07:48:33.352000','Preparing','NoError','NoError','SETEC-POWER','0'),(7,'2025-04-29 07:48:33.375000','Available','NoError','NoError','SETEC-POWER','0'),(246,'2025-04-29 07:48:33.395000','Available','NoError','NoError','SETEC-POWER','0'),(247,'2025-04-29 07:48:33.413000','Available','NoError','NoError','SETEC-POWER','0'),(9,'2025-04-29 07:48:33.432000','Available','NoError','NoError','SETEC-POWER','0'),(8,'2025-04-29 07:48:40.337000','Preparing','NoError','NoError','SETEC-POWER','0'),(7,'2025-04-29 07:48:40.358000','Available','NoError','NoError','SETEC-POWER','0'),(246,'2025-04-29 07:48:40.378000','Available','NoError','NoError','SETEC-POWER','0'),(247,'2025-04-29 07:48:40.400000','Available','NoError','NoError','SETEC-POWER','0'),(9,'2025-04-29 07:48:40.421000','Available','NoError','NoError','SETEC-POWER','0'),(9,'2025-04-29 07:49:06.126000','Unavailable','NoError',NULL,NULL,NULL),(8,'2025-04-29 07:49:06.145000','Unavailable','NoError',NULL,NULL,NULL),(7,'2025-04-29 07:49:06.165000','Unavailable','NoError',NULL,NULL,NULL),(246,'2025-04-29 07:49:06.184000','Unavailable','NoError',NULL,NULL,NULL),(247,'2025-04-29 07:49:06.202000','Unavailable','NoError',NULL,NULL,NULL),(9,'2025-04-29 07:50:25.055000','Available','NoError',NULL,NULL,NULL),(8,'2025-04-29 07:50:25.073000','Available','NoError',NULL,NULL,NULL),(7,'2025-04-29 07:50:25.096000','Available','NoError',NULL,NULL,NULL),(246,'2025-04-29 07:50:25.116000','Available','NoError',NULL,NULL,NULL),(247,'2025-04-29 07:50:25.143000','Available','NoError',NULL,NULL,NULL),(284,'2025-04-29 08:13:04.297000','Preparing','NoError','NoError','SETEC-POWER','0'),(285,'2025-04-29 08:13:04.322000','Available','NoError','NoError','SETEC-POWER','0'),(286,'2025-04-29 08:13:04.346000','Available','NoError','NoError','SETEC-POWER','0'),(287,'2025-04-29 08:13:04.367000','Available','NoError','NoError','SETEC-POWER','0'),(288,'2025-04-29 08:13:04.390000','Available','NoError','NoError','SETEC-POWER','0'),(288,'2025-04-29 08:13:34.344000','Unavailable','NoError',NULL,NULL,NULL),(284,'2025-04-29 08:13:34.362000','Unavailable','NoError',NULL,NULL,NULL),(285,'2025-04-29 08:13:34.379000','Unavailable','NoError',NULL,NULL,NULL),(286,'2025-04-29 08:13:34.397000','Unavailable','NoError',NULL,NULL,NULL),(287,'2025-04-29 08:13:34.419000','Unavailable','NoError',NULL,NULL,NULL),(284,'2025-04-29 08:17:57.320000','Preparing','NoError','NoError','SETEC-POWER','0'),(285,'2025-04-29 08:17:57.345000','Available','NoError','NoError','SETEC-POWER','0'),(286,'2025-04-29 08:17:57.370000','Available','NoError','NoError','SETEC-POWER','0'),(287,'2025-04-29 08:17:57.393000','Available','NoError','NoError','SETEC-POWER','0'),(288,'2025-04-29 08:17:57.413000','Available','NoError','NoError','SETEC-POWER','0'),(284,'2025-04-29 08:19:52.139000','Preparing','NoError','NoError','SETEC-POWER','0'),(285,'2025-04-29 08:19:52.184000','Available','NoError','NoError','SETEC-POWER','0'),(286,'2025-04-29 08:19:52.200000','Available','NoError','NoError','SETEC-POWER','0'),(287,'2025-04-29 08:19:52.218000','Available','NoError','NoError','SETEC-POWER','0'),(288,'2025-04-29 08:19:52.237000','Available','NoError','NoError','SETEC-POWER','0'),(304,'2025-04-30 00:00:30.000000','Preparing','NoError','NoError','SETEC-POWER','0'),(305,'2025-04-30 00:00:31.000000','Available','NoError','NoError','SETEC-POWER','0'),(306,'2025-04-30 00:00:31.000000','Available','NoError','NoError','SETEC-POWER','0'),(307,'2025-04-30 00:00:31.000000','Available','NoError','NoError','SETEC-POWER','0'),(308,'2025-04-30 00:00:31.000000','Available','NoError','NoError','SETEC-POWER','0'),(309,'2025-04-30 00:00:11.000000','Available','NoError','NoError','SETEC-POWER','0'),(310,'2025-04-30 00:00:11.000000','Available','NoError','NoError','SETEC-POWER','0'),(311,'2025-04-30 00:00:11.000000','Available','NoError','NoError','SETEC-POWER','0'),(312,'2025-05-06 00:00:01.000000','Available','NoError','NoError','SETEC-POWER','0'),(313,'2025-05-06 00:00:01.000000','Available','NoError','NoError','SETEC-POWER','0'),(314,'2025-05-06 00:00:01.000000','Available','NoError','NoError','SETEC-POWER','0'),(312,'2025-05-06 00:00:13.000000','Available','NoError','NoError','SETEC-POWER','0'),(313,'2025-05-06 00:00:13.000000','Available','NoError','NoError','SETEC-POWER','0'),(314,'2025-05-06 00:00:13.000000','Available','NoError','NoError','SETEC-POWER','0'),(312,'2025-05-06 00:00:39.000000','Available','NoError','NoError','SETEC-POWER','0'),(313,'2025-05-06 00:00:39.000000','Available','NoError','NoError','SETEC-POWER','0'),(314,'2025-05-06 00:00:39.000000','Available','NoError','NoError','SETEC-POWER','0'),(312,'2025-05-06 00:00:37.000000','Available','NoError','NoError','SETEC-POWER','0'),(313,'2025-05-06 00:00:37.000000','Available','NoError','NoError','SETEC-POWER','0'),(314,'2025-05-06 00:00:37.000000','Available','NoError','NoError','SETEC-POWER','0'),(312,'2025-05-06 00:00:31.000000','Available','NoError','NoError','SETEC-POWER','0'),(313,'2025-05-06 00:00:31.000000','Available','NoError','NoError','SETEC-POWER','0'),(314,'2025-05-06 00:00:31.000000','Available','NoError','NoError','SETEC-POWER','0'),(312,'2025-05-06 00:00:04.000000','Available','NoError','NoError','SETEC-POWER','0'),(313,'2025-05-06 00:00:04.000000','Available','NoError','NoError','SETEC-POWER','0'),(314,'2025-05-06 00:00:04.000000','Available','NoError','NoError','SETEC-POWER','0'),(7,'2025-05-08 00:00:23.000000','Available','NoError','NoError','SETEC-POWER','0'),(8,'2025-05-08 00:00:23.000000','Available','NoError','NoError','SETEC-POWER','0'),(179,'2025-05-08 00:00:23.000000','Available','NoError','NoError','SETEC-POWER','0'),(9,'2025-05-08 00:00:23.000000','Available','NoError','NoError','SETEC-POWER','0'),(180,'2025-05-08 00:00:23.000000','Available','NoError','NoError','SETEC-POWER','0'),(183,'2025-05-08 00:00:23.000000','Available','NoError','NoError','SETEC-POWER','0'),(179,'2025-05-08 00:00:46.000000','Available','NoError','NoError','SETEC-POWER','0'),(7,'2025-05-08 00:00:46.000000','Available','NoError','NoError','SETEC-POWER','0'),(180,'2025-05-08 00:00:46.000000','Available','NoError','NoError','SETEC-POWER','0'),(8,'2025-05-08 00:00:46.000000','Available','NoError','NoError','SETEC-POWER','0'),(183,'2025-05-08 00:00:46.000000','Available','NoError','NoError','SETEC-POWER','0'),(9,'2025-05-08 00:00:46.000000','Available','NoError','NoError','SETEC-POWER','0'),(179,'2025-05-08 00:00:01.000000','Unavailable','NoError','NoError','SETEC-POWER','0'),(180,'2025-05-08 00:00:01.000000','Unavailable','NoError','NoError','SETEC-POWER','0'),(183,'2025-05-08 00:00:01.000000','Unavailable','NoError','NoError','SETEC-POWER','0'),(179,'2025-05-08 00:00:15.000000','Available','NoError','NoError','SETEC-POWER','0'),(180,'2025-05-08 00:00:15.000000','Available','NoError','NoError','SETEC-POWER','0'),(183,'2025-05-08 00:00:15.000000','Available','NoError','NoError','SETEC-POWER','0'),(179,'2025-05-08 00:00:16.000000','Available','NoError','NoError','SETEC-POWER','0'),(180,'2025-05-08 00:00:16.000000','Available','NoError','NoError','SETEC-POWER','0'),(183,'2025-05-08 00:00:17.000000','Available','NoError','NoError','SETEC-POWER','0');
/*!40000 ALTER TABLE `connector_status` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fault_notification_email`
--

DROP TABLE IF EXISTS `fault_notification_email`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fault_notification_email` (
  `id` int NOT NULL AUTO_INCREMENT,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `enabled` tinyint(1) NOT NULL DEFAULT '1',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_notified_time` timestamp NULL DEFAULT NULL,
  `description` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`),
  KEY `idx_enabled` (`enabled`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fault_notification_email`
--

LOCK TABLES `fault_notification_email` WRITE;
/*!40000 ALTER TABLE `fault_notification_email` DISABLE KEYS */;
INSERT INTO `fault_notification_email` VALUES (1,'<EMAIL>',0,'2025-04-23 00:51:24','2025-04-24 06:50:30','test');
/*!40000 ALTER TABLE `fault_notification_email` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `issue_image`
--

DROP TABLE IF EXISTS `issue_image`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `issue_image` (
  `image_id` int NOT NULL AUTO_INCREMENT,
  `issue_id` int NOT NULL,
  `image_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `upload_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`image_id`),
  KEY `idx_issue_id` (`issue_id`),
  CONSTRAINT `issue_image_ibfk_1` FOREIGN KEY (`issue_id`) REFERENCES `charger_issue` (`issue_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `issue_image`
--

LOCK TABLES `issue_image` WRITE;
/*!40000 ALTER TABLE `issue_image` DISABLE KEYS */;
INSERT INTO `issue_image` VALUES (1,1,'/uploads/issues/1/display_error.jpg','2025-04-21 07:13:51'),(2,3,'uploads/issue-images\\issue_3_20250421_093650_af620144.png','2025-04-21 09:36:50'),(3,5,'uploads/issue-images\\issue_5_20250422_032739_1ba0e12c.png','2025-04-22 03:27:39'),(4,14,'uploads/issue-images/issue_14_20250423_012629_aaebb2c8.png','2025-04-23 01:26:29'),(5,15,'uploads/issue-images/issue_15_20250423_013457_294bada2.png','2025-04-23 01:34:57');
/*!40000 ALTER TABLE `issue_image` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `issue_maintenance_record`
--

DROP TABLE IF EXISTS `issue_maintenance_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `issue_maintenance_record` (
  `record_id` int NOT NULL AUTO_INCREMENT,
  `issue_id` int NOT NULL,
  `maintenance_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `maintainer_user_pk` int DEFAULT NULL,
  `maintenance_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`record_id`),
  KEY `maintainer_user_pk` (`maintainer_user_pk`),
  KEY `idx_issue_id` (`issue_id`),
  KEY `idx_maintenance_time` (`maintenance_time`),
  CONSTRAINT `issue_maintenance_record_ibfk_1` FOREIGN KEY (`issue_id`) REFERENCES `charger_issue` (`issue_id`) ON DELETE CASCADE,
  CONSTRAINT `issue_maintenance_record_ibfk_2` FOREIGN KEY (`maintainer_user_pk`) REFERENCES `web_user` (`web_user_pk`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=37 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `issue_maintenance_record`
--

LOCK TABLES `issue_maintenance_record` WRITE;
/*!40000 ALTER TABLE `issue_maintenance_record` DISABLE KEYS */;
INSERT INTO `issue_maintenance_record` VALUES (13,7,'2025-04-22 05:35:48',1,'test'),(15,7,'2025-04-22 05:53:20',1,'test'),(17,9,'2025-04-22 06:05:51',1,'test1111'),(19,8,'2025-04-22 06:10:21',1,'test1'),(21,8,'2025-04-22 06:10:36',1,'test2'),(22,9,'2025-04-22 06:31:06',1,'test222'),(23,9,'2025-04-22 06:34:21',1,'test3333'),(24,10,'2025-04-22 06:50:04',1,'test1'),(25,10,'2025-04-22 06:50:09',1,'test2'),(26,10,'2025-04-22 07:17:09',1,'test3'),(27,10,'2025-04-22 07:17:15',1,'test4'),(28,10,'2025-04-22 07:17:25',1,'test5'),(29,10,'2025-04-22 07:17:41',1,'test6'),(30,15,'2025-04-23 01:35:39',1,'test1'),(31,15,'2025-04-23 01:35:54',1,'test2'),(32,15,'2025-04-23 01:36:13',1,'test3'),(33,15,'2025-04-23 01:36:21',1,'test4'),(34,15,'2025-04-23 01:36:26',1,'test5'),(35,15,'2025-04-23 01:36:30',1,'test6'),(36,15,'2025-04-23 01:36:51',1,'test7');
/*!40000 ALTER TABLE `issue_maintenance_record` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ocpp_tag`
--

DROP TABLE IF EXISTS `ocpp_tag`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ocpp_tag` (
  `ocpp_tag_pk` int NOT NULL AUTO_INCREMENT,
  `id_tag` varchar(255) COLLATE utf8mb3_unicode_ci NOT NULL,
  `parent_id_tag` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `expiry_date` timestamp(6) NULL DEFAULT NULL,
  `max_active_transaction_count` int NOT NULL DEFAULT '1',
  `note` mediumtext COLLATE utf8mb3_unicode_ci,
  PRIMARY KEY (`ocpp_tag_pk`),
  UNIQUE KEY `idTag_UNIQUE` (`id_tag`),
  KEY `user_expiryDate_idx` (`expiry_date`),
  KEY `FK_ocpp_tag_parent_id_tag` (`parent_id_tag`),
  CONSTRAINT `FK_ocpp_tag_parent_id_tag` FOREIGN KEY (`parent_id_tag`) REFERENCES `ocpp_tag` (`id_tag`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ocpp_tag`
--

LOCK TABLES `ocpp_tag` WRITE;
/*!40000 ALTER TABLE `ocpp_tag` DISABLE KEYS */;
INSERT INTO `ocpp_tag` VALUES (1,'439c8105',NULL,NULL,1,NULL);
/*!40000 ALTER TABLE `ocpp_tag` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Temporary view structure for view `ocpp_tag_activity`
--

DROP TABLE IF EXISTS `ocpp_tag_activity`;
/*!50001 DROP VIEW IF EXISTS `ocpp_tag_activity`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `ocpp_tag_activity` AS SELECT 
 1 AS `ocpp_tag_pk`,
 1 AS `id_tag`,
 1 AS `parent_id_tag`,
 1 AS `expiry_date`,
 1 AS `max_active_transaction_count`,
 1 AS `note`,
 1 AS `active_transaction_count`,
 1 AS `in_transaction`,
 1 AS `blocked`*/;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `reservation`
--

DROP TABLE IF EXISTS `reservation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `reservation` (
  `reservation_pk` int unsigned NOT NULL AUTO_INCREMENT,
  `connector_pk` int unsigned NOT NULL,
  `transaction_pk` int unsigned DEFAULT NULL,
  `id_tag` varchar(255) COLLATE utf8mb3_unicode_ci NOT NULL,
  `start_datetime` datetime DEFAULT NULL,
  `expiry_datetime` datetime DEFAULT NULL,
  `status` varchar(255) COLLATE utf8mb3_unicode_ci NOT NULL,
  PRIMARY KEY (`reservation_pk`),
  UNIQUE KEY `reservation_pk_UNIQUE` (`reservation_pk`),
  UNIQUE KEY `transaction_pk_UNIQUE` (`transaction_pk`),
  KEY `FK_idTag_r_idx` (`id_tag`),
  KEY `reservation_start_idx` (`start_datetime`),
  KEY `reservation_expiry_idx` (`expiry_datetime`),
  KEY `reservation_status_idx` (`status`),
  KEY `FK_connector_pk_reserv_idx` (`connector_pk`),
  CONSTRAINT `FK_connector_pk_reserv` FOREIGN KEY (`connector_pk`) REFERENCES `connector` (`connector_pk`) ON DELETE CASCADE,
  CONSTRAINT `FK_reservation_ocpp_tag_id_tag` FOREIGN KEY (`id_tag`) REFERENCES `ocpp_tag` (`id_tag`) ON DELETE CASCADE,
  CONSTRAINT `FK_transaction_pk_r` FOREIGN KEY (`transaction_pk`) REFERENCES `transaction_start` (`transaction_pk`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `reservation`
--

LOCK TABLES `reservation` WRITE;
/*!40000 ALTER TABLE `reservation` DISABLE KEYS */;
INSERT INTO `reservation` VALUES (2,130,NULL,'439c8105','2025-04-27 08:41:20','2025-04-27 16:43:00','ACCEPTED');
/*!40000 ALTER TABLE `reservation` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `schema_version`
--

DROP TABLE IF EXISTS `schema_version`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `schema_version` (
  `installed_rank` int NOT NULL,
  `version` varchar(50) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `description` varchar(200) COLLATE utf8mb3_unicode_ci NOT NULL,
  `type` varchar(20) COLLATE utf8mb3_unicode_ci NOT NULL,
  `script` varchar(1000) COLLATE utf8mb3_unicode_ci NOT NULL,
  `checksum` int DEFAULT NULL,
  `installed_by` varchar(100) COLLATE utf8mb3_unicode_ci NOT NULL,
  `installed_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `execution_time` int NOT NULL,
  `success` tinyint(1) NOT NULL,
  PRIMARY KEY (`installed_rank`),
  KEY `schema_version_s_idx` (`success`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `schema_version`
--

LOCK TABLES `schema_version` WRITE;
/*!40000 ALTER TABLE `schema_version` DISABLE KEYS */;
INSERT INTO `schema_version` VALUES (1,'1.0.5','stevedb','SQL_BASELINE','B1_0_5__stevedb.sql',1755536181,'root','2025-04-15 08:13:52',712,1),(2,'1.0.6','update','SQL','V1_0_6__update.sql',-593788063,'root','2025-04-15 08:13:52',31,1),(3,'1.0.7','update','SQL','V1_0_7__update.sql',-1929107779,'root','2025-04-15 08:13:52',20,1),(4,'1.0.8','web user and user charge box','SQL','V1_0_8__web_user_and_user_charge_box.sql',238883439,'root','2025-04-15 09:18:19',213,1),(5,'1.0.9','remove authorities column','SQL','V1_0_10__remove_authorities_column.sql',2035225626,'root','2025-04-17 09:30:13',39,1),(6,'1.1.0','charger issue system','SQL','V1_1_0__charger_issue_system.sql',-609480177,'root','2025-04-21 01:56:54',40,1),(7,'1.1.1','fix charger issue system','SQL','V1_1_1__fix_charger_issue_system.sql',-18687803,'root','2025-04-21 01:56:54',297,1),(8,'1.1.2','notification email system','SQL','V1_1_2__notification_email_system.sql',-1064899446,'root','2025-04-22 09:45:10',50,1),(9,'1.1.3','add charging station fields','SQL','V1_1_3__add_charging_station_fields.sql',-149175378,'root','2025-04-27 06:05:13',33,1),(10,'1.1.4','update charging station','SQL','V1_1_4__update_charging_station.sql',694119868,'root','2025-04-27 07:29:28',55,1),(11,'1.1.5','set timezone to china','SQL','V1_1_5__set_timezone_to_china.sql',-1923010654,'steve','2025-04-28 02:28:51',15,1),(12,'1.1.6','set timezone to utc','SQL','V1_1_6__set_timezone_to_utc.sql',1421605423,'steve','2025-04-28 02:28:51',2,1),(13,'1.1.7','add update firmware log','SQL','V1_1_7__add_update_firmware_log.sql',-1112657638,'steve','2025-04-29 09:01:31',157,1),(14,'1.1.8','add session management','SQL','V1_1_8__add_session_management.sql',-782098961,'steve','2025-05-06 01:08:59',77,1),(15,'1.1.9','enhance update firmware log','SQL','V1_1_9__enhance_update_firmware_log.sql',-367557041,'steve','2025-05-08 08:28:36',154,1),(16,'1.1.10','fix update firmware batch','SQL','V1_1_10__fix_update_firmware_batch.sql',-463134857,'steve','2025-05-09 01:04:05',107,1),(17,'1.1.12','fix firmware log url length','SQL','V1_1_12__fix_firmware_log_url_length.sql',-378745019,'steve','2025-05-09 01:46:26',2,1),(18,'1.1.13','remove firmware status column','SQL','V1_1_13__remove_firmware_status_column.sql',-9769453,'steve','2025-05-09 02:36:22',22,1);
/*!40000 ALTER TABLE `schema_version` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `settings`
--

DROP TABLE IF EXISTS `settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `settings` (
  `app_id` varchar(40) COLLATE utf8mb3_unicode_ci NOT NULL,
  `heartbeat_interval_in_seconds` int DEFAULT NULL,
  `hours_to_expire` int DEFAULT NULL,
  `mail_enabled` tinyint(1) DEFAULT '0',
  `mail_host` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `mail_username` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `mail_password` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `mail_from` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `mail_protocol` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT 'smtp',
  `mail_port` int DEFAULT '25',
  `mail_recipients` text COLLATE utf8mb3_unicode_ci COMMENT 'comma separated list of email addresses',
  `notification_features` text COLLATE utf8mb3_unicode_ci COMMENT 'comma separated list',
  PRIMARY KEY (`app_id`),
  UNIQUE KEY `settings_id_UNIQUE` (`app_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `settings`
--

LOCK TABLES `settings` WRITE;
/*!40000 ALTER TABLE `settings` DISABLE KEYS */;
INSERT INTO `settings` VALUES ('U3RlY2tkb3NlblZlcndhbHR1bmc=',14400,1,1,'smtp.qq.com','<EMAIL>','xaguemolwcgzhecd','<EMAIL>','smtp',465,'<EMAIL>','OcppStationStatusFailure');
/*!40000 ALTER TABLE `settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Temporary view structure for view `transaction`
--

DROP TABLE IF EXISTS `transaction`;
/*!50001 DROP VIEW IF EXISTS `transaction`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `transaction` AS SELECT 
 1 AS `transaction_pk`,
 1 AS `connector_pk`,
 1 AS `id_tag`,
 1 AS `start_event_timestamp`,
 1 AS `start_timestamp`,
 1 AS `start_value`,
 1 AS `stop_event_actor`,
 1 AS `stop_event_timestamp`,
 1 AS `stop_timestamp`,
 1 AS `stop_value`,
 1 AS `stop_reason`*/;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `transaction_start`
--

DROP TABLE IF EXISTS `transaction_start`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `transaction_start` (
  `transaction_pk` int unsigned NOT NULL AUTO_INCREMENT,
  `event_timestamp` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `connector_pk` int unsigned NOT NULL,
  `id_tag` varchar(255) COLLATE utf8mb3_unicode_ci NOT NULL,
  `start_timestamp` timestamp(6) NULL DEFAULT NULL,
  `start_value` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`transaction_pk`),
  UNIQUE KEY `transaction_pk_UNIQUE` (`transaction_pk`),
  KEY `idTag_idx` (`id_tag`),
  KEY `connector_pk_idx` (`connector_pk`),
  KEY `transaction_start_idx` (`start_timestamp`),
  CONSTRAINT `FK_connector_pk_t` FOREIGN KEY (`connector_pk`) REFERENCES `connector` (`connector_pk`) ON DELETE CASCADE,
  CONSTRAINT `FK_transaction_ocpp_tag_id_tag` FOREIGN KEY (`id_tag`) REFERENCES `ocpp_tag` (`id_tag`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `transaction_start`
--

LOCK TABLES `transaction_start` WRITE;
/*!40000 ALTER TABLE `transaction_start` DISABLE KEYS */;
INSERT INTO `transaction_start` VALUES (1,'2025-04-27 08:21:35.119000',130,'439C8105','2025-04-27 08:21:34.028000','0'),(2,'2025-04-27 08:22:55.135000',130,'439c8105','2025-04-27 08:22:53.821000','0'),(3,'2025-04-27 08:45:52.223000',130,'439c8105','2025-04-27 08:45:51.202000','0');
/*!40000 ALTER TABLE `transaction_start` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `transaction_stop`
--

DROP TABLE IF EXISTS `transaction_stop`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `transaction_stop` (
  `transaction_pk` int unsigned NOT NULL,
  `event_timestamp` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `event_actor` enum('station','manual') COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `stop_timestamp` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `stop_value` varchar(255) COLLATE utf8mb3_unicode_ci NOT NULL,
  `stop_reason` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`transaction_pk`,`event_timestamp`),
  CONSTRAINT `FK_transaction_stop_transaction_pk` FOREIGN KEY (`transaction_pk`) REFERENCES `transaction_start` (`transaction_pk`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `transaction_stop`
--

LOCK TABLES `transaction_stop` WRITE;
/*!40000 ALTER TABLE `transaction_stop` DISABLE KEYS */;
INSERT INTO `transaction_stop` VALUES (1,'2025-04-27 08:22:37.714000','station','2025-04-27 08:22:36.675000','0','Local'),(2,'2025-04-27 08:23:43.311000','station','2025-04-27 08:23:42.271000','0','Remote'),(3,'2025-04-27 08:46:24.562000','manual','2025-04-27 08:46:11.117000','0',NULL),(3,'2025-04-27 08:48:11.189000','station','2025-04-27 08:48:10.321000','0','Other');
/*!40000 ALTER TABLE `transaction_stop` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `transaction_stop_failed`
--

DROP TABLE IF EXISTS `transaction_stop_failed`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `transaction_stop_failed` (
  `transaction_pk` int DEFAULT NULL,
  `charge_box_id` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `event_timestamp` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `event_actor` enum('station','manual') COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `stop_timestamp` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `stop_value` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `stop_reason` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `fail_reason` text COLLATE utf8mb3_unicode_ci
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `transaction_stop_failed`
--

LOCK TABLES `transaction_stop_failed` WRITE;
/*!40000 ALTER TABLE `transaction_stop_failed` DISABLE KEYS */;
/*!40000 ALTER TABLE `transaction_stop_failed` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `update_firmware_batch`
--

DROP TABLE IF EXISTS `update_firmware_batch`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `update_firmware_batch` (
  `batch_id` int NOT NULL AUTO_INCREMENT,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `total_count` int NOT NULL DEFAULT '0',
  `success_count` int NOT NULL DEFAULT '0',
  `pending_count` int NOT NULL DEFAULT '0',
  `error_count` int NOT NULL DEFAULT '0',
  `status` varchar(50) COLLATE utf8mb3_unicode_ci NOT NULL DEFAULT 'INPROGRESS',
  `created_by` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`batch_id`)
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci COMMENT='固件升级批处理';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `update_firmware_batch`
--

LOCK TABLES `update_firmware_batch` WRITE;
/*!40000 ALTER TABLE `update_firmware_batch` DISABLE KEYS */;
INSERT INTO `update_firmware_batch` VALUES (1,'2025-05-08 08:43:11',1,0,1,0,'INPROGRESS',NULL),(2,'2025-05-08 09:01:14',1,0,1,0,'INPROGRESS',NULL),(3,'2025-05-08 09:03:51',2,0,2,0,'INPROGRESS',NULL),(4,'2025-05-09 00:54:07',1,0,1,0,'INPROGRESS',NULL),(5,'2025-05-09 01:07:48',1,0,1,0,'INPROGRESS','system'),(6,'2025-05-09 01:21:27',1,0,1,0,'INPROGRESS','system'),(7,'2025-05-09 01:47:59',1,0,1,0,'INPROGRESS','system'),(8,'2025-05-09 02:08:15',1,0,1,0,'INPROGRESS','system'),(9,'2025-05-09 02:49:32',1,0,1,0,'INPROGRESS','system'),(10,'2025-05-09 02:58:19',1,0,1,0,'INPROGRESS','system'),(11,'2025-05-09 03:30:20',1,0,1,0,'INPROGRESS','system'),(12,'2025-05-09 03:30:37',1,0,1,0,'INPROGRESS','system'),(13,'2025-05-09 03:31:07',1,0,1,0,'INPROGRESS','system'),(14,'2025-05-09 03:31:26',1,0,1,0,'INPROGRESS','system'),(15,'2025-05-09 03:31:48',1,0,1,0,'INPROGRESS','system'),(16,'2025-05-09 03:32:26',1,0,1,0,'INPROGRESS','system'),(17,'2025-05-09 03:33:02',1,0,1,0,'INPROGRESS','system'),(18,'2025-05-09 03:33:25',1,0,1,0,'INPROGRESS','system'),(19,'2025-05-09 03:33:53',1,0,1,0,'INPROGRESS','system'),(20,'2025-05-09 03:34:18',1,0,1,0,'INPROGRESS','system'),(21,'2025-05-09 03:35:04',1,0,1,0,'INPROGRESS','system');
/*!40000 ALTER TABLE `update_firmware_batch` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `update_firmware_log`
--

DROP TABLE IF EXISTS `update_firmware_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `update_firmware_log` (
  `log_id` int NOT NULL AUTO_INCREMENT,
  `location` varchar(255) COLLATE utf8mb3_unicode_ci NOT NULL COMMENT '固件位置URI',
  `retries` int DEFAULT NULL COMMENT '重试次数',
  `retry_interval` int DEFAULT NULL COMMENT '重试间隔',
  `retrieve_datetime` datetime NOT NULL COMMENT '检索日期/时间',
  `sending_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发送时间',
  `last_updated` timestamp NULL DEFAULT NULL COMMENT '最后更新时间',
  `charge_box_id` varchar(255) COLLATE utf8mb3_unicode_ci NOT NULL COMMENT '充电桩ID',
  `status` varchar(50) COLLATE utf8mb3_unicode_ci DEFAULT NULL COMMENT '状态信息',
  `response` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL COMMENT '响应内容',
  `batch_id` int DEFAULT NULL COMMENT '批处理ID',
  PRIMARY KEY (`log_id`),
  KEY `update_firmware_log_charge_box_id_idx` (`charge_box_id`),
  KEY `update_firmware_log_sending_time_idx` (`sending_time`),
  KEY `fk_update_firmware_log_batch` (`batch_id`),
  CONSTRAINT `fk_update_firmware_log_batch` FOREIGN KEY (`batch_id`) REFERENCES `update_firmware_batch` (`batch_id`) ON DELETE SET NULL,
  CONSTRAINT `fk_update_firmware_log_charge_box` FOREIGN KEY (`charge_box_id`) REFERENCES `charge_box` (`charge_box_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=44 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci COMMENT='固件更新日志';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `update_firmware_log`
--

LOCK TABLES `update_firmware_log` WRITE;
/*!40000 ALTER TABLE `update_firmware_log` DISABLE KEYS */;
INSERT INTO `update_firmware_log` VALUES (33,'*********************************/SETEC_EVSE_V1.6.200.20250428.apk',NULL,NULL,'2025-05-09 10:59:00','2025-05-09 02:58:19','2025-05-09 02:58:46','CP002','InstallationFailed','Status notification received: InstallationFailed',NULL),(34,'*********************************/SETEC_EVSE_V1.6.200.20250428.apk',NULL,NULL,'2025-05-09 11:31:00','2025-05-09 03:30:20','2025-05-09 03:30:37','CP002','Downloaded','Status notification received: Downloaded',NULL),(35,'*********************************/SETEC_EVSE_V1.6.200.20250428.apk',NULL,NULL,'2025-05-09 11:34:00','2025-05-09 03:30:37','2025-05-09 03:30:47','CP002','InstallationFailed','Status notification received: InstallationFailed',NULL),(36,'*********************************/SETEC_EVSE_V1.6.200.20250428.apk',NULL,NULL,'2025-05-09 11:33:00','2025-05-09 03:31:07','2025-05-09 03:31:10','CP002','Downloading','Status notification received: Downloading',NULL),(37,'*********************************/SETEC_EVSE_V1.6.200.20250428.apk',NULL,NULL,'2025-05-09 11:35:00','2025-05-09 03:31:27','2025-05-09 03:31:40','CP002','InstallationFailed','Status notification received: InstallationFailed',NULL),(38,'*********************************/SETEC_EVSE_V1.6.200.20250428.apk',NULL,NULL,'2025-05-09 11:34:00','2025-05-09 03:31:48','2025-05-09 03:32:15','CP002','InstallationFailed','Status notification received: InstallationFailed',NULL),(39,'*********************************/SETEC_EVSE_V1.6.200.20250428.apk',NULL,NULL,'2025-05-09 11:33:00','2025-05-09 03:32:26','2025-05-09 03:32:53','CP002','InstallationFailed','Status notification received: InstallationFailed',NULL),(40,'*********************************/SETEC_EVSE_V1.6.200.20250428.apk',NULL,NULL,'2025-05-09 11:36:00','2025-05-09 03:33:02','2025-05-09 03:33:23','CP002','Installing','Status notification received: Installing',NULL),(41,'*********************************/SETEC_EVSE_V1.6.200.20250428.apk',NULL,NULL,'2025-05-09 11:39:00','2025-05-09 03:33:25','2025-05-09 03:33:29','CP002','InstallationFailed','Status notification received: InstallationFailed',NULL),(42,'*********************************/SETEC_EVSE_V1.6.200.20250428.apk',NULL,NULL,'2025-05-09 11:35:00','2025-05-09 03:33:53','2025-05-09 03:34:20','CP002','InstallationFailed','Status notification received: InstallationFailed',NULL),(43,'*********************************/SETEC_EVSE_V1.6.200.20250428.apk',NULL,NULL,'2025-05-09 11:38:00','2025-05-09 03:35:04','2025-05-09 03:35:32','CP002','InstallationFailed','Status notification received: InstallationFailed',NULL);
/*!40000 ALTER TABLE `update_firmware_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user`
--

DROP TABLE IF EXISTS `user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user` (
  `user_pk` int NOT NULL AUTO_INCREMENT,
  `ocpp_tag_pk` int DEFAULT NULL,
  `address_pk` int DEFAULT NULL,
  `first_name` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `last_name` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `birth_day` date DEFAULT NULL,
  `sex` char(1) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `phone` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `e_mail` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `note` text COLLATE utf8mb3_unicode_ci,
  PRIMARY KEY (`user_pk`),
  KEY `FK_user_ocpp_tag_otpk` (`ocpp_tag_pk`),
  KEY `FK_user_address_apk` (`address_pk`),
  CONSTRAINT `FK_user_address_apk` FOREIGN KEY (`address_pk`) REFERENCES `address` (`address_pk`) ON DELETE SET NULL,
  CONSTRAINT `FK_user_ocpp_tag_otpk` FOREIGN KEY (`ocpp_tag_pk`) REFERENCES `ocpp_tag` (`ocpp_tag_pk`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user`
--

LOCK TABLES `user` WRITE;
/*!40000 ALTER TABLE `user` DISABLE KEYS */;
INSERT INTO `user` VALUES (7,NULL,3,'test1','test1',NULL,'m',NULL,NULL,NULL),(8,NULL,4,'test2','2',NULL,'o',NULL,NULL,NULL),(12,NULL,5,'John','Doe',NULL,'m','123456789','<EMAIL>',NULL);
/*!40000 ALTER TABLE `user` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_charge_box`
--

DROP TABLE IF EXISTS `user_charge_box`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_charge_box` (
  `user_charge_box_pk` int NOT NULL AUTO_INCREMENT,
  `web_user_pk` int NOT NULL,
  `charge_box_pk` int NOT NULL,
  PRIMARY KEY (`user_charge_box_pk`),
  UNIQUE KEY `unique_user_charge_box` (`web_user_pk`,`charge_box_pk`),
  KEY `idx_user_charge_box_web_user_pk` (`web_user_pk`),
  KEY `idx_user_charge_box_charge_box_pk` (`charge_box_pk`),
  CONSTRAINT `user_charge_box_ibfk_1` FOREIGN KEY (`web_user_pk`) REFERENCES `web_user` (`web_user_pk`) ON DELETE CASCADE,
  CONSTRAINT `user_charge_box_ibfk_2` FOREIGN KEY (`charge_box_pk`) REFERENCES `charge_box` (`charge_box_pk`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_charge_box`
--

LOCK TABLES `user_charge_box` WRITE;
/*!40000 ALTER TABLE `user_charge_box` DISABLE KEYS */;
INSERT INTO `user_charge_box` VALUES (13,10,1),(14,10,2),(15,10,3),(19,10,4),(21,10,5),(22,10,7),(18,11,6);
/*!40000 ALTER TABLE `user_charge_box` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Temporary view structure for view `view_charger_issues`
--

DROP TABLE IF EXISTS `view_charger_issues`;
/*!50001 DROP VIEW IF EXISTS `view_charger_issues`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `view_charger_issues` AS SELECT 
 1 AS `issue_id`,
 1 AS `charge_box_pk`,
 1 AS `charge_box_id`,
 1 AS `report_time`,
 1 AS `reporter_user_pk`,
 1 AS `reporter_username`,
 1 AS `fault_description`,
 1 AS `status`,
 1 AS `ocpp_error_code`,
 1 AS `is_auto_reported`,
 1 AS `resolve_time`,
 1 AS `resolve_description`,
 1 AS `charge_point_vendor`,
 1 AS `charge_point_model`,
 1 AS `owner_user_pks`*/;
SET character_set_client = @saved_cs_client;

--
-- Temporary view structure for view `view_charging_stations`
--

DROP TABLE IF EXISTS `view_charging_stations`;
/*!50001 DROP VIEW IF EXISTS `view_charging_stations`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `view_charging_stations` AS SELECT 
 1 AS `charging_station_pk`,
 1 AS `station_name`,
 1 AS `operator_name`,
 1 AS `location`,
 1 AS `construction_date`,
 1 AS `operation_date`,
 1 AS `created_on`,
 1 AS `last_updated_on`,
 1 AS `web_user_pk`,
 1 AS `first_name`,
 1 AS `last_name`,
 1 AS `birth_day`,
 1 AS `sex`,
 1 AS `e_mail`,
 1 AS `phone`*/;
SET character_set_client = @saved_cs_client;

--
-- Temporary view structure for view `view_issue_complete`
--

DROP TABLE IF EXISTS `view_issue_complete`;
/*!50001 DROP VIEW IF EXISTS `view_issue_complete`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `view_issue_complete` AS SELECT 
 1 AS `issue_id`,
 1 AS `charge_box_pk`,
 1 AS `charge_box_id`,
 1 AS `report_time`,
 1 AS `reporter_user_pk`,
 1 AS `reporter_username`,
 1 AS `fault_description`,
 1 AS `status`,
 1 AS `ocpp_error_code`,
 1 AS `is_auto_reported`,
 1 AS `resolve_time`,
 1 AS `resolve_description`,
 1 AS `charge_point_vendor`,
 1 AS `charge_point_model`,
 1 AS `maintenance_count`,
 1 AS `image_count`,
 1 AS `owner_user_pks`,
 1 AS `owner_usernames`*/;
SET character_set_client = @saved_cs_client;

--
-- Temporary view structure for view `view_issue_image_count`
--

DROP TABLE IF EXISTS `view_issue_image_count`;
/*!50001 DROP VIEW IF EXISTS `view_issue_image_count`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `view_issue_image_count` AS SELECT 
 1 AS `issue_id`,
 1 AS `charge_box_id`,
 1 AS `status`,
 1 AS `report_time`,
 1 AS `image_count`*/;
SET character_set_client = @saved_cs_client;

--
-- Temporary view structure for view `view_maintenance_records`
--

DROP TABLE IF EXISTS `view_maintenance_records`;
/*!50001 DROP VIEW IF EXISTS `view_maintenance_records`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `view_maintenance_records` AS SELECT 
 1 AS `record_id`,
 1 AS `issue_id`,
 1 AS `maintenance_time`,
 1 AS `maintainer_user_pk`,
 1 AS `maintainer_username`,
 1 AS `maintenance_description`,
 1 AS `charge_box_pk`,
 1 AS `charge_box_id`,
 1 AS `issue_status`,
 1 AS `fault_description`*/;
SET character_set_client = @saved_cs_client;

--
-- Temporary view structure for view `view_owner_charge_boxes`
--

DROP TABLE IF EXISTS `view_owner_charge_boxes`;
/*!50001 DROP VIEW IF EXISTS `view_owner_charge_boxes`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `view_owner_charge_boxes` AS SELECT 
 1 AS `charge_box_pk`,
 1 AS `charge_box_id`,
 1 AS `endpoint_address`,
 1 AS `ocpp_protocol`,
 1 AS `registration_status`,
 1 AS `charge_point_vendor`,
 1 AS `charge_point_model`,
 1 AS `charge_point_serial_number`,
 1 AS `charge_box_serial_number`,
 1 AS `fw_version`,
 1 AS `fw_update_status`,
 1 AS `fw_update_timestamp`,
 1 AS `iccid`,
 1 AS `imsi`,
 1 AS `meter_type`,
 1 AS `meter_serial_number`,
 1 AS `diagnostics_status`,
 1 AS `diagnostics_timestamp`,
 1 AS `last_heartbeat_timestamp`,
 1 AS `description`,
 1 AS `note`,
 1 AS `location_latitude`,
 1 AS `location_longitude`,
 1 AS `address_pk`,
 1 AS `admin_address`,
 1 AS `insert_connector_status_after_transaction_msg`,
 1 AS `web_user_pk`*/;
SET character_set_client = @saved_cs_client;

--
-- Temporary view structure for view `view_owner_transactions`
--

DROP TABLE IF EXISTS `view_owner_transactions`;
/*!50001 DROP VIEW IF EXISTS `view_owner_transactions`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `view_owner_transactions` AS SELECT 
 1 AS `transaction_pk`,
 1 AS `connector_pk`,
 1 AS `id_tag`,
 1 AS `start_event_timestamp`,
 1 AS `start_timestamp`,
 1 AS `start_value`,
 1 AS `stop_event_actor`,
 1 AS `stop_event_timestamp`,
 1 AS `stop_timestamp`,
 1 AS `stop_value`,
 1 AS `stop_reason`,
 1 AS `charge_box_id`,
 1 AS `web_user_pk`*/;
SET character_set_client = @saved_cs_client;

--
-- Temporary view structure for view `view_station_info`
--

DROP TABLE IF EXISTS `view_station_info`;
/*!50001 DROP VIEW IF EXISTS `view_station_info`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `view_station_info` AS SELECT 
 1 AS `charge_box_pk`,
 1 AS `charge_box_id`,
 1 AS `station_name`,
 1 AS `operator_name`,
 1 AS `build_date`,
 1 AS `operation_date`,
 1 AS `web_user_pk`,
 1 AS `bind_user_name`*/;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `web_user`
--

DROP TABLE IF EXISTS `web_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `web_user` (
  `web_user_pk` int NOT NULL AUTO_INCREMENT,
  `username` varchar(500) NOT NULL,
  `password` varchar(500) NOT NULL,
  `api_password` varchar(500) DEFAULT NULL,
  `enabled` tinyint(1) NOT NULL,
  `user_role` enum('ADMIN','OPERATOR_FACTORY','OPERATOR_OWNER') NOT NULL DEFAULT 'ADMIN',
  PRIMARY KEY (`web_user_pk`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `web_user`
--

LOCK TABLES `web_user` WRITE;
/*!40000 ALTER TABLE `web_user` DISABLE KEYS */;
INSERT INTO `web_user` VALUES (1,'admin','$2a$10$wt4EMzOcgXtlnvR46hfKV.9ICUlWYEYkOYw2Ar9HS2CeNDHW/gjKe',NULL,1,'ADMIN'),(10,'test1','$2a$10$fahASrgbdalNCUjWNQVDnOnwM/liE.JXDUF8gUNc0lLUFsjR828k.',NULL,1,'OPERATOR_OWNER'),(11,'test2','$2a$10$IiI40mEWth8eB7/H6jRSxOH57geBetZAAnofFyN1NBJQ.SDnFLPAq',NULL,1,'OPERATOR_OWNER'),(12,'john','$2a$10$wt4EMzOcgXtlnvR46hfKV.9ICUlWYEYkOYw2Ar9HS2CeNDHW/gjKe',NULL,1,'OPERATOR_OWNER');
/*!40000 ALTER TABLE `web_user` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping events for database 'stevedb'
--

--
-- Dumping routines for database 'stevedb'
--
/*!50003 DROP PROCEDURE IF EXISTS `get_user_charger_issues` */;
ALTER DATABASE `stevedb` CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci ;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_unicode_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'IGNORE_SPACE,ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `get_user_charger_issues`(
    IN p_user_pk INT,
    IN p_user_role VARCHAR(50),
    IN p_status VARCHAR(20),
    IN p_start_date DATETIME,
    IN p_end_date DATETIME
)
BEGIN
    IF p_user_role = 'ADMIN' THEN
        -- 管理员可以查看所有故障
        SELECT * FROM view_issue_complete
        WHERE (p_status IS NULL OR status = p_status)
          AND (p_start_date IS NULL OR report_time >= p_start_date)
          AND (p_end_date IS NULL OR report_time <= p_end_date)
        ORDER BY report_time DESC;
    ELSEIF p_user_role = 'OPERATOR_FACTORY' THEN
        -- 工厂操作员可以查看所有故障
        SELECT * FROM view_issue_complete
        WHERE (p_status IS NULL OR status = p_status)
          AND (p_start_date IS NULL OR report_time >= p_start_date)
          AND (p_end_date IS NULL OR report_time <= p_end_date)
        ORDER BY report_time DESC;
    ELSEIF p_user_role = 'OPERATOR_OWNER' THEN
        -- 业主操作员只能查看自己拥有的充电桩的故障
        SELECT * FROM view_issue_complete
        WHERE FIND_IN_SET(p_user_pk, owner_user_pks)
          AND (p_status IS NULL OR status = p_status)
          AND (p_start_date IS NULL OR report_time >= p_start_date)
          AND (p_end_date IS NULL OR report_time <= p_end_date)
        ORDER BY report_time DESC;
    ELSE
        -- 默认情况，无权限
        SELECT 'No permission' as message;
    END IF;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
ALTER DATABASE `stevedb` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci ;

--
-- Final view structure for view `ocpp_tag_activity`
--

/*!50001 DROP VIEW IF EXISTS `ocpp_tag_activity`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb3 */;
/*!50001 SET character_set_results     = utf8mb3 */;
/*!50001 SET collation_connection      = utf8mb4_0900_ai_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `ocpp_tag_activity` AS select `o`.`ocpp_tag_pk` AS `ocpp_tag_pk`,`o`.`id_tag` AS `id_tag`,`o`.`parent_id_tag` AS `parent_id_tag`,`o`.`expiry_date` AS `expiry_date`,`o`.`max_active_transaction_count` AS `max_active_transaction_count`,`o`.`note` AS `note`,count(`t`.`id_tag`) AS `active_transaction_count`,(case when (count(`t`.`id_tag`) > 0) then 1 else 0 end) AS `in_transaction`,(case when (`o`.`max_active_transaction_count` = 0) then 1 else 0 end) AS `blocked` from (`ocpp_tag` `o` left join `transaction` `t` on(((`o`.`id_tag` = `t`.`id_tag`) and (`t`.`stop_timestamp` is null) and (`t`.`stop_value` is null)))) group by `o`.`ocpp_tag_pk`,`o`.`parent_id_tag`,`o`.`expiry_date`,`o`.`max_active_transaction_count`,`o`.`note` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `transaction`
--

/*!50001 DROP VIEW IF EXISTS `transaction`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb3 */;
/*!50001 SET character_set_results     = utf8mb3 */;
/*!50001 SET collation_connection      = utf8mb4_0900_ai_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `transaction` AS select `tx1`.`transaction_pk` AS `transaction_pk`,`tx1`.`connector_pk` AS `connector_pk`,`tx1`.`id_tag` AS `id_tag`,`tx1`.`event_timestamp` AS `start_event_timestamp`,`tx1`.`start_timestamp` AS `start_timestamp`,`tx1`.`start_value` AS `start_value`,`tx2`.`event_actor` AS `stop_event_actor`,`tx2`.`event_timestamp` AS `stop_event_timestamp`,`tx2`.`stop_timestamp` AS `stop_timestamp`,`tx2`.`stop_value` AS `stop_value`,`tx2`.`stop_reason` AS `stop_reason` from (`transaction_start` `tx1` left join `transaction_stop` `tx2` on(((`tx1`.`transaction_pk` = `tx2`.`transaction_pk`) and (`tx2`.`event_timestamp` = (select max(`s2`.`event_timestamp`) from `transaction_stop` `s2` where (`tx2`.`transaction_pk` = `s2`.`transaction_pk`)))))) */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `view_charger_issues`
--

/*!50001 DROP VIEW IF EXISTS `view_charger_issues`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_unicode_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `view_charger_issues` AS select `ci`.`issue_id` AS `issue_id`,`ci`.`charge_box_pk` AS `charge_box_pk`,`cb`.`charge_box_id` AS `charge_box_id`,`ci`.`report_time` AS `report_time`,`ci`.`reporter_user_pk` AS `reporter_user_pk`,`wu`.`username` AS `reporter_username`,`ci`.`fault_description` AS `fault_description`,`ci`.`status` AS `status`,`ci`.`ocpp_error_code` AS `ocpp_error_code`,`ci`.`is_auto_reported` AS `is_auto_reported`,`ci`.`resolve_time` AS `resolve_time`,`ci`.`resolve_description` AS `resolve_description`,`cb`.`charge_point_vendor` AS `charge_point_vendor`,`cb`.`charge_point_model` AS `charge_point_model`,(select group_concat(`ucb`.`web_user_pk` separator ',') from `user_charge_box` `ucb` where (`ucb`.`charge_box_pk` = `ci`.`charge_box_pk`)) AS `owner_user_pks` from ((`charger_issue` `ci` join `charge_box` `cb` on((`ci`.`charge_box_pk` = `cb`.`charge_box_pk`))) left join `web_user` `wu` on((`ci`.`reporter_user_pk` = `wu`.`web_user_pk`))) */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `view_charging_stations`
--

/*!50001 DROP VIEW IF EXISTS `view_charging_stations`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_0900_ai_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `view_charging_stations` AS select `cs`.`charging_station_pk` AS `charging_station_pk`,`cs`.`station_name` AS `station_name`,`cs`.`operator_name` AS `operator_name`,`cs`.`location` AS `location`,`cs`.`construction_date` AS `construction_date`,`cs`.`operation_date` AS `operation_date`,`cs`.`created_on` AS `created_on`,`cs`.`last_updated_on` AS `last_updated_on`,`cs`.`web_user_pk` AS `web_user_pk`,`u`.`first_name` AS `first_name`,`u`.`last_name` AS `last_name`,`u`.`birth_day` AS `birth_day`,`u`.`sex` AS `sex`,`u`.`e_mail` AS `e_mail`,`u`.`phone` AS `phone` from ((`charging_station` `cs` left join `web_user` `wu` on((`cs`.`web_user_pk` = `wu`.`web_user_pk`))) left join `user` `u` on((`wu`.`web_user_pk` = `u`.`user_pk`))) */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `view_issue_complete`
--

/*!50001 DROP VIEW IF EXISTS `view_issue_complete`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_unicode_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `view_issue_complete` AS select `ci`.`issue_id` AS `issue_id`,`ci`.`charge_box_pk` AS `charge_box_pk`,`cb`.`charge_box_id` AS `charge_box_id`,`ci`.`report_time` AS `report_time`,`ci`.`reporter_user_pk` AS `reporter_user_pk`,`wu_reporter`.`username` AS `reporter_username`,`ci`.`fault_description` AS `fault_description`,`ci`.`status` AS `status`,`ci`.`ocpp_error_code` AS `ocpp_error_code`,`ci`.`is_auto_reported` AS `is_auto_reported`,`ci`.`resolve_time` AS `resolve_time`,`ci`.`resolve_description` AS `resolve_description`,`cb`.`charge_point_vendor` AS `charge_point_vendor`,`cb`.`charge_point_model` AS `charge_point_model`,(select count(0) from `issue_maintenance_record` `imr` where (`imr`.`issue_id` = `ci`.`issue_id`)) AS `maintenance_count`,(select count(0) from `issue_image` `ii` where (`ii`.`issue_id` = `ci`.`issue_id`)) AS `image_count`,(select group_concat(`ucb`.`web_user_pk` separator ',') from `user_charge_box` `ucb` where (`ucb`.`charge_box_pk` = `ci`.`charge_box_pk`)) AS `owner_user_pks`,(select group_concat(distinct `wu_owner`.`username` separator ',') from (`user_charge_box` `ucb` join `web_user` `wu_owner` on((`ucb`.`web_user_pk` = `wu_owner`.`web_user_pk`))) where (`ucb`.`charge_box_pk` = `ci`.`charge_box_pk`)) AS `owner_usernames` from ((`charger_issue` `ci` join `charge_box` `cb` on((`ci`.`charge_box_pk` = `cb`.`charge_box_pk`))) left join `web_user` `wu_reporter` on((`ci`.`reporter_user_pk` = `wu_reporter`.`web_user_pk`))) */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `view_issue_image_count`
--

/*!50001 DROP VIEW IF EXISTS `view_issue_image_count`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_unicode_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `view_issue_image_count` AS select `ci`.`issue_id` AS `issue_id`,`cb`.`charge_box_id` AS `charge_box_id`,`ci`.`status` AS `status`,`ci`.`report_time` AS `report_time`,count(`ii`.`image_id`) AS `image_count` from ((`charger_issue` `ci` join `charge_box` `cb` on((`ci`.`charge_box_pk` = `cb`.`charge_box_pk`))) left join `issue_image` `ii` on((`ci`.`issue_id` = `ii`.`issue_id`))) group by `ci`.`issue_id` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `view_maintenance_records`
--

/*!50001 DROP VIEW IF EXISTS `view_maintenance_records`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_unicode_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `view_maintenance_records` AS select `imr`.`record_id` AS `record_id`,`imr`.`issue_id` AS `issue_id`,`imr`.`maintenance_time` AS `maintenance_time`,`imr`.`maintainer_user_pk` AS `maintainer_user_pk`,`wu`.`username` AS `maintainer_username`,`imr`.`maintenance_description` AS `maintenance_description`,`ci`.`charge_box_pk` AS `charge_box_pk`,`cb`.`charge_box_id` AS `charge_box_id`,`ci`.`status` AS `issue_status`,`ci`.`fault_description` AS `fault_description` from (((`issue_maintenance_record` `imr` join `charger_issue` `ci` on((`imr`.`issue_id` = `ci`.`issue_id`))) join `charge_box` `cb` on((`ci`.`charge_box_pk` = `cb`.`charge_box_pk`))) left join `web_user` `wu` on((`imr`.`maintainer_user_pk` = `wu`.`web_user_pk`))) */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `view_owner_charge_boxes`
--

/*!50001 DROP VIEW IF EXISTS `view_owner_charge_boxes`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_0900_ai_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `view_owner_charge_boxes` AS select `cb`.`charge_box_pk` AS `charge_box_pk`,`cb`.`charge_box_id` AS `charge_box_id`,`cb`.`endpoint_address` AS `endpoint_address`,`cb`.`ocpp_protocol` AS `ocpp_protocol`,`cb`.`registration_status` AS `registration_status`,`cb`.`charge_point_vendor` AS `charge_point_vendor`,`cb`.`charge_point_model` AS `charge_point_model`,`cb`.`charge_point_serial_number` AS `charge_point_serial_number`,`cb`.`charge_box_serial_number` AS `charge_box_serial_number`,`cb`.`fw_version` AS `fw_version`,`cb`.`fw_update_status` AS `fw_update_status`,`cb`.`fw_update_timestamp` AS `fw_update_timestamp`,`cb`.`iccid` AS `iccid`,`cb`.`imsi` AS `imsi`,`cb`.`meter_type` AS `meter_type`,`cb`.`meter_serial_number` AS `meter_serial_number`,`cb`.`diagnostics_status` AS `diagnostics_status`,`cb`.`diagnostics_timestamp` AS `diagnostics_timestamp`,`cb`.`last_heartbeat_timestamp` AS `last_heartbeat_timestamp`,`cb`.`description` AS `description`,`cb`.`note` AS `note`,`cb`.`location_latitude` AS `location_latitude`,`cb`.`location_longitude` AS `location_longitude`,`cb`.`address_pk` AS `address_pk`,`cb`.`admin_address` AS `admin_address`,`cb`.`insert_connector_status_after_transaction_msg` AS `insert_connector_status_after_transaction_msg`,`ucb`.`web_user_pk` AS `web_user_pk` from (`charge_box` `cb` join `user_charge_box` `ucb` on((`cb`.`charge_box_pk` = `ucb`.`charge_box_pk`))) */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `view_owner_transactions`
--

/*!50001 DROP VIEW IF EXISTS `view_owner_transactions`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_0900_ai_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `view_owner_transactions` AS select `t`.`transaction_pk` AS `transaction_pk`,`t`.`connector_pk` AS `connector_pk`,`t`.`id_tag` AS `id_tag`,`t`.`start_event_timestamp` AS `start_event_timestamp`,`t`.`start_timestamp` AS `start_timestamp`,`t`.`start_value` AS `start_value`,`t`.`stop_event_actor` AS `stop_event_actor`,`t`.`stop_event_timestamp` AS `stop_event_timestamp`,`t`.`stop_timestamp` AS `stop_timestamp`,`t`.`stop_value` AS `stop_value`,`t`.`stop_reason` AS `stop_reason`,`c`.`charge_box_id` AS `charge_box_id`,`ucb`.`web_user_pk` AS `web_user_pk` from (((`transaction` `t` join `connector` `c` on((`t`.`connector_pk` = `c`.`connector_pk`))) join `charge_box` `cb` on((`c`.`charge_box_id` = `cb`.`charge_box_id`))) join `user_charge_box` `ucb` on((`cb`.`charge_box_pk` = `ucb`.`charge_box_pk`))) */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `view_station_info`
--

/*!50001 DROP VIEW IF EXISTS `view_station_info`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_unicode_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `view_station_info` AS select `cb`.`charge_box_pk` AS `charge_box_pk`,`cb`.`charge_box_id` AS `charge_box_id`,`cb`.`station_name` AS `station_name`,`cb`.`operator_name` AS `operator_name`,`cb`.`build_date` AS `build_date`,`cb`.`operation_date` AS `operation_date`,`u`.`web_user_pk` AS `web_user_pk`,`u`.`username` AS `bind_user_name` from ((`charge_box` `cb` left join `user_charge_box` `ucb` on((`cb`.`charge_box_pk` = `ucb`.`charge_box_pk`))) left join `web_user` `u` on((`ucb`.`web_user_pk` = `u`.`web_user_pk`))) */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-05-09 11:48:35
