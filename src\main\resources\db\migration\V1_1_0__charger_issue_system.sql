-- 创建充电桩故障表
CREATE TABLE IF NOT EXISTS charger_issue (
    issue_id INT NOT NULL AUTO_INCREMENT,
    charge_box_pk INT NOT NULL,
    report_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    reporter_user_pk INT NULL,
    fault_description TEXT NOT NULL,
    status ENUM('NEW', 'IN_PROGRESS', 'RESOLVED') NOT NULL DEFAULT 'NEW',
    ocpp_error_code VARCHAR(50) NULL,
    is_auto_reported BOOLEAN NOT NULL DEFAULT FALSE,
    resolve_time TIMESTAMP NULL,
    resolve_description TEXT NULL,
    PRIMARY KEY (issue_id),
    FOREIGN KEY (charge_box_pk) REFERENCES charge_box(charge_box_pk) ON DELETE CASCADE,
    FOREIGN KEY (reporter_user_pk) REFERENCES web_user(web_user_pk) ON DELETE SET NULL,
    INDEX idx_charge_box_pk (charge_box_pk),
    INDEX idx_status (status),
    INDEX idx_report_time (report_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建故障图片表
CREATE TABLE IF NOT EXISTS issue_image (
    image_id INT NOT NULL AUTO_INCREMENT,
    issue_id INT NOT NULL,
    image_path VARCHAR(255) NOT NULL,
    upload_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (image_id),
    FOREIGN KEY (issue_id) REFERENCES charger_issue(issue_id) ON DELETE CASCADE,
    INDEX idx_issue_id (issue_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建维修记录表
CREATE TABLE IF NOT EXISTS issue_maintenance_record (
    record_id INT NOT NULL AUTO_INCREMENT,
    issue_id INT NOT NULL,
    maintenance_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    maintainer_user_pk INT NULL,
    maintenance_description TEXT NOT NULL,
    PRIMARY KEY (record_id),
    FOREIGN KEY (issue_id) REFERENCES charger_issue(issue_id) ON DELETE CASCADE,
    FOREIGN KEY (maintainer_user_pk) REFERENCES web_user(web_user_pk) ON DELETE SET NULL,
    INDEX idx_issue_id (issue_id),
    INDEX idx_maintenance_time (maintenance_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci; 