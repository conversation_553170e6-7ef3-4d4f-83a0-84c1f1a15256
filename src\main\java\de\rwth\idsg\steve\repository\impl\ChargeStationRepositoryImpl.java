/*
 * SteV<PERSON> - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.repository.impl;

import de.rwth.idsg.steve.repository.AddressRepository;
import de.rwth.idsg.steve.repository.ChargeStationRepository;
import de.rwth.idsg.steve.repository.dto.ChargeStation;
import de.rwth.idsg.steve.repository.dto.ChargeStationSelect;
import de.rwth.idsg.steve.utils.DateTimeUtils;
import de.rwth.idsg.steve.web.dto.Address;
import de.rwth.idsg.steve.web.dto.ChargeStationQueryForm;
import jooq.steve.db.tables.records.AddressRecord;
import jooq.steve.db.tables.records.ChargingStationRecord;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import com.google.common.base.Strings;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.Record;
import org.jooq.Record1;
import org.jooq.Result;
import org.jooq.SelectConditionStep;
import org.jooq.SelectJoinStep;
import org.jooq.SelectQuery;
import org.jooq.exception.DataAccessException;
import org.jooq.impl.DSL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

import static jooq.steve.db.Tables.ADDRESS;
import static jooq.steve.db.Tables.CHARGING_STATION;
import static jooq.steve.db.Tables.USER;
import static jooq.steve.db.Tables.VIEW_CHARGING_STATIONS;
import static jooq.steve.db.Tables.WEB_USER;

/**
 * <AUTHOR> Chou
 */
@Slf4j
@Repository
@Transactional(readOnly = true)
public class ChargeStationRepositoryImpl implements ChargeStationRepository {

    @Autowired private DSLContext ctx;
    @Autowired private AddressRepository addressRepository;

    @Override
    public ChargeStation.Details getDetails(int chargingStationPk) {
        SelectConditionStep<Record> query = ctx.select()
                .from(CHARGING_STATION)
                .leftJoin(WEB_USER).on(CHARGING_STATION.WEB_USER_PK.eq(WEB_USER.WEB_USER_PK))
                .leftJoin(USER).on(WEB_USER.WEB_USER_PK.eq(USER.USER_PK))
                .leftJoin(ADDRESS).on(CHARGING_STATION.ADDRESS_PK.eq(ADDRESS.ADDRESS_PK))
                .where(CHARGING_STATION.CHARGING_STATION_PK.eq(chargingStationPk));

        Record r = query.fetchOne();

        if (r == null) {
            throw new IllegalArgumentException("No charge station with id '" + chargingStationPk + "' found");
        }

        AddressRecord addressRecord = r.into(ADDRESS);

        return ChargeStation.Details.builder()
                .chargingStationPk(r.get(CHARGING_STATION.CHARGING_STATION_PK))
                .stationName(r.get(CHARGING_STATION.STATION_NAME))
                .operatorName(r.get(CHARGING_STATION.OPERATOR_NAME))
                .location(r.get(CHARGING_STATION.LOCATION))
                .constructionDate(r.get(CHARGING_STATION.CONSTRUCTION_DATE) != null ?
                        new DateTime(r.get(CHARGING_STATION.CONSTRUCTION_DATE)) : null)
                .operationDate(r.get(CHARGING_STATION.OPERATION_DATE) != null ?
                        new DateTime(r.get(CHARGING_STATION.OPERATION_DATE)) : null)
                .webUserPk(r.get(CHARGING_STATION.WEB_USER_PK))
                .note(r.get(CHARGING_STATION.NOTE))
                .address(addressRecord.into(Address.class))
                .firstName(r.get(USER.FIRST_NAME))
                .lastName(r.get(USER.LAST_NAME))
                .phone(r.get(USER.PHONE))
                .email(r.get(USER.E_MAIL))
                .build();
    }

    @Override
    public List<ChargeStation.Overview> getOverview(ChargeStationQueryForm form) {
        return getOverviewInternal(form);
    }

    @Override
    public List<ChargeStation.Overview> getOwnerChargeStations(int userPk, ChargeStationQueryForm form) {
        // 直接在数据库层面过滤，提高性能和安全性
        SelectQuery<Record> selectQuery = ctx.selectQuery();
        selectQuery.addFrom(VIEW_CHARGING_STATIONS);
        selectQuery.addSelect(
            VIEW_CHARGING_STATIONS.CHARGING_STATION_PK,
            VIEW_CHARGING_STATIONS.STATION_NAME,
            VIEW_CHARGING_STATIONS.OPERATOR_NAME,
            VIEW_CHARGING_STATIONS.LOCATION,
            VIEW_CHARGING_STATIONS.CONSTRUCTION_DATE,
            VIEW_CHARGING_STATIONS.OPERATION_DATE,
            VIEW_CHARGING_STATIONS.WEB_USER_PK,
            VIEW_CHARGING_STATIONS.FIRST_NAME,
            VIEW_CHARGING_STATIONS.LAST_NAME
        );

        // 添加用户权限过滤条件
        selectQuery.addConditions(VIEW_CHARGING_STATIONS.WEB_USER_PK.eq(userPk));

        // 应用查询表单过滤条件
        if (form != null) {
            if (Strings.isNullOrEmpty(form.getStationName()) == false) {
                selectQuery.addConditions(VIEW_CHARGING_STATIONS.STATION_NAME.containsIgnoreCase(form.getStationName()));
            }
            if (Strings.isNullOrEmpty(form.getOperatorName()) == false) {
                selectQuery.addConditions(VIEW_CHARGING_STATIONS.OPERATOR_NAME.containsIgnoreCase(form.getOperatorName()));
            }
            if (Strings.isNullOrEmpty(form.getAssignedUser()) == false) {
                // 使用FIRST_NAME和LAST_NAME组合搜索
                selectQuery.addConditions(
                    DSL.concat(VIEW_CHARGING_STATIONS.FIRST_NAME, DSL.val(" "), VIEW_CHARGING_STATIONS.LAST_NAME)
                        .containsIgnoreCase(form.getAssignedUser())
                );
            }
            if (form.getConstructionDate() != null) {
                DateTime dt = form.getConstructionDate().toDateTimeAtStartOfDay();
                selectQuery.addConditions(VIEW_CHARGING_STATIONS.CONSTRUCTION_DATE.greaterOrEqual(dt));
            }
            if (form.getOperationDate() != null) {
                DateTime dt = form.getOperationDate().toDateTimeAtStartOfDay();
                selectQuery.addConditions(VIEW_CHARGING_STATIONS.OPERATION_DATE.greaterOrEqual(dt));
            }
        }

        selectQuery.addOrderBy(VIEW_CHARGING_STATIONS.CHARGING_STATION_PK.asc());

        return selectQuery.fetch()
                         .map(r -> {
                             String firstName = r.get(VIEW_CHARGING_STATIONS.FIRST_NAME);
                             String lastName = r.get(VIEW_CHARGING_STATIONS.LAST_NAME);
                             String userName = (firstName != null && lastName != null) ?
                                 firstName + " " + lastName :
                                 (firstName != null ? firstName : (lastName != null ? lastName : ""));

                             return ChargeStation.Overview.builder()
                                     .chargingStationPk(r.get(VIEW_CHARGING_STATIONS.CHARGING_STATION_PK))
                                     .stationName(r.get(VIEW_CHARGING_STATIONS.STATION_NAME))
                                     .operatorName(r.get(VIEW_CHARGING_STATIONS.OPERATOR_NAME))
                                     .location(r.get(VIEW_CHARGING_STATIONS.LOCATION))
                                     .constructionDateDT(r.get(VIEW_CHARGING_STATIONS.CONSTRUCTION_DATE))
                                     .constructionDateFormatted(DateTimeUtils.humanize(r.get(VIEW_CHARGING_STATIONS.CONSTRUCTION_DATE)))
                                     .operationDateDT(r.get(VIEW_CHARGING_STATIONS.OPERATION_DATE))
                                     .operationDateFormatted(DateTimeUtils.humanize(r.get(VIEW_CHARGING_STATIONS.OPERATION_DATE)))
                                     .webUserPk(r.get(VIEW_CHARGING_STATIONS.WEB_USER_PK))
                                     .userName(userName)
                                     .build();
                         });
    }

    @Override
    public void addChargeStation(ChargeStation.Form form) {
        ctx.transaction(configuration -> {
            DSLContext dslContext = DSL.using(configuration);
            try {
                // 处理地址信息
                Integer addressPk = null;
                if (form.getAddress() != null && !form.getAddress().isEmpty()) {
                    addressPk = addressRepository.updateOrInsert(dslContext, form.getAddress());
                }

                // 直接执行INSERT语句而不是使用Record对象
                dslContext.insertInto(CHARGING_STATION)
                         .set(CHARGING_STATION.STATION_NAME, form.getStationName())
                         .set(CHARGING_STATION.OPERATOR_NAME, form.getOperatorName())
                         .set(CHARGING_STATION.LOCATION, form.getLocation())
                         .set(CHARGING_STATION.CONSTRUCTION_DATE, form.getConstructionDate() != null ?
                                 new DateTime(form.getConstructionDate().toDate()) : null)
                         .set(CHARGING_STATION.OPERATION_DATE, form.getOperationDate() != null ?
                                 new DateTime(form.getOperationDate().toDate()) : null)
                         .set(CHARGING_STATION.WEB_USER_PK, form.getWebUserPk())
                         .set(CHARGING_STATION.NOTE, form.getNote())
                         .set(CHARGING_STATION.ADDRESS_PK, addressPk)
                         .execute();

                log.debug("Added charge station '{}'", form.getStationName());
            } catch (DataAccessException e) {
                throw new RuntimeException("Error occurred while adding the charge station", e);
            }
        });
    }

    @Override
    public void updateChargeStation(ChargeStation.Form form) {
        ctx.transaction(configuration -> {
            DSLContext dslContext = DSL.using(configuration);
            try {
                // 首先检查记录是否存在
                int count = dslContext.fetchCount(
                    dslContext.selectFrom(CHARGING_STATION)
                             .where(CHARGING_STATION.CHARGING_STATION_PK.eq(form.getChargingStationPk()))
                );

                if (count == 0) {
                    throw new IllegalArgumentException("No charge station with id '" + form.getChargingStationPk() + "' found");
                }

                // 处理地址信息
                Integer addressPk = null;
                if (form.getAddress() != null && !form.getAddress().isEmpty()) {
                    addressPk = addressRepository.updateOrInsert(dslContext, form.getAddress());
                }

                // 直接执行UPDATE语句而不是使用Record对象
                dslContext.update(CHARGING_STATION)
                         .set(CHARGING_STATION.STATION_NAME, form.getStationName())
                         .set(CHARGING_STATION.OPERATOR_NAME, form.getOperatorName())
                         .set(CHARGING_STATION.LOCATION, form.getLocation())
                         .set(CHARGING_STATION.CONSTRUCTION_DATE, form.getConstructionDate() != null ?
                                 new DateTime(form.getConstructionDate().toDate()) : null)
                         .set(CHARGING_STATION.OPERATION_DATE, form.getOperationDate() != null ?
                                 new DateTime(form.getOperationDate().toDate()) : null)
                         .set(CHARGING_STATION.WEB_USER_PK, form.getWebUserPk())
                         .set(CHARGING_STATION.NOTE, form.getNote())
                         .set(CHARGING_STATION.ADDRESS_PK, addressPk)
                         .where(CHARGING_STATION.CHARGING_STATION_PK.eq(form.getChargingStationPk()))
                         .execute();
                
                log.debug("Updated charge station '{}'", form.getStationName());
            } catch (DataAccessException e) {
                throw new RuntimeException("Error occurred while updating the charge station", e);
            }
        });
    }

    @Override
    public void deleteChargeStation(int chargingStationPk) {
        ctx.transaction(configuration -> {
            DSLContext dslContext = DSL.using(configuration);
            try {
                int deleted = dslContext.delete(CHARGING_STATION)
                          .where(CHARGING_STATION.CHARGING_STATION_PK.equal(chargingStationPk))
                          .execute();
                
                if (deleted == 1) {
                    log.debug("Deleted charge station with id '{}'", chargingStationPk);
                } else {
                    log.warn("Charge station with id '{}' could not be deleted", chargingStationPk);
                }
            } catch (DataAccessException e) {
                throw new RuntimeException("Error occurred while deleting the charge station with id " + chargingStationPk, e);
            }
        });
    }
    
    // -------------------------------------------------------------------------
    // Private helpers
    // -------------------------------------------------------------------------

    private List<ChargeStation.Overview> getOverviewInternal(ChargeStationQueryForm form) {
        SelectJoinStep<Record> select = ctx.select().from(VIEW_CHARGING_STATIONS);
        Condition conditions = DSL.trueCondition();
        
        if (form.getStationName() != null && !form.getStationName().isEmpty()) {
            conditions = conditions.and(VIEW_CHARGING_STATIONS.STATION_NAME.contains(form.getStationName()));
        }
        
        if (form.getOperatorName() != null && !form.getOperatorName().isEmpty()) {
            conditions = conditions.and(VIEW_CHARGING_STATIONS.OPERATOR_NAME.contains(form.getOperatorName()));
        }
        
        if (form.getAssignedUser() != null && !form.getAssignedUser().isEmpty()) {
            conditions = conditions.and(
                DSL.concat(VIEW_CHARGING_STATIONS.FIRST_NAME, DSL.val(" "), VIEW_CHARGING_STATIONS.LAST_NAME)
                    .contains(form.getAssignedUser())
            );
        }
        
        if (form.getConstructionDate() != null) {
            DateTime dt = form.getConstructionDate().toDateTimeAtStartOfDay();
            conditions = conditions.and(VIEW_CHARGING_STATIONS.CONSTRUCTION_DATE.greaterOrEqual(dt));
        }
        
        if (form.getOperationDate() != null) {
            DateTime dt = form.getOperationDate().toDateTimeAtStartOfDay();
            conditions = conditions.and(VIEW_CHARGING_STATIONS.OPERATION_DATE.greaterOrEqual(dt));
        }
        
        Result<Record> result = select.where(conditions).fetch();
        
        return result.map(record -> {
            String userName = null;
            if (record.get(VIEW_CHARGING_STATIONS.FIRST_NAME) != null && 
                record.get(VIEW_CHARGING_STATIONS.LAST_NAME) != null) {
                userName = record.get(VIEW_CHARGING_STATIONS.FIRST_NAME) + " " + 
                           record.get(VIEW_CHARGING_STATIONS.LAST_NAME);
            }
            
            return ChargeStation.Overview.builder()
                    .chargingStationPk(record.get(VIEW_CHARGING_STATIONS.CHARGING_STATION_PK))
                    .stationName(record.get(VIEW_CHARGING_STATIONS.STATION_NAME))
                    .operatorName(record.get(VIEW_CHARGING_STATIONS.OPERATOR_NAME))
                    .location(record.get(VIEW_CHARGING_STATIONS.LOCATION))
                    .constructionDate(record.get(VIEW_CHARGING_STATIONS.CONSTRUCTION_DATE))
                    .operationDate(record.get(VIEW_CHARGING_STATIONS.OPERATION_DATE))
                    .webUserPk(record.get(VIEW_CHARGING_STATIONS.WEB_USER_PK))
                    .userName(userName)
                    .constructionDateFormatted(DateTimeUtils.humanize(record.get(VIEW_CHARGING_STATIONS.CONSTRUCTION_DATE)))
                    .operationDateFormatted(DateTimeUtils.humanize(record.get(VIEW_CHARGING_STATIONS.OPERATION_DATE)))
                    .constructionDateDT(record.get(VIEW_CHARGING_STATIONS.CONSTRUCTION_DATE))
                    .operationDateDT(record.get(VIEW_CHARGING_STATIONS.OPERATION_DATE))
                    .build();
        });
    }

    @Override
    public List<ChargeStationSelect> getChargeStationSelectList() {
        return ctx.select(CHARGING_STATION.CHARGING_STATION_PK,
                         CHARGING_STATION.STATION_NAME,
                         CHARGING_STATION.OPERATOR_NAME)
                  .from(CHARGING_STATION)
                  .orderBy(CHARGING_STATION.STATION_NAME)
                  .fetch()
                  .map(record -> new ChargeStationSelect(
                      record.get(CHARGING_STATION.CHARGING_STATION_PK),
                      record.get(CHARGING_STATION.STATION_NAME),
                      record.get(CHARGING_STATION.OPERATOR_NAME)
                  ));
    }

    @Override
    public List<ChargeStationSelect> getChargeStationSelectListByUser(Integer webUserPk) {
        // 如果webUserPk为null，返回所有充电站（管理员和工厂运营商）
        if (webUserPk == null) {
            return getChargeStationSelectList();
        }

        // 对于Charge Box Owner，只返回分配给他们的充电站
        return ctx.select(CHARGING_STATION.CHARGING_STATION_PK,
                         CHARGING_STATION.STATION_NAME,
                         CHARGING_STATION.OPERATOR_NAME)
                  .from(CHARGING_STATION)
                  .where(CHARGING_STATION.WEB_USER_PK.eq(webUserPk))
                  .orderBy(CHARGING_STATION.STATION_NAME)
                  .fetch()
                  .map(record -> new ChargeStationSelect(
                      record.get(CHARGING_STATION.CHARGING_STATION_PK),
                      record.get(CHARGING_STATION.STATION_NAME),
                      record.get(CHARGING_STATION.OPERATOR_NAME)
                  ));
    }

    @Override
    public boolean isChargeStationOwnedByUser(int chargingStationPk, int webUserPk) {
        return ctx.fetchExists(
            ctx.selectOne()
                .from(CHARGING_STATION)
                .where(CHARGING_STATION.CHARGING_STATION_PK.eq(chargingStationPk))
                .and(CHARGING_STATION.WEB_USER_PK.eq(webUserPk))
        );
    }
}
