/**
 * EVSE_OMS辅助样式类
 * 基于RuoYi-Vue样式改造
 */

/* 文本对齐 */
.text-left { text-align: left !important; }
.text-right { text-align: right !important; }
.text-center { text-align: center !important; }
.text-justify { text-align: justify !important; }

/* 文本样式 */
.text-bold { font-weight: bold !important; }
.text-normal { font-weight: normal !important; }
.text-italic { font-style: italic !important; }
.text-underline { text-decoration: underline !important; }
.text-uppercase { text-transform: uppercase !important; }
.text-lowercase { text-transform: lowercase !important; }
.text-capitalize { text-transform: capitalize !important; }

/* 文本大小 */
.text-sm { font-size: 12px !important; }
.text-md { font-size: 14px !important; }
.text-lg { font-size: 16px !important; }
.text-xl { font-size: 18px !important; }
.text-xxl { font-size: 22px !important; }

/* 文本颜色 */
.text-primary { color: #409EFF !important; }
.text-success { color: #67C23A !important; }
.text-info { color: #909399 !important; }
.text-warning { color: #E6A23C !important; }
.text-danger { color: #F56C6C !important; }
.text-muted { color: #909399 !important; }
.text-white { color: #ffffff !important; }

/* 边距 - Margin */
.m0 { margin: 0 !important; }
.mt0 { margin-top: 0 !important; }
.mr0 { margin-right: 0 !important; }
.mb0 { margin-bottom: 0 !important; }
.ml0 { margin-left: 0 !important; }

.m5 { margin: 5px !important; }
.mt5 { margin-top: 5px !important; }
.mr5 { margin-right: 5px !important; }
.mb5 { margin-bottom: 5px !important; }
.ml5 { margin-left: 5px !important; }

.m10 { margin: 10px !important; }
.mt10 { margin-top: 10px !important; }
.mr10 { margin-right: 10px !important; }
.mb10 { margin-bottom: 10px !important; }
.ml10 { margin-left: 10px !important; }

.m15 { margin: 15px !important; }
.mt15 { margin-top: 15px !important; }
.mr15 { margin-right: 15px !important; }
.mb15 { margin-bottom: 15px !important; }
.ml15 { margin-left: 15px !important; }

.m20 { margin: 20px !important; }
.mt20 { margin-top: 20px !important; }
.mr20 { margin-right: 20px !important; }
.mb20 { margin-bottom: 20px !important; }
.ml20 { margin-left: 20px !important; }

.m30 { margin: 30px !important; }
.mt30 { margin-top: 30px !important; }
.mr30 { margin-right: 30px !important; }
.mb30 { margin-bottom: 30px !important; }
.ml30 { margin-left: 30px !important; }

/* 内边距 - Padding */
.p0 { padding: 0 !important; }
.pt0 { padding-top: 0 !important; }
.pr0 { padding-right: 0 !important; }
.pb0 { padding-bottom: 0 !important; }
.pl0 { padding-left: 0 !important; }

.p5 { padding: 5px !important; }
.pt5 { padding-top: 5px !important; }
.pr5 { padding-right: 5px !important; }
.pb5 { padding-bottom: 5px !important; }
.pl5 { padding-left: 5px !important; }

.p10 { padding: 10px !important; }
.pt10 { padding-top: 10px !important; }
.pr10 { padding-right: 10px !important; }
.pb10 { padding-bottom: 10px !important; }
.pl10 { padding-left: 10px !important; }

.p15 { padding: 15px !important; }
.pt15 { padding-top: 15px !important; }
.pr15 { padding-right: 15px !important; }
.pb15 { padding-bottom: 15px !important; }
.pl15 { padding-left: 15px !important; }

.p20 { padding: 20px !important; }
.pt20 { padding-top: 20px !important; }
.pr20 { padding-right: 20px !important; }
.pb20 { padding-bottom: 20px !important; }
.pl20 { padding-left: 20px !important; }

.p30 { padding: 30px !important; }
.pt30 { padding-top: 30px !important; }
.pr30 { padding-right: 30px !important; }
.pb30 { padding-bottom: 30px !important; }
.pl30 { padding-left: 30px !important; }

/* 显示与可见性 */
.d-none { display: none !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-block { display: block !important; }
.d-table { display: table !important; }
.d-table-cell { display: table-cell !important; }
.d-flex { display: flex !important; }
.d-inline-flex { display: inline-flex !important; }

.visible { visibility: visible !important; }
.invisible { visibility: hidden !important; }

/* Flex布局辅助 */
.flex-row { flex-direction: row !important; }
.flex-column { flex-direction: column !important; }
.flex-row-reverse { flex-direction: row-reverse !important; }
.flex-column-reverse { flex-direction: column-reverse !important; }

.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }
.flex-wrap-reverse { flex-wrap: wrap-reverse !important; }

.justify-content-start { justify-content: flex-start !important; }
.justify-content-end { justify-content: flex-end !important; }
.justify-content-center { justify-content: center !important; }
.justify-content-between { justify-content: space-between !important; }
.justify-content-around { justify-content: space-around !important; }

.align-items-start { align-items: flex-start !important; }
.align-items-end { align-items: flex-end !important; }
.align-items-center { align-items: center !important; }
.align-items-baseline { align-items: baseline !important; }
.align-items-stretch { align-items: stretch !important; }

.align-self-start { align-self: flex-start !important; }
.align-self-end { align-self: flex-end !important; }
.align-self-center { align-self: center !important; }
.align-self-baseline { align-self: baseline !important; }
.align-self-stretch { align-self: stretch !important; }

.flex-grow-0 { flex-grow: 0 !important; }
.flex-grow-1 { flex-grow: 1 !important; }
.flex-shrink-0 { flex-shrink: 0 !important; }
.flex-shrink-1 { flex-shrink: 1 !important; }

/* 位置 */
.position-static { position: static !important; }
.position-relative { position: relative !important; }
.position-absolute { position: absolute !important; }
.position-fixed { position: fixed !important; }
.position-sticky { position: sticky !important; }

/* 边框 */
.border { border: 1px solid #EBEEF5 !important; }
.border-top { border-top: 1px solid #EBEEF5 !important; }
.border-right { border-right: 1px solid #EBEEF5 !important; }
.border-bottom { border-bottom: 1px solid #EBEEF5 !important; }
.border-left { border-left: 1px solid #EBEEF5 !important; }

.border-0 { border: 0 !important; }
.border-top-0 { border-top: 0 !important; }
.border-right-0 { border-right: 0 !important; }
.border-bottom-0 { border-bottom: 0 !important; }
.border-left-0 { border-left: 0 !important; }

/* 圆角 */
.rounded-0 { border-radius: 0 !important; }
.rounded-sm { border-radius: 2px !important; }
.rounded { border-radius: 4px !important; }
.rounded-lg { border-radius: 8px !important; }
.rounded-pill { border-radius: 50rem !important; }
.rounded-circle { border-radius: 50% !important; }

/* 背景色 */
.bg-primary { background-color: #409EFF !important; }
.bg-success { background-color: #67C23A !important; }
.bg-info { background-color: #909399 !important; }
.bg-warning { background-color: #E6A23C !important; }
.bg-danger { background-color: #F56C6C !important; }
.bg-light { background-color: #F8F9FA !important; }
.bg-dark { background-color: #343A40 !important; }
.bg-white { background-color: #FFFFFF !important; }
.bg-transparent { background-color: transparent !important; }

/* 阴影 */
.shadow-none { box-shadow: none !important; }
.shadow-sm { box-shadow: 0 .125rem .25rem rgba(0,0,0,.075) !important; }
.shadow { box-shadow: 0 .5rem 1rem rgba(0,0,0,.15) !important; }
.shadow-lg { box-shadow: 0 1rem 3rem rgba(0,0,0,.175) !important; }

/* 尺寸 */
.w-25 { width: 25% !important; }
.w-50 { width: 50% !important; }
.w-75 { width: 75% !important; }
.w-100 { width: 100% !important; }
.w-auto { width: auto !important; }

.h-25 { height: 25% !important; }
.h-50 { height: 50% !important; }
.h-75 { height: 75% !important; }
.h-100 { height: 100% !important; }
.h-auto { height: auto !important; }

.mw-100 { max-width: 100% !important; }
.mh-100 { max-height: 100% !important; }
.min-vw-100 { min-width: 100vw !important; }
.min-vh-100 { min-height: 100vh !important; }
.vw-100 { width: 100vw !important; }
.vh-100 { height: 100vh !important; }

/* 浮动与清除浮动 */
.float-left { float: left !important; }
.float-right { float: right !important; }
.float-none { float: none !important; }

.clearfix::after {
  display: block;
  clear: both;
  content: "";
}

/* 页面元素可见性 */
.overflow-auto { overflow: auto !important; }
.overflow-hidden { overflow: hidden !important; }

/* 鼠标样式 */
.cursor-default { cursor: default !important; }
.cursor-pointer { cursor: pointer !important; }
.cursor-help { cursor: help !important; }
.cursor-wait { cursor: wait !important; }
.cursor-text { cursor: text !important; }
.cursor-move { cursor: move !important; }
.cursor-not-allowed { cursor: not-allowed !important; }

/* 用户选择 */
.user-select-all { user-select: all !important; }
.user-select-auto { user-select: auto !important; }
.user-select-none { user-select: none !important; }

/* 垂直对齐 */
.align-baseline { vertical-align: baseline !important; }
.align-top { vertical-align: top !important; }
.align-middle { vertical-align: middle !important; }
.align-bottom { vertical-align: bottom !important; }
.align-text-bottom { vertical-align: text-bottom !important; }
.align-text-top { vertical-align: text-top !important; }

/* 文本溢出处理 */
.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}