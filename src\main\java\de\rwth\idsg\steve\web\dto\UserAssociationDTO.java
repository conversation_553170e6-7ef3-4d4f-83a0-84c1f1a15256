/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.web.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 用户关联数据传输对象
 * 用于管理user表和web_user表之间的关联关系
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserAssociationDTO {

    /**
     * web_user表的主键
     */
    private Integer webUserPk;
    
    /**
     * web_user的用户名
     */
    private String webUsername;
    
    /**
     * web_user的角色
     */
    private UserRole webUserRole;
    
    /**
     * web_user是否启用
     */
    private Boolean webUserEnabled;
    
    /**
     * 关联的user表主键（可能为null）
     */
    private Integer associatedUserPk;
    
    /**
     * 关联的user姓名（可能为null）
     */
    private String associatedUserName;
    
    /**
     * 关联的user邮箱（可能为null）
     */
    private String associatedUserEmail;
    
    /**
     * 关联的user电话（可能为null）
     */
    private String associatedUserPhone;
    
    /**
     * 是否已关联user
     */
    public boolean isAssociated() {
        return associatedUserPk != null;
    }
    
    /**
     * Get display association status
     */
    public String getAssociationStatus() {
        return isAssociated() ? "Associated" : "Not Associated";
    }

    /**
     * Get display user information
     */
    public String getDisplayInfo() {
        if (isAssociated()) {
            return String.format("%s (%s)", associatedUserName, associatedUserEmail);
        } else {
            return "No Associated User";
        }
    }
}
