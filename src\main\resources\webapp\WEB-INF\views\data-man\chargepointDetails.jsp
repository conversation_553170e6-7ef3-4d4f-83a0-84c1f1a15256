<%--

    SteV<PERSON> - SteckdosenVerwaltung - https://github.com/steve-community/steve
    Copyright (C) 2013-2025 SteVe Community Team
    All Rights Reserved.

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.

--%>
<%@ include file="../00-header.jsp" %>

<style>
    /* Add custom styles for better presentation if needed */
    .card {
        margin-bottom: 20px;
        border: 1px solid #ddd;
        border-radius: 4px;
    }
    .card-header {
        background-color: #f5f5f5;
        padding: 10px 15px;
        border-bottom: 1px solid #ddd;
        font-size: 1.1em;
        font-weight: bold;
    }
    .card-body {
        padding: 15px;
    }
    .table-details {
        width: 100%;
        table-layout: fixed;
        border-collapse: collapse;
    }
    .table-details td {
        padding: 8px;
        vertical-align: middle;
        box-sizing: border-box;
    }
    .table-details td:first-child {
        font-weight: bold;
    }
    .address-table td:first-child {
        width: 15%; /* Adjust as needed */
    }
    .address-table td:nth-child(2) {
        width: 55%; /* Adjust as needed, leaves some space on the right */
    }
    .tooltip-icon {
        margin-left: 5px;
        cursor: help;
        vertical-align: middle;
    }
</style>

<spring:hasBindErrors name="chargePointForm">
    <div class="alert alert-danger">
        Error while trying to update a charge point:
        <ul>
            <c:forEach var="error" items="${errors.allErrors}">
                <li><c:out value="${error.defaultMessage}" /></li>
            </c:forEach>
        </ul>
    </div>
</spring:hasBindErrors>

<div class="content">
    <div>
        <section>
            <span style="display: inline-flex; align-items: center;">
                Charge Point Details
                <a class="tooltip" href="#" style="display: inline-flex; align-items: center;"><img src="${ctxPath}/static/images/info.png" class="tooltip-icon" alt="Info">
                    <span>Read-only fields are updated by the charge point.</span>
                </a>
            </span>
        </section>

        <div class="card">
            <div class="card-header">Related Data Pages</div>
            <div class="card-body">
                <table class="table table-details">
                    <tbody>
                        <tr>
                            <td>Transactions:</td>
                            <td>
                                <a href="${ctxPath}/manager/transactions/query?chargeBoxId=${chargePointForm.chargeBoxId}&amp;type=ACTIVE" class="btn btn-sm btn-outline-primary">ACTIVE</a>
                                /
                                <a href="${ctxPath}/manager/transactions/query?chargeBoxId=${chargePointForm.chargeBoxId}&amp;type=ALL" class="btn btn-sm btn-outline-primary">ALL</a>
                            </td>
                        </tr>
                        <tr>
                            <td>Reservations:</td>
                            <td>
                                <a href="${ctxPath}/manager/reservations/query?chargeBoxId=${chargePointForm.chargeBoxId}&amp;periodType=ACTIVE" class="btn btn-sm btn-outline-primary">ACTIVE</a>
                            </td>
                        </tr>
                        <tr>
                            <td>Connector Status:</td>
                            <td>
                                <a href="${ctxPath}/manager/home/<USER>/query?chargeBoxId=${chargePointForm.chargeBoxId}" class="btn btn-sm btn-outline-primary">ALL</a>
                            </td>
                        </tr>
                        <tr>
                            <td>Charging Profiles:</td>
                            <td>
                                <a href="${ctxPath}/manager/chargingProfiles/assignments?chargeBoxId=${chargePointForm.chargeBoxId}" class="btn btn-sm btn-outline-primary">ALL</a>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <form:form action="${ctxPath}/manager/chargepoints/update" modelAttribute="chargePointForm">
            <form:hidden path="chargeBoxPk" readonly="true"/>

            <div class="card">
                <div class="card-header">OCPP Information</div>
                <div class="card-body">
                    <table class="table table-details">
                        <tbody>
                            <tr>
                                <td>ChargeBox ID:</td>
                                <td>
                                    <div style="display: flex; align-items: center; position: relative;">
                                        <form:input path="chargeBoxId" readonly="true" cssClass="form-control-plaintext" style="margin-right: 5px;" />
                                        <a class="tooltip" href="#" style="position: relative;">
                                            <img src="${ctxPath}/static/images/info.png" class="tooltip-icon" alt="Info" />
                                            <span>This field is set when adding a charge point, and cannot be changed later.</span>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            <tr><td>Endpoint Address:</td><td><encode:forHtml value="${cp.chargeBox.endpointAddress}" /></td></tr>
                            <tr><td>Ocpp Protocol:</td><td><encode:forHtml value="${cp.chargeBox.ocppProtocol}" /></td></tr>
                            <tr><td>Charge Point Vendor:</td><td><encode:forHtml value="${cp.chargeBox.chargePointVendor}" /></td></tr>
                            <tr><td>Charge Point Model:</td><td><encode:forHtml value="${cp.chargeBox.chargePointModel}" /></td></tr>
                            <tr><td>Charge Point Serial Number:</td><td><encode:forHtml value="${cp.chargeBox.chargePointSerialNumber}" /></td></tr>
                            <tr><td>Charge Box Serial Number:</td><td><encode:forHtml value="${cp.chargeBox.chargeBoxSerialNumber}" /></td></tr>
                            <tr><td>Firmware Version:</td><td><encode:forHtml value="${cp.chargeBox.fwVersion}" /></td></tr>
                            <tr><td>Firmware Update Timestamp:</td><td><encode:forHtml value="${cp.chargeBox.fwUpdateTimestamp}" /></td></tr>
                            <tr><td>Iccid:</td><td><encode:forHtml value="${cp.chargeBox.iccid}" /></td></tr>
                            <tr><td>Imsi:</td><td><encode:forHtml value="${cp.chargeBox.imsi}" /></td></tr>
                            <tr><td>Meter Type:</td><td><encode:forHtml value="${cp.chargeBox.meterType}" /></td></tr>
                            <tr><td>Meter Serial Number:</td><td><encode:forHtml value="${cp.chargeBox.meterSerialNumber}" /></td></tr>
                            <tr><td>Diagnostics Status:</td><td><encode:forHtml value="${cp.chargeBox.diagnosticsStatus}" /></td></tr>
                            <tr><td>Diagnostics Timestamp:</td><td><encode:forHtml value="${cp.chargeBox.diagnosticsTimestamp}" /></td></tr>
                            <tr><td>Last Heartbeat Timestamp:</td><td><encode:forHtml value="${cp.chargeBox.lastHeartbeatTimestamp}" /></td></tr>
                            <tr>
                                <td>Insert connector status after start/stop transaction:</td>
                                <td style="display: flex; align-items: center;">
                                    <div class="form-check" style="margin-right: 5px;">
                                        <form:checkbox path="insertConnectorStatusAfterTransactionMsg" cssClass="form-check-input"/>
                                    </div>
                                    <a class="tooltip" href="#"><img src="${ctxPath}/static/images/info.png" class="tooltip-icon" alt="Info">
                                        <span>After a transaction start/stop message, a charging station might send a connector status notification, but it is not required. If this is enabled, SteVe will update the connector status no matter what.</span>
                                    </a>
                                </td>
                            </tr>
                            <tr>
                                <td>Registration status:</td>
                                <td>
                                    <form:select path="registrationStatus" items="${registrationStatusList}" cssClass="form-control"/>
                                </td>
                            </tr>
                            <tr>
                                <td>Charging Station:</td>
                                <td>
                                    <div style="display: flex; align-items: center; gap: 8px;">
                                        <a class="tooltip" href="#" style="position: relative;">
                                            <img src="${ctxPath}/static/images/info.png" class="tooltip-icon" alt="Info" />
                                            <span>Select the charging station this charge point belongs to (optional)</span>
                                        </a>
                                        <form:select path="chargingStationPk" cssClass="form-control" style="flex: 1;">
                                            <form:option value="" label="-- Select Charging Station (Optional) --"/>
                                            <c:forEach items="${chargeStationList}" var="station">
                                                <form:option value="${station.chargingStationPk}" label="${station.displayName}"/>
                                            </c:forEach>
                                        </form:select>
                                    </div>
                                </td>
                            </tr>
                            <c:if test="${not empty ownerList}">
                            <tr>
                                <td>Owner:</td>
                                <td>
                                    <div style="display: flex; align-items: center; gap: 8px;">
                                        <a class="tooltip" href="#" style="position: relative;">
                                            <img src="${ctxPath}/static/images/info.png" class="tooltip-icon" alt="Info" />
                                            <span>Select the owner for this charge point (optional)</span>
                                        </a>
                                        <form:select path="ownerUserPk" cssClass="form-control" style="flex: 1;">
                                            <form:option value="" label="-- Select Owner (Optional) --"/>
                                            <c:forEach items="${ownerList}" var="owner">
                                                <form:option value="${owner.userPk}" label="${owner.username}"/>
                                            </c:forEach>
                                        </form:select>
                                    </div>
                                </td>
                            </tr>
                            </c:if>
                        </tbody>
                    </table>
                </div>
            </div>

            <form:hidden path="address.addressPk" readonly="true"/>
            <div class="card">
                <div class="card-header">Address Information</div>
                <div class="card-body">
                    <%@ include file="00-address.jsp" %>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">Miscellaneous</div>
                 <div class="card-body">
                    <c:set var="submitButtonName" value="update" />
                    <c:set var="submitButtonValue" value="Update" />
                    <%@ include file="00-cp-misc.jsp" %>
                </div>
            </div>
        </form:form>
    </div>
</div>
<%@ include file="../00-footer.jsp" %>
