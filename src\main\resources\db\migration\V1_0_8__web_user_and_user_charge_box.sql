-- Add user_role field to web_user table
ALTER TABLE web_user ADD COLUMN user_role ENUM('ADMIN', 'OPERATOR_FACTORY', 'OPERATOR_OWNER') NOT NULL DEFAULT 'ADMIN';

-- Create user_charge_box table for associating users with charge boxes
CREATE TABLE user_charge_box (
    user_charge_box_pk INT NOT NULL AUTO_INCREMENT,
    web_user_pk INT NOT NULL,
    charge_box_pk INT NOT NULL,
    PRIMARY KEY (user_charge_box_pk),
    UNIQUE KEY unique_user_charge_box (web_user_pk, charge_box_pk),
    FOREIGN KEY (web_user_pk) REFERENCES web_user (web_user_pk) ON DELETE CASCADE,
    FOREIGN KEY (charge_box_pk) REFERENCES charge_box (charge_box_pk) ON DELETE CASCADE
);

-- Add index for faster lookups by user or charge box
CREATE INDEX idx_user_charge_box_web_user_pk ON user_charge_box (web_user_pk);
CREATE INDEX idx_user_charge_box_charge_box_pk ON user_charge_box (charge_box_pk);

-- Update existing admin users to have the ADMIN role
UPDATE web_user SET user_role = 'ADMIN' WHERE JSON_CONTAINS(authorities, '"ROLE_ADMIN"', '$');