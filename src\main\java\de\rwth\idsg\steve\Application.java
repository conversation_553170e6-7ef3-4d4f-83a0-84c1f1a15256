/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve;

import de.rwth.idsg.steve.ftp.EmbeddedFtpServer;
import de.rwth.idsg.steve.service.DiagnosticFileDownloadService;
import de.rwth.idsg.steve.service.DiagnosticsProgressTracker;
import de.rwth.idsg.steve.service.ChargePointIdentityMappingService;
import de.rwth.idsg.steve.utils.LogFileRetriever;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.springframework.context.ApplicationContext;

import java.nio.file.Path;
import java.util.Optional;
import java.util.TimeZone;

/**
 * <AUTHOR> Goekay <<EMAIL>>
 * @since 14.01.2015
 */
@Slf4j
public class Application {

    private final JettyServer server = new JettyServer();
    private EmbeddedFtpServer ftpServer;

    public static void main(String[] args) throws Exception {
        // 设置默认编码为UTF-8
        System.setProperty("file.encoding", "UTF-8");
        java.nio.charset.Charset.defaultCharset();
        
        // For Hibernate validator
        System.setProperty("org.jboss.logging.provider", "slf4j");

        SteveConfiguration sc = SteveConfiguration.CONFIG;
        log.info("Loaded the properties. Starting with the '{}' profile", sc.getProfile());
        log.info("FTP Server Enabled (from config): {}", sc.isFtpEnabled());

        TimeZone.setDefault(TimeZone.getTimeZone(sc.getTimeZoneId()));
        DateTimeZone.setDefault(DateTimeZone.forID(sc.getTimeZoneId()));
        log.info("Date/time zone of the application is set to {}. Current date/time: {}", sc.getTimeZoneId(), DateTime.now());

        Optional<Path> path = LogFileRetriever.INSTANCE.getPath();
        boolean loggingToFile = path.isPresent();
        if (loggingToFile) {
            System.out.println("Log file: " + path.get().toAbsolutePath());
        }

        Application app = new Application();

        try {
            app.start();
            app.join();
        } catch (Exception e) {
            log.error("Application failed to start", e);

            if (loggingToFile) {
                System.err.println("Application failed to start");
                e.printStackTrace();
            }

            app.stop();
        }
    }

    public void start() throws Exception {
        // Start Jetty server first to initialize Spring context
        server.start();

        // Now configure FTP server with Spring beans
        if (SteveConfiguration.CONFIG.isFtpEnabled()) {
            ftpServer = new EmbeddedFtpServer(SteveConfiguration.CONFIG);

            // Get Spring context from JettyServer
            ApplicationContext springContext = server.getSpringContext();
            if (springContext != null) {
                // Inject Spring beans into FTP server
                try {
                    DiagnosticsProgressTracker progressTracker = springContext.getBean(DiagnosticsProgressTracker.class);
                    DiagnosticFileDownloadService downloadService = springContext.getBean(DiagnosticFileDownloadService.class);
                    ChargePointIdentityMappingService identityMappingService = springContext.getBean(ChargePointIdentityMappingService.class);

                    ftpServer.setProgressTracker(progressTracker);
                    ftpServer.setDownloadService(downloadService);
                    ftpServer.setIdentityMappingService(identityMappingService);

                    log.info("Successfully injected Spring beans into FTP server (including identity mapping service)");
                } catch (Exception e) {
                    log.warn("Failed to inject Spring beans into FTP server: {}", e.getMessage());
                }
            } else {
                log.warn("Spring context not available, FTP server will run without Spring bean integration");
            }

            ftpServer.start();
        } else {
            log.info("Embedded FTP server is disabled in configuration, not starting it.");
        }
    }

    public void join() throws Exception {
        server.join();
    }

    public void stop() throws Exception {
        if (ftpServer != null) {
            ftpServer.stop();
        }
        if (server != null && server.isStarted()) {
            server.stop();
        }
    }
}
