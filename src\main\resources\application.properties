# Multipart 配置
spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=50MB
spring.servlet.multipart.max-request-size=50MB
spring.servlet.multipart.file-size-threshold=0

# Firmware Upload Configuration
# !! 重要 !! 下面的路径必须是FTP服务器上 /firmware/ 目录对应的实际物理磁盘路径
# FTP服务器的home目录将是这个路径的父目录
# 例如: 如果设置为 ftp_files/firmware/, 那么FTP home是 ftp_files/
# 这样FTP客户端可以访问 /firmware/ 和 /logs/ 目录
steve.firmware.physical-upload-path=ftp_files/firmware/

# HTTP访问路径 (FTP启用后，这个将不再用于生成给充电桩的URL)
steve.firmware.http-access-path=/firmware_files/

# New FTP Configuration
steve.firmware.ftp.enabled=true
steve.firmware.ftp.username=user
steve.firmware.ftp.password=123
steve.firmware.ftp.ip=************
steve.firmware.ftp.port=9988
steve.firmware.ftp.base.path=/firmware/
steve.firmware.ftp.log-path=ftp_files/logs/ftp-server.log
steve.firmware.ftp.passive.external-address=************
steve.firmware.ftp.passive.ports=50000-51000

# 故障图片上传路径
steve.issue.image.upload-path=uploads/issue-images

# 时区设置 - 中国时区
spring.jackson.time-zone=Asia/Shanghai
spring.jpa.properties.hibernate.jdbc.time_zone=Asia/Shanghai
user.timezone=Asia/Shanghai