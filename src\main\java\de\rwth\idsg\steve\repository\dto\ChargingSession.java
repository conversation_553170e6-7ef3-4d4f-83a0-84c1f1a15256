package de.rwth.idsg.steve.repository.dto;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import org.joda.time.DateTime;

/**
 * 充电会话数据传输对象
 * 
 * <AUTHOR> Assistant
 */
@Getter
@Builder
public class ChargingSession {
    
    private final Integer sessionId;
    private final String chargeBoxId;
    private final Integer connectorId;
    private final Integer transactionId;
    private final DateTime sessionStartTime;
    private final DateTime sessionEndTime;
    
    @Setter
    private ChargingSessionStatus status;
    
    @Setter
    private boolean successful;
    
    @Setter
    private boolean hasPreparing;
    
    @Setter
    private boolean hasCharging;
    
    @Setter
    private boolean hasValidMeterValues;
    
    @Setter
    private boolean hasFinishing;
    
    @Setter
    private int meterValueCount;
    
    @Setter
    private int validMeterValueCount;
    
    private final DateTime createdAt;
    private final DateTime updatedAt;
    
    /**
     * 充电会话状态枚举
     */
    public enum ChargingSessionStatus {
        PREPARING,
        CHARGING,
        FINISHING,
        COMPLETED,
        FAILED
    }
    
    /**
     * 检查充电会话是否满足成功条件
     * 成功条件：有preparing -> charging -> 有效MeterValues -> finishing
     */
    public boolean isSuccessful() {
        return hasPreparing && hasCharging && hasValidMeterValues && hasFinishing;
    }
    
    /**
     * 更新会话状态并检查是否完成
     */
    public void updateStatus(ChargingSessionStatus newStatus) {
        this.status = newStatus;
        
        switch (newStatus) {
            case PREPARING:
                this.hasPreparing = true;
                break;
            case CHARGING:
                this.hasCharging = true;
                break;
            case FINISHING:
                this.hasFinishing = true;
                break;
            case COMPLETED:
            case FAILED:
                this.successful = isSuccessful();
                break;
        }
    }
    
    /**
     * 增加MeterValues计数
     */
    public void incrementMeterValueCount(boolean isValid) {
        this.meterValueCount++;
        if (isValid) {
            this.validMeterValueCount++;
            this.hasValidMeterValues = true;
        }
    }
}
