/*
 * SteV<PERSON> - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.repository.impl;

import de.rwth.idsg.steve.repository.UserChargeBoxRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.ArrayList;

import static jooq.steve.db.Tables.CHARGE_BOX;
import static jooq.steve.db.Tables.USER_CHARGE_BOX;

/**
 * Implementation of UserChargeBoxRepository
 *
 * @since 1.0.8
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class UserChargeBoxRepositoryImpl implements UserChargeBoxRepository {

    private final DSLContext ctx;

    @Override
    public void assignChargeBoxToUser(int webUserPk, int chargeBoxPk) {
        // Use insert ignore to avoid duplicates
        ctx.insertInto(USER_CHARGE_BOX)
            .set(USER_CHARGE_BOX.WEB_USER_PK, webUserPk)
            .set(USER_CHARGE_BOX.CHARGE_BOX_PK, chargeBoxPk)
            .onDuplicateKeyIgnore()
            .execute();
    }

    @Override
    public void removeChargeBoxFromUser(int webUserPk, int chargeBoxPk) {
        ctx.deleteFrom(USER_CHARGE_BOX)
            .where(USER_CHARGE_BOX.WEB_USER_PK.eq(webUserPk))
            .and(USER_CHARGE_BOX.CHARGE_BOX_PK.eq(chargeBoxPk))
            .execute();
    }

    @Override
    public void removeAllChargeBoxesFromUser(int webUserPk) {
        ctx.deleteFrom(USER_CHARGE_BOX)
            .where(USER_CHARGE_BOX.WEB_USER_PK.eq(webUserPk))
            .execute();
    }

    @Override
    public void removeAllUsersFromChargeBox(int chargeBoxPk) {
        ctx.deleteFrom(USER_CHARGE_BOX)
            .where(USER_CHARGE_BOX.CHARGE_BOX_PK.eq(chargeBoxPk))
            .execute();
    }

    @Override
    public List<Integer> getChargeBoxPksByUser(int webUserPk) {
        return ctx.select(USER_CHARGE_BOX.CHARGE_BOX_PK)
            .from(USER_CHARGE_BOX)
            .where(USER_CHARGE_BOX.WEB_USER_PK.eq(webUserPk))
            .fetch(USER_CHARGE_BOX.CHARGE_BOX_PK);
    }

    @Override
    public List<String> getChargeBoxIdsByUser(int webUserPk) {
        try {
            log.debug("Fetching charge box IDs for user with PK: {}", webUserPk);
            
            if (webUserPk <= 0) {
                log.warn("Invalid user PK: {}", webUserPk);
                return new ArrayList<>();
            }
            
            // 简化查询，直接获取充电桩ID
            List<String> chargeBoxIds = ctx.select(CHARGE_BOX.CHARGE_BOX_ID)
                .from(USER_CHARGE_BOX)
                .join(CHARGE_BOX).on(CHARGE_BOX.CHARGE_BOX_PK.eq(USER_CHARGE_BOX.CHARGE_BOX_PK))
                .where(USER_CHARGE_BOX.WEB_USER_PK.eq(webUserPk))
                .fetch(CHARGE_BOX.CHARGE_BOX_ID);
            
            log.debug("Found {} charge box IDs for user {}", 
                     chargeBoxIds.size(), webUserPk);
            
            if (!chargeBoxIds.isEmpty()) {
                log.debug("Charge box IDs: {}", String.join(", ", chargeBoxIds));
            }
            
            return chargeBoxIds;
        } catch (Exception e) {
            log.error("Error fetching charge box IDs for user {}: {}", webUserPk, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<Integer> getUserPksByChargeBox(int chargeBoxPk) {
        return ctx.select(USER_CHARGE_BOX.WEB_USER_PK)
            .from(USER_CHARGE_BOX)
            .where(USER_CHARGE_BOX.CHARGE_BOX_PK.eq(chargeBoxPk))
            .fetch(USER_CHARGE_BOX.WEB_USER_PK);
    }

    @Override
    public boolean isChargeBoxAssignedToUser(int webUserPk, int chargeBoxPk) {
        return ctx.fetchExists(
            ctx.selectOne()
                .from(USER_CHARGE_BOX)
                .where(USER_CHARGE_BOX.WEB_USER_PK.eq(webUserPk))
                .and(USER_CHARGE_BOX.CHARGE_BOX_PK.eq(chargeBoxPk))
        );
    }

    @Override
    public boolean isChargeBoxAssignedToUser(int webUserPk, String chargeBoxId) {
        return ctx.fetchExists(
            ctx.selectOne()
                .from(USER_CHARGE_BOX)
                .join(CHARGE_BOX).on(CHARGE_BOX.CHARGE_BOX_PK.eq(USER_CHARGE_BOX.CHARGE_BOX_PK))
                .where(USER_CHARGE_BOX.WEB_USER_PK.eq(webUserPk))
                .and(CHARGE_BOX.CHARGE_BOX_ID.eq(chargeBoxId))
        );
    }

    @Override
    public boolean isChargeBoxAssignedToUserByPk(int webUserPk, int chargeBoxPk) {
        log.info("=== isChargeBoxAssignedToUserByPk Debug ===");
        log.info("Checking assignment: webUserPk={}, chargeBoxPk={}", webUserPk, chargeBoxPk);

        boolean exists = ctx.fetchExists(
            ctx.selectOne()
                .from(USER_CHARGE_BOX)
                .where(USER_CHARGE_BOX.WEB_USER_PK.eq(webUserPk))
                .and(USER_CHARGE_BOX.CHARGE_BOX_PK.eq(chargeBoxPk))
        );

        log.info("Assignment exists: {}", exists);

        // 额外调试：查看用户的所有分配
        var assignments = ctx.select(USER_CHARGE_BOX.CHARGE_BOX_PK)
                             .from(USER_CHARGE_BOX)
                             .where(USER_CHARGE_BOX.WEB_USER_PK.eq(webUserPk))
                             .fetch();

        log.info("User {} has {} charge box assignments: {}",
                 webUserPk, assignments.size(), assignments.stream().map(r -> r.value1()).toList());

        return exists;
    }

    @Override
    public List<String> getAllAssignedChargeBoxIds() {
        try {
            log.debug("Fetching all assigned charge box IDs");
            
            // 获取所有已分配的充电桩ID
            List<String> chargeBoxIds = ctx.selectDistinct(CHARGE_BOX.CHARGE_BOX_ID)
                .from(USER_CHARGE_BOX)
                .join(CHARGE_BOX).on(CHARGE_BOX.CHARGE_BOX_PK.eq(USER_CHARGE_BOX.CHARGE_BOX_PK))
                .fetch(CHARGE_BOX.CHARGE_BOX_ID);
            
            log.debug("Found {} charge box IDs that are assigned to users", chargeBoxIds.size());
            
            if (!chargeBoxIds.isEmpty() && chargeBoxIds.size() < 10) {
                log.debug("Assigned charge box IDs: {}", String.join(", ", chargeBoxIds));
            }
            
            return chargeBoxIds;
        } catch (Exception e) {
            log.error("Error fetching all assigned charge box IDs: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }
}