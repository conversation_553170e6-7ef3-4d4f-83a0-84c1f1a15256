package de.rwth.idsg.steve.repository;

import de.rwth.idsg.steve.repository.dto.ChargingSession;
import de.rwth.idsg.steve.repository.dto.ChargingSuccessStats;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 充电成功率统计数据访问接口
 * 
 * <AUTHOR> Assistant
 */
public interface ChargingSuccessRepository {
    
    // -------------------------------------------------------------------------
    // 充电会话管理
    // -------------------------------------------------------------------------
    
    /**
     * 创建新的充电会话
     */
    int createChargingSession(String chargeBoxId, int connectorId, Integer transactionId);
    
    /**
     * 根据充电桩ID和连接器ID获取活跃的充电会话
     */
    Optional<ChargingSession> getActiveSession(String chargeBoxId, int connectorId);
    
    /**
     * 根据事务ID获取充电会话
     */
    Optional<ChargingSession> getSessionByTransactionId(Integer transactionId);
    
    /**
     * 更新充电会话状态
     */
    void updateSessionStatus(int sessionId, ChargingSession.ChargingSessionStatus status);
    
    /**
     * 更新充电会话的MeterValues计数
     */
    void updateSessionMeterValues(int sessionId, int meterValueCount, int validMeterValueCount, boolean hasValidMeterValues);
    
    /**
     * 完成充电会话
     */
    void completeSession(int sessionId, boolean isSuccessful);
    
    // -------------------------------------------------------------------------
    // 充电成功率统计
    // -------------------------------------------------------------------------
    
    /**
     * 获取所有充电桩的成功率统计
     */
    Map<String, ChargingSuccessStats> getAllSuccessStats();

    /**
     * 获取指定用户拥有的充电桩的成功率统计
     *
     * @param webUserPk 用户主键
     * @return 充电桩ID到成功率统计的映射
     */
    Map<String, ChargingSuccessStats> getSuccessStatsByUser(Integer webUserPk);

    /**
     * 获取指定充电桩的成功率统计
     */
    Optional<ChargingSuccessStats> getSuccessStats(String chargeBoxId);
    
    /**
     * 更新充电桩的成功率统计
     */
    void updateSuccessStats(String chargeBoxId, int totalSessions, int successfulSessions);
    
    /**
     * 初始化充电桩的成功率统计（如果不存在）
     */
    void initializeSuccessStats(String chargeBoxId);
    
    /**
     * 重新计算所有充电桩的成功率统计
     */
    void recalculateAllSuccessStats();
    
    /**
     * 重新计算指定充电桩的成功率统计
     */
    void recalculateSuccessStats(String chargeBoxId);
}
