<%--

    SteV<PERSON> - SteckdosenVerwaltung - https://github.com/steve-community/steve
    Copyright (C) 2013-2025 SteVe Community Team
    All Rights Reserved.

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.

--%>
<%@ taglib uri="jakarta.tags.core" prefix="c" %>
<%--
<table class="userInput">
	<tr>
		<td style="vertical-align:top">
            <input type="button" value="Select All" onClick="selectAll(document.getElementById('chargePointSelectList'))"><input type="button" value="Select None" onClick="selectNone(document.getElementById('chargePointSelectList'))">
        </td>
		<td>
			<form:select path="chargePointSelectList" size="5" multiple="true">
				<c:forEach items="${cpList}" var="cp">
					<form:option value="${cp.ocppProtocol};${cp.chargeBoxId};${cp.endpointAddress}" label="${cp.chargeBoxId}"/>
				</c:forEach>
			</form:select>
		</td>
	</tr>
</table>
--%>

<div class="multi-select-container">
    <div class="selected-options" tabindex="0">Click to select Charge Points</div>
    <div class="options-dropdown" style="display: none;">
        <input type="text" class="search-input" placeholder="Search Charge Points...">
        <div>
            <input type="checkbox" id="select-all-options" class="select-all-checkbox">
            <label for="select-all-options">Select All / None</label>
        </div>
        <ul class="options-list">
            <c:forEach items="${cpList}" var="cp">
                <c:if test="${cp.ocppProtocol != null}">
                    <li>
                        <input type="checkbox" id="cp-${cp.chargeBoxId}" name="selectedChargePoints" value="${cp.ocppProtocol};${cp.chargeBoxId};${cp.endpointAddress}">
                        <label for="cp-${cp.chargeBoxId}">${cp.chargeBoxId}</label>
                    </li>
                </c:if>
            </c:forEach>
        </ul>
    </div>
    <%-- Keep the original select hidden to store values for form submission --%>
    <form:select path="chargePointSelectList" multiple="true" style="display:none;">
        <c:forEach items="${cpList}" var="cp">
            <c:if test="${cp.ocppProtocol != null}">
                <form:option value="${cp.ocppProtocol};${cp.chargeBoxId};${cp.endpointAddress}" label="${cp.chargeBoxId}"/>
            </c:if>
        </c:forEach>
    </form:select>
</div>

<style>
.multi-select-container {
    position: relative;
    width: 100%; /* Adjust width as needed */
    border: 1px solid #ccc;
    border-radius: 4px;
}

.selected-options {
    padding: 10px;
    cursor: pointer;
    min-height: 20px; /* Ensure it has some height even when empty */
    line-height: 1.5;
}

.options-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    border: 1px solid #ccc;
    border-top: none;
    background-color: white;
    z-index: 9999;
    max-height: 200px; /* Limit height and make it scrollable */
    overflow-y: auto;
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.search-input {
    width: calc(100% - 20px); /* Full width minus padding */
    padding: 8px 10px;
    border: 1px solid #eee;
    border-bottom: 1px solid #ccc;
    box-sizing: border-box; /* Include padding and border in the element's total width and height */
}

.options-dropdown div {
    padding: 8px 10px;
    border-bottom: 1px solid #eee;
}

.options-dropdown div label {
    margin-left: 5px;
}

.options-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.options-list li {
    padding: 8px 10px;
    cursor: pointer;
}

.options-list li:hover {
    background-color: #f0f0f0;
}

.options-list li input[type="checkbox"] {
    margin-right: 8px;
}

.options-list li label {
    cursor: pointer;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const container = document.querySelector('.multi-select-container');
    if (!container) {
        console.error('FATAL: Could not find the multi-select-container element.');
        return;
    }

    const selectedOptionsDisplay = container.querySelector('.selected-options');
    const dropdown = container.querySelector('.options-dropdown');
    const searchInput = container.querySelector('.search-input');
    const optionsList = container.querySelector('.options-list');
    const selectAllCheckbox = container.querySelector('#select-all-options');
    // Use name attribute for a more reliable selection of the Spring Form generated select
    const hiddenSelect = document.querySelector('select[name="chargePointSelectList"]') ||
                         document.querySelector('form select[name="chargePointSelectList"]') ||
                         document.querySelector('form.ocpp-operation-form select[name="chargePointSelectList"]');

    if (!hiddenSelect) {
        console.error('FATAL: Could not find the hidden select element for charge points. Form submission will likely fail.');
        return; // Stop execution if the critical element is not found
    }

    if (!selectedOptionsDisplay || !dropdown || !optionsList) {
        console.error('FATAL: Could not find required elements for charge point selection.');
        return;
    }
    
    // Toggle dropdown visibility
    selectedOptionsDisplay.addEventListener('click', function() {
        dropdown.style.display = dropdown.style.display === 'none' ? 'block' : 'none';
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', function(event) {
        if (!container.contains(event.target)) {
            dropdown.style.display = 'none';
        }
    });

    // Prevent dropdown from closing when clicking inside it (except on selectedOptionsDisplay)
    dropdown.addEventListener('click', function(event) {
        event.stopPropagation();
    });
    
    // Populate hidden select and update display
    function updateSelection() {
        const selectedValues = [];
        const displayTexts = [];
        optionsList.querySelectorAll('input[type="checkbox"]:checked').forEach(function(checkbox) {
            selectedValues.push(checkbox.value);
            // Find the label associated with this checkbox to get the display text
            const labelElement = checkbox.parentElement.querySelector('label[for="' + checkbox.id + '"]');
            if (labelElement) {
                 displayTexts.push(labelElement.textContent);
            }
        });

        // Update hidden select - this is crucial for form submission
        // Ensure all options are first unselected, then select the new ones.
        // This is important for 'multiple' selects.
        Array.from(hiddenSelect.options).forEach(function(option) {
            option.selected = false;
        });
        selectedValues.forEach(function(value) {
            const optionToSelect = Array.from(hiddenSelect.options).find(opt => opt.value === value);
            if (optionToSelect) {
                optionToSelect.selected = true;
            }
        });
        
        // It's generally good practice to dispatch a change event for other scripts or frameworks.
        // However, for standard form submission, the 'selected' property itself is what matters.
        var event = new Event('change', { bubbles: true });
        hiddenSelect.dispatchEvent(event);

        if (displayTexts.length > 0) {
            selectedOptionsDisplay.textContent = displayTexts.join(', ');
        } else {
            selectedOptionsDisplay.textContent = 'Click to select Charge Points';
        }
    }

    // Event listener for individual checkboxes
    optionsList.querySelectorAll('input[type="checkbox"]').forEach(function(checkbox) {
        checkbox.addEventListener('change', updateSelection);
    });

    // Event listener for "Select All / None" checkbox
    selectAllCheckbox.addEventListener('change', function() {
        const isChecked = this.checked;
        optionsList.querySelectorAll('li').forEach(function(item) {
            // Only change visible checkboxes (respecting search filter)
            if (item.style.display !== 'none') {
                item.querySelector('input[type="checkbox"]').checked = isChecked;
            }
        });
        updateSelection();
    });

    // Event listener for search input
    searchInput.addEventListener('input', function() {
        const filter = this.value.toLowerCase();
        let allVisibleAndChecked = true;
        let anyVisible = false;

        optionsList.querySelectorAll('li').forEach(function(item) {
            const label = item.querySelector('label').textContent.toLowerCase();
            if (label.includes(filter)) {
                item.style.display = '';
                anyVisible = true;
                if (!item.querySelector('input[type="checkbox"]').checked) {
                    allVisibleAndChecked = false;
                }
            } else {
                item.style.display = 'none';
            }
        });
        
        // Update "Select All" checkbox state based on filtered items
        if (anyVisible) {
            selectAllCheckbox.checked = allVisibleAndChecked;
        } else {
             // If no items are visible, uncheck "Select All" (or keep its current state if desired)
            selectAllCheckbox.checked = false; 
        }
    });
    
    // Initial update in case of pre-selected values (e.g., on form validation error and page reload)
    // This ensures the display text and hidden select are synchronized with the actual checkboxes state.
    function initializeSelection() {
        // Ensure hiddenSelect is available
        if (!hiddenSelect) return; 

        const selectedValuesFromHiddenSelect = Array.from(hiddenSelect.selectedOptions).map(opt => opt.value);
        let allVisibleAndChecked = true;
        let anyVisible = false; 

        optionsList.querySelectorAll('li').forEach(function(item) {
            const checkbox = item.querySelector('input[type="checkbox"]');
            if (selectedValuesFromHiddenSelect.includes(checkbox.value)) {
                checkbox.checked = true;
            }
            // Initial check for selectAll state based on visible items
            if (item.style.display !== 'none') { // Assuming all are visible initially or search hasn't run
                anyVisible = true;
                if (!checkbox.checked) {
                    allVisibleAndChecked = false;
                }
            }
        });
        if(anyVisible){
            selectAllCheckbox.checked = allVisibleAndChecked;
        }
        updateSelection(); // Call to update the display text and ensure consistency
    }

    initializeSelection();
});
</script>
<br>
