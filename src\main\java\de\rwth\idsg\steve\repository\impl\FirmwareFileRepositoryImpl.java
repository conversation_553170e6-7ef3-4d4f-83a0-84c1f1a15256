/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.repository.impl;

import de.rwth.idsg.steve.repository.FirmwareFileRepository;
import de.rwth.idsg.steve.repository.dto.FirmwareFile;
import de.rwth.idsg.steve.utils.DateTimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.jooq.DSLContext;
import org.jooq.Record;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

import static jooq.steve.db.tables.FirmwareFile.FIRMWARE_FILE;
import jooq.steve.db.enums.FirmwareFileFileType;
import jooq.steve.db.enums.FirmwareFileUploadStatus;

/**
 * 固件文件管理Repository实现类
 */
@Slf4j
@Repository
public class FirmwareFileRepositoryImpl implements FirmwareFileRepository {

    @Autowired
    private DSLContext ctx;

    @Override
    public List<FirmwareFile> getSuccessfulFirmwareFiles() {
        return ctx.selectFrom(FIRMWARE_FILE)
                 .where(FIRMWARE_FILE.UPLOAD_STATUS.eq(FirmwareFileUploadStatus.SUCCESS))
                 .orderBy(FIRMWARE_FILE.UPLOAD_TIME.desc())
                 .fetch(this::mapToFirmwareFile);
    }

    @Override
    public List<FirmwareFile> getAllFirmwareFiles() {
        return ctx.select()
                 .from(FIRMWARE_FILE)
                 .orderBy(FIRMWARE_FILE.UPLOAD_TIME.desc())
                 .fetch(this::mapToFirmwareFile);
    }

    @Override
    public List<String> getAllFirmwareFilenames() {
        return ctx.select(FIRMWARE_FILE.FILENAME)
                 .from(FIRMWARE_FILE)
                 .orderBy(FIRMWARE_FILE.UPLOAD_TIME.desc())
                 .fetch(FIRMWARE_FILE.FILENAME);
    }

    @Override
    public List<String> getSuccessfulFirmwareFilenames() {
        return ctx.select(FIRMWARE_FILE.FILENAME)
                 .from(FIRMWARE_FILE)
                 .where(FIRMWARE_FILE.UPLOAD_STATUS.eq(FirmwareFileUploadStatus.SUCCESS))
                 .orderBy(FIRMWARE_FILE.UPLOAD_TIME.desc())
                 .fetch(FIRMWARE_FILE.FILENAME);
    }

    @Override
    public FirmwareFile getFirmwareFileByFilename(String filename) {
        Record record = ctx.selectFrom(FIRMWARE_FILE)
                          .where(FIRMWARE_FILE.FILENAME.eq(filename))
                          .fetchOne();
        
        return record != null ? mapToFirmwareFile(record) : null;
    }

    @Override
    public Integer addFirmwareFile(FirmwareFile firmwareFile) {
        FirmwareFileFileType jooqFileType = convertToJooqFileType(firmwareFile.getFileType());
        FirmwareFileUploadStatus jooqUploadStatus = convertToJooqUploadStatus(firmwareFile.getUploadStatus());

        return ctx.insertInto(FIRMWARE_FILE)
                 .set(FIRMWARE_FILE.FILENAME, firmwareFile.getFilename())
                 .set(FIRMWARE_FILE.FILE_TYPE, jooqFileType)
                 .set(FIRMWARE_FILE.UPLOAD_TIME, firmwareFile.getUploadTime())
                 .set(FIRMWARE_FILE.UPLOAD_STATUS, jooqUploadStatus)
                 .set(FIRMWARE_FILE.UPLOADED_BY_USER_PK, firmwareFile.getUploadedByUserPk())
                 .set(FIRMWARE_FILE.FILE_SIZE, firmwareFile.getFileSize())
                 .set(FIRMWARE_FILE.DESCRIPTION, firmwareFile.getDescription())
                 .returning(FIRMWARE_FILE.FIRMWARE_FILE_PK)
                 .fetchOne()
                 .getFirmwareFilePk();
    }

    @Override
    public int updateFirmwareFileStatus(String filename, FirmwareFile.UploadStatus status) {
        FirmwareFileUploadStatus jooqStatus = convertToJooqUploadStatus(status);
        return ctx.update(FIRMWARE_FILE)
                 .set(FIRMWARE_FILE.UPLOAD_STATUS, jooqStatus)
                 .where(FIRMWARE_FILE.FILENAME.eq(filename))
                 .execute();
    }

    @Override
    public int updateUploadStatus(String filename, FirmwareFile.UploadStatus fromStatus,
                                 FirmwareFile.UploadStatus toStatus, String description) {
        FirmwareFileUploadStatus jooqFromStatus = convertToJooqUploadStatus(fromStatus);
        FirmwareFileUploadStatus jooqToStatus = convertToJooqUploadStatus(toStatus);

        return ctx.update(FIRMWARE_FILE)
                 .set(FIRMWARE_FILE.UPLOAD_STATUS, jooqToStatus)
                 .set(FIRMWARE_FILE.DESCRIPTION, description)
                 .where(FIRMWARE_FILE.FILENAME.eq(filename)
                       .and(FIRMWARE_FILE.UPLOAD_STATUS.eq(jooqFromStatus)))
                 .execute();
    }

    @Override
    public int deleteFirmwareFile(String filename) {
        return ctx.deleteFrom(FIRMWARE_FILE)
                 .where(FIRMWARE_FILE.FILENAME.eq(filename))
                 .execute();
    }

    @Override
    public boolean existsByFilename(String filename) {
        return ctx.fetchExists(
            ctx.selectOne()
               .from(FIRMWARE_FILE)
               .where(FIRMWARE_FILE.FILENAME.eq(filename))
        );
    }



    @Override
    public List<FirmwareFile> getFirmwareFilesByUser(Integer userPk) {
        return ctx.selectFrom(FIRMWARE_FILE)
                 .where(FIRMWARE_FILE.UPLOADED_BY_USER_PK.eq(userPk))
                 .orderBy(FIRMWARE_FILE.UPLOAD_TIME.desc())
                 .fetch(this::mapToFirmwareFile);
    }

    /**
     * 将数据库记录映射为FirmwareFile对象
     */
    private FirmwareFile mapToFirmwareFile(Record record) {
        FirmwareFileFileType jooqFileType = record.get(FIRMWARE_FILE.FILE_TYPE);
        FirmwareFileUploadStatus jooqUploadStatus = record.get(FIRMWARE_FILE.UPLOAD_STATUS);

        return FirmwareFile.builder()
                .firmwareFilePk(record.get(FIRMWARE_FILE.FIRMWARE_FILE_PK))
                .filename(record.get(FIRMWARE_FILE.FILENAME))
                .fileType(convertFromJooqFileType(jooqFileType))
                .uploadTime(record.get(FIRMWARE_FILE.UPLOAD_TIME))
                .uploadStatus(convertFromJooqUploadStatus(jooqUploadStatus))
                .uploadedByUserPk(record.get(FIRMWARE_FILE.UPLOADED_BY_USER_PK))
                .fileSize(record.get(FIRMWARE_FILE.FILE_SIZE))
                .description(record.get(FIRMWARE_FILE.DESCRIPTION))
                .build();
    }

    /**
     * 转换 DTO 枚举到 jOOQ 枚举
     */
    private FirmwareFileFileType convertToJooqFileType(FirmwareFile.FileType fileType) {
        if (fileType == null) return null;
        switch (fileType) {
            case FIRMWARE: return FirmwareFileFileType.FIRMWARE;
            case APPLICATION: return FirmwareFileFileType.APPLICATION;
            default: throw new IllegalArgumentException("Unknown file type: " + fileType);
        }
    }

    private FirmwareFileUploadStatus convertToJooqUploadStatus(FirmwareFile.UploadStatus uploadStatus) {
        if (uploadStatus == null) return null;
        switch (uploadStatus) {
            case PENDING: return FirmwareFileUploadStatus.PENDING;
            case SUCCESS: return FirmwareFileUploadStatus.SUCCESS;
            case FAILED: return FirmwareFileUploadStatus.FAILED;
            default: throw new IllegalArgumentException("Unknown upload status: " + uploadStatus);
        }
    }

    /**
     * 转换 jOOQ 枚举到 DTO 枚举
     */
    private FirmwareFile.FileType convertFromJooqFileType(FirmwareFileFileType jooqFileType) {
        if (jooqFileType == null) return null;
        switch (jooqFileType) {
            case FIRMWARE: return FirmwareFile.FileType.FIRMWARE;
            case APPLICATION: return FirmwareFile.FileType.APPLICATION;
            default: throw new IllegalArgumentException("Unknown jOOQ file type: " + jooqFileType);
        }
    }

    private FirmwareFile.UploadStatus convertFromJooqUploadStatus(FirmwareFileUploadStatus jooqUploadStatus) {
        if (jooqUploadStatus == null) return null;
        switch (jooqUploadStatus) {
            case PENDING: return FirmwareFile.UploadStatus.PENDING;
            case SUCCESS: return FirmwareFile.UploadStatus.SUCCESS;
            case FAILED: return FirmwareFile.UploadStatus.FAILED;
            default: throw new IllegalArgumentException("Unknown jOOQ upload status: " + jooqUploadStatus);
        }
    }
}
