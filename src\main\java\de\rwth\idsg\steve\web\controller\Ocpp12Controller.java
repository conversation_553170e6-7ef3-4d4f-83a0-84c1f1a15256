/*
 * SteV<PERSON> - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.web.controller;

import de.rwth.idsg.steve.SteveConfiguration;
import de.rwth.idsg.steve.ocpp.OcppVersion;
import de.rwth.idsg.steve.service.ChargePointHelperService;
import de.rwth.idsg.steve.service.ChargePointServiceClient;
import de.rwth.idsg.steve.service.OcppTagService;
import de.rwth.idsg.steve.service.DiagnosticsTaskManager;
import de.rwth.idsg.steve.service.DiagnosticsTaskInfo;
import de.rwth.idsg.steve.web.dto.ocpp.ChangeAvailabilityParams;
import de.rwth.idsg.steve.web.dto.ocpp.ChangeConfigurationParams;
import de.rwth.idsg.steve.web.dto.ocpp.ConfigurationKeyEnum;
import de.rwth.idsg.steve.web.dto.ocpp.ConfigurationKeyReadWriteEnum;
import de.rwth.idsg.steve.web.dto.ocpp.GetDiagnosticsParams;
import de.rwth.idsg.steve.web.dto.ocpp.MultipleChargePointSelect;
import de.rwth.idsg.steve.web.dto.ocpp.RemoteStartTransactionParams;
import de.rwth.idsg.steve.web.dto.ocpp.RemoteStopTransactionParams;
import de.rwth.idsg.steve.web.dto.ocpp.ResetParams;
import de.rwth.idsg.steve.web.dto.ocpp.UnlockConnectorParams;
import de.rwth.idsg.steve.web.dto.ocpp.UpdateFirmwareParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import jakarta.validation.Valid;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.Enumeration;
import java.util.Map;

import static de.rwth.idsg.steve.web.dto.ocpp.ConfigurationKeyReadWriteEnum.RW;

/**
 * <AUTHOR> Goekay <<EMAIL>>
 * @since 15.08.2014
 */
@Slf4j
@Controller
@RequestMapping(value = "/manager/operations/v1.2")
public class Ocpp12Controller {

    @Autowired protected ChargePointHelperService chargePointHelperService;
    @Autowired protected OcppTagService ocppTagService;
    @Autowired protected ChargePointServiceClient chargePointServiceClient;
    @Autowired private DiagnosticsTaskManager diagnosticsTaskManager;

    protected static final String PARAMS = "params";

    // -------------------------------------------------------------------------
    // Paths
    // -------------------------------------------------------------------------

    private static final String CHANGE_AVAIL_PATH = "/ChangeAvailability";
    protected static final String CHANGE_CONF_PATH = "/ChangeConfiguration";
    private static final String CLEAR_CACHE_PATH = "/ClearCache";
    private static final String GET_DIAG_PATH = "/GetDiagnostics";
    private static final String REMOTE_START_TX_PATH = "/RemoteStartTransaction";
    private static final String REMOTE_STOP_TX_PATH = "/RemoteStopTransaction";
    private static final String RESET_PATH = "/Reset";
    private static final String UNLOCK_CON_PATH = "/UnlockConnector";
    private static final String UPDATE_FIRM_PATH = "/UpdateFirmware";

    protected static final String REDIRECT_TASKS_PATH = "redirect:/manager/operations/v1.6/";

    // -------------------------------------------------------------------------
    // Helpers
    // -------------------------------------------------------------------------

    protected void setCommonAttributesForTx(Model model) {
        setCommonAttributes(model);
    }

    protected void setCommonAttributes(Model model) {
        model.addAttribute("cpList", chargePointHelperService.getChargePoints(OcppVersion.V_12));
        model.addAttribute("opVersion", "v1.2");
    }

    protected Map<String, String> getConfigurationKeys(ConfigurationKeyReadWriteEnum confEnum) {
        // this conf enum is only relevant for versions >= occp 1.6
        return ConfigurationKeyEnum.OCPP_12_MAP;
    }

    protected String getRedirectPath() {
        return "redirect:/manager/operations/v1.2/ChangeAvailability";
    }

    protected String getPrefix() {
        return "op12";
    }

    protected void setActiveUserIdTagList(Model model) {
        model.addAttribute("idTagList", ocppTagService.getActiveIdTags());
    }

    // -------------------------------------------------------------------------
    // Http methods (GET)
    // -------------------------------------------------------------------------

    @RequestMapping(method = RequestMethod.GET)
    public String getBase() {
        return getRedirectPath();
    }

    @RequestMapping(value = CHANGE_AVAIL_PATH, method = RequestMethod.GET)
    public String getChangeAvail(Model model) {
        setCommonAttributes(model);
        model.addAttribute(PARAMS, new ChangeAvailabilityParams());
        return getPrefix() + CHANGE_AVAIL_PATH;
    }

    @RequestMapping(value = CHANGE_CONF_PATH, method = RequestMethod.GET)
    public String getChangeConf(Model model) {
        setCommonAttributes(model);
        model.addAttribute(PARAMS, new ChangeConfigurationParams());
        model.addAttribute("ocppConfKeys", getConfigurationKeys(RW));
        return getPrefix() + CHANGE_CONF_PATH;
    }

    @RequestMapping(value = CLEAR_CACHE_PATH, method = RequestMethod.GET)
    public String getClearCache(Model model) {
        setCommonAttributes(model);
        model.addAttribute(PARAMS, new MultipleChargePointSelect());
        return getPrefix() + CLEAR_CACHE_PATH;
    }

    @RequestMapping(value = GET_DIAG_PATH, method = RequestMethod.GET)
    public String getGetDiag(Model model) {
        setCommonAttributes(model);
        model.addAttribute(PARAMS, new GetDiagnosticsParams());

        // 添加FTP服务器信息到模型中
        if (SteveConfiguration.CONFIG.isFtpEnabled()) {
            String ftpInfo = String.format("ftp://%s@%s:%d/logs/",
                SteveConfiguration.CONFIG.getFtpUsername(),
                SteveConfiguration.CONFIG.getFtpIp(),
                SteveConfiguration.CONFIG.getFtpPort());
            model.addAttribute("ftpServerInfo", ftpInfo);
        } else {
            model.addAttribute("ftpServerInfo", "FTP server is not enabled");
        }

        return getPrefix() + GET_DIAG_PATH;
    }

    @RequestMapping(value = REMOTE_START_TX_PATH, method = RequestMethod.GET)
    public String getRemoteStartTx(Model model) {
        setCommonAttributesForTx(model);
        setActiveUserIdTagList(model);
        model.addAttribute(PARAMS, new RemoteStartTransactionParams());
        return getPrefix() + REMOTE_START_TX_PATH;
    }

    @RequestMapping(value = REMOTE_STOP_TX_PATH, method = RequestMethod.GET)
    public String getRemoteStopTx(Model model) {
        setCommonAttributesForTx(model);
        model.addAttribute(PARAMS, new RemoteStopTransactionParams());
        return getPrefix() + REMOTE_STOP_TX_PATH;
    }

    @RequestMapping(value = RESET_PATH, method = RequestMethod.GET)
    public String getReset(Model model) {
        setCommonAttributes(model);
        model.addAttribute(PARAMS, new ResetParams());
        return getPrefix() + RESET_PATH;
    }

    @RequestMapping(value = UNLOCK_CON_PATH, method = RequestMethod.GET)
    public String getUnlockCon(Model model) {
        setCommonAttributes(model);
        model.addAttribute(PARAMS, new UnlockConnectorParams());
        return getPrefix() + UNLOCK_CON_PATH;
    }

    @RequestMapping(value = UPDATE_FIRM_PATH, method = RequestMethod.GET)
    public String getUpdateFirm(Model model) {
        setCommonAttributes(model);
        model.addAttribute(PARAMS, new UpdateFirmwareParams());
        return getPrefix() + UPDATE_FIRM_PATH;
    }

    // -------------------------------------------------------------------------
    // Http methods (POST)
    // -------------------------------------------------------------------------

    @RequestMapping(value = CHANGE_AVAIL_PATH, method = RequestMethod.POST)
    public String postChangeAvail(@Valid @ModelAttribute(PARAMS) ChangeAvailabilityParams params,
                                  BindingResult result, Model model) {
        if (result.hasErrors()) {
            setCommonAttributes(model);
            return getPrefix() + CHANGE_AVAIL_PATH;
        }
        return REDIRECT_TASKS_PATH + chargePointServiceClient.changeAvailability(params);
    }

    @RequestMapping(value = CHANGE_CONF_PATH, method = RequestMethod.POST)
    public String postChangeConf(@Valid @ModelAttribute(PARAMS) ChangeConfigurationParams params,
                                 BindingResult result, Model model) {
        if (result.hasErrors()) {
            setCommonAttributes(model);
            model.addAttribute("ocppConfKeys", getConfigurationKeys(RW));
            return getPrefix() + CHANGE_CONF_PATH;
        }
        return REDIRECT_TASKS_PATH + chargePointServiceClient.changeConfiguration(params);
    }

    @RequestMapping(value = CLEAR_CACHE_PATH, method = RequestMethod.POST)
    public String postClearCache(@Valid @ModelAttribute(PARAMS) MultipleChargePointSelect params,
                                 BindingResult result, Model model) {
        if (result.hasErrors()) {
            setCommonAttributes(model);
            return getPrefix() + CLEAR_CACHE_PATH;
        }
        return REDIRECT_TASKS_PATH + chargePointServiceClient.clearCache(params);
    }

    @RequestMapping(value = GET_DIAG_PATH, method = RequestMethod.POST)
    public String postGetDiag(@Valid @ModelAttribute(PARAMS) GetDiagnosticsParams params,
                              BindingResult result, Model model, HttpServletRequest request) {

        // 调试：打印所有请求参数
        log.info("=== GetDiagnostics Form Submission Debug ===");
        log.info("Request parameters:");
        request.getParameterMap().forEach((key, values) -> {
            log.info("  {}: {}", key, java.util.Arrays.toString(values));
        });
        log.info("Params object: chargePointSelectList size = {}",
                params.getChargePointSelectList() != null ? params.getChargePointSelectList().size() : "null");

        // 手动处理充电桩选择（修复Spring绑定问题）
        String[] chargePointParams = request.getParameterValues("chargePointSelectList");
        if (chargePointParams != null && chargePointParams.length > 0) {
            log.info("Found chargePointSelectList parameters: {}", java.util.Arrays.toString(chargePointParams));
            // 手动创建ChargePointSelect对象
            java.util.List<de.rwth.idsg.steve.repository.dto.ChargePointSelect> chargePoints = new java.util.ArrayList<>();
            for (String param : chargePointParams) {
                if (param != null && !param.trim().isEmpty()) {
                    String[] parts = param.split(";");
                    if (parts.length >= 3) {
                        de.rwth.idsg.steve.repository.dto.ChargePointSelect cp =
                            new de.rwth.idsg.steve.repository.dto.ChargePointSelect(
                                de.rwth.idsg.steve.ocpp.OcppProtocol.valueOf(parts[0]),
                                parts[1],
                                parts[2]
                            );
                        chargePoints.add(cp);
                        log.info("Added charge point: {}", parts[1]);
                    }
                }
            }
            params.setChargePointSelectList(chargePoints);
            log.info("Manually set chargePointSelectList size: {}", chargePoints.size());
        }

        log.info("============================================");

        // 处理日期范围：将日期转换为日期时间范围
        org.joda.time.LocalDate today = org.joda.time.LocalDate.now();

        if (params.getStartDate() == null) {
            // 默认开始日期：昨天
            params.setStartDate(today.minusDays(1));
            log.info("Set default start date to yesterday: {}", params.getStartDate());
        }

        if (params.getStopDate() == null) {
            // 默认结束日期：昨天（获取昨天一整天的日志）
            params.setStopDate(today.minusDays(1));
            log.info("Set default stop date to yesterday: {}", params.getStopDate());
        }

        // 将日期转换为日期时间范围
        // 开始时间：开始日期的00:00:00
        params.setStart(params.getStartDate().toLocalDateTime(org.joda.time.LocalTime.MIDNIGHT));
        // 结束时间：结束日期的23:59:59
        params.setStop(params.getStopDate().toLocalDateTime(new org.joda.time.LocalTime(23, 59, 59)));

        log.info("GetDiagnostics date range: {} to {} (converted to time range: {} to {})",
                params.getStartDate(), params.getStopDate(), params.getStart(), params.getStop());

        // 自动生成FTP Location路径
        try {
            String ftpLocation = generateDiagnosticsFtpLocation();
            params.setLocation(ftpLocation);
            log.info("Auto-generated GetDiagnostics FTP location: {}", ftpLocation);
        } catch (Exception e) {
            log.error("Failed to generate FTP location for GetDiagnostics: {}", e.getMessage());
            result.rejectValue("location", "error.ftp.location", "Failed to generate FTP path: " + e.getMessage());
        }

        // 检查充电桩选择
        if (params.getChargePointSelectList() == null || params.getChargePointSelectList().isEmpty()) {
            log.warn("No charge points selected for GetDiagnostics operation");
            result.rejectValue("chargePointSelectList", "error.chargepoint.required", "Please select at least one charge point");
        } else {
            log.info("Selected {} charge points for GetDiagnostics: {}",
                    params.getChargePointSelectList().size(),
                    params.getChargePointSelectList().stream()
                            .map(cp -> cp.getChargeBoxId())
                            .toList());
        }

        if (result.hasErrors()) {
            log.warn("GetDiagnostics form has validation errors: {}", result.getAllErrors());
            setCommonAttributes(model);
            // 重新添加FTP服务器信息
            if (SteveConfiguration.CONFIG.isFtpEnabled()) {
                String ftpInfo = String.format("ftp://%s@%s:%d/logs/",
                    SteveConfiguration.CONFIG.getFtpUsername(),
                    SteveConfiguration.CONFIG.getFtpIp(),
                    SteveConfiguration.CONFIG.getFtpPort());
                model.addAttribute("ftpServerInfo", ftpInfo);
            } else {
                model.addAttribute("ftpServerInfo", "FTP server is not enabled");
            }
            return getPrefix() + GET_DIAG_PATH;
        }

        // 检查是否有单个充电桩的请求，如果是，尝试复用现有任务
        log.info("🔧 Ocpp12Controller.postGetDiag: checking for task reuse, chargePointCount={}",
                params.getChargePointSelectList().size());

        if (params.getChargePointSelectList().size() == 1) {
            String chargeBoxId = params.getChargePointSelectList().get(0).getChargeBoxId();
            log.info("🔧 Single charge point request for: {}", chargeBoxId);

            DiagnosticsTaskInfo existingTask = diagnosticsTaskManager.getValidTask(chargeBoxId);

            if (existingTask != null) {
                log.info("🔄 Reusing existing diagnostics task for {}: taskId={}, remaining={}min",
                        chargeBoxId, existingTask.getTaskId(), existingTask.getRemainingMinutes());

                String redirectPath = REDIRECT_TASKS_PATH + existingTask.getTaskId();
                log.info("Redirecting to existing task: {}", redirectPath);
                return redirectPath;
            } else {
                log.info("🔧 No existing valid task found for {}, will create new task", chargeBoxId);
            }
        } else {
            log.info("🔧 Multiple charge points selected, skipping task reuse logic");
        }

        log.info("Calling chargePointServiceClient.getDiagnostics with params: location={}, chargePoints={}",
                params.getLocation(),
                params.getChargePointSelectList().stream()
                        .map(cp -> cp.getChargeBoxId())
                        .toList());

        int taskId = chargePointServiceClient.getDiagnostics(params);
        log.info("GetDiagnostics task created with ID: {}", taskId);

        // 注册新任务到任务管理器（仅对单个充电桩）
        if (params.getChargePointSelectList().size() == 1) {
            String chargeBoxId = params.getChargePointSelectList().get(0).getChargeBoxId();
            log.info("🔧 Registering new task for {}: taskId={}", chargeBoxId, taskId);
            diagnosticsTaskManager.registerTask(chargeBoxId, taskId);
        } else {
            log.info("🔧 Multiple charge points, not registering task in manager");
        }

        String redirectPath = REDIRECT_TASKS_PATH + taskId;
        log.info("Redirecting to: {}", redirectPath);

        return redirectPath;
    }

    @RequestMapping(value = REMOTE_START_TX_PATH, method = RequestMethod.POST)
    public String postRemoteStartTx(@Valid @ModelAttribute(PARAMS) RemoteStartTransactionParams params,
                                    BindingResult result, Model model) {
        if (result.hasErrors()) {
            setCommonAttributesForTx(model);
            setActiveUserIdTagList(model);
            return getPrefix() + REMOTE_START_TX_PATH;
        }
        return REDIRECT_TASKS_PATH + chargePointServiceClient.remoteStartTransaction(params);
    }

    @RequestMapping(value = REMOTE_STOP_TX_PATH, method = RequestMethod.POST)
    public String postRemoteStopTx(@Valid @ModelAttribute(PARAMS) RemoteStopTransactionParams params,
                                   BindingResult result, Model model) {
        if (result.hasErrors()) {
            setCommonAttributesForTx(model);
            return getPrefix() + REMOTE_STOP_TX_PATH;
        }
        return REDIRECT_TASKS_PATH + chargePointServiceClient.remoteStopTransaction(params);
    }

    @RequestMapping(value = RESET_PATH, method = RequestMethod.POST)
    public String postReset(@Valid @ModelAttribute(PARAMS) ResetParams params,
                            BindingResult result, Model model) {
        if (result.hasErrors()) {
            setCommonAttributes(model);
            return getPrefix() + RESET_PATH;
        }
        return REDIRECT_TASKS_PATH + chargePointServiceClient.reset(params);
    }

    @RequestMapping(value = UNLOCK_CON_PATH, method = RequestMethod.POST)
    public String postUnlockCon(@Valid @ModelAttribute(PARAMS) UnlockConnectorParams params,
                                BindingResult result, Model model) {
        if (result.hasErrors()) {
            setCommonAttributes(model);
            return getPrefix() + UNLOCK_CON_PATH;
        }
        return REDIRECT_TASKS_PATH + chargePointServiceClient.unlockConnector(params);
    }

    @RequestMapping(value = UPDATE_FIRM_PATH, method = RequestMethod.POST)
    public String postUpdateFirm(@Valid @ModelAttribute(PARAMS) UpdateFirmwareParams params,
                                 BindingResult result, Model model) {
        if (result.hasErrors()) {
            setCommonAttributes(model);
            return getPrefix() + UPDATE_FIRM_PATH;
        }
        return REDIRECT_TASKS_PATH + chargePointServiceClient.updateFirmware(params);
    }

    /**
     * 生成GetDiagnostics的FTP Location路径
     */
    private String generateDiagnosticsFtpLocation() throws IOException {
        if (!SteveConfiguration.CONFIG.isFtpEnabled()) {
            throw new IOException("FTP server is not enabled");
        }

        String ftpIp = SteveConfiguration.CONFIG.getFtpIp();
        if ("auto".equalsIgnoreCase(ftpIp) || !org.springframework.util.StringUtils.hasText(ftpIp)) {
            log.info("Attempting to dynamically determine internal IP for GetDiagnostics FTP as ftpIp property is 'auto'.");
            ftpIp = getInternalIpAddress();
            if (ftpIp == null) {
                throw new IOException("Could not automatically determine a site-local IP address for FTP.");
            }
            log.info("Dynamically determined internal IP for GetDiagnostics FTP: {}", ftpIp);
        }

        int ftpPort = SteveConfiguration.CONFIG.getFtpPort();
        String ftpUser = SteveConfiguration.CONFIG.getFtpUsername();
        String ftpPassword = SteveConfiguration.CONFIG.getFtpPassword();

        // 使用 /logs/ 路径用于诊断日志
        // 注意：某些充电桩可能不支持子目录，如果文件仍在根目录，这是充电桩限制
        String ftpLogsPath = "/logs/";

        String location = String.format("ftp://%s:%s@%s:%d%s", ftpUser, ftpPassword, ftpIp, ftpPort, ftpLogsPath);
        log.info("Generated GetDiagnostics FTP URL: {}", location);
        return location;
    }

    /**
     * 获取内部IP地址（从OcppAjaxController复制的方法）
     */
    private String getInternalIpAddress() throws IOException {
        try {
            Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
            while (networkInterfaces.hasMoreElements()) {
                NetworkInterface networkInterface = networkInterfaces.nextElement();
                if (networkInterface.isLoopback() || !networkInterface.isUp()) {
                    continue;
                }
                Enumeration<InetAddress> addresses = networkInterface.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    InetAddress address = addresses.nextElement();
                    if (address.isSiteLocalAddress() && address instanceof Inet4Address) {
                        return address.getHostAddress();
                    }
                }
            }
        } catch (SocketException e) {
            throw new IOException("Failed to determine internal IP address", e);
        }
        return null;
    }
}
