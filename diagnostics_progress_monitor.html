<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GetDiagnostics Progress Monitor</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #007cba;
        }
        .progress-item {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .charge-point-id {
            font-weight: bold;
            font-size: 18px;
            color: #333;
        }
        .status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status.initializing { background-color: #fff3cd; color: #856404; }
        .status.request-sent { background-color: #cce5ff; color: #004085; }
        .status.request-acknowledged { background-color: #d4edda; color: #155724; }
        .status.generating-file { background-color: #ffeaa7; color: #6c5700; }
        .status.uploading-file { background-color: #a8e6cf; color: #0f5132; }
        .status.file-ready { background-color: #cce5ff; color: #004085; }
        .status.downloading { background-color: #fff3cd; color: #856404; }
        .status.download-completed { background-color: #d1ecf1; color: #0c5460; }
        .status.completed { background-color: #d1ecf1; color: #0c5460; }
        .status.failed { background-color: #f8d7da; color: #721c24; }
        
        .progress-bar-container {
            width: 100%;
            height: 25px;
            background-color: #e9ecef;
            border-radius: 12px;
            overflow: hidden;
            margin: 10px 0;
            position: relative;
        }
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #007cba, #28a745);
            border-radius: 12px;
            transition: width 0.5s ease;
            position: relative;
        }
        .progress-bar.failed {
            background: linear-gradient(90deg, #dc3545, #c82333);
        }
        .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-weight: bold;
            font-size: 12px;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }
        .progress-message {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
        .elapsed-time {
            font-size: 12px;
            color: #999;
            margin-top: 5px;
        }
        .no-progress {
            text-align: center;
            padding: 40px;
            color: #666;
            font-style: italic;
        }
        .refresh-button {
            background-color: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-bottom: 20px;
        }
        .refresh-button:hover {
            background-color: #0056b3;
        }
        .auto-refresh {
            margin-left: 10px;
        }
        .auto-refresh input[type="checkbox"] {
            margin-right: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 GetDiagnostics Progress Monitor</h1>
            <p>Real-time progress tracking for diagnostic file uploads</p>
        </div>
        
        <div class="controls">
            <button class="refresh-button" onclick="refreshProgress()">🔄 Refresh</button>
            <button class="refresh-button" onclick="clearAllProgress()" style="background-color: #dc3545;">🧹 Clear All</button>
            <label class="auto-refresh">
                <input type="checkbox" id="autoRefresh" checked> Auto-refresh (5s)
            </label>
        </div>
        
        <div id="progressContainer">
            <div class="no-progress">
                <p>No active GetDiagnostics operations found.</p>
                <p>Start a GetDiagnostics request to see progress here.</p>
            </div>
        </div>
    </div>

    <script>
        let autoRefreshInterval;
        
        function formatElapsedTime(milliseconds) {
            const seconds = Math.floor(milliseconds / 1000);
            const minutes = Math.floor(seconds / 60);
            const hours = Math.floor(minutes / 60);
            
            if (hours > 0) {
                return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
            } else if (minutes > 0) {
                return `${minutes}m ${seconds % 60}s`;
            } else {
                return `${seconds}s`;
            }
        }
        
        function getStatusClass(status) {
            return status.toLowerCase().replace(/\s+/g, '-');
        }
        
        function createProgressItem(chargeBoxId, data) {
            const percentage = data.percentage;
            const status = data.status;
            const message = data.message;
            const elapsedTime = data.elapsedTime;

            const isFailed = percentage === -1;
            const displayPercentage = isFailed ? 0 : percentage;

            // Check if file is ready for download
            const isFileReady = status === 'File Ready';
            const isDownloading = status === 'Downloading';
            const isDownloadCompleted = status === 'Download Completed';

            // Create download button if file is ready
            let downloadButton = '';
            if (isFileReady) {
                downloadButton = `<button onclick="downloadFile('${chargeBoxId}')" style="background-color: #28a745; color: white; border: none; padding: 2px 8px; border-radius: 3px; font-size: 10px; margin-right: 5px;">📥 Download</button>`;
            } else if (isDownloading) {
                downloadButton = `<button disabled style="background-color: #6c757d; color: white; border: none; padding: 2px 8px; border-radius: 3px; font-size: 10px; margin-right: 5px;">⏳ Downloading...</button>`;
            } else if (isDownloadCompleted) {
                downloadButton = `<button disabled style="background-color: #17a2b8; color: white; border: none; padding: 2px 8px; border-radius: 3px; font-size: 10px; margin-right: 5px;">✅ Downloaded</button>`;
            }

            return `
                <div class="progress-item">
                    <div class="progress-header">
                        <div class="charge-point-id">📱 ${chargeBoxId}</div>
                        <div>
                            ${downloadButton}
                            <button onclick="clearProgress('${chargeBoxId}')" style="background-color: #ffc107; color: #000; border: none; padding: 2px 8px; border-radius: 3px; font-size: 10px; margin-right: 5px;">Clear</button>
                            <div class="status ${getStatusClass(status)}" style="display: inline-block;">${status}</div>
                        </div>
                    </div>
                    <div class="progress-bar-container">
                        <div class="progress-bar ${isFailed ? 'failed' : ''}" style="width: ${displayPercentage}%">
                            <div class="progress-text">${isFailed ? 'Failed' : displayPercentage + '%'}</div>
                        </div>
                    </div>
                    <div class="progress-message">💬 ${message}</div>
                    <div class="elapsed-time">⏱️ Elapsed: ${formatElapsedTime(elapsedTime)}</div>
                </div>
            `;
        }
        
        async function refreshProgress() {
            try {
                const response = await fetch('/api/diagnostics/progress');
                const data = await response.json();
                
                const container = document.getElementById('progressContainer');
                
                if (Object.keys(data).length === 0) {
                    container.innerHTML = `
                        <div class="no-progress">
                            <p>No active GetDiagnostics operations found.</p>
                            <p>Start a GetDiagnostics request to see progress here.</p>
                        </div>
                    `;
                } else {
                    let html = '';
                    for (const [chargeBoxId, progressData] of Object.entries(data)) {
                        html += createProgressItem(chargeBoxId, progressData);
                    }
                    container.innerHTML = html;

                    // Check for auto-download opportunities
                    checkForAutoDownload(data);
                }

                console.log('Progress refreshed:', data);
            } catch (error) {
                console.error('Failed to refresh progress:', error);
                document.getElementById('progressContainer').innerHTML = `
                    <div class="no-progress">
                        <p>❌ Failed to load progress data</p>
                        <p>Error: ${error.message}</p>
                    </div>
                `;
            }
        }

        async function clearProgress(chargeBoxId) {
            try {
                const response = await fetch(`/api/diagnostics/progress/${chargeBoxId}`, {
                    method: 'DELETE'
                });

                if (response.ok) {
                    console.log(`Progress cleared for ${chargeBoxId}`);
                    refreshProgress(); // Refresh to show updated state
                } else {
                    console.error(`Failed to clear progress for ${chargeBoxId}`);
                }
            } catch (error) {
                console.error(`Error clearing progress for ${chargeBoxId}:`, error);
            }
        }

        async function clearAllProgress() {
            if (confirm('Are you sure you want to clear all progress tracking? This will allow repeat GetDiagnostics requests.')) {
                try {
                    const response = await fetch('/api/diagnostics/progress', {
                        method: 'DELETE'
                    });

                    if (response.ok) {
                        console.log('All progress cleared');
                        refreshProgress(); // Refresh to show updated state
                    } else {
                        console.error('Failed to clear all progress');
                    }
                } catch (error) {
                    console.error('Error clearing all progress:', error);
                }
            }
        }

        function setupAutoRefresh() {
            const checkbox = document.getElementById('autoRefresh');
            
            function updateAutoRefresh() {
                if (autoRefreshInterval) {
                    clearInterval(autoRefreshInterval);
                }
                
                if (checkbox.checked) {
                    autoRefreshInterval = setInterval(refreshProgress, 5000);
                    console.log('Auto-refresh enabled (5s interval)');
                } else {
                    console.log('Auto-refresh disabled');
                }
            }
            
            checkbox.addEventListener('change', updateAutoRefresh);
            updateAutoRefresh(); // Initial setup
        }

        // Download file function
        async function downloadFile(chargeBoxId) {
            try {
                console.log(`📥 Starting download for charge point: ${chargeBoxId}`);

                // First check if file is available
                const statusResponse = await fetch(`/api/diagnostics/download-status/${chargeBoxId}`);
                const statusData = await statusResponse.json();
                console.log(`📋 Download status for ${chargeBoxId}:`, statusData);

                if (!statusData.hasDownloadableFile) {
                    console.warn(`⚠️ No downloadable file available for ${chargeBoxId}`);
                    return;
                }

                // Create a temporary link to trigger download
                const downloadUrl = `/api/diagnostics/download/${chargeBoxId}`;
                console.log(`🔗 Download URL: ${downloadUrl}`);

                const link = document.createElement('a');
                link.href = downloadUrl;
                link.download = ''; // Let the server determine the filename
                link.style.display = 'none';

                // Add event listeners to track download
                link.addEventListener('click', function() {
                    console.log(`🖱️ Download link clicked for ${chargeBoxId}`);
                });

                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                console.log(`✅ Download initiated for charge point: ${chargeBoxId}`);

                // Refresh progress to show updated status
                setTimeout(refreshProgress, 1000);

            } catch (error) {
                console.error(`❌ Failed to download file for ${chargeBoxId}:`, error);
                alert(`Failed to download file for ${chargeBoxId}: ${error.message}`);
            }
        }

        // Auto-download functionality
        let autoDownloadedFiles = new Set();

        function checkForAutoDownload(progressData) {
            console.log('🔍 Checking for auto-download opportunities:', progressData);

            for (const [chargeBoxId, data] of Object.entries(progressData)) {
                console.log(`📊 Charge point ${chargeBoxId}: status="${data.status}", percentage=${data.percentage}`);

                if (data.status === 'File Ready' && !autoDownloadedFiles.has(chargeBoxId)) {
                    console.log(`🚀 Auto-downloading file for charge point: ${chargeBoxId}`);
                    autoDownloadedFiles.add(chargeBoxId);

                    // Auto-download after a short delay
                    setTimeout(() => {
                        console.log(`⏰ Auto-download timeout triggered for: ${chargeBoxId}`);
                        downloadFile(chargeBoxId);
                    }, 2000);
                } else if (data.status === 'File Ready') {
                    console.log(`⚠️ File ready for ${chargeBoxId} but already in auto-download set`);
                }

                // Clean up completed downloads from the set
                if (data.status === 'Download Completed') {
                    console.log(`✅ Cleaning up completed download for: ${chargeBoxId}`);
                    autoDownloadedFiles.delete(chargeBoxId);
                }
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('GetDiagnostics Progress Monitor initialized');
            refreshProgress(); // Initial load
            setupAutoRefresh();
        });
        
        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }
        });
    </script>
</body>
</html>
