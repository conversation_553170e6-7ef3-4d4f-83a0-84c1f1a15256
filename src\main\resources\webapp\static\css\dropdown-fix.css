/**
 * 下拉菜单显示修复样式
 * 专门修复EVSE_OMS项目中下拉菜单被遮挡的问题
 * 修复z-index层级问题和容器裁剪问题
 */

/* 修复ownerAssignmentDetails.jsp中的下拉菜单 */
.multi-select-menu {
    z-index: 9999 !important;
}

/* 修复00-cp-multiple.jsp中的下拉菜单 */
.options-dropdown {
    z-index: 9999 !important;
}

/* 确保父容器不会裁剪下拉菜单 */
.tab-content {
    overflow: visible !important;
}

.multi-select-container {
    overflow: visible !important;
}

.multi-select-dropdown {
    overflow: visible !important;
}

/* 确保选项卡容器不会裁剪下拉菜单 */
.tab-container {
    overflow: visible !important;
}

/* 确保内容容器不会裁剪下拉菜单 */
.content-container {
    overflow: visible !important;
}

/* 确保页面主要容器不会裁剪下拉菜单 */
.content {
    overflow: visible !important;
}

/* 特别针对layout-fix.css中的.content-container样式进行覆盖 */
.content .content-container {
    overflow: visible !important;
}

/* 确保表格容器不会裁剪下拉菜单 */
.table-responsive {
    overflow: visible !important;
}

/* 针对可能的表单容器 */
form {
    overflow: visible !important;
}
