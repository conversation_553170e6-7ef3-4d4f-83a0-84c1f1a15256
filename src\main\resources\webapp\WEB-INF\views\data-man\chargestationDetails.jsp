<%--

    SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
    Copyright (C) 2013-2025 SteVe Community Team
    All Rights Reserved.

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.

--%>
<%@ include file="../00-header.jsp" %>

<style>
    /* 改善表格容器的样式 */
    .table-container {
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    /* 粘性表头样式 */
    .sticky-top {
        background-color: #f8f9fa !important;
        z-index: 10;
    }

    /* 搜索框样式 */
    .input-group .form-control:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
    }

    /* 徽章样式改进 */
    .badge {
        font-size: 0.75em;
    }

    /* 表格行悬停效果 */
    .table-hover tbody tr:hover {
        background-color: rgba(0,123,255,.075);
    }

    /* 选中行高亮 */
    .table tbody tr.selected {
        background-color: rgba(40,167,69,.1);
    }

    /* 计数器样式 */
    .text-muted {
        font-size: 0.875em;
    }

    /* 无结果提示样式 */
    #noAssociatedResults, #noUnassignedResults {
        background-color: #f8f9fa;
        border-radius: 0.375rem;
        border: 1px solid #dee2e6;
    }

    /* 按钮组间距 */
    .btn + .btn {
        margin-left: 0.25rem;
    }

    /* 搜索按钮样式 */
    .input-group-append .btn {
        border-left: 0;
    }

    .input-group-append .btn:first-child {
        border-left: 1px solid #ced4da;
    }

    /* 搜索按钮悬停效果 */
    .btn-outline-primary:hover {
        background-color: #007bff;
        border-color: #007bff;
    }

    /* 响应式改进 */
    @media (max-width: 768px) {
        .table-container {
            max-height: 300px;
        }

        .col-md-6, .col-md-3 {
            margin-bottom: 0.5rem;
        }
    }
</style>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>

<script type="text/javascript">
$(document).ready(function() {
    // 关联充电桩的全选/取消全选
    $('#checkAllAssociated').change(function() {
        $('.associatedCheckbox').prop('checked', this.checked);
    });

    $('#selectAllAssociated').click(function() {
        $('.associatedCheckbox').prop('checked', true);
        $('#checkAllAssociated').prop('checked', true);
    });

    $('#deselectAllAssociated').click(function() {
        $('.associatedCheckbox').prop('checked', false);
        $('#checkAllAssociated').prop('checked', false);
    });

    // 未分配充电桩的全选/取消全选
    $('#checkAllUnassigned').change(function() {
        $('.unassignedCheckbox').prop('checked', this.checked);
    });

    $('#selectAllUnassigned').click(function() {
        $('.unassignedCheckbox').prop('checked', true);
        $('#checkAllUnassigned').prop('checked', true);
    });

    $('#deselectAllUnassigned').click(function() {
        $('.unassignedCheckbox').prop('checked', false);
        $('#checkAllUnassigned').prop('checked', false);
    });

    // 监听单个复选框变化，更新全选状态
    $('.associatedCheckbox').change(function() {
        var total = $('.associatedCheckbox').length;
        var checked = $('.associatedCheckbox:checked').length;
        $('#checkAllAssociated').prop('checked', total === checked);
    });

    $('.unassignedCheckbox').change(function() {
        var total = $('.unassignedCheckbox').length;
        var checked = $('.unassignedCheckbox:checked').length;
        $('#checkAllUnassigned').prop('checked', total === checked);
    });
});
</script>
<spring:hasBindErrors name="chargeStationForm">
    <div class="error">
        Error while trying to update a charge station:
        <ul>
            <c:forEach var="error" items="${errors.allErrors}">
                <li>${error.defaultMessage}</li>
            </c:forEach>
        </ul>
    </div>
</spring:hasBindErrors>
<script type="text/javascript">
    $(document).ready(function() {
        $('.dateTimePicker').datetimepicker({
            dateFormat: 'yy-mm-dd'
        });
    });
</script>
<div class="content"><div>
    <section><span>
        Charge Station Details
        <a class="tooltip" href="#"><img src="${ctxPath}/static/images/info.png" style="vertical-align:middle">
            <span>Edit charge station details.</span>
        </a>
    </span></section>

    <form:form action="${ctxPath}/manager/chargestations/update" modelAttribute="chargeStationForm">

        <form:hidden path="chargingStationPk" readonly="true"/>
        <table class="userInput">
            <thead><tr><th>Basic Information</th><th></th></thead>
            <tbody>
                <tr>
                    <td>Station Name:</td>
                    <td>
                        <form:input path="stationName" />
                    </td>
                </tr>
                <tr>
                    <td>Operator Name:</td>
                    <td>
                        <form:input path="operatorName" />
                    </td>
                </tr>
                <tr>
                    <td>Assigned User:</td>
                    <td>
                        <c:choose>
                            <c:when test="${chargeStation.firstName != null}">
                                ${chargeStation.firstName} ${chargeStation.lastName} (${chargeStation.email})
                            </c:when>
                            <c:otherwise>
                                <i>Not assigned</i>
                            </c:otherwise>
                        </c:choose>
                    </td>
                </tr>
                <tr>
                    <td>Construction Date:</td>
                    <td>
                        <form:input path="constructionDate" class="dateTimePicker" />
                        <a class="tooltip" href="#"><img src="${ctxPath}/static/images/info.png" style="vertical-align:middle">
                            <span>Format: YYYY-MM-DD HH:MM</span>
                        </a>
                    </td>
                </tr>
                <tr>
                    <td>Operation Date:</td>
                    <td>
                        <form:input path="operationDate" class="dateTimePicker" />
                        <a class="tooltip" href="#"><img src="${ctxPath}/static/images/info.png" style="vertical-align:middle">
                            <span>Format: YYYY-MM-DD HH:MM</span>
                        </a>
                    </td>
                </tr>
                <tr>
                    <td>Location:</td>
                    <td>
                        <form:input path="location" />
                    </td>
                </tr>
                <tr>
                    <td>Notes:</td>
                    <td>
                        <form:textarea path="note" />
                    </td>
                </tr>
            </tbody>
        </table>

        <form:hidden path="address.addressPk" readonly="true"/>
        <table class="userInput">
            <thead><tr><th>Address</th><th></th></thead>
            <tbody>
                <tr><td>Street:</td><td><form:input path="address.street"/></td></tr>
                <tr><td>House Number:</td><td><form:input path="address.houseNumber"/></td></tr>
                <tr><td>Zip Code:</td><td><form:input path="address.zipCode"/></td></tr>
                <tr><td>City:</td><td><form:input path="address.city"/></td></tr>
                <tr><td>Country:</td><td><form:select path="address.country" items="${countryCodes}"/></td></tr>
            </tbody>
        </table>

        <div class="submit-button">
            <input type="submit" name="update" value="Update">
            <input type="submit" name="backToOverview" value="Back to Overview">
        </div>

    </form:form>

    <!-- 关联充电桩列表 -->
    <div class="card">
        <div class="card-header">
            Associated Charge Points
            <span class="badge badge-secondary">${fn:length(associatedChargePoints)}</span>
        </div>
        <div class="card-body">
            <c:choose>
                <c:when test="${empty associatedChargePoints}">
                    <p class="text-muted">No charge points are currently associated with this charging station.</p>
                </c:when>
                <c:otherwise>
                    <!-- 搜索和过滤工具栏 -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" id="searchAssociated" class="form-control" placeholder="Search by Charge Box ID, Description, or Owner...">
                                <div class="input-group-append">
                                    <button class="btn btn-outline-primary" type="button" onclick="filterAssociatedChargePoints()">
                                        <i class="fas fa-search"></i> Search
                                    </button>
                                    <button class="btn btn-outline-secondary" type="button" onclick="clearAssociatedSearch()">
                                        <i class="fas fa-times"></i> Clear
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select id="statusFilterAssociated" class="form-control">
                                <option value="">All Status</option>
                                <option value="online">Online</option>
                                <option value="offline">Offline</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <div class="text-muted small">
                                <div>Total: <span id="totalAssociatedCount">${fn:length(associatedChargePoints)}</span></div>
                                <div>Showing: <span id="visibleAssociatedCount">${fn:length(associatedChargePoints)}</span></div>
                                <div id="associatedSearchStatus" class="text-info" style="display: none;">
                                    <i class="fas fa-search"></i> Search active
                                </div>
                            </div>
                        </div>
                    </div>

                    <form id="batchUnassignForm" action="${ctxPath}/manager/chargestations/batchUnassign" method="post">
                        <input type="hidden" name="stationPk" value="${chargeStation.chargingStationPk}"/>
                        <input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}"/>

                        <!-- 操作按钮 -->
                        <div class="mb-3">
                            <button type="button" id="selectAllAssociated" class="btn btn-sm btn-outline-secondary">Select All Visible</button>
                            <button type="button" id="deselectAllAssociated" class="btn btn-sm btn-outline-secondary">Deselect All</button>
                            <button type="submit" class="btn btn-sm btn-warning" onclick="return confirm('Are you sure you want to unassign selected charge points from this station?')">
                                <i class="fas fa-minus"></i> Unassign Selected (<span id="selectedAssociatedCount">0</span>)
                            </button>
                        </div>

                        <!-- 表格容器，设置最大高度和滚动 -->
                        <div class="table-container" style="max-height: 400px; overflow-y: auto; border: 1px solid #dee2e6; border-radius: 0.375rem;">
                            <table class="table table-striped table-hover mb-0" id="associatedChargePointsTable">
                                <thead class="thead-light sticky-top">
                                    <tr>
                                        <th style="width: 50px;"><input type="checkbox" id="checkAllAssociated"/></th>
                                        <th style="width: 150px;">Charge Box ID</th>
                                        <th>Description</th>
                                        <th style="width: 120px;">OCPP Protocol</th>
                                        <th style="width: 150px;">Last Heartbeat</th>
                                        <th style="width: 80px;">Status</th>
                                        <th style="width: 120px;">Success Rate</th>
                                        <th style="width: 100px;">Owner</th>
                                        <th style="width: 80px;">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <c:forEach items="${associatedChargePoints}" var="cp">
                                        <tr class="associated-row"
                                            data-chargebox-id="${fn:toLowerCase(cp.chargeBoxId)}"
                                            data-description="${fn:toLowerCase(cp.description)}"
                                            data-owner="${fn:toLowerCase(cp.owner)}"
                                            data-status="${cp.online ? 'online' : 'offline'}">
                                            <td>
                                                <input type="checkbox" name="selectedAssociatedChargePoints" value="${cp.chargeBoxPk}" class="associatedCheckbox"/>
                                            </td>
                                            <td>
                                                <a href="${ctxPath}/manager/chargepoints/details/${cp.chargeBoxId}" class="text-decoration-none">
                                                    <strong>${cp.chargeBoxId}</strong>
                                                </a>
                                            </td>
                                            <td class="text-muted">${cp.description}</td>
                                            <td>
                                                <span class="badge badge-info">${cp.ocppProtocol}</span>
                                            </td>
                                            <td class="text-muted small">${cp.lastHeartbeatTimestamp}</td>
                                            <td>
                                                <c:choose>
                                                    <c:when test="${cp.online}">
                                                        <span class="badge badge-success">Online</span>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <span class="badge badge-secondary">Offline</span>
                                                    </c:otherwise>
                                                </c:choose>
                                            </td>
                                            <td>
                                                <span class="badge badge-info small">
                                                    ${cp.successRate} (${cp.successfulChargingSessions}/${cp.totalChargingSessions})
                                                </span>
                                            </td>
                                            <td class="text-muted small">${cp.owner}</td>
                                            <td>
                                                <a href="${ctxPath}/manager/chargepoints/details/${cp.chargeBoxId}"
                                                   class="btn btn-sm btn-outline-primary">Edit</a>
                                            </td>
                                        </tr>
                                    </c:forEach>
                                </tbody>
                            </table>
                        </div>

                        <!-- 没有找到结果的提示 -->
                        <div id="noAssociatedResults" class="text-center text-muted py-4" style="display: none;">
                            <i class="fas fa-search fa-2x mb-2"></i>
                            <p>No charge points found matching your search criteria.</p>
                        </div>
                    </form>
                </c:otherwise>
            </c:choose>
        </div>
    </div>

    <!-- 批量分配充电桩 -->
    <div class="card">
        <div class="card-header">
            Assign Charge Points to This Station
        </div>
        <div class="card-body">
            <c:choose>
                <c:when test="${empty unassignedChargePoints}">
                    <p class="text-muted">No unassigned charge points available.</p>
                </c:when>
                <c:otherwise>
                    <!-- 搜索和过滤工具栏 -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" id="searchUnassigned" class="form-control" placeholder="Search by Charge Box ID or Description...">
                                <div class="input-group-append">
                                    <button class="btn btn-outline-primary" type="button" onclick="filterUnassignedChargePoints()">
                                        <i class="fas fa-search"></i> Search
                                    </button>
                                    <button class="btn btn-outline-secondary" type="button" onclick="clearUnassignedSearch()">
                                        <i class="fas fa-times"></i> Clear
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select id="protocolFilterUnassigned" class="form-control">
                                <option value="">All Protocols</option>
                                <option value="V_16_SOAP">OCPP 1.6 SOAP</option>
                                <option value="V_16_JSON">OCPP 1.6 JSON</option>
                                <option value="V_15_SOAP">OCPP 1.5 SOAP</option>
                                <option value="V_12_SOAP">OCPP 1.2 SOAP</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <div class="text-muted small">
                                <div>Total: <span id="totalUnassignedCount">${fn:length(unassignedChargePoints)}</span></div>
                                <div>Showing: <span id="visibleUnassignedCount">${fn:length(unassignedChargePoints)}</span></div>
                                <div id="unassignedSearchStatus" class="text-info" style="display: none;">
                                    <i class="fas fa-search"></i> Search active
                                </div>
                            </div>
                        </div>
                    </div>

                    <form id="batchAssignForm" action="${ctxPath}/manager/chargestations/batchAssign" method="post">
                        <input type="hidden" name="stationPk" value="${chargeStation.chargingStationPk}"/>
                        <input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}"/>

                        <!-- 操作按钮 -->
                        <div class="mb-3">
                            <button type="button" id="selectAllUnassigned" class="btn btn-sm btn-outline-secondary">Select All Visible</button>
                            <button type="button" id="deselectAllUnassigned" class="btn btn-sm btn-outline-secondary">Deselect All</button>
                            <button type="submit" class="btn btn-sm btn-success" onclick="return confirm('Are you sure you want to assign selected charge points to this station?')">
                                <i class="fas fa-plus"></i> Assign Selected (<span id="selectedUnassignedCount">0</span>)
                            </button>
                        </div>

                        <!-- 表格容器，设置最大高度和滚动 -->
                        <div class="table-container" style="max-height: 400px; overflow-y: auto; border: 1px solid #dee2e6; border-radius: 0.375rem;">
                            <table class="table table-striped table-hover mb-0" id="unassignedChargePointsTable">
                                <thead class="thead-light sticky-top">
                                    <tr>
                                        <th style="width: 50px;"><input type="checkbox" id="checkAllUnassigned"/></th>
                                        <th style="width: 200px;">Charge Box ID</th>
                                        <th>Description</th>
                                        <th style="width: 150px;">OCPP Protocol</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <c:forEach items="${unassignedChargePoints}" var="cp">
                                        <tr class="unassigned-row" data-chargebox-id="${fn:toLowerCase(cp.chargeBoxId)}" data-description="${fn:toLowerCase(cp.description)}" data-protocol="${cp.ocppProtocol}">
                                            <td>
                                                <input type="checkbox" name="selectedChargePoints" value="${cp.chargeBoxId}" class="unassignedCheckbox"/>
                                            </td>
                                            <td>
                                                <a href="${ctxPath}/manager/chargepoints/details/${cp.chargeBoxId}" class="text-decoration-none">
                                                    <strong>${cp.chargeBoxId}</strong>
                                                </a>
                                            </td>
                                            <td class="text-muted">${cp.description}</td>
                                            <td>
                                                <span class="badge badge-info">${cp.ocppProtocol}</span>
                                            </td>
                                        </tr>
                                    </c:forEach>
                                </tbody>
                            </table>
                        </div>

                        <!-- 没有找到结果的提示 -->
                        <div id="noUnassignedResults" class="text-center text-muted py-4" style="display: none;">
                            <i class="fas fa-search fa-2x mb-2"></i>
                            <p>No charge points found matching your search criteria.</p>
                        </div>
                    </form>
                </c:otherwise>
            </c:choose>
        </div>
    </div>
</div></div>

<script>
$(document).ready(function() {
    // ===== 已关联充电桩的搜索和过滤功能 =====
    window.filterAssociatedChargePoints = function() {
        const searchTerm = $('#searchAssociated').val().toLowerCase();
        const statusFilter = $('#statusFilterAssociated').val();
        let visibleCount = 0;

        console.log('Filtering associated charge points:', {
            searchTerm: searchTerm,
            statusFilter: statusFilter,
            totalRows: $('.associated-row').length
        });

        $('.associated-row').each(function() {
            const $row = $(this);
            const chargeBoxId = String($row.data('chargebox-id') || '').toLowerCase();
            const description = String($row.data('description') || '').toLowerCase();
            const owner = String($row.data('owner') || '').toLowerCase();
            const status = $row.data('status');

            const matchesSearch = !searchTerm ||
                chargeBoxId.includes(searchTerm) ||
                description.includes(searchTerm) ||
                owner.includes(searchTerm);
            const matchesStatus = !statusFilter || status === statusFilter;

            if (matchesSearch && matchesStatus) {
                $row.show();
                // 重新启用可见行的复选框
                $row.find('.associatedCheckbox').prop('disabled', false);
                visibleCount++;
            } else {
                $row.hide();
                // 取消隐藏行的选中状态和禁用复选框
                const checkbox = $row.find('.associatedCheckbox');
                checkbox.prop('checked', false);
                checkbox.prop('disabled', true);
                $row.removeClass('selected');
            }
        });

        // 更新计数
        $('#visibleAssociatedCount').text(visibleCount);

        // 显示搜索状态
        const hasActiveSearch = searchTerm || statusFilter;
        $('#associatedSearchStatus').toggle(hasActiveSearch);

        // 显示/隐藏无结果提示
        if (visibleCount === 0) {
            $('#associatedChargePointsTable').hide();
            $('#noAssociatedResults').show();
        } else {
            $('#associatedChargePointsTable').show();
            $('#noAssociatedResults').hide();
        }

        // 更新选中计数
        updateAssociatedSelectedCount();
    }

    // 清除已关联充电桩搜索
    window.clearAssociatedSearch = function() {
        console.log('Clearing associated search');
        $('#searchAssociated').val('');
        $('#statusFilterAssociated').val('');
        filterAssociatedChargePoints();
    }

    // 更新已关联充电桩选中计数
    function updateAssociatedSelectedCount() {
        const selectedCount = $('.associated-row:visible .associatedCheckbox:checked').length;
        $('#selectedAssociatedCount').text(selectedCount);

        // 更新全选复选框状态
        const visibleCheckboxes = $('.associated-row:visible .associatedCheckbox');
        const checkedCheckboxes = $('.associated-row:visible .associatedCheckbox:checked');

        if (visibleCheckboxes.length === 0) {
            $('#checkAllAssociated').prop('indeterminate', false).prop('checked', false);
        } else if (checkedCheckboxes.length === visibleCheckboxes.length) {
            $('#checkAllAssociated').prop('indeterminate', false).prop('checked', true);
        } else if (checkedCheckboxes.length > 0) {
            $('#checkAllAssociated').prop('indeterminate', true);
        } else {
            $('#checkAllAssociated').prop('indeterminate', false).prop('checked', false);
        }
    }

    // ===== 未分配充电桩的搜索和过滤功能 =====
    window.filterUnassignedChargePoints = function() {
        const searchTerm = $('#searchUnassigned').val().toLowerCase();
        const protocolFilter = $('#protocolFilterUnassigned').val();
        let visibleCount = 0;

        console.log('Filtering unassigned charge points:', {
            searchTerm: searchTerm,
            protocolFilter: protocolFilter,
            totalRows: $('.unassigned-row').length
        });

        $('.unassigned-row').each(function() {
            const $row = $(this);
            const chargeBoxId = String($row.data('chargebox-id') || '').toLowerCase();
            const description = String($row.data('description') || '').toLowerCase();
            const protocol = $row.data('protocol');

            const matchesSearch = !searchTerm ||
                chargeBoxId.includes(searchTerm) ||
                description.includes(searchTerm);
            const matchesProtocol = !protocolFilter || protocol === protocolFilter;

            if (matchesSearch && matchesProtocol) {
                $row.show();
                // 重新启用可见行的复选框
                $row.find('.unassignedCheckbox').prop('disabled', false);
                visibleCount++;
            } else {
                $row.hide();
                // 取消隐藏行的选中状态和禁用复选框
                const checkbox = $row.find('.unassignedCheckbox');
                checkbox.prop('checked', false);
                checkbox.prop('disabled', true);
                $row.removeClass('selected');
            }
        });

        // 更新计数
        $('#visibleUnassignedCount').text(visibleCount);

        // 显示搜索状态
        const hasActiveSearch = searchTerm || protocolFilter;
        $('#unassignedSearchStatus').toggle(hasActiveSearch);

        // 显示/隐藏无结果提示
        if (visibleCount === 0) {
            $('#unassignedChargePointsTable').hide();
            $('#noUnassignedResults').show();
        } else {
            $('#unassignedChargePointsTable').show();
            $('#noUnassignedResults').hide();
        }

        // 更新选中计数
        updateUnassignedSelectedCount();
    }

    // 清除未分配充电桩搜索
    window.clearUnassignedSearch = function() {
        console.log('Clearing unassigned search');
        $('#searchUnassigned').val('');
        $('#protocolFilterUnassigned').val('');
        filterUnassignedChargePoints();
    }

    // 更新未分配充电桩选中计数
    function updateUnassignedSelectedCount() {
        const selectedCount = $('.unassigned-row:visible .unassignedCheckbox:checked').length;
        $('#selectedUnassignedCount').text(selectedCount);

        // 更新全选复选框状态
        const visibleCheckboxes = $('.unassigned-row:visible .unassignedCheckbox');
        const checkedCheckboxes = $('.unassigned-row:visible .unassignedCheckbox:checked');

        if (visibleCheckboxes.length === 0) {
            $('#checkAllUnassigned').prop('indeterminate', false).prop('checked', false);
        } else if (checkedCheckboxes.length === visibleCheckboxes.length) {
            $('#checkAllUnassigned').prop('indeterminate', false).prop('checked', true);
        } else if (checkedCheckboxes.length > 0) {
            $('#checkAllUnassigned').prop('indeterminate', true);
        } else {
            $('#checkAllUnassigned').prop('indeterminate', false).prop('checked', false);
        }
    }

    // ===== 已关联充电桩事件绑定 =====
    // 搜索框事件（实时搜索）
    $('#searchAssociated').on('input', filterAssociatedChargePoints);

    // 搜索框回车键事件
    $('#searchAssociated').on('keypress', function(e) {
        if (e.which === 13) { // Enter key
            filterAssociatedChargePoints();
        }
    });

    // 状态过滤器事件
    $('#statusFilterAssociated').on('change', filterAssociatedChargePoints);

    // 全选/取消全选（只对可见行）
    $('#checkAllAssociated').on('change', function() {
        const isChecked = $(this).prop('checked');
        $('.associated-row:visible .associatedCheckbox').prop('checked', isChecked);
        $('.associated-row:visible').toggleClass('selected', isChecked);
        updateAssociatedSelectedCount();
    });

    // 单个复选框变化
    $(document).on('change', '.associatedCheckbox', function() {
        updateAssociatedSelectedCount();
        // 高亮选中的行
        $(this).closest('tr').toggleClass('selected', $(this).prop('checked'));
    });

    // Select All Visible 按钮
    $('#selectAllAssociated').on('click', function() {
        $('.associated-row:visible .associatedCheckbox').prop('checked', true);
        $('.associated-row:visible').addClass('selected');
        updateAssociatedSelectedCount();
    });

    // Deselect All 按钮
    $('#deselectAllAssociated').on('click', function() {
        $('.associatedCheckbox').prop('checked', false);
        $('.associated-row').removeClass('selected');
        updateAssociatedSelectedCount();
    });

    // ===== 未分配充电桩事件绑定 =====
    // 搜索框事件（实时搜索）
    $('#searchUnassigned').on('input', filterUnassignedChargePoints);

    // 搜索框回车键事件
    $('#searchUnassigned').on('keypress', function(e) {
        if (e.which === 13) { // Enter key
            filterUnassignedChargePoints();
        }
    });

    // 协议过滤器事件
    $('#protocolFilterUnassigned').on('change', filterUnassignedChargePoints);

    // 全选/取消全选（只对可见行）
    $('#checkAllUnassigned').on('change', function() {
        const isChecked = $(this).prop('checked');
        $('.unassigned-row:visible .unassignedCheckbox').prop('checked', isChecked);
        $('.unassigned-row:visible').toggleClass('selected', isChecked);
        updateUnassignedSelectedCount();
    });

    // 单个复选框变化
    $(document).on('change', '.unassignedCheckbox', function() {
        updateUnassignedSelectedCount();
        // 高亮选中的行
        $(this).closest('tr').toggleClass('selected', $(this).prop('checked'));
    });

    // Select All Visible 按钮
    $('#selectAllUnassigned').on('click', function() {
        $('.unassigned-row:visible .unassignedCheckbox').prop('checked', true);
        $('.unassigned-row:visible').addClass('selected');
        updateUnassignedSelectedCount();
    });

    // Deselect All 按钮
    $('#deselectAllUnassigned').on('click', function() {
        $('.unassignedCheckbox').prop('checked', false);
        $('.unassigned-row').removeClass('selected');
        updateUnassignedSelectedCount();
    });

    // ===== 表单提交验证 =====
    // 确保只提交可见且选中的复选框
    $('#batchAssignForm').on('submit', function(e) {
        // 禁用所有隐藏行的复选框，确保它们不会被提交
        $('.unassigned-row:hidden .unassignedCheckbox').prop('disabled', true);

        // 检查是否有选中的可见项
        const visibleSelected = $('.unassigned-row:visible .unassignedCheckbox:checked').length;
        if (visibleSelected === 0) {
            e.preventDefault();
            alert('Please select at least one charge point to assign.');
            return false;
        }

        return true;
    });

    $('#batchUnassignForm').on('submit', function(e) {
        // 禁用所有隐藏行的复选框，确保它们不会被提交
        $('.associated-row:hidden .associatedCheckbox').prop('disabled', true);

        // 检查是否有选中的可见项
        const visibleSelected = $('.associated-row:visible .associatedCheckbox:checked').length;
        if (visibleSelected === 0) {
            e.preventDefault();
            alert('Please select at least one charge point to unassign.');
            return false;
        }

        return true;
    });

    // ===== 初始化检查 =====
    console.log('Page initialization:', {
        associatedRows: $('.associated-row').length,
        unassignedRows: $('.unassigned-row').length,
        searchInputAssociated: $('#searchAssociated').length,
        searchInputUnassigned: $('#searchUnassigned').length,
        statusFilter: $('#statusFilterAssociated').length,
        protocolFilter: $('#protocolFilterUnassigned').length
    });

    // ===== 初始化 =====
    updateAssociatedSelectedCount();
    updateUnassignedSelectedCount();
});
</script>

<%@ include file="../00-footer.jsp" %>