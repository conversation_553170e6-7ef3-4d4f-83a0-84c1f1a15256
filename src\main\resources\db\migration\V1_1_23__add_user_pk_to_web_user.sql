-- Add user_pk field to web_user table to establish association with user table
-- This solves the design issue of missing direct association between the two tables

-- 1. Add user_pk field
ALTER TABLE web_user ADD COLUMN user_pk INT DEFAULT NULL;

-- 2. Add foreign key constraint
ALTER TABLE web_user ADD CONSTRAINT FK_web_user_user_pk 
    FOREIGN KEY (user_pk) REFERENCES user (user_pk) 
    ON DELETE SET NULL ON UPDATE CASCADE;

-- 3. Add index for better query performance
CREATE INDEX idx_web_user_user_pk ON web_user (user_pk);

-- 4. Data migration: Try to establish associations based on existing heuristic logic
-- Only attempt to establish associations for OPERATOR_OWNER role users

-- 4.1 Exact match: firstName + lastName = username
UPDATE web_user w 
SET user_pk = (
    SELECT u.user_pk 
    FROM user u 
    WHERE LOWER(CONCAT(u.first_name, u.last_name)) = LOWER(w.username)
    LIMIT 1
)
WHERE w.user_role = 'OPERATOR_OWNER' 
  AND w.user_pk IS NULL;

-- 4.2 Underscore separated match: firstName_lastName = username  
UPDATE web_user w 
SET user_pk = (
    SELECT u.user_pk 
    FROM user u 
    WHERE LOWER(CONCAT(u.first_name, '_', u.last_name)) = LOWER(w.username)
    LIMIT 1
)
WHERE w.user_role = 'OPERATOR_OWNER' 
  AND w.user_pk IS NULL;

-- 4.3 Space separated match: firstName lastName = username
UPDATE web_user w 
SET user_pk = (
    SELECT u.user_pk 
    FROM user u 
    WHERE LOWER(CONCAT(u.first_name, ' ', u.last_name)) = LOWER(w.username)
    LIMIT 1
)
WHERE w.user_role = 'OPERATOR_OWNER' 
  AND w.user_pk IS NULL;

-- 4.4 First name only match (last attempt)
UPDATE web_user w 
SET user_pk = (
    SELECT u.user_pk 
    FROM user u 
    WHERE LOWER(u.first_name) = LOWER(w.username)
    LIMIT 1
)
WHERE w.user_role = 'OPERATOR_OWNER' 
  AND w.user_pk IS NULL;

-- Migration completed successfully
