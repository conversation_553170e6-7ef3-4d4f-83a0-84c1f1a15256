package de.rwth.idsg.steve.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 充电桩标识映射服务测试
 * 验证OCPP标识和维护标识之间的映射功能
 */
class ChargePointIdentityMappingServiceTest {

    private ChargePointIdentityMappingService mappingService;

    @BeforeEach
    void setUp() {
        mappingService = new ChargePointIdentityMappingService();
    }

    @Test
    @DisplayName("测试基本映射功能")
    void testBasicMapping() {
        // 添加映射
        mappingService.addMapping("CP001", "CP002");
        
        // 验证正向映射
        assertEquals("CP002", mappingService.getDevOpsIdentity("CP001"));
        
        // 验证反向映射
        assertEquals("CP001", mappingService.getOcppIdentity("CP002"));
        
        // 验证映射存在性检查
        assertTrue(mappingService.hasOcppMapping("CP001"));
        assertTrue(mappingService.hasDevOpsMapping("CP002"));
    }

    @Test
    @DisplayName("测试动态学习模式初始化")
    void testDynamicLearningInitialization() {
        // 验证初始状态没有预配置的映射
        assertEquals(0, mappingService.getMappingCount());
        assertFalse(mappingService.hasOcppMapping("CP001"));
    }

    @Test
    @DisplayName("测试无映射情况")
    void testNoMapping() {
        // 对于没有映射的标识，应该返回原标识
        assertEquals("CP999", mappingService.getDevOpsIdentity("CP999"));
        assertEquals("CP888", mappingService.getOcppIdentity("CP888"));
        
        // 验证映射不存在
        assertFalse(mappingService.hasOcppMapping("CP999"));
        assertFalse(mappingService.hasDevOpsMapping("CP888"));
    }

    @Test
    @DisplayName("测试映射移除")
    void testRemoveMapping() {
        // 添加映射
        mappingService.addMapping("CP003", "CP004");
        assertTrue(mappingService.hasOcppMapping("CP003"));
        
        // 移除映射
        mappingService.removeMapping("CP003");
        assertFalse(mappingService.hasOcppMapping("CP003"));
        assertFalse(mappingService.hasDevOpsMapping("CP004"));
        
        // 验证移除后返回原标识
        assertEquals("CP003", mappingService.getDevOpsIdentity("CP003"));
    }

    @Test
    @DisplayName("测试动态学习功能")
    void testDynamicLearning() {
        // 初始状态没有映射
        assertEquals(0, mappingService.getMappingCount());

        // 第一次遇到不匹配的情况，应该自动学习
        String result = mappingService.resolveChargePointIdentity("CP001_diagnostics_20240101.zip", "CP002");
        assertEquals("CP002", result);

        // 验证已经学习了映射关系
        assertEquals(1, mappingService.getMappingCount());
        assertTrue(mappingService.hasOcppMapping("CP001"));
        assertEquals("CP002", mappingService.getDevOpsIdentity("CP001"));

        // 第二次遇到相同情况，应该使用已学习的映射
        result = mappingService.resolveChargePointIdentity("CP001_diagnostics_20240102.zip", "CP002");
        assertEquals("CP002", result);

        // 映射数量不应该增加
        assertEquals(1, mappingService.getMappingCount());
    }

    @Test
    @DisplayName("测试批量映射操作")
    void testBatchMappings() {
        Map<String, String> batchMappings = Map.of(
            "CP005", "CP006",
            "CP007", "CP008",
            "CP009", "CP010"
        );
        
        mappingService.addMappings(batchMappings);
        
        // 验证所有映射都已添加
        for (Map.Entry<String, String> entry : batchMappings.entrySet()) {
            assertEquals(entry.getValue(), mappingService.getDevOpsIdentity(entry.getKey()));
        }
        
        // 验证映射数量
        assertTrue(mappingService.getMappingCount() >= batchMappings.size());
    }

    @Test
    @DisplayName("测试清除所有映射")
    void testClearAllMappings() {
        // 添加一些映射
        mappingService.addMapping("CP011", "CP012");
        mappingService.addMapping("CP013", "CP014");
        
        assertTrue(mappingService.getMappingCount() > 0);
        
        // 清除所有映射
        mappingService.clearAllMappings();
        
        assertEquals(0, mappingService.getMappingCount());
        assertFalse(mappingService.hasOcppMapping("CP011"));
    }

    @Test
    @DisplayName("测试空值处理")
    void testNullHandling() {
        // 测试null输入
        assertNull(mappingService.getDevOpsIdentity(null));
        assertNull(mappingService.getOcppIdentity(null));
        
        // 测试添加null映射
        mappingService.addMapping(null, "CP002");
        mappingService.addMapping("CP001", null);
        
        // 验证null映射不会被添加
        assertFalse(mappingService.hasOcppMapping(null));
    }

    @Test
    @DisplayName("测试复杂标识映射")
    void testComplexIdentityMapping() {
        // 测试MAC地址格式的映射
        mappingService.addMapping("A25A58A962D9", "CP005");
        mappingService.addMapping("B36B69C073E1", "CP001");
        
        assertEquals("CP005", mappingService.getDevOpsIdentity("A25A58A962D9"));
        assertEquals("CP001", mappingService.getDevOpsIdentity("B36B69C073E1"));
        
        // 测试智能解析
        String result = mappingService.resolveChargePointIdentity("A25A58A962D9_diag.zip", "CP005");
        assertEquals("CP005", result);
    }

    @Test
    @DisplayName("测试映射统计信息")
    void testMappingStats() {
        int initialCount = mappingService.getMappingCount();
        
        mappingService.addMapping("TEST001", "TEST002");
        assertEquals(initialCount + 1, mappingService.getMappingCount());
        
        Map<String, String> allMappings = mappingService.getAllMappings();
        assertTrue(allMappings.containsKey("TEST001"));
        assertEquals("TEST002", allMappings.get("TEST001"));
    }

    @Test
    @DisplayName("测试用户场景：动态学习CP001文件名但连接为CP002")
    void testUserScenarioDynamicLearning() {
        // 初始状态没有映射
        assertEquals(0, mappingService.getMappingCount());

        // 模拟用户描述的场景：充电桩连接使用CP002，但文件名包含CP001
        String fileName = "CP001_diagnostics_20240128_143000.zip";
        String connectedId = "CP002";

        String resolvedId = mappingService.resolveChargePointIdentity(fileName, connectedId);

        // 应该解析为连接的维护标识CP002，并自动学习映射
        assertEquals("CP002", resolvedId);

        // 验证已经自动学习了映射关系
        assertEquals(1, mappingService.getMappingCount());
        assertEquals("CP002", mappingService.getDevOpsIdentity("CP001"));
        assertEquals("CP001", mappingService.getOcppIdentity("CP002"));

        // 后续相同的情况应该直接使用学习到的映射
        String secondResult = mappingService.resolveChargePointIdentity("CP001_diagnostics_20240129.zip", "CP002");
        assertEquals("CP002", secondResult);
        assertEquals(1, mappingService.getMappingCount()); // 映射数量不变
    }

    @Test
    @DisplayName("测试多个充电桩的动态学习场景")
    void testMultipleChargePointDynamicLearning() {
        // 初始状态没有映射
        assertEquals(0, mappingService.getMappingCount());

        // 模拟多个不同充电桩的动态学习过程
        assertEquals("CP002", mappingService.resolveChargePointIdentity("CP001_diag.zip", "CP002"));
        assertEquals("CP004", mappingService.resolveChargePointIdentity("CP003_diag.zip", "CP004"));
        assertEquals("CP006", mappingService.resolveChargePointIdentity("CP005_diag.zip", "CP006"));

        // 验证已经学习了3个映射关系
        assertEquals(3, mappingService.getMappingCount());
        assertEquals("CP002", mappingService.getDevOpsIdentity("CP001"));
        assertEquals("CP004", mappingService.getDevOpsIdentity("CP003"));
        assertEquals("CP006", mappingService.getDevOpsIdentity("CP005"));

        // 测试已学习的映射会被正确使用
        assertEquals("CP002", mappingService.resolveChargePointIdentity("CP001_diag2.zip", "CP002"));
        assertEquals(3, mappingService.getMappingCount()); // 映射数量不变

        // 测试不会产生错误的交叉映射
        assertEquals("CP002", mappingService.resolveChargePointIdentity("CP001_diag.zip", "CP002"));
        assertNotEquals("CP004", mappingService.resolveChargePointIdentity("CP001_diag.zip", "CP002"));
    }
}
