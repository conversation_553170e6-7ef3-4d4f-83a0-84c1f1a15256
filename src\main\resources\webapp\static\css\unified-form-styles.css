/**
 * 统一表单样式系统
 * 解决EVSE_OMS项目中的表单布局和对齐问题
 */

/* ===== 基础重置和全局样式 ===== */
* {
    box-sizing: border-box;
}

/* ===== Bootstrap网格系统补充 ===== */
.container {
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px;
}

.col-md-4 {
    position: relative;
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
}

.col-md-6 {
    position: relative;
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
    flex: 0 0 50%;
    max-width: 50%;
}

.col-md-12 {
    position: relative;
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
    flex: 0 0 100%;
    max-width: 100%;
}

/* 响应式断点 */
@media (max-width: 768px) {
    .col-md-4,
    .col-md-6 {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

/* ===== 搜索面板统一样式 ===== */
.search-panel {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

.search-panel .form-group {
    margin-bottom: 20px;
    display: flex;
    flex-direction: column;
}

.search-panel label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    font-size: 14px;
    line-height: 1.4;
}

/* ===== 表单控件统一样式 ===== */
.form-control {
    display: block;
    width: 100%;
    height: 38px;
    padding: 8px 12px;
    font-size: 14px;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: 4px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    box-sizing: border-box;
    vertical-align: middle;
}

/* 确保所有输入类型都有统一高度 */
input[type="text"].form-control,
input[type="number"].form-control,
input[type="password"].form-control,
input[type="email"].form-control,
input[type="date"].form-control,
input[type="datetime-local"].form-control,
select.form-control,
textarea.form-control {
    height: 38px;
    padding: 8px 12px;
    line-height: 1.5;
    box-sizing: border-box;
}

/* 文本域特殊处理 */
textarea.form-control {
    height: auto;
    min-height: 38px;
    resize: vertical;
}

.form-control:focus {
    color: #495057;
    background-color: #fff;
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-control::placeholder {
    color: #6c757d;
    opacity: 1;
}

.form-control:disabled,
.form-control[readonly] {
    background-color: #e9ecef;
    opacity: 1;
}

/* 日期输入框特殊样式 */
input[type="date"].form-control {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: textfield;
}

/* ===== 按钮统一样式 ===== */
.btn {
    display: inline-block;
    font-weight: 400;
    color: #212529;
    text-align: center;
    vertical-align: middle;
    user-select: none;
    background-color: transparent;
    border: 1px solid transparent;
    padding: 8px 16px;
    font-size: 14px;
    line-height: 1.5;
    border-radius: 4px;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    cursor: pointer;
    text-decoration: none;
    height: 38px;
    box-sizing: border-box;
    white-space: nowrap;
    min-width: 80px;
}

/* 确保所有按钮类型都有统一样式 */
input[type="submit"],
input[type="button"],
input[type="reset"],
button {
    height: 38px;
    padding: 8px 16px;
    font-size: 14px;
    line-height: 1.5;
    box-sizing: border-box;
    vertical-align: middle;
    border-radius: 4px;
    cursor: pointer;
    white-space: nowrap;
    min-width: 80px;
}

.btn:hover {
    text-decoration: none;
}

.btn:focus {
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn-primary {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary:hover {
    color: #fff;
    background-color: #0069d9;
    border-color: #0062cc;
}

.btn-primary:focus {
    box-shadow: 0 0 0 0.2rem rgba(38, 143, 255, 0.5);
}

.btn-success {
    color: #fff;
    background-color: #28a745;
    border-color: #28a745;
}

.btn-success:hover {
    color: #fff;
    background-color: #218838;
    border-color: #1e7e34;
}

.btn-info {
    color: #fff;
    background-color: #17a2b8;
    border-color: #17a2b8;
}

.btn-info:hover {
    color: #fff;
    background-color: #138496;
    border-color: #117a8b;
}

.btn-standard {
    color: #495057;
    background-color: #f8f9fa;
    border-color: #dee2e6;
}

.btn-standard:hover {
    color: #495057;
    background-color: #e2e6ea;
    border-color: #dae0e5;
}

/* ===== 按钮组和对齐 ===== */
.d-flex {
    display: flex !important;
}

.align-items-end {
    align-items: flex-end !important;
}

.align-items-center {
    align-items: center !important;
}

.justify-content-end {
    justify-content: flex-end !important;
}

.ms-2 {
    margin-left: 0.5rem !important;
}

.flex-grow-1 {
    flex-grow: 1 !important;
}

/* ===== 间距工具类 ===== */
.mb-3 {
    margin-bottom: 1rem !important;
}

.mb-4 {
    margin-bottom: 1.5rem !important;
}

.mt-2 {
    margin-top: 0.5rem !important;
}

.mt-3 {
    margin-top: 1rem !important;
}

/* ===== 搜索面板按钮区域特殊处理 ===== */
.search-panel .btn-group {
    display: flex;
    gap: 8px;
    margin-top: 8px;
}

.search-panel .btn-group .btn {
    min-width: 100px;
    flex: 1;
}

/* ===== 表格样式优化 ===== */
.table-responsive {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.table {
    width: 100%;
    margin-bottom: 1rem;
    color: #212529;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 12px;
    vertical-align: middle;
    border-top: 1px solid #dee2e6;
}

.table thead th {
    vertical-align: bottom;
    border-bottom: 2px solid #dee2e6;
    background-color: #f8f9fa;
    font-weight: 600;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.05);
}

.table-hover tbody tr:hover {
    color: #212529;
    background-color: rgba(0, 0, 0, 0.075);
}

.table-bordered {
    border: 1px solid #dee2e6;
}

.table-bordered th,
.table-bordered td {
    border: 1px solid #dee2e6;
}

/* ===== 文本对齐工具类 ===== */
.text-center {
    text-align: center !important;
}

.text-left {
    text-align: left !important;
}

.text-right {
    text-align: right !important;
}

/* ===== 特殊修复：搜索表单对齐 ===== */
.search-panel .row:last-child .form-group {
    display: flex;
    align-items: flex-end;
    height: 100%;
    margin-bottom: 0;
}

.search-panel .row:last-child .form-group .btn {
    margin-top: 0;
}
