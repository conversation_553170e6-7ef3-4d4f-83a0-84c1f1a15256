/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.web.dto;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Charge Point Fault Form
 */
@Getter
@Setter
public class ChargerIssueForm {
    
    private Integer issueId;
    
    private Integer chargeBoxPk;
    
    @NotBlank(message = "Please select a charge point")
    private String chargeBoxId;
    
    @NotBlank(message = "Please enter fault description")
    private String faultDescription;
    
    private String ocppErrorCode;

    private ChargerIssueStatus status;

    private Boolean isAutoReported;

    private List<MultipartFile> images = new ArrayList<>();

    private String resolveDescription;

    private Date reportTime;

    private String vendorErrorCode;

    private String vendorId;
} 