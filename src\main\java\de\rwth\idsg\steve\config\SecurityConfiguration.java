/*
 * SteV<PERSON> - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.config;

import de.rwth.idsg.steve.web.security.ChargeBoxOwnerAuthorizationInterceptor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.security.access.hierarchicalroles.RoleHierarchy;
import org.springframework.security.access.hierarchicalroles.RoleHierarchyImpl;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.factory.PasswordEncoderFactories;
import org.springframework.security.crypto.password.DelegatingPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.www.BasicAuthenticationFilter;

import static de.rwth.idsg.steve.SteveConfiguration.CONFIG;

/**
 * <AUTHOR> Goekay <<EMAIL>>
 * @since 07.01.2015
 */
@Slf4j
@RequiredArgsConstructor
@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true)
public class SecurityConfiguration {

    private final ChargeBoxOwnerAuthorizationInterceptor chargeBoxOwnerAuthorizationInterceptor;

    /**
     * Define role hierarchy:
     * - ADMIN can do everything
     * - OPERATOR_FACTORY can do everything except user management
     * - OPERATOR_OWNER can only manage assigned charge boxes
     */
    @Bean
    public RoleHierarchy roleHierarchy() {
        RoleHierarchyImpl roleHierarchy = new RoleHierarchyImpl();
        String hierarchy = "ADMIN > OPERATOR_FACTORY > OPERATOR_OWNER";
        roleHierarchy.setHierarchy(hierarchy);
        return roleHierarchy;
    }

    /**
     * Password encoding changed with spring-security 5.0.0. We either have to use a prefix before the password to
     * indicate which actual encoder {@link DelegatingPasswordEncoder} should use [1, 2] or specify the encoder as we do.
     *
     * [1] https://spring.io/blog/2017/11/01/spring-security-5-0-0-rc1-released#password-storage-format
     * [2] {@link PasswordEncoderFactories#createDelegatingPasswordEncoder()}
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return CONFIG.getAuth().getPasswordEncoder();
    }

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        final String prefix = CONFIG.getSpringManagerMapping();

        return http
            .authorizeHttpRequests(
                req -> req
                    .requestMatchers(
                        "/static/**",
                        CONFIG.getCxfMapping() + "/**",
                        WebSocketConfiguration.PATH_INFIX + "**",
                        "/WEB-INF/views/**" // https://github.com/spring-projects/spring-security/issues/13285#issuecomment-1579097065
                    ).permitAll()
                    // Admin can access everything
                    .requestMatchers(prefix + "/users/**").hasAuthority("ADMIN")
                    // Only Admin can access these restricted pages
                    .requestMatchers(prefix + "/ocppTags/**",
                                    prefix + "/chargingProfiles/**",
                                    prefix + "/reservations/**",
                                    prefix + "/transactions/**",
                                    prefix + "/data-man/connectorStatus/**",
                                    prefix + "/operations/v1.6/**",
                                    prefix + "/chargepoints/import",
                                    prefix + "/chargepoints/import/progress/*").hasAuthority("ADMIN")
                    // Admin and Factory Operator can access these
                    .requestMatchers(prefix + "/ownerAssignments/**",
                                    prefix + "/operations/faults/**",
                                    prefix + "/notification/**",
                                    prefix + "/settings/**",
                                    prefix + "/log/**",
                                    prefix + "/about/**").hasAnyAuthority("ADMIN", "OPERATOR_FACTORY")
                    // API endpoints for authenticated users
                    .requestMatchers(prefix + "/api/**").authenticated()
                    // All authenticated users can access these
                    .requestMatchers(prefix + "/**").authenticated()
            )
            // SOAP stations are making POST calls for communication. even though the following path is permitted for
            // all access, there is a global default behaviour from spring security: enable CSRF for all POSTs.
            // we need to disable CSRF for SOAP paths explicitly.
            .csrf(c -> c.ignoringRequestMatchers(
                CONFIG.getCxfMapping() + "/**",
                prefix + "/operations/report-fault",  // Allow file uploads in fault report submission forms
                prefix + "/chargepoints/import", // Allow file uploads for bulk import
                prefix + "/chargepoints/delete/**", // Disable CSRF for delete operations
                prefix + "/api/**" // Disable CSRF for API endpoints
            ))
            .sessionManagement(
                req -> req.invalidSessionUrl(prefix + "/signin")
            )
            .formLogin(
                req -> req.loginPage(prefix + "/signin").permitAll()
            )
            .logout(
                req -> req.logoutUrl(prefix + "/signout")
            )
            .build();
    }

    @Bean
    @Order(1)
    public SecurityFilterChain apiKeyFilterChain(HttpSecurity http, ApiAuthenticationManager apiAuthenticationManager) throws Exception {
        return http.securityMatcher(CONFIG.getApiMapping() + "/**")
            .csrf(k -> k.disable())
            .sessionManagement(k -> k.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            .addFilter(new BasicAuthenticationFilter(apiAuthenticationManager, apiAuthenticationManager))
            .authorizeHttpRequests(k -> k.anyRequest().authenticated())
            .build();
    }
}