/**
 * JavaScript功能用于处理OCPP操作的提交和反馈
 */
$(document).ready(function() {
    // 为所有OCPP操作表单添加事件处理
    $('.ocpp-operation-form').submit(function(e) {
        e.preventDefault();
        
        var form = $(this);
        var submitButton = form.find('input[type="submit"]');
        
        // 防止重复提交
        if (submitButton.prop('disabled')) {
            return false;
        }
        
        // 禁用提交按钮
        submitButton.prop('disabled', true);
        
        var actionUrl = form.attr('action');
        var ajaxData;
        var processDataFlag = true;
        var contentTypeFlag = 'application/x-www-form-urlencoded; charset=UTF-8'; // Default jQuery content type

        // 检查表单是否用于文件上传
        if (form.attr('enctype') === 'multipart/form-data') {
            ajaxData = new FormData(form[0]); // 使用 FormData
            processDataFlag = false; // 必须为 false，以便 jQuery 不处理 FormData
            contentTypeFlag = false; // 必须为 false，以便 jQuery 不设置 Content-Type，浏览器会自动设置
        } else {
            ajaxData = form.serialize(); // 对于非文件上传表单，继续使用 serialize
        }
        
        // 显示加载提示
        showLoadingOverlay();
        
        // 获取 CSRF token
        var csrfToken = $("meta[name='_csrf']").attr("content");
        var csrfHeader = $("meta[name='_csrf_header']").attr("content");

        // 使用AJAX提交表单
        $.ajax({
            type: "POST",
            url: actionUrl,
            data: ajaxData,
            dataType: 'json',
            processData: processDataFlag, 
            contentType: contentTypeFlag,
            beforeSend: function(xhr) {
                // 如果 CSRF token 和 header 名称存在，则添加到请求头
                if (csrfHeader && csrfToken) {
                    xhr.setRequestHeader(csrfHeader, csrfToken);
                }
            },
            success: function(response, textStatus, jqXHR) {
                // 检查响应类型
                if (typeof response === 'object' && response.success !== undefined) {
                    // JSON响应
                    if (response.success) {
                        showNotification('success', 'Operation completed successfully');
                    } else {
                        showNotification('error', 'Operation failed: ' + response.errorMessage);
                    }
                } else if (typeof response === 'string' && response.indexOf('<!DOCTYPE html>') !== -1) {
                    // HTML响应（重定向后的页面）- 这通常表示成功
                    showNotification('success', 'GetDiagnostics request sent successfully. Redirecting to task details...');
                    setTimeout(function() {
                        // 重新加载页面或跳转到操作页面
                        window.location.reload();
                    }, 2000);
                } else {
                    // 其他类型的成功响应
                    showNotification('success', 'Operation completed successfully');
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                var errorMessage = getDetailedErrorMessage(jqXHR, textStatus, errorThrown);
                showNotification('error', errorMessage);
            },
            complete: function(jqXHR, textStatus) {
                hideLoadingOverlay();
                var responseSuccess = false;
                if (jqXHR.responseJSON && typeof jqXHR.responseJSON.success !== 'undefined') {
                    responseSuccess = jqXHR.responseJSON.success;
                } else if (textStatus === 'success') { 
                    // If dataType was json but response was not, responseJSON might be undefined
                    // We assume success if textStatus is success and no explicit failure in responseJSON
                    // This part might need refinement based on actual server responses for non-JSON success
                    try {
                        var parsedResponse = JSON.parse(jqXHR.responseText);
                        if (typeof parsedResponse.success !== 'undefined') {
                           responseSuccess = parsedResponse.success;
                        } else {
                            // If 'success' field is not present, but AJAX call was successful (e.g. 200 OK)
                            // and it's not a JSON error from Spring, consider it a form of success.
                            // This handles cases where the server might not strictly return {success: true/false}
                            // but the HTTP call itself was fine.
                            responseSuccess = (jqXHR.status >= 200 && jqXHR.status < 300);
                        }
                    } catch (e) {
                         // If parsing fails, and it was a 2xx response, assume success.
                         // This is a fallback. Ideally, server always returns consistent JSON.
                         responseSuccess = (jqXHR.status >= 200 && jqXHR.status < 300);
                    }
                }
                
                setTimeout(function() {
                    submitButton.prop('disabled', false);
                }, responseSuccess ? 5000 : 3000); // 5s for success, 3s for error
            }
        });
    });
});

// 显示加载覆盖层
function showLoadingOverlay() {
    if ($('#loading-overlay').length === 0) {
        $('body').append('<div id="loading-overlay"><div class="loading-spinner"></div></div>');
    }
    $('#loading-overlay').show();
}

// 隐藏加载覆盖层
function hideLoadingOverlay() {
    $('#loading-overlay').hide();
}

// 获取详细的错误消息
function getDetailedErrorMessage(jqXHR, textStatus, errorThrown) {
    var errorMessage = '';

    // 根据HTTP状态码提供更具体的错误信息
    switch(jqXHR.status) {
        case 0:
            errorMessage = 'Network connection failed. Please check your internet connection.';
            break;
        case 400:
            errorMessage = 'Invalid request parameters. Please check your input.';
            break;
        case 401:
            errorMessage = 'Authentication failed. Please login again.';
            break;
        case 403:
            errorMessage = 'Access denied. You do not have permission to perform this operation.';
            break;
        case 404:
            errorMessage = 'Service not found. The requested operation is not available.';
            break;
        case 408:
            errorMessage = 'Request timeout. The operation took too long to complete.';
            break;
        case 500:
            errorMessage = 'Server internal error. Please try again later.';
            break;
        case 502:
            errorMessage = 'Bad gateway. The server is temporarily unavailable.';
            break;
        case 503:
            errorMessage = 'Service unavailable. The server is temporarily overloaded.';
            break;
        case 504:
            errorMessage = 'Gateway timeout. The server took too long to respond.';
            break;
        default:
            // 尝试从响应中获取具体错误信息
            if (jqXHR.responseJSON && jqXHR.responseJSON.errorMessage) {
                errorMessage = jqXHR.responseJSON.errorMessage;
            } else if (jqXHR.responseJSON && jqXHR.responseJSON.message) {
                errorMessage = jqXHR.responseJSON.message;
            } else if (jqXHR.responseText) {
                try {
                    var errorResponse = JSON.parse(jqXHR.responseText);
                    if (errorResponse.message) {
                        errorMessage = errorResponse.message;
                    } else if (errorResponse.errorMessage) {
                        errorMessage = errorResponse.errorMessage;
                    } else {
                        errorMessage = 'Request failed with status: ' + jqXHR.status;
                    }
                } catch(e) {
                    // 如果是重定向响应（通常是成功的），不显示错误
                    if (jqXHR.status >= 300 && jqXHR.status < 400) {
                        errorMessage = 'Request completed successfully. Redirecting...';
                    } else {
                        errorMessage = 'Request failed with status: ' + jqXHR.status + ' (' + textStatus + ')';
                    }
                }
            } else {
                errorMessage = 'Request failed with status: ' + jqXHR.status + ' (' + textStatus + ')';
            }
    }

    return errorMessage;
}

// 显示通知消息
function showNotification(type, message) {
    // 移除之前的通知
    $('.notification').remove();

    var notificationClass = 'notification ' + type;
    var notification = $('<div class="' + notificationClass + '">' + message + '</div>');

    $('body').append(notification);

    // 显示通知
    setTimeout(function() {
        notification.addClass('show');
    }, 10);

    // 几秒后自动隐藏
    setTimeout(function() {
        notification.removeClass('show');
        setTimeout(function() {
            notification.remove();
        }, 300);
    }, 3000);
}