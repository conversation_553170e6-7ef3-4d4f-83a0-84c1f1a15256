/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.web.controller;

import de.rwth.idsg.steve.repository.ChargeStationRepository;
import de.rwth.idsg.steve.repository.ChargePointRepository;
import de.rwth.idsg.steve.repository.dto.ChargeStation;
import de.rwth.idsg.steve.repository.dto.ChargePoint;
import de.rwth.idsg.steve.repository.dto.ChargePointSelect;
import de.rwth.idsg.steve.service.PermissionService;
import de.rwth.idsg.steve.utils.ControllerHelper;
import de.rwth.idsg.steve.web.dto.ChargeStationQueryForm;
import jooq.steve.db.tables.records.WebUserRecord;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.propertyeditors.StringTrimmerEditor;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import jakarta.validation.Valid;
import java.beans.PropertyEditorSupport;
import java.util.List;

/**
 * Controller for Charge Station management
 */
@Slf4j
@Controller
@RequestMapping(value = "/manager/chargestations")
public class ChargeStationController {

    @Autowired protected ChargeStationRepository chargeStationRepository;
    @Autowired protected ChargePointRepository chargePointRepository;
    @Autowired protected PermissionService permissionService;

    protected static final String PARAMS = "params";

    // -------------------------------------------------------------------------
    // Paths
    // -------------------------------------------------------------------------

    protected static final String QUERY_PATH = "/query";
    protected static final String DETAILS_PATH = "/details/{stationPk}";
    protected static final String DELETE_PATH = "/delete/{stationPk}";
    protected static final String UPDATE_PATH = "/update";
    protected static final String ADD_PATH = "/add";

    @InitBinder
    public void initBinder(WebDataBinder binder) {
        binder.registerCustomEditor(String.class, new StringTrimmerEditor(true));
        binder.registerCustomEditor(DateTime.class, new PropertyEditorSupport() {
            private final DateTimeFormatter dateFormatter = DateTimeFormat.forPattern("yyyy-MM-dd");
            private final DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm");

            @Override
            public void setAsText(String text) {
                if (text == null || text.isEmpty()) {
                    setValue(null);
                } else {
                    try {
                        // 尝试解析完整的日期时间格式
                        setValue(dateTimeFormatter.parseDateTime(text));
                    } catch (IllegalArgumentException e) {
                        try {
                            // 如果失败，尝试仅解析日期格式
                            setValue(dateFormatter.parseDateTime(text));
                        } catch (IllegalArgumentException ex) {
                            // 如果两种格式都解析失败，抛出异常
                            throw new IllegalArgumentException("Date format must be yyyy-MM-dd or yyyy-MM-dd HH:mm", ex);
                        }
                    }
                }
            }

            @Override
            public String getAsText() {
                DateTime value = (DateTime) getValue();
                return value == null ? "" : dateTimeFormatter.print(value);
            }
        });
    }

    // -------------------------------------------------------------------------
    // HTTP methods
    // -------------------------------------------------------------------------

    @RequestMapping(method = RequestMethod.GET)
    public String getOverview(Model model) {
        initList(model, new ChargeStationQueryForm());
        return "data-man/chargestations";
    }

    @RequestMapping(value = QUERY_PATH, method = RequestMethod.GET)
    public String getQuery(@ModelAttribute(PARAMS) ChargeStationQueryForm params, Model model) {
        initList(model, params);
        return "data-man/chargestations";
    }
    
    @RequestMapping(value = DETAILS_PATH, method = RequestMethod.GET)
    public String getDetails(@PathVariable("stationPk") int stationPk, Model model) {
        ChargeStation.Details cs = chargeStationRepository.getDetails(stationPk);
        
        // Create and populate form object from details
        ChargeStation.Form form = new ChargeStation.Form();
        form.setChargingStationPk(cs.getChargingStationPk());
        form.setStationName(cs.getStationName());
        form.setOperatorName(cs.getOperatorName());
        form.setLocation(cs.getLocation());
        form.setConstructionDate(cs.getConstructionDate());
        form.setOperationDate(cs.getOperationDate());
        form.setWebUserPk(cs.getWebUserPk());
        form.setNote(cs.getNote());
        form.setAddress(cs.getAddress());
        
        model.addAttribute("chargeStationForm", form);
        model.addAttribute("chargeStation", cs);

        // 获取关联的充电桩列表
        List<ChargePoint.Overview> associatedChargePoints = chargePointRepository.getChargePointsByStation(stationPk);
        model.addAttribute("associatedChargePoints", associatedChargePoints);

        // 获取未关联充电站的充电桩列表，用于批量分配
        // 根据用户权限过滤充电桩列表
        List<ChargePointSelect> unassignedChargePoints;
        if (permissionService.isOperatorOwner()) {
            try {
                WebUserRecord currentUser = permissionService.getCurrentUser();
                unassignedChargePoints = chargePointRepository.getUnassignedChargePointsByOwner(currentUser.getWebUserPk());
            } catch (Exception e) {
                log.warn("Failed to get current user for unassigned charge points filtering", e);
                unassignedChargePoints = List.of(); // Return empty list on error
            }
        } else {
            // 管理员和工厂运营商可以看到所有未分配的充电桩
            unassignedChargePoints = chargePointRepository.getUnassignedChargePoints();
        }
        model.addAttribute("unassignedChargePoints", unassignedChargePoints);

        addCountryCodes(model);

        return "data-man/chargestationDetails";
    }
    
    @RequestMapping(value = ADD_PATH, method = RequestMethod.GET)
    public String addGet(Model model) {
        model.addAttribute("chargeStationForm", new ChargeStation.Form());
        return "data-man/chargestationAdd";
    }
    
    @RequestMapping(value = ADD_PATH, method = RequestMethod.POST)
    public String addPost(@Valid @ModelAttribute("chargeStationForm") ChargeStation.Form form,
                        BindingResult result, Model model) {
        if (result.hasErrors()) {
            return "data-man/chargestationAdd";
        }

        // 如果当前用户是Charge Box Owner，自动设置webUserPk为创建者
        if (permissionService.isOperatorOwner()) {
            try {
                WebUserRecord currentUser = permissionService.getCurrentUser();
                if (currentUser != null) {
                    form.setWebUserPk(currentUser.getWebUserPk());
                    log.info("Auto-assigned charge station {} to user {} ({})",
                            form.getStationName(), currentUser.getUsername(), currentUser.getWebUserPk());
                }
            } catch (Exception e) {
                log.error("Failed to auto-assign charge station {} to current user: {}",
                         form.getStationName(), e.getMessage(), e);
                // 继续执行，不影响充电站创建
            }
        }

        chargeStationRepository.addChargeStation(form);
        return toOverview();
    }
    
    @RequestMapping(value = UPDATE_PATH, method = RequestMethod.POST)
    public String update(@Valid @ModelAttribute("chargeStationForm") ChargeStation.Form form,
                       BindingResult result, Model model,
                       @RequestParam(value = "backToOverview", required = false) String backToOverview) {
        
        // 如果点击了"返回列表"按钮，直接重定向到列表页面
        if (backToOverview != null) {
            return toOverview();
        }
        
        if (result.hasErrors()) {
            ChargeStation.Details cs = chargeStationRepository.getDetails(form.getChargingStationPk());
            model.addAttribute("chargeStation", cs);
            addCountryCodes(model);
            return "data-man/chargestationDetails";
        }
        
        chargeStationRepository.updateChargeStation(form);
        return toOverview();
    }
    
    @RequestMapping(value = DELETE_PATH, method = RequestMethod.POST)
    public String delete(@PathVariable("stationPk") int stationPk) {
        // 检查删除权限
        if (!permissionService.canDeleteChargeStation(stationPk)) {
            log.warn("User does not have permission to delete charge station with pk: {}", stationPk);
            throw new AccessDeniedException("You do not have permission to delete this charge station.");
        }

        chargeStationRepository.deleteChargeStation(stationPk);
        return toOverview();
    }

    @RequestMapping(value = "/batchAssign", method = RequestMethod.POST)
    public String batchAssignChargePoints(@RequestParam("stationPk") int stationPk,
                                        @RequestParam(value = "selectedChargePoints", required = false) List<String> chargeBoxIds,
                                        Model model) {
        if (chargeBoxIds != null && !chargeBoxIds.isEmpty()) {
            try {
                // 将chargeBoxId转换为chargeBoxPk
                List<Integer> chargeBoxPks = chargeBoxIds.stream()
                    .map(chargePointRepository::getChargeBoxPkFromChargeBoxId)
                    .filter(pk -> pk != null)
                    .collect(java.util.stream.Collectors.toList());

                if (!chargeBoxPks.isEmpty()) {
                    chargePointRepository.batchUpdateChargingStation(chargeBoxPks, stationPk);
                    model.addAttribute("successMessage",
                        "Successfully assigned " + chargeBoxPks.size() + " charge points to this station.");
                } else {
                    model.addAttribute("errorMessage", "No valid charge points found for assignment.");
                }
            } catch (Exception e) {
                model.addAttribute("errorMessage", "Failed to assign charge points: " + e.getMessage());
            }
        } else {
            model.addAttribute("errorMessage", "No charge points selected for assignment.");
        }

        return "redirect:/manager/chargestations/details/" + stationPk;
    }

    @RequestMapping(value = "/batchUnassign", method = RequestMethod.POST)
    public String batchUnassignChargePoints(@RequestParam("stationPk") int stationPk,
                                          @RequestParam(value = "selectedAssociatedChargePoints", required = false) List<Integer> chargeBoxPks,
                                          Model model) {
        if (chargeBoxPks != null && !chargeBoxPks.isEmpty()) {
            try {
                chargePointRepository.batchUpdateChargingStation(chargeBoxPks, null);
                model.addAttribute("successMessage",
                    "Successfully unassigned " + chargeBoxPks.size() + " charge points from this station.");
            } catch (Exception e) {
                model.addAttribute("errorMessage", "Failed to unassign charge points: " + e.getMessage());
            }
        } else {
            model.addAttribute("errorMessage", "No charge points selected for unassignment.");
        }

        return "redirect:/manager/chargestations/details/" + stationPk;
    }

    private void initList(Model model, ChargeStationQueryForm params) {
        model.addAttribute(PARAMS, params);
        
        // Filter charge station list based on user role
        List<ChargeStation.Overview> stations;
        
        if (permissionService.isOperatorOwner()) {
            try {
                WebUserRecord currentUser = permissionService.getCurrentUser();
                int webUserPk = currentUser.getWebUserPk();
                stations = chargeStationRepository.getOwnerChargeStations(webUserPk, params);
                log.debug("Filtered charge stations for owner with userPk: {}, found: {}", 
                        webUserPk, stations.size());
            } catch (Exception e) {
                log.error("Error filtering charge stations for owner: {}", e.getMessage(), e);
                stations = List.of(); // Return empty list
            }
        } else {
            // Admins and factory operators can see all charge stations
            stations = chargeStationRepository.getOverview(params);
        }
        
        model.addAttribute("stationList", stations);
    }

    protected String toOverview() {
        return "redirect:/manager/chargestations";
    }

    protected void addCountryCodes(Model model) {
        model.addAttribute("countryCodes", ControllerHelper.COUNTRY_DROPDOWN);
    }
} 