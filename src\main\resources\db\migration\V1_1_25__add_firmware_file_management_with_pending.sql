-- Add firmware file management table with PENDING status
CREATE TABLE IF NOT EXISTS `firmware_file` (
    `firmware_file_pk` INT(11) NOT NULL AUTO_INCREMENT,
    `filename` VARCHAR(255) NOT NULL COMMENT 'File name',
    `file_type` ENUM('FIRMWARE', 'AP<PERSON><PERSON>ATION') NOT NULL COMMENT 'File type: FIRMWARE(.bin), APPLICATION(.apk)',
    `upload_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Upload time',
    `upload_status` ENUM('PENDING', 'SUCCESS', 'FAILED') NOT NULL COMMENT 'Upload status: PENDING (uploaded, awaiting installation), SUCCESS (installed), FAILED (upload or installation failed)',
    `uploaded_by_user_pk` INT(11) NULL COMMENT 'User ID who uploaded the file',
    `file_size` BIGINT NULL COMMENT 'File size in bytes',
    `description` VARCHAR(500) NULL COMMENT 'File description',
    <PERSON><PERSON>ARY KEY (`firmware_file_pk`),
    UNIQUE KEY `unique_filename` (`filename`),
    INDEX `idx_upload_status` (`upload_status`),
    INDEX `idx_file_type` (`file_type`),
    INDEX `idx_upload_time` (`upload_time`),
    FOREIGN KEY (`uploaded_by_user_pk`) REFERENCES `web_user`(`web_user_pk`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Firmware file management table';
