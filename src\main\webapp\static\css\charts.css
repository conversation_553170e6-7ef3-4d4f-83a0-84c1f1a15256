/**
 * 图表样式
 */

/* 图表区域样式 */
.charts-section {
    margin-top: 40px;
    padding: 20px 0;
    border-top: 1px solid #ebeef5;
}

.charts-section h3 {
    color: #333;
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 30px;
}

/* 图表容器布局 */
.charts-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 30px;
    margin: 0 auto;
    max-width: 1200px;
}

/* 单个图表项 */
.chart-item {
    flex: 0 0 calc(33.333% - 30px);
    min-width: 300px;
    max-width: 400px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 21, 41, 0.08);
    padding: 20px;
    transition: all 0.3s ease;
}

.chart-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 4px 20px rgba(0, 21, 41, 0.12);
}

/* 图表包装器 */
.chart-wrapper {
    position: relative;
    height: 300px;  /* 恢复正常高度 */
    width: 100%;
    background-color: #ffffff;
    border-radius: 4px;
}

.chart-item h4 {
    text-align: center;
    margin-bottom: 15px;
    color: #333;
    font-size: 14px;
    font-weight: 600;
}

/* 错误消息样式 */
.error-message {
    text-align: center;
    color: #f56c6c;
    font-size: 16px;
    padding: 40px;
    background: #fef0f0;
    border: 1px solid #fbc4c4;
    border-radius: 4px;
    margin: 20px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .chart-item {
        flex: 0 0 calc(50% - 30px);
    }
}

@media (max-width: 768px) {
    .chart-item {
        flex: 0 0 calc(100% - 30px);
        min-width: 280px;
    }

    .charts-container {
        gap: 20px;
        padding: 0 10px;
    }

    .chart-wrapper {
        height: 250px;  /* 移动端恢复原来的高度 */
    }
}

@media (max-width: 480px) {
    .chart-item {
        min-width: 260px;
        padding: 15px;
    }
    
    .chart-wrapper {
        height: 220px;
    }
    
    .charts-section h3 {
        font-size: 18px;
    }
}
