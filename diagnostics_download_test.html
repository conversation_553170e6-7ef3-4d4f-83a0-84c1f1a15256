<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diagnostics Download Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background-color: #007cba;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #005a8b;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin-top: 10px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        input[type="text"] {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 3px;
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Diagnostics Download Test</h1>
        
        <div class="test-section">
            <h3>1. Test File Ready Status</h3>
            <p>Simulate marking a file as ready for download:</p>
            <input type="text" id="testChargeBoxId" placeholder="Charge Box ID (e.g., CP001)" value="CP001">
            <button onclick="testMarkFileReady()">Mark File Ready</button>
        </div>
        
        <div class="test-section">
            <h3>2. Check Progress Status</h3>
            <button onclick="checkAllProgress()">Check All Progress</button>
            <button onclick="checkDownloadableFiles()">Check Downloadable Files</button>
        </div>
        
        <div class="test-section">
            <h3>3. Manual Download Test</h3>
            <input type="text" id="downloadChargeBoxId" placeholder="Charge Box ID" value="CP001">
            <button onclick="testDownload()">Test Download</button>
        </div>
        
        <div class="test-section">
            <h3>4. Auto-Download Test</h3>
            <p>This will check for files ready for download and trigger auto-download:</p>
            <button onclick="testAutoDownload()">Test Auto-Download Logic</button>
            <label>
                <input type="checkbox" id="enableAutoDownload" checked> Enable Auto-Download
            </label>
        </div>
        
        <div class="test-section">
            <h3>📋 Test Log</h3>
            <button onclick="clearLog()">Clear Log</button>
            <div id="testLog" class="log"></div>
        </div>
    </div>

    <script>
        let autoDownloadedFiles = new Set();
        
        function log(message) {
            const logDiv = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function clearLog() {
            document.getElementById('testLog').innerHTML = '';
        }
        
        async function testMarkFileReady() {
            const chargeBoxId = document.getElementById('testChargeBoxId').value;
            if (!chargeBoxId) {
                log('❌ Please enter a Charge Box ID');
                return;
            }
            
            try {
                log(`🧪 Testing mark file ready for: ${chargeBoxId}`);
                const response = await fetch(`/api/diagnostics/test/mark-file-ready/${chargeBoxId}`, {
                    method: 'POST'
                });
                const data = await response.json();
                log(`✅ Response: ${JSON.stringify(data)}`);
                
                // Check progress after marking file ready
                setTimeout(checkAllProgress, 1000);
            } catch (error) {
                log(`❌ Error: ${error.message}`);
            }
        }
        
        async function checkAllProgress() {
            try {
                log('📊 Checking all progress...');
                const response = await fetch('/api/diagnostics/progress');
                const data = await response.json();
                log(`📋 Progress data: ${JSON.stringify(data, null, 2)}`);
                
                // Check for auto-download if enabled
                if (document.getElementById('enableAutoDownload').checked) {
                    checkForAutoDownload(data);
                }
            } catch (error) {
                log(`❌ Error checking progress: ${error.message}`);
            }
        }
        
        async function checkDownloadableFiles() {
            try {
                log('📁 Checking downloadable files...');
                const response = await fetch('/api/diagnostics/downloadable-files');
                const data = await response.json();
                log(`📋 Downloadable files: ${JSON.stringify(data, null, 2)}`);
            } catch (error) {
                log(`❌ Error checking downloadable files: ${error.message}`);
            }
        }
        
        async function testDownload() {
            const chargeBoxId = document.getElementById('downloadChargeBoxId').value;
            if (!chargeBoxId) {
                log('❌ Please enter a Charge Box ID');
                return;
            }
            
            try {
                log(`📥 Testing download for: ${chargeBoxId}`);
                
                // Check status first
                const statusResponse = await fetch(`/api/diagnostics/download-status/${chargeBoxId}`);
                const statusData = await statusResponse.json();
                log(`📋 Download status: ${JSON.stringify(statusData)}`);
                
                if (!statusData.hasDownloadableFile) {
                    log('⚠️ No downloadable file available');
                    return;
                }
                
                // Trigger download
                const downloadUrl = `/api/diagnostics/download/${chargeBoxId}`;
                log(`🔗 Download URL: ${downloadUrl}`);
                
                const link = document.createElement('a');
                link.href = downloadUrl;
                link.download = '';
                link.style.display = 'none';
                
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                log('✅ Download initiated');
            } catch (error) {
                log(`❌ Error testing download: ${error.message}`);
            }
        }
        
        function checkForAutoDownload(progressData) {
            log('🔍 Checking for auto-download opportunities...');
            
            for (const [chargeBoxId, data] of Object.entries(progressData)) {
                log(`📊 ${chargeBoxId}: status="${data.status}", percentage=${data.percentage}`);
                
                if (data.status === 'File Ready' && !autoDownloadedFiles.has(chargeBoxId)) {
                    log(`🚀 Auto-downloading file for: ${chargeBoxId}`);
                    autoDownloadedFiles.add(chargeBoxId);
                    
                    setTimeout(() => {
                        log(`⏰ Auto-download timeout triggered for: ${chargeBoxId}`);
                        testDownload();
                    }, 2000);
                }
            }
        }
        
        async function testAutoDownload() {
            log('🤖 Testing auto-download logic...');
            await checkAllProgress();
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('🧪 Diagnostics Download Test initialized');
        });
    </script>
</body>
</html>
