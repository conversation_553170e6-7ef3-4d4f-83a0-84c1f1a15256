/**
 * Content Layout Styles
 * Adjust content area to fill available space
 */

/* Make content area fill the full width */
.content {
  width: 100% !important;
  max-width: 100% !important;
  margin: 20px auto !important;
  padding: 25px !important;
  box-sizing: border-box !important;
  border-radius: 0 !important;
}

/* Main wrapper adjustment */
.main-wrapper {
  padding-top: 55px !important;
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}

/* Container adjustment */
.container {
  width: 100% !important;
  max-width: 100% !important;
  padding: 0 !important;
  margin: 0 auto !important;
}

/* Tile wrapper layout adjustment */
.tileWrapper {
  display: flex !important;
  flex-wrap: wrap !important;
  justify-content: center !important;
  gap: 25px !important;
  width: 100% !important;
  max-width: 100% !important;
  padding: 20px 0 !important;
  margin: 0 auto !important;
}

/* Individual tile adjustment for better spacing */
.tileRow1 {
  flex: 0 0 calc(25% - 30px) !important;
  min-width: 220px !important;
  max-width: none !important;
  height: auto !important;
  min-height: 140px !important;
  margin: 0 !important;
}

/* Responsive adjustments */
@media (max-width: 1400px) {
  .tileRow1 {
    flex: 0 0 calc(33.333% - 30px) !important;
  }
}

@media (max-width: 1100px) {
  .tileRow1 {
    flex: 0 0 calc(50% - 30px) !important;
  }
}

@media (max-width: 768px) {
  .tileRow1 {
    flex: 0 0 calc(100% - 30px) !important;
  }
}

/* Remove any borders on the main content */
.main {
  border: none !important;
}

/* System overview title adjustment */
.content h2 {
  margin-top: 0 !important;
  margin-bottom: 25px !important;
  text-align: center !important;
  font-size: 24px !important;
  font-weight: bold !important;
  color: #333 !important;
}