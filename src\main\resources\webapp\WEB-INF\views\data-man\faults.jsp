<%--
    SteV<PERSON> - SteckdosenVerwaltung - https://github.com/steve-community/steve
    Copyright (C) 2013-2025 SteVe Community Team
    All Rights Reserved.

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.
--%>
<%@ include file="../00-header.jsp" %>
<%@ include file="../00-op-bind-errors.jsp" %>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<style>
    .filter-box {
        background-color: #f9f9f9;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
    }
    .action-box {
        margin: 15px 0;
    }
    .add-button {
        padding: 8px 15px;
        background-color: #5cb85c;
        color: white;
        text-decoration: none;
        border-radius: 4px;
        display: inline-block;
    }
    .add-button:hover {
        background-color: #4cae4c;
    }
    .status-new {
        background-color: #f0ad4e;
        color: white;
        padding: 3px 8px;
        border-radius: 3px;
    }
    .status-in-progress {
        background-color: #5bc0de;
        color: white;
        padding: 3px 8px;
        border-radius: 3px;
    }
    .status-resolved {
        background-color: #5cb85c;
        color: white;
        padding: 3px 8px;
        border-radius: 3px;
    }
    #faultTable {
        width: 100%;
        border-collapse: collapse;
        margin-top: 20px;
    }
    #faultTable th {
        background-color: #f5f5f5;
        padding: 10px;
        text-align: left;
    }
    #faultTable td {
        padding: 8px 10px;
        border-bottom: 1px solid #ddd;
    }
    #faultTable tr:hover {
        background-color: #f9f9f9;
    }
    .action-link {
        margin-right: 10px;
        color: #337ab7;
        text-decoration: none;
    }
    .action-link:hover {
        text-decoration: underline;
    }
</style>
<script type="text/javascript">
    $(document).ready(function() {
        $("#issueTable").dataTable({
            "paging": false,
            "ordering": false,
            "bInfo": false,
            "bFilter": false,
            "bLengthChange": false
        });
    });
</script>
<div class="content">
    <div>
        <section><span>Charger Fault List</span></section>
        
        <form:form action="${ctxPath}/manager/faults/list" method="get" modelAttribute="faultForm" style="margin: 15px 0;">
            <div class="row">
                <div class="col-md-6" style="padding-right: 15px;">
                    <div class="form-group">
                        <label for="chargeBoxId">Charger Station</label>
                        <form:select path="chargeBoxId" id="chargeBoxId" class="form-control">
                            <option value="">All</option>
                            <c:forEach items="${ownerChargePoints}" var="cp">
                                <option value="${cp.chargeBoxId}" ${cp.chargeBoxId eq faultForm.chargeBoxId ? 'selected' : ''}>${cp.chargeBoxId} (${cp.description})</option>
                            </c:forEach>
                        </form:select>
                    </div>
                    <div class="form-group">
                        <label for="status">Status Filter</label>
                        <form:select path="status" id="status" class="form-control">
                            <option value="">All</option>
                            <c:forEach items="${statuses}" var="status">
                                <option value="${status}" ${status eq selectedStatus ? 'selected' : ''}>${status.description}</option>
                            </c:forEach>
                        </form:select>
                    </div>
                </div>
                <div class="col-md-6" style="padding-left: 15px;">
                    <div class="form-group">
                        <label for="startDate">Start Date</label>
                        <form:input path="startDate" id="startDate" type="date" class="form-control"/>
                    </div>
                    <div class="form-group">
                        <label for="endDate">End Date</label>
                        <form:input path="endDate" id="endDate" type="date" class="form-control"/>
                    </div>
                </div>
            </div>
            <div class="form-group" style="text-align: right;">
                <button type="submit" class="btn btn-primary">Filter</button>
                <button type="button" class="btn btn-default" onclick="location.href='${ctxPath}/manager/faults/list'">Reset</button>
            </div>
        </form:form>
        
        <div class="action-row" style="margin-bottom: 15px;">
            <a href="${ctxPath}/manager/faults/add" class="btn btn-default">Report New Fault</a>
        </div>
        
        <table class="res" id="issueTable" style="width: 100%; margin-top: 20px;">
            <thead>
            <tr>
                <th>Charger Station</th>
                <th>Report Time</th>
                <th>Reporter</th>
                <th>Status</th>
                <th>Description</th>
                <th>Actions</th>
            </tr>
            </thead>
            <tbody>
            <c:forEach items="${issues}" var="issue">
                <tr>
                    <td><a href="${ctxPath}/manager/chargepoints/details/${issue.chargeBoxPk}">${issue.chargeBoxId}</a></td>
                    <td><joda:format value="${issue.reportTime}" pattern="yyyy-MM-dd HH:mm:ss"/></td>
                    <td>${issue.reporterUsername}</td>
                    <td>${issue.status.description}</td>
                    <td>${issue.faultDescription}</td>
                    <td>
                        <a href="${ctxPath}/manager/faults/details/${issue.issueId}" class="btn btn-default">View</a>
                        <c:if test="${issue.inProgress() || issue.isNew()}">
                            <a href="${ctxPath}/manager/faults/edit/${issue.issueId}" class="btn btn-default">Edit</a>
                        </c:if>
                    </td>
                </tr>
            </c:forEach>
            </tbody>
        </table>
    </div>
</div>
<%@ include file="../00-footer.jsp" %>