/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.service;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;

/**
 * 诊断任务信息类
 * 用于跟踪每个充电桩的诊断任务状态和生命周期
 * 
 * <AUTHOR> Community Team
 * @since 2025.01
 */
@Data
@Slf4j
public class DiagnosticsTaskInfo {
    
    /**
     * 任务ID
     */
    private final Integer taskId;
    
    /**
     * 充电桩ID
     */
    private final String chargeBoxId;
    
    /**
     * 任务创建时间
     */
    private final LocalDateTime createdAt;
    
    /**
     * 任务过期时间
     */
    private final LocalDateTime expiresAt;
    
    /**
     * 任务状态
     */
    private TaskStatus status;
    
    /**
     * 诊断文件名（如果已生成）
     */
    private String fileName;
    
    /**
     * 任务状态枚举
     */
    public enum TaskStatus {
        CREATED,        // 任务已创建
        IN_PROGRESS,    // 正在处理中
        FILE_READY,     // 文件已准备好
        DOWNLOADED,     // 已下载
        EXPIRED         // 已过期
    }
    
    /**
     * 构造函数
     * 
     * @param taskId 任务ID
     * @param chargeBoxId 充电桩ID
     * @param expiryMinutes 过期时间（分钟）
     */
    public DiagnosticsTaskInfo(Integer taskId, String chargeBoxId, int expiryMinutes) {
        this.taskId = taskId;
        this.chargeBoxId = chargeBoxId;
        this.createdAt = LocalDateTime.now();
        this.expiresAt = this.createdAt.plusMinutes(expiryMinutes);
        this.status = TaskStatus.CREATED;
        
        log.info("📋 Created diagnostics task info for {}: taskId={}, expiresAt={}", 
                chargeBoxId, taskId, expiresAt);
    }
    
    /**
     * 检查任务是否已过期
     * 
     * @return true如果任务已过期
     */
    public boolean isExpired() {
        boolean expired = LocalDateTime.now().isAfter(expiresAt);
        if (expired && status != TaskStatus.EXPIRED) {
            log.info("⏰ Task {} for {} has expired", taskId, chargeBoxId);
            status = TaskStatus.EXPIRED;
        }
        return expired;
    }
    
    /**
     * 检查任务是否仍然有效（未过期且状态正常）
     * 
     * @return true如果任务有效
     */
    public boolean isValid() {
        return !isExpired() && status != TaskStatus.EXPIRED;
    }
    
    /**
     * 标记文件已准备好
     * 
     * @param fileName 文件名
     */
    public void markFileReady(String fileName) {
        this.fileName = fileName;
        this.status = TaskStatus.FILE_READY;
        log.info("✅ File ready for task {} ({}): {}", taskId, chargeBoxId, fileName);
    }
    
    /**
     * 标记文件已下载
     */
    public void markDownloaded() {
        this.status = TaskStatus.DOWNLOADED;
        log.info("📥 File downloaded for task {} ({})", taskId, chargeBoxId);
    }
    
    /**
     * 获取剩余有效时间（分钟）
     * 
     * @return 剩余分钟数，如果已过期返回0
     */
    public long getRemainingMinutes() {
        if (isExpired()) {
            return 0;
        }
        LocalDateTime now = LocalDateTime.now();
        return java.time.Duration.between(now, expiresAt).toMinutes();
    }
    
    /**
     * 获取任务描述信息
     * 
     * @return 任务描述
     */
    public String getDescription() {
        return String.format("Task %d for %s (Status: %s, Remaining: %d min)", 
                taskId, chargeBoxId, status, getRemainingMinutes());
    }
}
