/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.web.controller;

import de.rwth.idsg.steve.ocpp.OcppProtocol;
import de.rwth.idsg.steve.ocpp.OcppTransport;
import de.rwth.idsg.steve.ocpp.OcppVersion;
import de.rwth.idsg.steve.repository.ChargePointRepository;
import de.rwth.idsg.steve.repository.dto.ChargePointSelect;
import de.rwth.idsg.steve.repository.dto.RegistrationStatus;
import de.rwth.idsg.steve.service.ChargePointService;
import de.rwth.idsg.steve.service.FaultService;
import de.rwth.idsg.steve.service.UserService;
import de.rwth.idsg.steve.web.dto.ChargerIssueDTO;
import de.rwth.idsg.steve.web.dto.ChargerIssueForm;
import de.rwth.idsg.steve.web.dto.ChargerIssueStatus;
import de.rwth.idsg.steve.web.dto.CustomUserDetails;
import de.rwth.idsg.steve.web.dto.FaultForm;
import de.rwth.idsg.steve.web.dto.KeyValue;
import de.rwth.idsg.steve.web.dto.MaintenanceRecordDTO;
import de.rwth.idsg.steve.web.dto.MaintenanceRecordForm;
import de.rwth.idsg.steve.web.dto.Ocpp15TransactionDetails;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import jakarta.validation.Valid;
import java.security.Principal;
import java.util.Collections;
import java.util.List;
import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * 充电桩故障控制器
 */
@Slf4j
@Controller
@RequestMapping(value = "/manager/faults")
@PreAuthorize("hasAnyAuthority('ADMIN', 'OPERATOR_FACTORY', 'OPERATOR_OWNER')")
public class FaultController {

    @Autowired
    private FaultService faultService;

    @Autowired
    private ChargePointRepository chargePointRepository;

    @Autowired
    private ChargePointService chargePointService;

    @Autowired
    private UserService userService;

    // -------------------------------------------------------------------------
    // Paths
    // -------------------------------------------------------------------------

    private static final String FAULT_LIST_PATH = "/list";
    private static final String FAULT_DETAILS_PATH = "/details/{issueId}";
    private static final String FAULT_ADD_PATH = "/add";
    private static final String FAULT_EDIT_PATH = "/edit/{issueId}";
    private static final String FAULT_RESOLVE_PATH = "/resolve/{issueId}";
    private static final String FAULT_UPDATE_STATUS_PATH = "/status/{issueId}";
    private static final String FAULT_MAINTENANCE_ADD_PATH = "/maintenance/add";
    private static final String FAULT_PATH = "";

    // -------------------------------------------------------------------------
    // HTTP methods
    // -------------------------------------------------------------------------

    @RequestMapping(method = RequestMethod.GET)
    public String getRoot() {
        return "redirect:/manager/faults/list";
    }

    @RequestMapping(value = FAULT_LIST_PATH, method = RequestMethod.GET)
    public String getList(Model model, @ModelAttribute("faultForm") FaultForm form) {
        ChargerIssueStatus status = form.getStatus();
        Integer userId = form.getUserId();
        String chargeBoxId = form.getChargeBoxId();
        
        List<ChargerIssueDTO> issues;
        if (status != null || userId != null || (chargeBoxId != null && !chargeBoxId.isEmpty())) {
            issues = faultService.getFilteredIssues(form);
        } else {
            issues = faultService.getIssues();
        }

        // 获取充电站列表并添加到model中
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        List<ChargePointSelect> chargePoints;
        if (auth != null && auth.getPrincipal() instanceof CustomUserDetails) {
            CustomUserDetails userDetails = (CustomUserDetails) auth.getPrincipal();
            chargePoints = getOwnerChargePoints(auth);
        } else {
            chargePoints = chargePointRepository.getChargePointSelectList();
        }
        model.addAttribute("ownerChargePoints", chargePoints);

        model.addAttribute("issues", issues);
        model.addAttribute("statuses", ChargerIssueStatus.values());
        model.addAttribute("selectedStatus", status);
        model.addAttribute("selectedUserId", userId);
        model.addAttribute("selectedChargeBoxId", chargeBoxId);
        model.addAttribute("faultForm", form);
        return "data-man/faults";
    }

    @RequestMapping(value = FAULT_DETAILS_PATH, method = RequestMethod.GET)
    public String getDetails(@PathVariable("issueId") int issueId, Model model) {
        ChargerIssueDTO issue = faultService.getIssue(issueId);
        if (issue == null) {
            return "redirect:/manager/faults/list";
        }

        List<MaintenanceRecordDTO> maintenanceRecords = faultService.getMaintenanceRecords(issueId);

        model.addAttribute("issue", issue);
        model.addAttribute("records", maintenanceRecords);
        model.addAttribute("statuses", ChargerIssueStatus.values());

        // 添加维护记录表单
        MaintenanceRecordForm recordForm = new MaintenanceRecordForm();
        recordForm.setIssueId(issueId);
        model.addAttribute("maintenanceForm", recordForm);

        return "data-man/faultDetails";
    }

    @RequestMapping(value = FAULT_ADD_PATH, method = RequestMethod.GET)
    public String addForm(Model model) {
        initAddForm(model, new ChargerIssueForm());
        return "data-man/faultAdd";
    }

    @RequestMapping(value = FAULT_ADD_PATH, method = RequestMethod.POST)
    public String add(@Valid @ModelAttribute("faultForm") ChargerIssueForm form,
                      BindingResult result, Model model, Principal principal) {
        if (result.hasErrors()) {
            initAddForm(model, form);
            return "data-man/faultAdd";
        }

        int issueId = faultService.createIssue(form, principal != null ? principal.getName() : null);
        
        // 如果有图片上传，保存图片
        if (form.getImages() != null && !form.getImages().isEmpty()) {
            faultService.saveIssueImages(issueId, form.getImages());
        }

        return "redirect:/manager/faults/details/" + issueId;
    }

    @RequestMapping(value = FAULT_EDIT_PATH, method = RequestMethod.GET)
    public String editForm(@PathVariable("issueId") int issueId, Model model) {
        ChargerIssueDTO issue = faultService.getIssue(issueId);
        if (issue == null) {
            return "redirect:/manager/faults/list";
        }

        ChargerIssueForm form = new ChargerIssueForm();
        form.setIssueId(issue.getIssueId());
        form.setChargeBoxPk(issue.getChargeBoxPk());
        form.setFaultDescription(issue.getFaultDescription());
        form.setOcppErrorCode(issue.getOcppErrorCode());
        form.setStatus(issue.getStatus());
        form.setIsAutoReported(issue.getIsAutoReported());
        form.setResolveDescription(issue.getResolveDescription());

        initEditForm(model, form);
        return "data-man/faultEdit";
    }

    @RequestMapping(value = FAULT_EDIT_PATH, method = RequestMethod.POST)
    public String edit(@Valid @ModelAttribute("faultForm") ChargerIssueForm form,
                       BindingResult result, Model model) {
        if (result.hasErrors()) {
            initEditForm(model, form);
            return "data-man/faultEdit";
        }

        faultService.updateIssue(form);
        
        // 如果有图片上传，保存图片
        if (form.getImages() != null && !form.getImages().isEmpty()) {
            faultService.saveIssueImages(form.getIssueId(), form.getImages());
        }

        return "redirect:/manager/faults/details/" + form.getIssueId();
    }

    @RequestMapping(value = FAULT_RESOLVE_PATH, method = RequestMethod.POST)
    public String resolve(@PathVariable("issueId") int issueId,
                         @RequestParam("resolveDescription") String resolveDescription,
                         Principal principal) {
        faultService.resolveIssue(issueId, resolveDescription, principal != null ? principal.getName() : null);
        return "redirect:/manager/faults/details/" + issueId;
    }

    @RequestMapping(value = FAULT_UPDATE_STATUS_PATH, method = RequestMethod.POST)
    public String updateStatus(@PathVariable("issueId") int issueId,
                               @RequestParam("status") String statusStr,
                               Principal principal) {
        try {
            ChargerIssueStatus status = ChargerIssueStatus.valueOf(statusStr);
            faultService.updateIssueStatus(issueId, status, principal != null ? principal.getName() : null);
        } catch (IllegalArgumentException e) {
            log.warn("Invalid status parameter: {}", statusStr);
        }
        return "redirect:/manager/faults/details/" + issueId;
    }

    @RequestMapping(value = FAULT_MAINTENANCE_ADD_PATH, method = RequestMethod.POST)
    public String addMaintenanceRecord(@Valid @ModelAttribute("maintenanceForm") MaintenanceRecordForm form,
                                      BindingResult result, Principal principal) {
        if (result.hasErrors()) {
            return "redirect:/manager/faults/details/" + form.getIssueId();
        }

        faultService.addMaintenanceRecord(form, principal != null ? principal.getName() : null);
        return "redirect:/manager/faults/details/" + form.getIssueId();
    }

    @PreAuthorize("hasRole('ADMIN') or hasRole('OPERATOR') or hasRole('OPERATOR_FACTORY')")
    @GetMapping(value = FAULT_PATH + "/overview")
    public String getFault(Model model) {
        initCommonAttributes(model);
        model.addAttribute("faultForm", new FaultForm());
        model.addAttribute("chargePointIssues", faultService.getIssues());
        model.addAttribute("chargePoints", getChargePointList());
        return "data-man/faults";
    }
    
    @PreAuthorize("hasRole('ADMIN') or hasRole('OPERATOR') or hasRole('OPERATOR_FACTORY')")
    @RequestMapping(value = FAULT_PATH + "/chargepoints", method = RequestMethod.GET)
    @ResponseBody
    public List<ChargePointSelect> getOwnerChargePoints(Authentication authentication) {
        CustomUserDetails userDetails = (CustomUserDetails) authentication.getPrincipal();
        Integer userPk = userDetails.getUserRecord().getWebUserPk();
        return chargePointRepository.getChargePointSelectByOwner(userPk);
    }

    @PreAuthorize("hasRole('ADMIN') or hasRole('OPERATOR') or hasRole('OPERATOR_FACTORY')")
    @RequestMapping(value = FAULT_PATH, method = RequestMethod.POST)
    // ... existing code ...

    @GetMapping(value = FAULT_PATH + "/simple-list")
    public String getSimpleList(Model model) {
        return faultList(model);
    }
    
    private String faultList(Model model) {
        List<ChargerIssueDTO> issues = faultService.getIssues();
        model.addAttribute("issueList", issues);
        
        // 添加空的过滤表单
        model.addAttribute("filterForm", new FaultForm());
        
        // 添加状态列表
        List<KeyValue> statusList = Arrays.stream(ChargerIssueStatus.values())
                .map(s -> new KeyValue(s.name(), s.getDescription()))
                .collect(Collectors.toList());
        model.addAttribute("statusList", statusList);
        
        // 添加当前用户的充电站列表
        Integer webUserPk = getWebUserId();
        model.addAttribute("ownerChargePoints", chargePointService.getChargePointSelectByOwner(webUserPk));
        
        return "data-man/faults";
    }
    
    @GetMapping(value = FAULT_PATH + "/filtered")
    @PreAuthorize("hasAnyRole('ADMIN', 'OPERATOR_FACTORY', 'OPERATOR_OWNER')")
    public String getFilteredList(@ModelAttribute FaultForm form, Model model) {
        List<KeyValue> statusList = Arrays.stream(ChargerIssueStatus.values())
                .map(s -> new KeyValue(s.name(), s.getDescription()))
                .collect(Collectors.toList());
        model.addAttribute("statusList", statusList);
        
        Integer webUserPk = getWebUserId();
        if (webUserPk != null) {
            form.setUserId(webUserPk);
        }
        
        List<ChargerIssueDTO> issues = faultService.getFilteredIssues(form);
        model.addAttribute("issueList", issues);
        model.addAttribute("filterForm", form);
        
        model.addAttribute("ownerChargePoints", chargePointService.getChargePointSelectByOwner(webUserPk));
        
        return "data-man/faults";
    }

    // -------------------------------------------------------------------------
    // Private helpers
    // -------------------------------------------------------------------------

    private void initCommonAttributes(Model model) {
        model.addAttribute("statuses", ChargerIssueStatus.values());
    }

    private void initAddForm(Model model, ChargerIssueForm form) {
        model.addAttribute("faultForm", form);
        model.addAttribute("statuses", ChargerIssueStatus.values());
        model.addAttribute("chargePoints", getChargePointList());
    }

    private void initEditForm(Model model, ChargerIssueForm form) {
        model.addAttribute("faultForm", form);
        model.addAttribute("statuses", ChargerIssueStatus.values());
        model.addAttribute("chargePoints", getChargePointList());
    }

    @ModelAttribute("chargePointList")
    public List<ChargePointSelect> getChargePointList() {
        return chargePointRepository.getChargePointSelect(OcppProtocol.V_16_SOAP, 
                Collections.singletonList(RegistrationStatus.ACCEPTED.value()));
    }

    private Integer getWebUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.getPrincipal() instanceof UserDetails) {
            UserDetails userDetails = (UserDetails) authentication.getPrincipal();
            return userService.getWebUserId(userDetails.getUsername());
        }
        return null;
    }
} 