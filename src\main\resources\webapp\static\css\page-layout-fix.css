/**
 * 页面布局修复样式
 * 专门修复EVSE_OMS项目中各个页面的布局和对齐问题
 */

/* ===== 1. 充电桩页面按钮布局修复 ===== */

/* 修复充电桩页面按钮组布局 */
.chargepoints-page .filter-item:last-child .d-flex {
    gap: 8px !important;
    justify-content: flex-start !important;
}

.chargepoints-page .filter-item:last-child .btn {
    flex-grow: 0 !important;
    flex-shrink: 0 !important;
    min-width: 80px !important;
    max-width: 120px !important;
    white-space: nowrap !important;
}

/* 确保按钮不会过度拉伸 */
.chargepoints-page .filter-item:last-child .btn.flex-grow-1 {
    flex-grow: 0 !important;
}

/* ===== 2. 表单页面左对齐修复 ===== */

/* 修复userInput表格的对齐问题 */
table.userInput {
    width: 100% !important;
    max-width: none !important;
    margin: 0 !important;
    border-collapse: collapse !important;
    background-color: #fff !important;
    border-radius: 6px !important;
    overflow: hidden !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08) !important;
}

table.userInput td:first-child {
    text-align: left !important;
    width: 250px !important;
    padding: 15px 20px !important;
    background-color: #f8f9fa !important;
    font-weight: 500 !important;
    color: #495057 !important;
    border-bottom: 1px solid #dee2e6 !important;
    vertical-align: middle !important;
}

table.userInput td:last-child {
    padding: 15px 20px !important;
    border-bottom: 1px solid #dee2e6 !important;
    vertical-align: middle !important;
}

table.userInput tr:last-child td {
    border-bottom: none !important;
}

/* 修复userInputFullPage表格的对齐问题 */
table.userInputFullPage {
    width: 100% !important;
    max-width: none !important;
    margin: 0 !important;
    border-collapse: collapse !important;
    background-color: #fff !important;
    border-radius: 6px !important;
    overflow: hidden !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08) !important;
}

table.userInputFullPage td:first-child {
    text-align: left !important;
    width: 300px !important;
    padding: 15px 20px !important;
    background-color: #f8f9fa !important;
    font-weight: 500 !important;
    color: #495057 !important;
    border-bottom: 1px solid #dee2e6 !important;
    vertical-align: top !important;
}

table.userInputFullPage td:last-child {
    padding: 15px 20px !important;
    border-bottom: 1px solid #dee2e6 !important;
    vertical-align: top !important;
}

table.userInputFullPage tr:last-child td {
    border-bottom: none !important;
}

/* ===== 3. 表单输入框统一样式 ===== */

table.userInput input[type="text"],
table.userInput input[type="email"],
table.userInput input[type="password"],
table.userInput select,
table.userInput textarea,
table.userInputFullPage input[type="text"],
table.userInputFullPage input[type="email"],
table.userInputFullPage input[type="password"],
table.userInputFullPage select,
table.userInputFullPage textarea {
    width: 100% !important;
    max-width: 400px !important;
    height: 38px !important;
    padding: 8px 12px !important;
    border: 1px solid #ced4da !important;
    border-radius: 4px !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
    color: #495057 !important;
    background-color: #fff !important;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out !important;
}

table.userInput textarea,
table.userInputFullPage textarea {
    height: auto !important;
    min-height: 80px !important;
    resize: vertical !important;
}

table.userInput input:focus,
table.userInput select:focus,
table.userInput textarea:focus,
table.userInputFullPage input:focus,
table.userInputFullPage select:focus,
table.userInputFullPage textarea:focus {
    border-color: #80bdff !important;
    outline: 0 !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
}

/* ===== 4. 按钮样式统一 ===== */

table.userInput input[type="submit"],
table.userInput button,
table.userInputFullPage input[type="submit"],
table.userInputFullPage button {
    height: 38px !important;
    padding: 8px 16px !important;
    margin-right: 8px !important;
    border: 1px solid transparent !important;
    border-radius: 4px !important;
    font-size: 14px !important;
    font-weight: 400 !important;
    line-height: 1.5 !important;
    text-align: center !important;
    text-decoration: none !important;
    vertical-align: middle !important;
    cursor: pointer !important;
    user-select: none !important;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out !important;
}

/* 主要按钮样式 */
table.userInput input[type="submit"],
table.userInputFullPage input[type="submit"] {
    color: #fff !important;
    background-color: #007bff !important;
    border-color: #007bff !important;
}

table.userInput input[type="submit"]:hover,
table.userInputFullPage input[type="submit"]:hover {
    color: #fff !important;
    background-color: #0056b3 !important;
    border-color: #004085 !important;
}

/* ===== 5. 通用按钮样式修复 ===== */

/* 修复所有.btn按钮的样式，确保有明显的边框和背景 */
.btn {
    display: inline-block !important;
    padding: 8px 16px !important;
    margin-bottom: 0 !important;
    font-size: 14px !important;
    font-weight: 400 !important;
    line-height: 1.42857143 !important;
    text-align: center !important;
    white-space: nowrap !important;
    vertical-align: middle !important;
    cursor: pointer !important;
    user-select: none !important;
    background-image: none !important;
    border: 1px solid transparent !important;
    border-radius: 4px !important;
    text-decoration: none !important;
    transition: all 0.15s ease-in-out !important;
}

/* 默认按钮样式 */
.btn-default {
    color: #333 !important;
    background-color: #fff !important;
    border-color: #ccc !important;
}

.btn-default:hover,
.btn-default:focus {
    color: #333 !important;
    background-color: #e6e6e6 !important;
    border-color: #adadad !important;
    text-decoration: none !important;
}

/* 确保链接按钮也有正确的样式 */
a.btn-default {
    color: #333 !important;
    background-color: #fff !important;
    border: 1px solid #ccc !important;
    text-decoration: none !important;
    display: inline-block !important;
}

a.btn-default:hover,
a.btn-default:focus,
a.btn-default:active {
    color: #333 !important;
    background-color: #e6e6e6 !important;
    border-color: #adadad !important;
    text-decoration: none !important;
}

/* 主要按钮样式 */
.btn-primary {
    color: #fff !important;
    background-color: #007bff !important;
    border-color: #007bff !important;
}

.btn-primary:hover,
.btn-primary:focus {
    color: #fff !important;
    background-color: #0056b3 !important;
    border-color: #004085 !important;
    text-decoration: none !important;
}

/* 链接形式的主要按钮 */
a.btn-primary {
    color: #fff !important;
    background-color: #007bff !important;
    border: 1px solid #007bff !important;
    text-decoration: none !important;
    display: inline-block !important;
}

a.btn-primary:hover,
a.btn-primary:focus,
a.btn-primary:active {
    color: #fff !important;
    background-color: #0056b3 !important;
    border-color: #004085 !important;
    text-decoration: none !important;
}

/* 成功按钮样式 */
.btn-success {
    color: #fff !important;
    background-color: #28a745 !important;
    border-color: #28a745 !important;
}

.btn-success:hover,
.btn-success:focus {
    color: #fff !important;
    background-color: #1e7e34 !important;
    border-color: #1c7430 !important;
    text-decoration: none !important;
}

/* 链接形式的成功按钮 */
a.btn-success {
    color: #fff !important;
    background-color: #28a745 !important;
    border: 1px solid #28a745 !important;
    text-decoration: none !important;
    display: inline-block !important;
}

a.btn-success:hover,
a.btn-success:focus,
a.btn-success:active {
    color: #fff !important;
    background-color: #1e7e34 !important;
    border-color: #1c7430 !important;
    text-decoration: none !important;
}

/* 危险按钮样式 */
.btn-danger {
    color: #fff !important;
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
}

.btn-danger:hover,
.btn-danger:focus {
    color: #fff !important;
    background-color: #c82333 !important;
    border-color: #bd2130 !important;
    text-decoration: none !important;
}

/* 信息按钮样式 */
.btn-info {
    color: #fff !important;
    background-color: #17a2b8 !important;
    border-color: #17a2b8 !important;
}

.btn-info:hover,
.btn-info:focus {
    color: #fff !important;
    background-color: #117a8b !important;
    border-color: #10707f !important;
    text-decoration: none !important;
}

/* 特殊修复：action-row中的按钮 */
.action-row {
    display: flex !important;
    align-items: center !important;
    gap: 15px !important;
    margin-bottom: 20px !important;
    flex-wrap: nowrap !important;
}

.action-row .btn {
    margin: 0 !important;
    min-width: 140px !important;
    max-width: 180px !important;
    flex-shrink: 0 !important;
    height: 38px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 8px 16px !important;
    box-sizing: border-box !important;
}

.action-row form {
    margin: 0 !important;
    padding: 0 !important;
    display: flex !important;
    align-items: center !important;
}

.action-row form .btn {
    width: 100% !important;
}

/* 修复充电桩页面的按钮布局 */
.search-panel .filter-item .d-flex {
    display: flex !important;
    gap: 10px !important;
    align-items: center !important;
}

.search-panel .filter-item .d-flex .btn {
    flex: 1 !important;
    min-width: 100px !important;
    max-width: 120px !important;
    margin: 0 !important;
    white-space: nowrap !important;
}

.search-panel .filter-item .d-flex .btn.ms-2 {
    margin-left: 0 !important;
}

/* 确保按钮容器有足够的宽度 */
.search-panel .filter-item:last-child {
    min-width: 350px !important;
}

/* ===== 7. 特定页面修复 ===== */

/* Owner Assignments页面 */
.content section span {
    font-size: 18px !important;
    font-weight: 600 !important;
    color: #181c32 !important;
    margin-bottom: 20px !important;
    display: block !important;
}

/* Transactions页面 */
.content form {
    margin-bottom: 25px !important;
}

/* Connector Status页面 */
.content table.userInput {
    margin-bottom: 25px !important;
}

/* Notification页面 */
.content .form-group {
    margin-bottom: 25px !important;
}

.content .action-row {
    margin-bottom: 20px !important;
    display: flex !important;
    gap: 10px !important;
    align-items: center !important;
}

/* Settings页面 */
.content section {
    margin-bottom: 20px !important;
    margin-top: 30px !important;
}

.content section:first-child {
    margin-top: 0 !important;
}

/* ===== 8. 复选框和单选框样式 ===== */

table.userInput input[type="checkbox"],
table.userInput input[type="radio"],
table.userInputFullPage input[type="checkbox"],
table.userInputFullPage input[type="radio"] {
    width: auto !important;
    height: auto !important;
    margin-right: 8px !important;
    vertical-align: middle !important;
}

/* ===== 9. 响应式设计 ===== */

@media (max-width: 768px) {
    table.userInput,
    table.userInputFullPage {
        display: block !important;
        width: 100% !important;
    }
    
    table.userInput tr,
    table.userInputFullPage tr {
        display: block !important;
        margin-bottom: 15px !important;
        border: 1px solid #dee2e6 !important;
        border-radius: 6px !important;
        overflow: hidden !important;
    }
    
    table.userInput td,
    table.userInputFullPage td {
        display: block !important;
        width: 100% !important;
        text-align: left !important;
        border-bottom: 1px solid #dee2e6 !important;
    }
    
    table.userInput td:first-child,
    table.userInputFullPage td:first-child {
        background-color: #f8f9fa !important;
        font-weight: 600 !important;
        padding: 10px 15px 5px 15px !important;
    }
    
    table.userInput td:last-child,
    table.userInputFullPage td:last-child {
        padding: 5px 15px 10px 15px !important;
        border-bottom: none !important;
    }
}

/* ===== 10. 内容容器样式 ===== */

.content {
    padding: 25px !important;
    background-color: #f8f9fa !important;
    min-height: calc(100vh - 200px) !important;
}

.content > div {
    background-color: #fff !important;
    padding: 25px !important;
    border-radius: 6px !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08) !important;
}

/* ===== 11. 错误和成功消息样式 ===== */

.error {
    background-color: #f8d7da !important;
    color: #721c24 !important;
    padding: 12px 15px !important;
    border: 1px solid #f5c6cb !important;
    border-radius: 4px !important;
    margin-bottom: 20px !important;
}

.success {
    background-color: #d4edda !important;
    color: #155724 !important;
    padding: 12px 15px !important;
    border: 1px solid #c3e6cb !important;
    border-radius: 4px !important;
    margin-bottom: 20px !important;
}

/* ===== 12. 特殊元素修复 ===== */

/* 修复#add_space元素的样式 */
#add_space {
    padding-top: 15px !important;
}

/* 修复表格中的按钮间距 */
table.userInput td button + button,
table.userInput td input + input,
table.userInputFullPage td button + button,
table.userInputFullPage td input + input {
    margin-left: 8px !important;
}
