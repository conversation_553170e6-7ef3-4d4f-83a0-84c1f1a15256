package de.rwth.idsg.steve.web.dto;

import lombok.Builder;
import lombok.Getter;

import java.util.List;

/**
 * 图表数据传输对象
 * 
 * <AUTHOR> Assistant
 */
@Getter
@Builder
public class ChartDataDTO {
    
    /**
     * 图表标签（X轴或饼图标签）
     */
    private final List<String> labels;
    
    /**
     * 图表数据值
     */
    private final List<Integer> data;
    
    /**
     * 图表背景颜色
     */
    private final List<String> backgroundColor;
    
    /**
     * 图表边框颜色
     */
    private final List<String> borderColor;
    
    /**
     * 图表标题
     */
    private final String title;
    
    /**
     * 图表类型（pie, bar, line等）
     */
    private final String type;

    /**
     * 额外数据（用于tooltip显示，如充电次数等）
     */
    private final List<Integer> additionalData;
}
