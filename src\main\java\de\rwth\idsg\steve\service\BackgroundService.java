/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.service;

import de.rwth.idsg.steve.config.DelegatingTaskExecutor;
import de.rwth.idsg.steve.repository.dto.ChargePointSelect;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.function.Consumer;

/**
 * <AUTHOR> Goekay <<EMAIL>>
 * @since 08.03.2018
 */
@RequiredArgsConstructor
public class BackgroundService {

    private final DelegatingTaskExecutor asyncTaskExecutor;

    public static BackgroundService with(DelegatingTaskExecutor asyncTaskExecutor) {
        return new BackgroundService(asyncTaskExecutor);
    }

    public Runner forFirst(List<ChargePointSelect> list) {
        return new BackgroundSingleRunner(list.get(0));
    }

    public Runner forEach(List<ChargePointSelect> list) {
        return new BackgroundListRunner(list);
    }

    public interface Runner {
        void execute(Consumer<ChargePointSelect> consumer);
    }

    @RequiredArgsConstructor(access = AccessLevel.PRIVATE)
    private class BackgroundSingleRunner implements Runner {
        private final ChargePointSelect cps;

        @Override
        public void execute(Consumer<ChargePointSelect> consumer) {
            asyncTaskExecutor.execute(() -> consumer.accept(cps));
        }
    }

    @RequiredArgsConstructor(access = AccessLevel.PRIVATE)
    private class BackgroundListRunner implements Runner {
        private final List<ChargePointSelect> list;

        @Override
        public void execute(Consumer<ChargePointSelect> consumer) {
            asyncTaskExecutor.execute(() -> list.forEach(consumer));
        }
    }
}
