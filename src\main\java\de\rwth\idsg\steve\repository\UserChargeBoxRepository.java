/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.repository;

import java.util.List;

/**
 * Repository interface for user_charge_box table operations.
 *
 * @since 1.0.8
 */
public interface UserChargeBoxRepository {

    /**
     * Assign a charge box to a user (typically an OPERATOR_OWNER)
     *
     * @param webUserPk    The primary key of the web_user
     * @param chargeBoxPk  The primary key of the charge_box
     */
    void assignChargeBoxToUser(int webUserPk, int chargeBoxPk);

    /**
     * Remove a charge box assignment from a user
     *
     * @param webUserPk    The primary key of the web_user
     * @param chargeBoxPk  The primary key of the charge_box
     */
    void removeChargeBoxFromUser(int webUserPk, int chargeBoxPk);

    /**
     * Remove all charge box assignments for a specific user
     *
     * @param webUserPk The primary key of the web_user
     */
    void removeAllChargeBoxesFromUser(int webUserPk);

    /**
     * Remove all user assignments for a specific charge box
     *
     * @param chargeBoxPk The primary key of the charge_box
     */
    void removeAllUsersFromChargeBox(int chargeBoxPk);

    /**
     * Get charge box primary keys assigned to a user
     *
     * @param webUserPk The primary key of the web_user
     * @return List of charge box primary keys
     */
    List<Integer> getChargeBoxPksByUser(int webUserPk);

    /**
     * Get charge box IDs assigned to a user
     *
     * @param webUserPk The primary key of the web_user
     * @return List of charge box IDs
     */
    List<String> getChargeBoxIdsByUser(int webUserPk);

    /**
     * Get web user primary keys assigned to a charge box
     *
     * @param chargeBoxPk The primary key of the charge_box
     * @return List of web user primary keys
     */
    List<Integer> getUserPksByChargeBox(int chargeBoxPk);

    /**
     * Check if a user has access to a specific charge box
     *
     * @param webUserPk   The primary key of the web_user
     * @param chargeBoxPk The primary key of the charge_box
     * @return true if the user has access, false otherwise
     */
    boolean isChargeBoxAssignedToUser(int webUserPk, int chargeBoxPk);

    /**
     * Check if a user has access to a specific charge box by ID
     *
     * @param webUserPk   The primary key of the web_user
     * @param chargeBoxId The ID of the charge_box
     * @return true if the user has access, false otherwise
     */
    boolean isChargeBoxAssignedToUser(int webUserPk, String chargeBoxId);

    /**
     * Check if a user has access to a specific charge box by primary key
     * 通过充电桩主键检查是否分配给用户
     *
     * @param webUserPk   The primary key of the web_user
     * @param chargeBoxPk The primary key of the charge_box
     * @return true if the user has access, false otherwise
     */
    boolean isChargeBoxAssignedToUserByPk(int webUserPk, int chargeBoxPk);

    /**
     * 获取所有已分配给任何用户的充电桩ID列表
     *
     * @return 所有已分配的充电桩ID列表
     */
    List<String> getAllAssignedChargeBoxIds();
}