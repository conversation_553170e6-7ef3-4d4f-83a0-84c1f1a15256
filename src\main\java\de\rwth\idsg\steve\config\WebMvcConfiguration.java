/*
 * SteV<PERSON> - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.config;

import de.rwth.idsg.steve.web.security.ChargeBoxOwnerAuthorizationInterceptor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.web.config.EnableSpringDataWebSupport;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import static de.rwth.idsg.steve.SteveConfiguration.CONFIG;

/**
 * Web MVC configuration.
 *
 * @since 1.0.8
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
@EnableSpringDataWebSupport
public class WebMvcConfiguration implements WebMvcConfigurer {

    private final ChargeBoxOwnerAuthorizationInterceptor chargeBoxOwnerAuthorizationInterceptor;
    
    @Value("${steve.issue.image.upload-path:uploads/issue-images}")
    private String uploadPath;

    @Value("${steve.firmware.physical-upload-path}")
    private String physicalFirmwareUploadPath;

    @Value("${steve.firmware.http-access-path}")
    private String httpFirmwareAccessPath;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // Add the ChargeBoxOwnerAuthorizationInterceptor for charge point related paths
        String prefix = CONFIG.getSpringManagerMapping();
        
        registry.addInterceptor(chargeBoxOwnerAuthorizationInterceptor)
                .addPathPatterns(prefix + "/chargepoints/details/*")  // 使用*而不是**，避免多级路径匹配问题
                .addPathPatterns(prefix + "/chargepoints/delete/*")
                .addPathPatterns(prefix + "/chargepoints/update/**")
                .addPathPatterns(prefix + "/operations/**")
                .addPathPatterns(prefix + "/transactions/**")
                .addPathPatterns(prefix + "/reservations/**")
                .addPathPatterns(prefix + "/chargingProfiles/assignments/**");
    }
    
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 注册静态资源目录，使上传的图片可通过URL访问
        
        // 处理直接文件名格式 (如 newFilename)
        // registry.addResourceHandler("/static/images/**")
        //         .addResourceLocations("file:uploads/issue-images/");
                
        // 处理包含完整路径格式 (如 uploads/issue-images/filename)
        registry.addResourceHandler("/static/uploads/issue-images/**")
                .addResourceLocations("file:uploads/issue-images/");
                
        // 处理其他路径格式 (如 /uploads/issues/**/*)
        registry.addResourceHandler("/static/uploads/issues/**")
                .addResourceLocations("file:uploads/issues/");

        // Configure firmware resource handler using values from properties
        // Temporarily commented out to isolate the problem
        /*
        if (this.httpFirmwareAccessPath != null && !this.httpFirmwareAccessPath.isEmpty() &&
            this.physicalFirmwareUploadPath != null && !this.physicalFirmwareUploadPath.isEmpty()) {
            
            String configuredHttpPath = this.httpFirmwareAccessPath;

            if (!configuredHttpPath.startsWith("/")) {
                configuredHttpPath = "/" + configuredHttpPath;
            }
            if (!configuredHttpPath.endsWith("/")) {
                configuredHttpPath = configuredHttpPath + "/";
            }

            String finalHttpPathPattern = configuredHttpPath + "**"; 

            String physicalPathLocation = this.physicalFirmwareUploadPath;
            if (!physicalPathLocation.startsWith("file:")) {
                physicalPathLocation = "file:" + physicalPathLocation;
            }
            if (!physicalPathLocation.endsWith("/")) {
                physicalPathLocation = physicalPathLocation + "/";
            }
            
            log.info("Registering firmware resource handler: Pattern='{}', Location='{}'", finalHttpPathPattern, physicalPathLocation);
            registry.addResourceHandler(finalHttpPathPattern)
                    .addResourceLocations(physicalPathLocation);
        } else {
            log.warn("Firmware upload paths (steve.firmware.http-access-path or steve.firmware.physical-upload-path) are not configured. Firmware file serving will not work.");
        }
        */
    }
}