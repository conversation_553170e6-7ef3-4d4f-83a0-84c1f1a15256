<%--
    SteV<PERSON> - SteckdosenVerwaltung - https://github.com/steve-community/steve
    Copyright (C) 2013-2025 SteVe Community Team
    All Rights Reserved.

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.
--%>
<%@ include file="../00-header.jsp" %>
<style>
    .content {
        max-width: 1000px;
        margin: 0 auto;
    }
    .form-container {
        background-color: #f9f9f9;
        padding: 25px;
        border-radius: 5px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        margin-top: 20px;
    }
    .userInputFullWidth {
        width: 100%;
    }
    .userInputFullWidth td {
        padding: 12px 8px;
    }
    .userInputFullWidth td:first-child {
        width: 180px;
        vertical-align: top;
        padding-top: 20px;
    }
    .required-field {
        color: #d9534f;
    }
    .form-input {
        width: 100%;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
    }
    .form-select {
        width: 100%;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        background-color: white;
    }
    .form-textarea {
        width: 100%;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        min-height: 120px;
    }
    .file-input-container {
        padding: 15px;
        border: 2px dashed #ddd;
        border-radius: 4px;
        text-align: center;
        margin-top: 5px;
    }
    .btn-primary {
        background-color: #5cb85c;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 4px;
        cursor: pointer;
        font-weight: bold;
    }
    .btn-primary:hover {
        background-color: #4cae4c;
    }
    .btn-secondary {
        background-color: #f0f0f0;
        color: #333;
        border: 1px solid #ddd;
        padding: 9px 20px;
        border-radius: 4px;
        cursor: pointer;
        text-decoration: none;
        display: inline-block;
    }
    .btn-secondary:hover {
        background-color: #e0e0e0;
    }
    .note {
        font-size: 13px;
        color: #777;
        margin-top: 5px;
    }
    .error {
        color: #d9534f;
        font-size: 13px;
        display: block;
        margin-top: 5px;
    }
</style>
<div class="content">
    <section><span>Report Fault</span></section>
    
    <div class="form-container">
        <form:form action="${ctxPath}/manager/faults/add" method="post" 
                modelAttribute="faultForm" enctype="multipart/form-data">
        
            <table class="userInputFullWidth">
                <tr>
                    <td><form:label path="chargeBoxPk">Charge Point: <span class="required-field">*</span></form:label></td>
                    <td>
                        <form:select path="chargeBoxPk" required="required" cssClass="form-select">
                            <option value="" selected>-- Select Charge Point --</option>
                            <c:forEach items="${chargePoints}" var="cp">
                                <option value="${cp.chargeBoxPk}" ${cp.chargeBoxPk eq faultForm.chargeBoxPk ? 'selected' : ''}>
                                    ${cp.chargeBoxId}
                                </option>
                            </c:forEach>
                        </form:select>
                        <form:errors path="chargeBoxPk" cssClass="error" />
                    </td>
                </tr>
                <tr>
                    <td><form:label path="faultDescription">Fault Description: <span class="required-field">*</span></form:label></td>
                    <td>
                        <form:textarea path="faultDescription" required="required" cssClass="form-textarea" />
                        <form:errors path="faultDescription" cssClass="error" />
                    </td>
                </tr>
                <tr>
                    <td><form:label path="ocppErrorCode">OCPP Error Code:</form:label></td>
                    <td>
                        <form:input path="ocppErrorCode" cssClass="form-input" />
                        <form:errors path="ocppErrorCode" cssClass="error" />
                    </td>
                </tr>
                <tr>
                    <td><form:label path="status">Status:</form:label></td>
                    <td>
                        <form:select path="status" cssClass="form-select">
                            <c:forEach items="${statuses}" var="status">
                                <form:option value="${status}" label="${status.value()}" />
                            </c:forEach>
                        </form:select>
                        <form:errors path="status" cssClass="error" />
                    </td>
                </tr>
                <tr>
                    <td><label for="images">Fault Images:</label></td>
                    <td>
                        <div class="file-input-container">
                            <input type="file" name="images" multiple accept="image/*" />
                            <p class="note">You can select multiple images (hold Ctrl key to select multiple)</p>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td></td>
                    <td style="padding-top: 20px;">
                        <input type="submit" value="Submit" class="btn-primary">
                        <a href="${ctxPath}/manager/faults/list" class="btn-secondary" style="margin-left: 15px;">Cancel</a>
                    </td>
                </tr>
            </table>
        </form:form>
    </div>
</div>
<%@ include file="../00-footer.jsp" %> 