<%--
    SteV<PERSON> - SteckdosenVerwaltung - https://github.com/steve-community/steve
    Copyright (C) 2013-2025 SteVe Community Team
    All Rights Reserved.

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.
--%>
<%@ include file="../00-header.jsp" %>
<div class="content">
    <section><span>编辑故障</span></section>
    
    <form:form action="${ctxPath}/manager/faults/edit/${faultForm.issueId}" method="post" 
              modelAttribute="faultForm" enctype="multipart/form-data">
    
        <form:hidden path="issueId" />
        <form:hidden path="isAutoReported" />
        
        <table class="userInputFullWidth">
            <tr>
                <td><form:label path="chargeBoxPk">充电桩: <span class="required-field">*</span></form:label></td>
                <td>
                    <form:select path="chargeBoxPk" required="required">
                        <c:forEach items="${chargePoints}" var="cp">
                            <option value="${cp.chargeBoxPk}" ${cp.chargeBoxPk eq faultForm.chargeBoxPk ? 'selected' : ''}>
                                ${cp.chargeBoxId}
                            </option>
                        </c:forEach>
                    </form:select>
                    <form:errors path="chargeBoxPk" cssClass="error" />
                </td>
            </tr>
            <tr>
                <td><form:label path="faultDescription">故障描述: <span class="required-field">*</span></form:label></td>
                <td>
                    <form:textarea path="faultDescription" required="required" rows="5" cols="50" />
                    <form:errors path="faultDescription" cssClass="error" />
                </td>
            </tr>
            <tr>
                <td><form:label path="ocppErrorCode">OCPP错误码:</form:label></td>
                <td>
                    <form:input path="ocppErrorCode" />
                    <form:errors path="ocppErrorCode" cssClass="error" />
                </td>
            </tr>
            <tr>
                <td><form:label path="status">状态:</form:label></td>
                <td>
                    <form:select path="status">
                        <c:forEach items="${statuses}" var="status">
                            <form:option value="${status}" label="${status.value()}" />
                        </c:forEach>
                    </form:select>
                    <form:errors path="status" cssClass="error" />
                </td>
            </tr>
            <c:if test="${faultForm.status eq 'RESOLVED'}">
                <tr>
                    <td><form:label path="resolveDescription">解决方案描述:</form:label></td>
                    <td>
                        <form:textarea path="resolveDescription" rows="5" cols="50" />
                        <form:errors path="resolveDescription" cssClass="error" />
                    </td>
                </tr>
            </c:if>
            <tr>
                <td><label for="images">添加更多图片:</label></td>
                <td>
                    <input type="file" name="images" multiple accept="image/*" />
                    <p class="note">可以选择多张图片（按住Ctrl键多选）</p>
                </td>
            </tr>
            <tr>
                <td></td>
                <td>
                    <input type="submit" value="更新">
                    <a href="${ctxPath}/manager/faults/details/${faultForm.issueId}" class="btn">取消</a>
                </td>
            </tr>
        </table>
    </form:form>
</div>
<%@ include file="../00-footer.jsp" %> 