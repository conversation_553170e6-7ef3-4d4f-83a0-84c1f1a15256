/**
 * 表单布局修复样式
 * 专门解决EVSE_OMS项目中表单元素对齐和间距问题
 */

/* ===== 全局表单重置 ===== */
* {
    box-sizing: border-box;
}

/* ===== 搜索面板特殊修复 ===== */
.search-panel {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 20px;
    margin-bottom: 25px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

.search-panel .row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px;
    align-items: flex-end; /* 关键：让所有元素底部对齐 */
}

.search-panel .col-md-4 {
    position: relative;
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
    margin-bottom: 15px;
}

.search-panel .form-group {
    margin-bottom: 0;
    display: flex;
    flex-direction: column;
    height: 100%;
    justify-content: flex-end; /* 让内容向底部对齐 */
}

.search-panel label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    font-size: 14px;
    line-height: 1.4;
    display: block;
}

/* ===== 统一所有表单控件高度 ===== */
.form-control,
input[type="text"],
input[type="number"],
input[type="password"],
input[type="email"],
input[type="date"],
input[type="datetime-local"],
select,
textarea {
    height: 38px !important;
    padding: 8px 12px !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
    border: 1px solid #ced4da !important;
    border-radius: 4px !important;
    background-color: #fff !important;
    color: #495057 !important;
    box-sizing: border-box !important;
    vertical-align: middle !important;
    width: 100% !important;
}

/* 文本域特殊处理 */
textarea {
    height: auto !important;
    min-height: 38px !important;
    resize: vertical !important;
}

/* ===== 统一所有按钮样式 ===== */
.btn,
input[type="submit"],
input[type="button"],
input[type="reset"],
button {
    height: 38px !important;
    padding: 8px 16px !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
    border-radius: 4px !important;
    cursor: pointer !important;
    white-space: nowrap !important;
    min-width: 80px !important;
    box-sizing: border-box !important;
    vertical-align: middle !important;
    display: inline-block !important;
    text-align: center !important;
    text-decoration: none !important;
    border: 1px solid transparent !important;
    transition: all 0.15s ease-in-out !important;
}

/* ===== 按钮颜色样式 ===== */
.btn-primary,
input[type="submit"] {
    background-color: #007bff !important;
    border-color: #007bff !important;
    color: #fff !important;
}

.btn-primary:hover,
input[type="submit"]:hover {
    background-color: #0069d9 !important;
    border-color: #0062cc !important;
    color: #fff !important;
}

.btn-success {
    background-color: #28a745 !important;
    border-color: #28a745 !important;
    color: #fff !important;
}

.btn-success:hover {
    background-color: #218838 !important;
    border-color: #1e7e34 !important;
    color: #fff !important;
}

.btn-info {
    background-color: #17a2b8 !important;
    border-color: #17a2b8 !important;
    color: #fff !important;
}

.btn-info:hover {
    background-color: #138496 !important;
    border-color: #117a8b !important;
    color: #fff !important;
}

.btn-standard {
    background-color: #f8f9fa !important;
    border-color: #dee2e6 !important;
    color: #495057 !important;
}

.btn-standard:hover {
    background-color: #e2e6ea !important;
    border-color: #dae0e5 !important;
    color: #495057 !important;
}

/* ===== 按钮组对齐 ===== */
.btn-group {
    display: flex !important;
    gap: 8px !important;
    align-items: center !important;
    flex-wrap: wrap !important;
}

.btn-group .btn {
    margin: 0 !important;
    flex: 0 0 auto !important;
}

/* ===== 表格内按钮对齐 ===== */
.table .btn {
    margin: 2px !important;
    min-width: 70px !important;
}

/* ===== 搜索面板按钮特殊处理 ===== */
.search-panel .btn {
    margin-top: 0 !important;
    width: 100% !important;
    max-width: 120px !important;
}

/* ===== 表单行对齐 ===== */
.form-row {
    display: flex !important;
    flex-wrap: wrap !important;
    margin-right: -15px !important;
    margin-left: -15px !important;
    align-items: flex-end !important;
}

.form-row .form-group {
    margin-bottom: 0 !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: flex-end !important;
}

/* ===== 水平表单对齐 ===== */
.form-horizontal .form-group {
    display: flex !important;
    align-items: center !important;
    margin-bottom: 15px !important;
}

.form-horizontal .control-label {
    text-align: right !important;
    padding-top: 0 !important;
    margin-bottom: 0 !important;
    line-height: 38px !important;
}

/* ===== 内联表单对齐 ===== */
.form-inline {
    display: flex !important;
    flex-wrap: wrap !important;
    align-items: center !important;
    gap: 15px !important;
}

.form-inline .form-group {
    display: flex !important;
    align-items: center !important;
    margin-bottom: 0 !important;
    gap: 8px !important;
}

.form-inline .form-control {
    width: auto !important;
    min-width: 150px !important;
}

.form-inline .btn {
    margin-left: 8px !important;
}

/* ===== 特殊页面修复 ===== */

/* 充电桩列表页面顶部搜索栏 */
.chargepoints-search {
    background-color: #f8f9fa !important;
    padding: 15px !important;
    border-radius: 6px !important;
    margin-bottom: 20px !important;
    border: 1px solid #e9ecef !important;
}

.chargepoints-search .form-inline {
    justify-content: space-between !important;
    align-items: center !important;
}

.chargepoints-search .form-group {
    margin-right: 15px !important;
}

.chargepoints-search .form-group:last-child {
    margin-right: 0 !important;
}

/* 充电桩页面filter-item布局修复 */
.filter-item {
    display: flex !important;
    flex-direction: column !important;
    margin-right: 15px !important;
    margin-bottom: 15px !important;
    min-width: 150px !important;
    flex: 0 0 auto !important;
}

.filter-item label {
    font-weight: 600 !important;
    color: #495057 !important;
    margin-bottom: 5px !important;
    font-size: 14px !important;
    line-height: 1.4 !important;
    white-space: nowrap !important;
}

.filter-item .form-control,
.filter-item .form-select {
    height: 38px !important;
    padding: 8px 12px !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
    border: 1px solid #ced4da !important;
    border-radius: 4px !important;
    background-color: #fff !important;
    color: #495057 !important;
    box-sizing: border-box !important;
    width: 100% !important;
}

.filter-item .form-control-sm,
.filter-item .form-select-sm {
    height: 38px !important;
    padding: 8px 12px !important;
    font-size: 14px !important;
}

/* 搜索面板中的d-flex容器 */
.search-panel .d-flex {
    display: flex !important;
    flex-wrap: wrap !important;
    align-items: flex-end !important;
    gap: 0 !important;
}

/* 按钮区域特殊处理 */
.filter-item:last-child {
    margin-right: 0 !important;
}

.filter-item .d-flex {
    gap: 8px !important;
    margin-top: 5px !important;
}

.filter-item .btn {
    height: 38px !important;
    padding: 8px 12px !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
    white-space: nowrap !important;
    min-width: 80px !important;
    flex-grow: 0 !important;
    flex-shrink: 0 !important;
    max-width: 120px !important;
}

/* 交易页面搜索表单 */
.transaction-search .form-group {
    margin-bottom: 15px !important;
}

.transaction-search .form-control {
    margin-bottom: 0 !important;
}

/* ===== 响应式修复 ===== */
@media (max-width: 768px) {
    .search-panel .col-md-4 {
        flex: 0 0 100% !important;
        max-width: 100% !important;
        margin-bottom: 15px !important;
    }
    
    .search-panel .btn {
        width: 100% !important;
        max-width: none !important;
    }
    
    .btn-group {
        flex-direction: column !important;
        gap: 10px !important;
    }
    
    .btn-group .btn {
        width: 100% !important;
    }
    
    .form-inline {
        flex-direction: column !important;
        align-items: stretch !important;
    }
    
    .form-inline .form-group {
        width: 100% !important;
        margin-bottom: 15px !important;
    }
    
    .form-inline .form-control {
        width: 100% !important;
    }
}

/* ===== 清除默认样式冲突 ===== */
input, select, textarea, button {
    font-family: inherit !important;
    font-size: inherit !important;
}

/* 移除浏览器默认样式 */
input:focus,
select:focus,
textarea:focus,
button:focus {
    outline: none !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
}

/* ===== 特殊元素对齐修复 ===== */
.tooltip {
    vertical-align: middle !important;
}

.tooltip img {
    vertical-align: middle !important;
    margin-left: 5px !important;
}

/* 状态指示器对齐 */
.status-indicator {
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    height: 24px !important;
    padding: 0 8px !important;
    border-radius: 4px !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    vertical-align: middle !important;
}
