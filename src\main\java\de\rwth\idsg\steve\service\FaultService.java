/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.service;

import de.rwth.idsg.steve.web.dto.ChargerIssueDTO;
import de.rwth.idsg.steve.web.dto.ChargerIssueForm;
import de.rwth.idsg.steve.web.dto.ChargerIssueStatus;
import de.rwth.idsg.steve.web.dto.FaultForm;
import de.rwth.idsg.steve.web.dto.MaintenanceRecordDTO;
import de.rwth.idsg.steve.web.dto.MaintenanceRecordForm;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * 充电桩故障服务接口
 */
public interface FaultService {

    /**
     * 获取所有故障
     */
    List<ChargerIssueDTO> getIssues();
    
    /**
     * 按状态获取故障
     */
    List<ChargerIssueDTO> getIssuesByStatus(ChargerIssueStatus status);
    
    /**
     * 获取特定充电桩的故障
     */
    List<ChargerIssueDTO> getIssuesByChargeBoxId(String chargeBoxId);
    
    /**
     * 根据ID获取故障
     */
    ChargerIssueDTO getIssue(Integer issueId);
    
    /**
     * 根据过滤条件获取故障列表
     */
    List<ChargerIssueDTO> getFilteredIssues(FaultForm form);
    
    /**
     * 创建故障
     */
    int createIssue(ChargerIssueForm form, String username);
    
    /**
     * 更新故障
     */
    void updateIssue(ChargerIssueForm form);
    
    /**
     * 更新故障状态
     */
    void updateIssueStatus(int issueId, ChargerIssueStatus status, String username);
    
    /**
     * 解决故障
     */
    void resolveIssue(int issueId, String resolveDescription, String username);
    
    /**
     * 上传并保存故障图片
     */
    void saveIssueImages(int issueId, List<MultipartFile> images);
    
    /**
     * 添加维护记录
     */
    int addMaintenanceRecord(MaintenanceRecordForm form, String username);
    
    /**
     * 获取故障的维护记录
     */
    List<MaintenanceRecordDTO> getMaintenanceRecords(int issueId);
    
    /**
     * 根据OCPP错误代码自动创建故障
     */
    int createAutoIssue(String chargeBoxId, String ocppErrorCode, String faultDescription);

    /**
     * 根据OCPP错误代码和厂商错误代码自动创建故障
     */
    int createAutoIssue(String chargeBoxId, String ocppErrorCode, String faultDescription, String vendorErrorCode, String vendorId);
} 