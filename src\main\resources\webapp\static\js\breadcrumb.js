/**
 * 自动生成面包屑导航
 */
document.addEventListener('DOMContentLoaded', function() {
    // 获取当前路径
    const path = window.location.pathname;
    const ctxPath = document.querySelector('meta[name="ctx"]').getAttribute('content');
    const cleanPath = path.replace(ctxPath, '');
    
    // 如果不是首页，才显示面包屑
    if (cleanPath !== '/manager/home' && cleanPath !== '/manager/') {
        // 创建面包屑容器
        const breadcrumb = document.createElement('div');
        breadcrumb.className = 'breadcrumb';
        
        // 分割路径
        const pathParts = cleanPath.split('/').filter(part => part);
        
        // 添加首页链接
        const homeLink = document.createElement('a');
        homeLink.href = ctxPath + '/manager/home';
        homeLink.textContent = 'Home';
        breadcrumb.appendChild(homeLink);
        
        // 添加分隔符
        const separator = document.createElement('span');
        separator.className = 'separator';
        separator.textContent = '>';
        breadcrumb.appendChild(separator);
        
        // 构建当前页面的名称
        let currentPageName = '';
        if (pathParts.length > 0) {
            const lastPart = pathParts[pathParts.length - 1];
            
            // 映射路径到英文名称
            const pathMappings = {
                'home': 'Home',
                'chargepoints': 'Charge Points',
                'ocppTags': 'OCPP Tags',
                'users': 'Users',
                'ownerAssignments': 'Owner Assignments',
                'chargingProfiles': 'Charging Profiles',
                'reservations': 'Reservations',
                'transactions': 'Transactions',
                'operations': 'Operations',
                'settings': 'Settings',
                'log': 'Log',
                'about': 'About',
                'v1.6': 'OCPP v1.6',
                'faults': 'Fault Management',
                'report-fault': 'Report Fault',
                'notification': 'Fault Notification',
                'connectorStatus': 'Connector Status'
            };
            
            currentPageName = pathMappings[lastPart] || lastPart;
        }
        
        // 添加当前页面名称
        const currentPage = document.createElement('span');
        currentPage.textContent = currentPageName;
        breadcrumb.appendChild(currentPage);
        
        // 将面包屑添加到内容区域顶部
        const contentContainer = document.querySelector('.content-container');
        if (contentContainer) {
            contentContainer.insertBefore(breadcrumb, contentContainer.firstChild);
        }
    }
}); 