/*
 * SteV<PERSON> - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.utils;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.joda.time.LocalDateTime;
import org.joda.time.Period;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.joda.time.format.PeriodFormatter;
import org.joda.time.format.PeriodFormatterBuilder;

import java.time.ZoneId;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> Goekay <<EMAIL>>
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class DateTimeUtils {

    private static final DateTimeFormatter HUMAN_FORMATTER = DateTimeFormat.forPattern("yyyy-MM-dd 'at' HH:mm");
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormat.forPattern("HH:mm");

    private static final PeriodFormatter PERIOD_FORMATTER = new PeriodFormatterBuilder()
            .printZeroNever()
            .appendDays().appendSuffix(" day", " days").appendSeparator(" ")
            .appendHours().appendSuffix(" hour", " hours").appendSeparator(" ")
            .appendMinutes().appendSuffix(" minute", " minutes").appendSeparator(" ")
            .appendSeconds().appendSuffix(" second", " seconds")
            .toFormatter();

    public static DateTime toDateTime(LocalDateTime ldt) {
        if (ldt == null) {
            return null;
        } else {
            return ldt.toDateTime();
        }
    }

    public static LocalDateTime toLocalDateTime(DateTime dt) {
        if (dt == null) {
            return null;
        } else {
            return dt.toLocalDateTime();
        }
    }

    /**
     * Convert java.time.LocalDateTime to org.joda.time.DateTime
     */
    public static DateTime toDateTime(java.time.LocalDateTime ldt) {
        if (ldt == null) {
            return null;
        } else {
            return new DateTime(ldt.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        }
    }

    /**
     * Print the date/time nicer, if it's from today, yesterday or tomorrow.
     */
    public static String humanize(DateTime dt) {
        if (dt == null) {
            return "";
        }

        // Equalize time fields before comparing date fields
        DateTime inputAtMidnight = dt.withTimeAtStartOfDay();
        DateTime todayAtMidnight = DateTime.now().withTimeAtStartOfDay();

        // Is it today?
        if (inputAtMidnight.equals(todayAtMidnight)) {
            return "Today at " + TIME_FORMATTER.print(dt);

        // Is it yesterday?
        } else if (inputAtMidnight.equals(todayAtMidnight.minusDays(1))) {
            return "Yesterday at " + TIME_FORMATTER.print(dt);

        // Is it tomorrow?
        } else if (inputAtMidnight.equals(todayAtMidnight.plusDays(1))) {
            return "Tomorrow at " + TIME_FORMATTER.print(dt);

        // So long ago OR in the future...
        } else {
            return HUMAN_FORMATTER.print(dt);
        }
    }

    public static String timeElapsed(DateTime from, DateTime to) {
        return PERIOD_FORMATTER.print(new Period(from, to));
    }

    public static long getOffsetFromUtcInSeconds() {
        DateTimeZone timeZone = DateTimeZone.getDefault();
        DateTime now = DateTime.now();
        long offsetInMilliseconds = timeZone.getOffset(now.getMillis());
        return TimeUnit.MILLISECONDS.toSeconds(offsetInMilliseconds);
    }

    // 修改工具方法，始终返回DateTime对象
    public static DateTime convertLocalDateTimeValue(Object value) {
        if (value instanceof LocalDateTime) {
            return toDateTime((LocalDateTime) value);
        } else if (value instanceof java.time.LocalDateTime) {
            return toDateTime((java.time.LocalDateTime) value);
        } else if (value instanceof java.sql.Timestamp) {
            // 处理java.sql.Timestamp类型
            java.sql.Timestamp timestamp = (java.sql.Timestamp) value;
            return new DateTime(timestamp.getTime());
        } else if (value instanceof DateTime) {
            return (DateTime) value;
        }
        // 如果无法处理，返回null
        return null;
    }
}
