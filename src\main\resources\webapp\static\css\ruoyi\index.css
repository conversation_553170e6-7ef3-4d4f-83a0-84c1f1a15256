/**
 * EVSE_OMS主样式入口
 * 基于RuoYi-Vue样式改造
 */

/* 引入各个模块样式 */
@import url("ruoyi.css");
@import url("theme.css");
@import url("buttons.css");
@import url("table.css");
@import url("form.css");
@import url("helpers.css");
@import url("style-override.css");
@import url("nav-layout.css");
@import url("content-layout.css");

/**
 * 统一设置一些全局样式
 */

/* 清除浮动 */
.clearfix:before,
.clearfix:after {
  display: table;
  content: " ";
}

.clearfix:after {
  clear: both;
}

/* 响应式图片 */
.img-responsive {
  display: block;
  max-width: 100%;
  height: auto;
}

/* 文本对齐 */
.text-left { text-align: left; }
.text-right { text-align: right; }
.text-center { text-align: center; }
.text-justify { text-align: justify; }
.text-nowrap { white-space: nowrap; }

/* 文本颜色 */
.text-primary { color: #409EFF; }
.text-success { color: #67C23A; }
.text-info { color: #909399; }
.text-warning { color: #E6A23C; }
.text-danger { color: #F56C6C; }
.text-muted { color: #909399; }

/* 背景颜色 */
.bg-primary { background-color: #409EFF; color: #fff; }
.bg-success { background-color: #67C23A; color: #fff; }
.bg-info { background-color: #909399; color: #fff; }
.bg-warning { background-color: #E6A23C; color: #fff; }
.bg-danger { background-color: #F56C6C; color: #fff; }

/* 隐藏显示 */
.hide { display: none !important; }
.show { display: block !important; }
.invisible { visibility: hidden; }

/* 文本截断 */
.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 浮动 */
.pull-left { float: left !important; }
.pull-right { float: right !important; }

/* 边距工具类 */
.no-margin { margin: 0 !important; }
.no-padding { padding: 0 !important; }

/* 边框 */
.no-border { border: 0 !important; }
.border { border: 1px solid #ebeef5; }
.rounded { border-radius: 4px; }
.circle { border-radius: 50%; }

/* Flex布局辅助 */
.d-flex { display: flex; }
.flex-row { flex-direction: row; }
.flex-column { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }
.justify-content-start { justify-content: flex-start; }
.justify-content-end { justify-content: flex-end; }
.justify-content-center { justify-content: center; }
.justify-content-between { justify-content: space-between; }
.justify-content-around { justify-content: space-around; }
.align-items-start { align-items: flex-start; }
.align-items-end { align-items: flex-end; }
.align-items-center { align-items: center; }
.align-items-baseline { align-items: baseline; }
.align-items-stretch { align-items: stretch; }

/* 阴影 */
.shadow-sm { box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05); }
.shadow { box-shadow: 0 1px 3px rgba(0, 21, 41, 0.08); }
.shadow-lg { box-shadow: 0 3px 5px rgba(0, 21, 41, 0.15); }

/* 过渡动画 */
.transition-all { transition: all .3s; }

/* 鼠标样式 */
.cursor-pointer { cursor: pointer; }
.cursor-not-allowed { cursor: not-allowed; }

/* 响应式工具类 */
@media (max-width: 768px) {
  .hidden-xs { display: none !important; }
}

@media (min-width: 769px) and (max-width: 992px) {
  .hidden-sm { display: none !important; }
}

@media (min-width: 993px) and (max-width: 1200px) {
  .hidden-md { display: none !important; }
}

@media (min-width: 1201px) {
  .hidden-lg { display: none !important; }
}