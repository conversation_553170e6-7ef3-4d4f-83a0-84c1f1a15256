<%--
    SteV<PERSON> - SteckdosenVerwaltung - https://github.com/steve-community/steve
    Copyright (C) 2013-2025 SteVe Community Team
    All Rights Reserved.

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.
--%>
<%@ include file="00-header.jsp" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<div class="content">
    <div>
        <section><span>Fault Details</span></section>
        <div class="action-buttons">
            <a href="${ctxPath}/manager/operations/faults" class="btn">Return to List</a>
            <a href="${ctxPath}/manager/operations/update-fault/${issue.issueId}" class="btn">Update Status</a>
        </div>
        
        <table class="userInputFullWidth">
            <tr>
                <td>Fault ID:</td>
                <td>${issue.issueId}</td>
                <td>Charge Point:</td>
                <td>
                    <a href="${ctxPath}/manager/chargepoints/details/${issue.chargeBoxPk}">
                        ${issue.chargeBoxId}
                    </a>
                </td>
            </tr>
            <tr>
                <td>Report Time:</td>
                <td><fmt:formatDate value="${issue.reportTime}" pattern="yyyy-MM-dd HH:mm:ss" /></td>
                <td>Report Type:</td>
                <td>${issue.isAutoReported ? 'Automatic' : 'Manual'}</td>
            </tr>
            <tr>
                <td>Reporter:</td>
                <td>${issue.reporterUsername}</td>
                <td>OCPP Error Code:</td>
                <td>${issue.ocppErrorCode}</td>
            </tr>
            <tr>
                <td>Current Status:</td>
                <td>
                    <c:choose>
                        <c:when test="${issue.status eq 'NEW'}"><span class="status-new">New</span></c:when>
                        <c:when test="${issue.status eq 'IN_PROGRESS'}"><span class="status-in-progress">In Progress</span></c:when>
                        <c:when test="${issue.status eq 'RESOLVED'}"><span class="status-resolved">Resolved</span></c:when>
                        <c:otherwise>${issue.status}</c:otherwise>
                    </c:choose>
                </td>
                <td>Owner:</td>
                <td>${issue.chargeBoxOwner}</td>
            </tr>
            <tr>
                <td>Fault Description:</td>
                <td colspan="3">${issue.faultDescription}</td>
            </tr>
            <c:if test="${not empty issue.resolveDescription}">
                <tr>
                    <td>Solution:</td>
                    <td colspan="3">${issue.resolveDescription}</td>
                </tr>
            </c:if>
            <c:if test="${not empty issue.resolveTime}">
                <tr>
                    <td>Resolution Time:</td>
                    <td colspan="3"><fmt:formatDate value="${issue.resolveTime}" pattern="yyyy-MM-dd HH:mm:ss" /></td>
                </tr>
            </c:if>
        </table>
    </div>
    
    <br>
    
    <div>
        <section><span>Fault Images</span></section>
        <div class="image-gallery">
            <c:if test="${empty issue.imagePaths}">
                <p>No uploaded images</p>
            </c:if>
            <c:forEach items="${issue.imagePaths}" var="imagePath">
                <div class="image-item">
                    <c:choose>
                        <%-- 处理完整路径格式，如 uploads/issue-images/filename --%>
                        <c:when test="${fn:startsWith(imagePath, 'uploads/')}">
                            <a href="${ctxPath}/static/${imagePath}" target="_blank">
                                <img src="${ctxPath}/static/${imagePath}" 
                                    alt="Fault Image" class="fault-image" />
                            </a>
                        </c:when>
                        <%-- 处理以/开头的路径格式，如 /uploads/issues/... --%>
                        <c:when test="${fn:startsWith(imagePath, '/')}">
                            <a href="${ctxPath}/static${imagePath}" target="_blank">
                                <img src="${ctxPath}/static${imagePath}" 
                                    alt="Fault Image" class="fault-image" />
                            </a>
                        </c:when>
                        <%-- 处理其他格式（默认是纯文件名） --%>
                        <c:otherwise>
                            <a href="${ctxPath}/static/images/${imagePath}" target="_blank">
                                <img src="${ctxPath}/static/images/${imagePath}" 
                                    alt="Fault Image" class="fault-image" />
                            </a>
                        </c:otherwise>
                    </c:choose>
                </div>
            </c:forEach>
        </div>
    </div>
    
    <br>
    
    <div>
        <section><span>Maintenance Records</span></section>
        <c:if test="${empty maintenanceRecords}">
            <p>No maintenance records</p>
        </c:if>
        <c:if test="${not empty maintenanceRecords}">
            <table class="res">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Maintenance Time</th>
                        <th>Maintainer</th>
                        <th>Maintenance Content</th>
                    </tr>
                </thead>
                <tbody>
                <c:forEach items="${maintenanceRecords}" var="record">
                    <tr>
                        <td>${record.recordId}</td>
                        <td><fmt:formatDate value="${record.maintenanceTime}" pattern="yyyy-MM-dd HH:mm:ss" /></td>
                        <td>${record.maintainerUsername}</td>
                        <td>${record.maintenanceDescription}</td>
                    </tr>
                </c:forEach>
                </tbody>
            </table>
        </c:if>
    </div>
    
    <br>
    
    <div>
        <section><span>Add Maintenance Record</span></section>
        <form:form action="${ctxPath}/manager/operations/add-maintenance/${issue.issueId}" method="post" 
                  modelAttribute="maintenanceForm">
            
            <table class="userInputFullWidth">
                <tr>
                    <td><form:hidden path="issueId" value="${issue.issueId}" /></td>
                </tr>
                <tr>
                    <td><form:label path="maintenanceDescription">Maintenance Content: <span class="required-field">*</span></form:label></td>
                    <td>
                        <form:textarea path="maintenanceDescription" required="required" rows="3" cols="50" 
                                     placeholder="Please describe maintenance content and result" />
                        <form:errors path="maintenanceDescription" cssClass="error" />
                    </td>
                </tr>
                <tr>
                    <td></td>
                    <td>
                        <input type="submit" value="Add Maintenance Record">
                    </td>
                </tr>
            </table>
        </form:form>
    </div>
    
    <c:if test="${issue.status ne 'RESOLVED'}">
        <br>
        
        <div>
            <section><span>Resolve Fault</span></section>
            <form action="${ctxPath}/manager/operations/resolve/${issue.issueId}" method="post">
                <table class="userInputFullWidth">
                    <tr>
                        <td><label for="resolveDescription">Solution: <span class="required-field">*</span></label></td>
                        <td>
                            <textarea name="resolveDescription" id="resolveDescription" required rows="3" cols="50" 
                                     placeholder="Please describe the solution and result"></textarea>
                        </td>
                    </tr>
                    <tr>
                        <td></td>
                        <td>
                            <input type="submit" value="Mark as Resolved">
                        </td>
                    </tr>
                </table>
            </form>
        </div>
    </c:if>
</div>

<%@ include file="00-footer.jsp" %> 