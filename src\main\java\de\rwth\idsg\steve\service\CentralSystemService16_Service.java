/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.service;

import de.rwth.idsg.steve.ocpp.OcppProtocol;
import de.rwth.idsg.steve.ocpp.OcppStatusNotificationProcessor;
import de.rwth.idsg.steve.repository.OcppServerRepository;
import de.rwth.idsg.steve.repository.SettingsRepository;
import de.rwth.idsg.steve.repository.ChargePointRepository;
import de.rwth.idsg.steve.repository.UpdateFirmwareLogRepository;
import de.rwth.idsg.steve.repository.dto.InsertConnectorStatusParams;
import de.rwth.idsg.steve.service.FirmwareFileService;
import de.rwth.idsg.steve.repository.dto.InsertTransactionParams;
import de.rwth.idsg.steve.repository.dto.UpdateChargeboxParams;
import de.rwth.idsg.steve.repository.dto.UpdateTransactionParams;
import de.rwth.idsg.steve.service.notification.OccpStationBooted;
import de.rwth.idsg.steve.service.notification.OcppFirmwareStatusUpdate;
import de.rwth.idsg.steve.service.notification.OcppTransactionEnded;
import de.rwth.idsg.steve.service.notification.OcppTransactionStarted;
import de.rwth.idsg.steve.web.dto.UpdateFirmwareLog;
import jooq.steve.db.enums.TransactionStopEventActor;
import lombok.extern.slf4j.Slf4j;
import ocpp.cs._2015._10.AuthorizationStatus;
import ocpp.cs._2015._10.AuthorizeRequest;
import ocpp.cs._2015._10.AuthorizeResponse;
import ocpp.cs._2015._10.BootNotificationRequest;
import ocpp.cs._2015._10.BootNotificationResponse;
import ocpp.cs._2015._10.ChargePointStatus;
import ocpp.cs._2015._10.DataTransferRequest;
import ocpp.cs._2015._10.DataTransferResponse;
import ocpp.cs._2015._10.DataTransferStatus;
import ocpp.cs._2015._10.DiagnosticsStatusNotificationRequest;
import ocpp.cs._2015._10.DiagnosticsStatusNotificationResponse;
import ocpp.cs._2015._10.FirmwareStatusNotificationRequest;
import ocpp.cs._2015._10.FirmwareStatusNotificationResponse;
import ocpp.cs._2015._10.HeartbeatRequest;
import ocpp.cs._2015._10.HeartbeatResponse;
import ocpp.cs._2015._10.IdTagInfo;
import ocpp.cs._2015._10.MeterValuesRequest;
import ocpp.cs._2015._10.MeterValuesResponse;
import ocpp.cs._2015._10.RegistrationStatus;
import ocpp.cs._2015._10.StartTransactionRequest;
import ocpp.cs._2015._10.StartTransactionResponse;
import ocpp.cs._2015._10.StatusNotificationRequest;
import ocpp.cs._2015._10.StatusNotificationResponse;
import ocpp.cs._2015._10.StopTransactionRequest;
import ocpp.cs._2015._10.StopTransactionResponse;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> Goekay <<EMAIL>>
 * @since 13.03.2018
 */
@Slf4j
@Service
public class CentralSystemService16_Service {

    @Autowired private OcppServerRepository ocppServerRepository;
    @Autowired private SettingsRepository settingsRepository;
    @Autowired private ChargePointRepository chargePointRepository;

    @Autowired private OcppTagService ocppTagService;
    @Autowired private ApplicationEventPublisher applicationEventPublisher;
    @Autowired private ChargePointHelperService chargePointHelperService;
    @Autowired private OcppStatusNotificationProcessor statusNotificationProcessor;
    @Autowired private UpdateFirmwareLogRepository updateFirmwareLogRepository;
    @Autowired private ChargingSuccessService chargingSuccessService;
    @Autowired private DiagnosticsProgressTracker diagnosticsProgressTracker;
    @Autowired private FirmwareFileService firmwareFileService;

    public BootNotificationResponse bootNotification(BootNotificationRequest parameters, String chargeBoxIdentity,
                                                     OcppProtocol ocppProtocol) {

        Optional<RegistrationStatus> status = chargePointHelperService.getRegistrationStatus(chargeBoxIdentity);
        applicationEventPublisher.publishEvent(new OccpStationBooted(chargeBoxIdentity, status));
        DateTime now = DateTime.now();

        if (status.isEmpty()) {
            // Applies only to stations not in db (regardless of the registration_status field from db)
            log.error("The chargebox '{}' is NOT in database.", chargeBoxIdentity);
        } else {
            // Applies to all stations in db (even with registration_status Rejected)
            log.info("The boot of the chargebox '{}' with registration status '{}' is acknowledged.", chargeBoxIdentity, status);
            UpdateChargeboxParams params =
                    UpdateChargeboxParams.builder()
                                         .ocppProtocol(ocppProtocol)
                                         .vendor(parameters.getChargePointVendor())
                                         .model(parameters.getChargePointModel())
                                         .pointSerial(parameters.getChargePointSerialNumber())
                                         .boxSerial(parameters.getChargeBoxSerialNumber())
                                         .fwVersion(parameters.getFirmwareVersion())
                                         .iccid(parameters.getIccid())
                                         .imsi(parameters.getImsi())
                                         .meterType(parameters.getMeterType())
                                         .meterSerial(parameters.getMeterSerialNumber())
                                         .chargeBoxId(chargeBoxIdentity)
                                         .heartbeatTimestamp(now)
                                         .build();

            ocppServerRepository.updateChargebox(params);
            
            // 检查是否有未完成的固件升级记录
            try {
                List<UpdateFirmwareLog> recentLogs = updateFirmwareLogRepository.getRecentLogs(chargeBoxIdentity, 7200); // 查找最近2小时内的记录
                if (recentLogs != null && !recentLogs.isEmpty()) {
                    for (UpdateFirmwareLog firmwareLog : recentLogs) {
                        if ("Installing".equals(firmwareLog.getStatus())) {
                            // 如果启动但未被接受（或状态不存在），可能是升级失败
                            if (!status.isPresent() || status.get() != RegistrationStatus.ACCEPTED) {
                                int updated = updateFirmwareLogRepository.updateStatus(firmwareLog.getLogId(), "InstallationFailed", 
                                        "Firmware installation may have failed. Boot notification received with status: " + 
                                        (status.isPresent() ? status.get().toString() : "UNKNOWN"));
                                if (updated > 0) {
                                    log.warn("Updated firmware log status after boot for chargeBoxId={}, new status=InstallationFailed", String.valueOf(chargeBoxIdentity));
                                    
                                    // 发布固件升级失败事件
                                    OcppFirmwareStatusUpdate event = OcppFirmwareStatusUpdate.builder()
                                        .chargeBoxId(chargeBoxIdentity)
                                        .status("InstallationFailed")
                                        .firmwareLocation(firmwareLog.getLocation())
                                        .timestamp(DateTime.now())
                                        .build();
                                    applicationEventPublisher.publishEvent(event);
                                }
                            }
                            break; // 只处理最近的一条记录
                        }
                    }
                }
            } catch (Exception e) {
                log.error("Error checking firmware update status during boot notification: {}", e.getMessage(), e);
                // 不影响主要业务流程，继续处理
            }
        }

        return new BootNotificationResponse()
                .withStatus(status.orElse(RegistrationStatus.REJECTED))
                .withCurrentTime(now)
                .withInterval(settingsRepository.getHeartbeatIntervalInSeconds());
    }

    public FirmwareStatusNotificationResponse firmwareStatusNotification(
            FirmwareStatusNotificationRequest parameters, String chargeBoxIdentity) {
        String status = parameters.getStatus().value();
        ocppServerRepository.updateChargeboxFirmwareStatus(chargeBoxIdentity, status);
        
        // 添加明确的状态日志
        log.info("Received firmware status notification from {}: {}", chargeBoxIdentity, status);
        
        // 查找最近的固件升级记录并更新状态
        try {
            List<UpdateFirmwareLog> recentLogs = updateFirmwareLogRepository.getRecentLogs(chargeBoxIdentity, 3600); // 查找1小时内的记录
            if (recentLogs != null && !recentLogs.isEmpty()) {
                UpdateFirmwareLog latestLog = recentLogs.get(0); // 最新的记录
                
                // 更新状态
                String responseMsg = "Status notification received: " + status;
                int updated = updateFirmwareLogRepository.updateStatus(latestLog.getLogId(), status, responseMsg);
                if (updated > 0) {
                    log.info("Updated firmware log status for chargeBoxId={}, new status={}", chargeBoxIdentity, status);
                    
                    // Now, update the charge_box table with the mapped status
                    String lastUpgradeStatus = mapToLastUpgradeStatus(status);
                    chargePointRepository.updateLastUpgradeStatus(chargeBoxIdentity, lastUpgradeStatus);

                    // Handle firmware file status updates based on installation status
                    handleFirmwareFileStatusUpdate(status, latestLog.getLocation());

                    // 发布事件
                    OcppFirmwareStatusUpdate event = OcppFirmwareStatusUpdate.builder()
                        .chargeBoxId(chargeBoxIdentity)
                        .status(status)
                        .firmwareLocation(latestLog.getLocation())
                        .timestamp(DateTime.now())
                        .build();
                    applicationEventPublisher.publishEvent(event);
                }
            }
        } catch (Exception e) {
            log.error("Error updating firmware status in log: {}", e.getMessage(), e);
        }
        
        return new FirmwareStatusNotificationResponse();
    }

    private String mapToLastUpgradeStatus(String status) {
        if (status == null) {
            return null;
        }
        switch (status) {
            case "Installed":
                return "Upgrade succeeded";
            case "Downloading":
            case "Installing":
            case "Downloaded":
                return "Upgrading";
            case "DownloadFailed":
            case "InstallationFailed":
            case "Installation timeout":
                return "Upgrade failed";
            default:
                return null;
        }
    }

    public StatusNotificationResponse statusNotification(
            StatusNotificationRequest parameters, String chargeBoxIdentity) {
        // Optional field
        DateTime timestamp = parameters.isSetTimestamp() ? parameters.getTimestamp() : DateTime.now();

        InsertConnectorStatusParams params =
                InsertConnectorStatusParams.builder()
                                           .chargeBoxId(chargeBoxIdentity)
                                           .connectorId(parameters.getConnectorId())
                                           .status(parameters.getStatus().value())
                                           .errorCode(parameters.getErrorCode().value())
                                           .timestamp(timestamp)
                                           .errorInfo(parameters.getInfo())
                                           .vendorId(parameters.getVendorId())
                                           .vendorErrorCode(parameters.getVendorErrorCode())
                                           .build();

        ocppServerRepository.insertConnectorStatus(params);

        // 使用通知处理器处理状态通知
        statusNotificationProcessor.process16(
            chargeBoxIdentity,
            parameters.getConnectorId(),
            parameters.getStatus(),
            parameters.getErrorCode(),
            parameters.getInfo(),
            timestamp.toString(),
            parameters.getVendorErrorCode(),
            parameters.getVendorId()
        );

        // 处理充电成功率统计
        try {
            chargingSuccessService.handleStatusNotification(
                chargeBoxIdentity,
                parameters.getConnectorId(),
                parameters.getStatus(),
                null // 状态通知中没有事务ID
            );
        } catch (Exception e) {
            log.error("处理充电成功率统计时发生错误: chargeBoxId={}, connectorId={}, status={}",
                    chargeBoxIdentity, parameters.getConnectorId(), parameters.getStatus(), e);
        }

        return new StatusNotificationResponse();
    }

    public MeterValuesResponse meterValues(MeterValuesRequest parameters, String chargeBoxIdentity) {
        Integer transactionId = getTransactionId(parameters);

        ocppServerRepository.insertMeterValues(
                chargeBoxIdentity,
                parameters.getMeterValue(),
                parameters.getConnectorId(),
                transactionId
        );

        // 处理充电成功率统计
        try {
            chargingSuccessService.handleMeterValues(
                chargeBoxIdentity,
                parameters.getConnectorId(),
                parameters.getMeterValue(),
                transactionId
            );
        } catch (Exception e) {
            log.error("处理MeterValues充电成功率统计时发生错误: chargeBoxId={}, connectorId={}, transactionId={}",
                    chargeBoxIdentity, parameters.getConnectorId(), transactionId, e);
        }

        return new MeterValuesResponse();
    }

    public DiagnosticsStatusNotificationResponse diagnosticsStatusNotification(
            DiagnosticsStatusNotificationRequest parameters, String chargeBoxIdentity) {
        String status = parameters.getStatus().value();
        ocppServerRepository.updateChargeboxDiagnosticsStatus(chargeBoxIdentity, status);

        // 更新诊断进度跟踪
        updateDiagnosticsProgress(chargeBoxIdentity, status);

        return new DiagnosticsStatusNotificationResponse();
    }

    public StartTransactionResponse startTransaction(StartTransactionRequest parameters, String chargeBoxIdentity) {
        // Get the authorization info of the user, before making tx changes (will affectAuthorizationStatus)
        IdTagInfo info = ocppTagService.getIdTagInfo(
                parameters.getIdTag(),
                true,
                chargeBoxIdentity,
                parameters.getConnectorId(),
                () -> new IdTagInfo().withStatus(AuthorizationStatus.INVALID) // IdTagInfo is required
        );

        InsertTransactionParams params =
                InsertTransactionParams.builder()
                                       .chargeBoxId(chargeBoxIdentity)
                                       .connectorId(parameters.getConnectorId())
                                       .idTag(parameters.getIdTag())
                                       .startTimestamp(parameters.getTimestamp())
                                       .startMeterValue(Integer.toString(parameters.getMeterStart()))
                                       .reservationId(parameters.getReservationId())
                                       .eventTimestamp(DateTime.now())
                                       .build();

        int transactionId = ocppServerRepository.insertTransaction(params);

        applicationEventPublisher.publishEvent(new OcppTransactionStarted(transactionId, params));

        return new StartTransactionResponse()
                .withIdTagInfo(info)
                .withTransactionId(transactionId);
    }

    public StopTransactionResponse stopTransaction(StopTransactionRequest parameters, String chargeBoxIdentity) {
        int transactionId = parameters.getTransactionId();
        String stopReason = parameters.isSetReason() ? parameters.getReason().value() : null;

        // Get the authorization info of the user, before making tx changes (will affectAuthorizationStatus)
        IdTagInfo idTagInfo = ocppTagService.getIdTagInfo(
                parameters.getIdTag(),
                false,
                chargeBoxIdentity,
                null,
                () -> null
        );

        UpdateTransactionParams params =
                UpdateTransactionParams.builder()
                                       .chargeBoxId(chargeBoxIdentity)
                                       .transactionId(transactionId)
                                       .stopTimestamp(parameters.getTimestamp())
                                       .stopMeterValue(Integer.toString(parameters.getMeterStop()))
                                       .stopReason(stopReason)
                                       .eventTimestamp(DateTime.now())
                                       .eventActor(TransactionStopEventActor.station)
                                       .build();

        ocppServerRepository.updateTransaction(params);

        ocppServerRepository.insertMeterValues(chargeBoxIdentity, parameters.getTransactionData(), transactionId);

        applicationEventPublisher.publishEvent(new OcppTransactionEnded(params));

        return new StopTransactionResponse().withIdTagInfo(idTagInfo);
    }

    public HeartbeatResponse heartbeat(HeartbeatRequest parameters, String chargeBoxIdentity) {
        DateTime now = DateTime.now();
        ocppServerRepository.updateChargeboxHeartbeat(chargeBoxIdentity, now);

        return new HeartbeatResponse().withCurrentTime(now);
    }

    public AuthorizeResponse authorize(AuthorizeRequest parameters, String chargeBoxIdentity) {
        // Get the authorization info of the user
        IdTagInfo idTagInfo = ocppTagService.getIdTagInfo(
                parameters.getIdTag(),
                false,
                chargeBoxIdentity,
                null,
                () -> new IdTagInfo().withStatus(AuthorizationStatus.INVALID)
        );

        return new AuthorizeResponse().withIdTagInfo(idTagInfo);
    }

    /**
     * Dummy implementation. This is new in OCPP 1.5. It must be vendor-specific.
     */
    public DataTransferResponse dataTransfer(DataTransferRequest parameters, String chargeBoxIdentity) {
        log.info("[Data Transfer] Charge point: {}, Vendor Id: {}", chargeBoxIdentity, parameters.getVendorId());
        if (parameters.isSetMessageId()) {
            log.info("[Data Transfer] Message Id: {}", parameters.getMessageId());
        }
        if (parameters.isSetData()) {
            log.info("[Data Transfer] Data: {}", parameters.getData());
        }

        // OCPP requires a status to be set. Since this is a dummy impl, set it to "Accepted".
        // https://github.com/steve-community/steve/pull/36
        return new DataTransferResponse().withStatus(DataTransferStatus.ACCEPTED);
    }

    // -------------------------------------------------------------------------
    // Helpers
    // -------------------------------------------------------------------------

    /**
     * https://github.com/steve-community/steve/issues/1415
     */
    private Integer getTransactionId(MeterValuesRequest parameters) {
        Integer transactionId = parameters.getTransactionId();
        if (transactionId == null) {
            return null;
        } else if (transactionId < 1) {
            log.warn("MeterValues transactionId is invalid ({}), ignoring it", transactionId);
            return null;
        }
        return transactionId;
    }

    /**
     * 更新诊断进度跟踪
     */
    private void updateDiagnosticsProgress(String chargeBoxId, String status) {
        try {
            log.info("📊 Received DiagnosticsStatusNotification: chargeBoxId={}, status={}", chargeBoxId, status);

            if (diagnosticsProgressTracker != null && diagnosticsProgressTracker.isTracking(chargeBoxId)) {
                switch (status.toLowerCase()) {
                    case "idle":
                        // 充电桩空闲，可能是请求已发送但还未开始处理
                        break;
                    case "uploaded":
                        // 文件已上传完成，但我们不在这里标记为完成
                        // 因为文件可能还在传输中，等待文件监控器检测到稳定文件
                        diagnosticsProgressTracker.updateProgress(chargeBoxId, 80, "Upload Completed",
                                "Charge point reported file upload completed. Waiting for file to be ready for download...");
                        log.info("✅ Charge point {} reported diagnostics upload completed", chargeBoxId);
                        break;
                    case "uploading":
                        diagnosticsProgressTracker.markFileUploading(chargeBoxId);
                        break;
                    default:
                        log.info("📋 Unhandled diagnostics status: {} for charge point: {}", status, chargeBoxId);
                        break;
                }
            } else {
                log.debug("No active diagnostics tracking for charge point: {}", chargeBoxId);
            }
        } catch (Exception e) {
            log.error("Error updating diagnostics progress for {}: {}", chargeBoxId, e.getMessage(), e);
        }
    }

    /**
     * Handle firmware file status updates based on firmware installation status
     * @param status Firmware installation status
     * @param firmwareLocation Firmware file location URL
     */
    private void handleFirmwareFileStatusUpdate(String status, String firmwareLocation) {
        if (firmwareLocation == null || firmwareLocation.isEmpty()) {
            log.debug("No firmware location provided, skipping file status update");
            return;
        }

        try {
            // Extract filename from firmware location URL
            String filename = extractFilenameFromUrl(firmwareLocation);
            if (filename == null || filename.isEmpty()) {
                log.warn("Could not extract filename from firmware location: {}", firmwareLocation);
                return;
            }

            switch (status) {
                case "Installed":
                    // Firmware successfully installed, mark file as SUCCESS
                    boolean marked = firmwareFileService.markFirmwareAsInstalled(filename);
                    if (marked) {
                        log.info("Successfully marked firmware file as installed: {}", filename);
                    } else {
                        log.warn("Failed to mark firmware file as installed (no pending record found): {}", filename);
                    }
                    break;

                case "DownloadFailed":
                case "InstallationFailed":
                case "Installation timeout":
                    // Firmware installation failed, mark file as FAILED
                    boolean failed = firmwareFileService.markFirmwareAsFailed(filename, status);
                    if (failed) {
                        log.info("Successfully marked firmware file as failed: {} ({})", filename, status);
                    } else {
                        log.warn("Failed to mark firmware file as failed (no pending record found): {}", filename);
                    }
                    break;

                default:
                    // Other statuses (Downloading, Installing, Downloaded) don't change file status
                    log.debug("Firmware status '{}' does not require file status update for: {}", status, filename);
                    break;
            }
        } catch (Exception e) {
            log.error("Error handling firmware file status update for location {}: {}", firmwareLocation, e.getMessage(), e);
        }
    }

    /**
     * Extract filename from firmware location URL
     * @param firmwareLocation Firmware location URL (e.g., ftp://user:pass@host:port/path/filename.bin)
     * @return Filename or null if extraction fails
     */
    private String extractFilenameFromUrl(String firmwareLocation) {
        try {
            // Handle FTP URLs like: ftp://user:pass@host:port/path/filename.bin
            if (firmwareLocation.contains("/")) {
                String[] parts = firmwareLocation.split("/");
                String filename = parts[parts.length - 1];

                // Remove any query parameters or fragments
                if (filename.contains("?")) {
                    filename = filename.substring(0, filename.indexOf("?"));
                }
                if (filename.contains("#")) {
                    filename = filename.substring(0, filename.indexOf("#"));
                }

                return filename.isEmpty() ? null : filename;
            }
            return null;
        } catch (Exception e) {
            log.error("Error extracting filename from URL: {}", firmwareLocation, e);
            return null;
        }
    }
}