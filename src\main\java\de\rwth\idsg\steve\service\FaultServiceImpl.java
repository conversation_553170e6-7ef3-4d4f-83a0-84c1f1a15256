/*
 * SteV<PERSON> - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.service;

import de.rwth.idsg.steve.repository.ChargePointRepository;
import de.rwth.idsg.steve.repository.ChargerIssueRepository;
import de.rwth.idsg.steve.repository.WebUserRepository;
import de.rwth.idsg.steve.web.dto.ChargerIssueDTO;
import de.rwth.idsg.steve.web.dto.ChargerIssueForm;
import de.rwth.idsg.steve.web.dto.ChargerIssueStatus;
import de.rwth.idsg.steve.web.dto.FaultForm;
import de.rwth.idsg.steve.web.dto.MaintenanceRecordDTO;
import de.rwth.idsg.steve.web.dto.MaintenanceRecordForm;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * Charge point fault service implementation
 */
@Slf4j
@Service
public class FaultServiceImpl implements FaultService {

    @Autowired
    private ChargerIssueRepository chargerIssueRepository;

    @Autowired
    private ChargePointRepository chargePointRepository;

    @Autowired
    private WebUserRepository webUserRepository;
    
    @Autowired
    private ChargerIssueImageService imageService;
    
    @Autowired
    private FaultNotificationService notificationService;

    @Value("${issue.image.upload.dir:uploads/issues}")
    private String imageUploadDir;

    @Override
    public List<ChargerIssueDTO> getIssues() {
        return chargerIssueRepository.getIssues();
    }

    @Override
    public List<ChargerIssueDTO> getIssuesByStatus(ChargerIssueStatus status) {
        return chargerIssueRepository.getIssuesByStatus(status);
    }

    @Override
    public List<ChargerIssueDTO> getIssuesByChargeBoxId(String chargeBoxId) {
        return chargerIssueRepository.getIssuesByChargeBoxId(chargeBoxId);
    }

    @Override
    public ChargerIssueDTO getIssue(Integer issueId) {
        return chargerIssueRepository.getIssue(issueId);
    }

    @Override
    @Transactional
    public int createIssue(ChargerIssueForm form, String username) {
        Integer userPk = null;
        if (username != null) {
            userPk = webUserRepository.getUserPkByUsername(username);
        }
        int issueId = chargerIssueRepository.createIssue(form, userPk);
        
        // 获取创建的故障详情
        ChargerIssueDTO newIssue = chargerIssueRepository.getIssue(issueId);
        
        // 发送邮件通知
        try {
            notificationService.sendNewIssueNotification(newIssue);
        } catch (Exception e) {
            // 即使通知失败，也不影响故障的创建
            log.error("发送故障通知失败", e);
        }
        
        return issueId;
    }

    @Override
    @Transactional
    public void updateIssue(ChargerIssueForm form) {
        chargerIssueRepository.updateIssue(form);
    }

    @Override
    @Transactional
    public void updateIssueStatus(int issueId, ChargerIssueStatus status, String username) {
        chargerIssueRepository.updateIssueStatus(issueId, status);
    }

    @Override
    @Transactional
    public void resolveIssue(int issueId, String resolveDescription, String username) {
        chargerIssueRepository.resolveIssue(issueId, resolveDescription);
    }

    @Override
    @Transactional
    public void saveIssueImages(int issueId, List<MultipartFile> images) {
        if (images == null || images.isEmpty()) {
            return;
        }

        for (MultipartFile image : images) {
            if (image != null && !image.isEmpty()) {
                try {
                    log.debug("Processing image upload: size={}, name={}", 
                             image.getSize(), image.getOriginalFilename());
                    imageService.saveImage(issueId, image);
                } catch (IOException e) {
                    log.error("Failed to save fault image: {}", e.getMessage(), e);
                    // Continue processing other images, don't interrupt the whole process due to one image failure
                }
            }
        }
    }

    @Override
    @Transactional
    public int addMaintenanceRecord(MaintenanceRecordForm form, String username) {
        Integer userPk = null;
        if (username != null) {
            userPk = webUserRepository.getUserPkByUsername(username);
        }
        return chargerIssueRepository.addMaintenanceRecord(form, userPk);
    }

    @Override
    public List<MaintenanceRecordDTO> getMaintenanceRecords(int issueId) {
        return chargerIssueRepository.getMaintenanceRecords(issueId);
    }

    @Override
    public List<ChargerIssueDTO> getFilteredIssues(FaultForm form) {
        return chargerIssueRepository.getFilteredIssues(form);
    }

    @Override
    @Transactional
    public int createAutoIssue(String chargeBoxId, String ocppErrorCode, String faultDescription) {
        return createAutoIssue(chargeBoxId, ocppErrorCode, faultDescription, null, null);
    }

    @Override
    @Transactional
    public int createAutoIssue(String chargeBoxId, String ocppErrorCode, String faultDescription, String vendorErrorCode, String vendorId) {
        // First get the primary key based on charge point ID
        Integer chargeBoxPk = chargePointRepository.getChargeBoxPkFromChargeBoxId(chargeBoxId);
        if (chargeBoxPk == null) {
            log.warn("Attempting to create auto-fault for unknown charge point ID: {}", chargeBoxId);
            return -1;
        }

        log.debug("开始创建自动故障记录: ChargeBoxId={}, ErrorCode={}, VendorErrorCode={}, VendorId={}",
                  chargeBoxId, ocppErrorCode, vendorErrorCode, vendorId);
        int issueId = chargerIssueRepository.createAutoIssue(chargeBoxPk, ocppErrorCode, faultDescription, vendorErrorCode, vendorId);

        // 获取创建的故障详情并发送邮件通知
        if (issueId > 0) {
            log.debug("已创建自动故障记录，ID={}, 开始发送通知", issueId);
            ChargerIssueDTO newIssue = chargerIssueRepository.getIssue(issueId);
            try {
                notificationService.sendNewIssueNotification(newIssue);
                log.debug("已成功发送故障通知邮件，ID={}", issueId);
            } catch (Exception e) {
                log.error("发送自动故障通知失败: {}", e.getMessage(), e);
            }
        }

        return issueId;
    }
} 