package de.rwth.idsg.steve.ftp;

import de.rwth.idsg.steve.SteveConfiguration;
import de.rwth.idsg.steve.service.DiagnosticFileDownloadService;
import de.rwth.idsg.steve.service.DiagnosticsProgressTracker;
import de.rwth.idsg.steve.service.ChargePointIdentityMappingService;
import org.apache.ftpserver.FtpServer;
import org.apache.ftpserver.FtpServerFactory;
import org.apache.ftpserver.ftplet.Authority;
import org.apache.ftpserver.ftplet.FtpException;
import org.apache.ftpserver.ftplet.UserManager;
import org.apache.ftpserver.listener.ListenerFactory;
import org.apache.ftpserver.DataConnectionConfigurationFactory;
import org.apache.ftpserver.usermanager.impl.PropertiesUserManager;
import org.apache.ftpserver.usermanager.SaltedPasswordEncryptor;
import org.apache.ftpserver.usermanager.PasswordEncryptor;
import org.apache.ftpserver.usermanager.ClearTextPasswordEncryptor;
import org.apache.ftpserver.usermanager.impl.BaseUser;
import org.apache.ftpserver.usermanager.impl.WritePermission;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

public class EmbeddedFtpServer {

    private static final Logger LOG = LoggerFactory.getLogger(EmbeddedFtpServer.class);
    private FtpServer ftpServer;
    private final SteveConfiguration config;
    private DiagnosticsProgressTracker progressTracker;
    private DiagnosticFileDownloadService downloadService;
    private ChargePointIdentityMappingService identityMappingService;

    // 文件稳定性检查：存储文件路径和上次检查的大小
    private final ConcurrentMap<String, Long> fileStabilityCheck = new ConcurrentHashMap<>();

    public EmbeddedFtpServer(SteveConfiguration steveConfig) {
        this.config = steveConfig;
    }

    public void setProgressTracker(DiagnosticsProgressTracker progressTracker) {
        this.progressTracker = progressTracker;
    }

    public void setDownloadService(DiagnosticFileDownloadService downloadService) {
        this.downloadService = downloadService;
    }

    public void setIdentityMappingService(ChargePointIdentityMappingService identityMappingService) {
        this.identityMappingService = identityMappingService;
    }

    public void start() {
        if (!config.isFtpEnabled()) {
            LOG.info("Embedded FTP server is disabled by configuration.");
            return;
        }

        LOG.info("Starting Embedded FTP server...");

        try {
            // Ensure FTP directories exist
            Path physicalUploadPath = Paths.get(config.getPhysicalFirmwareUploadPath());
            String ftpLogFileStr = config.getFtpLogPath();
            Path ftpLogPathDir = null;
            if (ftpLogFileStr != null && !ftpLogFileStr.trim().isEmpty()) {
                Path ftpLogFilePath = Paths.get(ftpLogFileStr);
                ftpLogPathDir = ftpLogFilePath.getParent();
            }

            if (!Files.exists(physicalUploadPath)) {
                Files.createDirectories(physicalUploadPath);
                LOG.info("Created FTP physical upload directory: {}", physicalUploadPath.toAbsolutePath());
            }
            if (ftpLogPathDir != null && !Files.exists(ftpLogPathDir)) {
                Files.createDirectories(ftpLogPathDir);
                LOG.info("Created FTP log directory: {}", ftpLogPathDir.toAbsolutePath());
            }
            
            LOG.info("--- FTP Server Configuration ---");
            LOG.info("Port: {}", config.getFtpPort());
            LOG.info("Listener Address: 0.0.0.0");
            LOG.info("Client-facing IP (for URL): {}", config.getFtpIp());
            LOG.info("Passive Ports: {}", config.getFtpPassivePorts());
            LOG.info("Passive External Address: {}", config.getFtpPassiveExternalAddress());
            LOG.info("User: {}", config.getFtpUsername());
            LOG.info("Physical Upload Path: {}", config.getPhysicalFirmwareUploadPath());
            LOG.info("---------------------------------");
            
            FtpServerFactory serverFactory = new FtpServerFactory();
            ListenerFactory listenerFactory = new ListenerFactory();

            // Set the port
            listenerFactory.setPort(config.getFtpPort());
            
            // Define the server address to listen on (0.0.0.0 for all interfaces)
            // The config.getFtpIp() is for the client-facing URL.
            // The server itself should listen on 0.0.0.0 to be accessible via any internal IP.
            listenerFactory.setServerAddress("0.0.0.0"); 
            LOG.info("FTP Server will listen on 0.0.0.0:{}", config.getFtpPort());

            // Add passive mode configuration for NAT/firewall
            DataConnectionConfigurationFactory dataConFactory = new DataConnectionConfigurationFactory();
            final String passivePorts = config.getFtpPassivePorts();
            if (passivePorts != null && !passivePorts.trim().isEmpty()) {
                dataConFactory.setPassivePorts(passivePorts);
                LOG.info("FTP passive ports are set to: {}", passivePorts);
            }
            final String passiveExternalAddress = config.getFtpPassiveExternalAddress();
            if (passiveExternalAddress != null && !passiveExternalAddress.trim().isEmpty()) {
                dataConFactory.setPassiveExternalAddress(passiveExternalAddress);
                LOG.info("FTP passive external address is set to: {}", passiveExternalAddress);
            } else {
                LOG.warn("FTP passive external address is not set. This might cause issues in NAT environments.");
            }

            listenerFactory.setDataConnectionConfiguration(dataConFactory.createDataConnectionConfiguration());

            serverFactory.addListener("default", listenerFactory.createListener());

            // Setup user manager directly
            // We don't need a users.properties file as we add users programmatically.
            // Using ClearTextPasswordEncryptor as passwords are set in plain text for BaseUser.
            PasswordEncryptor passwordEncryptor = new ClearTextPasswordEncryptor(); 
            // The users.properties file argument can be null if not loading from file.
            // The admin name for the UserManager itself can also be a default or null.
            UserManager userManager = new PropertiesUserManager(passwordEncryptor, (File) null, config.getFtpUsername());
            
            // Create user
            BaseUser user = new BaseUser();
            user.setName(config.getFtpUsername());
            user.setPassword(config.getFtpPassword());

            // Set user's home directory to be the parent of the physicalFirmwareUploadPath,
            // so that the last component of physicalFirmwareUploadPath becomes the first part of the path
            // requested by the client relative to their FTP home.
            // config.getPhysicalFirmwareUploadPath() is D:/Git/EVSE_OMS/ftp_files/firmware/
            // config.getFtpBasePath() is /firmware/
            // User home should be D:/Git/EVSE_OMS/ftp_files/
            Path physicalUploadPathObj = Paths.get(config.getPhysicalFirmwareUploadPath());
            File userHomeDirFile;
            if (physicalUploadPathObj.getParent() != null) {
                userHomeDirFile = physicalUploadPathObj.getParent().toFile();
            } else {
                // Should not happen if physicalFirmwareUploadPath is a multi-level path like configured
                LOG.error("Cannot determine parent directory for physicalFirmwareUploadPath: {}. Using it directly as FTP home (might be incorrect).", physicalUploadPathObj);
                userHomeDirFile = physicalUploadPathObj.toFile();
            }

            if (!userHomeDirFile.exists()) {
                if (userHomeDirFile.mkdirs()) {
                    LOG.info("Created FTP user home directory: {}", userHomeDirFile.getAbsolutePath());
                } else {
                    LOG.error("Failed to create FTP user home directory: {}", userHomeDirFile.getAbsolutePath());
                    throw new IOException("Failed to create FTP user home directory: " + userHomeDirFile.getAbsolutePath());
                }
            }

            // Ensure logs directory exists for GetDiagnostics
            File logsDir = new File(userHomeDirFile, "logs");
            if (!logsDir.exists()) {
                if (logsDir.mkdirs()) {
                    LOG.info("Created FTP logs directory: {}", logsDir.getAbsolutePath());
                } else {
                    LOG.error("Failed to create FTP logs directory: {}", logsDir.getAbsolutePath());
                }
            } else {
                LOG.info("FTP logs directory already exists: {}", logsDir.getAbsolutePath());
            }

            // Start a background task to monitor and move diagnostic files (as backup for legacy charge points)
            // This is kept as a fallback for charge points that still upload to FTP root instead of directly to /logs/
            LOG.info("Starting diagnostic file monitor as backup for legacy charge points that upload to FTP root");
            startDiagnosticFileMonitor(userHomeDirFile, logsDir);
            if (!userHomeDirFile.isDirectory()) {
                LOG.error("FTP User home directory is not a directory: {}", userHomeDirFile.getAbsolutePath());
                throw new FtpException("FTP User home directory is not a directory: " + userHomeDirFile.getAbsolutePath());
            }
            user.setHomeDirectory(userHomeDirFile.getAbsolutePath());
            LOG.info("FTP user '{}' home directory set to: {}", user.getName(), user.getHomeDirectory());

            // Ensure the actual firmware directory (physicalUploadPath) exists under the user home
            if (!Files.exists(physicalUploadPathObj)) {
                 Files.createDirectories(physicalUploadPathObj);
                 LOG.info("Ensured physical firmware upload subdirectory exists: {}", physicalUploadPathObj.toAbsolutePath());
            }

            List<Authority> authorities = new ArrayList<>();
            authorities.add(new WritePermission()); // Allow write for firmware uploads if needed by this server directly (though SteVe handles writes)
            user.setAuthorities(authorities);

            userManager.save(user); 
            serverFactory.setUserManager(userManager);
            
            // Configure logging (basic example, can be more sophisticated with Log4j/Logback appenders)
            // Note: Apache FtpServer uses SLF4J. If SLF4J is already configured in the project, logs should appear.
            // The steve.firmware.ftp.log-path is more for a custom file appender if needed.
            // For now, rely on existing SLF4J setup. We can add specific file appender later if logs are not captured.

            ftpServer = serverFactory.createServer();
            ftpServer.start();
            LOG.info("Embedded FTP server started on port {} and listening on 0.0.0.0. User '{}' with home '{}'",
                    config.getFtpPort(), config.getFtpUsername(), user.getHomeDirectory());

        } catch (FtpException e) {
            LOG.error("Failed to start Embedded FTP server (FtpException): {}", e.getMessage(), e);
        } catch (IOException e) {
            LOG.error("Failed to start Embedded FTP server (IOException): {}", e.getMessage(), e);
        } catch (Exception e) {
            LOG.error("An unexpected error occurred while starting Embedded FTP server: {}", e.getMessage(), e);
        }
    }

    public void stop() {
        if (ftpServer != null && !ftpServer.isStopped()) {
            LOG.info("Stopping Embedded FTP server...");
            ftpServer.stop();
            ftpServer = null;
            LOG.info("Embedded FTP server stopped successfully.");
        } else {
            LOG.info("Embedded FTP server is not running or already stopped.");
        }
    }

    // Main method for standalone testing (optional)
    public static void main(String[] args) {
        // Create a dummy SteveConfiguration for testing
        SteveConfiguration dummyConfig = SteveConfiguration.CONFIG; // This will load from main.properties

        // Manually override for testing if needed, or ensure main.properties has dev values
        // For this test to run standalone, main.properties needs to be accessible.

        System.setProperty("steve.firmware.ftp.enabled", "true");
        // System.setProperty("steve.firmware.physical-upload-path", "target/ftp_home/firmware_files"); // Example
        // System.setProperty("steve.firmware.ftp.username", "testuser");
        // System.setProperty("steve.firmware.ftp.password", "testpass");
        // System.setProperty("steve.firmware.ftp.port", "2221");
        // System.setProperty("steve.firmware.ftp.base.path", "/files/");
        // System.setProperty("steve.firmware.ftp.log-path", "target/ftp_home/logs/ftp.log");
        
        // Re-initialize config if properties were set via System.setProperty after initial load
        // This is tricky as SteveConfiguration is an enum singleton.
        // For standalone test, it's better to create a mock/simple config object.

        LOG.info("Test: FTP Enabled: {}", dummyConfig.isFtpEnabled());
        LOG.info("Test: FTP Port: {}", dummyConfig.getFtpPort());
        LOG.info("Test: FTP User: {}", dummyConfig.getFtpUsername());
        LOG.info("Test: FTP Phys Path: {}", dummyConfig.getPhysicalFirmwareUploadPath());
        LOG.info("Test: FTP Base Path: {}", dummyConfig.getFtpBasePath());


        if (!dummyConfig.isFtpEnabled()) {
            LOG.error("FTP is not enabled in the dummy config. Exiting test.");
            return;
        }
        
        EmbeddedFtpServer server = new EmbeddedFtpServer(dummyConfig);
        server.start();

        LOG.info("Embedded FTP server running for standalone test. Press Ctrl+C to stop.");
        // Keep server running
        Runtime.getRuntime().addShutdownHook(new Thread(server::stop));
        
        // To prevent main from exiting immediately
        try {
            Thread.currentThread().join();
        } catch (InterruptedException e) {
            LOG.warn("Standalone FTP server test interrupted.");
            Thread.currentThread().interrupt();
        }
    }

    /**
     * Start a background task to monitor for diagnostic files and register them for download
     * When a diagnostic file is detected, it will be made available for user download
     * and then deleted after successful download.
     */
    private void startDiagnosticFileMonitor(File ftpRoot, File logsDir) {
        Thread monitorThread = new Thread(() -> {
            LOG.info("Started diagnostic file monitor for FTP root: {} (files will be made available for download)", ftpRoot.getAbsolutePath());
            while (!Thread.currentThread().isInterrupted()) {
                try {
                    // Check for .zip files in FTP root that look like diagnostic files
                    File[] zipFiles = ftpRoot.listFiles((dir, name) ->
                        name.toLowerCase().endsWith(".zip") &&
                        name.matches(".*[0-9]{8}_[0-9]{6}\\.zip")); // Pattern: *_YYYYMMDD_HHMMSS.zip

                    if (zipFiles != null) {
                        for (File zipFile : zipFiles) {
                            String filePath = zipFile.getAbsolutePath();
                            long currentSize = zipFile.length();

                            // 检查文件最小大小（至少1KB，避免处理空文件或正在创建的文件）
                            if (currentSize < 1024) {
                                LOG.debug("⏳ File {} is too small ({} bytes), waiting for more data...",
                                        zipFile.getName(), currentSize);
                                continue;
                            }

                            // 检查文件稳定性
                            Long previousSize = fileStabilityCheck.get(filePath);
                            if (previousSize == null) {
                                // 第一次检测到文件，记录大小
                                fileStabilityCheck.put(filePath, currentSize);
                                LOG.info("🔍 First detection of diagnostic file: {} (size: {} bytes)",
                                        zipFile.getName(), currentSize);
                                continue;
                            } else if (!previousSize.equals(currentSize)) {
                                // 文件大小还在变化，更新记录
                                fileStabilityCheck.put(filePath, currentSize);
                                LOG.info("📈 File {} size changed: {} -> {} bytes, still uploading...",
                                        zipFile.getName(), previousSize, currentSize);
                                continue;
                            }

                            // 文件大小稳定，认为上传完成
                            LOG.info("✅ File {} is stable (size: {} bytes), processing...",
                                    zipFile.getName(), currentSize);

                            // 从稳定性检查中移除
                            fileStabilityCheck.remove(filePath);

                            // 生成标准化文件名
                            String newFileName = generateStandardizedFileName(zipFile.getName());
                            File renamedFile = new File(ftpRoot, newFileName);

                            // 重命名文件以标准化
                            if (!zipFile.getName().equals(newFileName)) {
                                if (zipFile.renameTo(renamedFile)) {
                                    LOG.info("📝 Renamed diagnostic file: {} -> {}", zipFile.getName(), newFileName);
                                    zipFile = renamedFile; // 更新文件引用
                                    filePath = zipFile.getAbsolutePath(); // 更新文件路径
                                } else {
                                    LOG.warn("Failed to rename diagnostic file: {}", zipFile.getName());
                                }
                            }

                            // 提取充电桩ID并进行智能映射
                            String extractedChargePointId = extractChargePointIdFromFileName(zipFile.getName());
                            String resolvedChargePointId = resolveChargePointIdWithMapping(extractedChargePointId, zipFile.getName());

                            // 注册文件供下载
                            if (downloadService != null) {
                                downloadService.registerDownloadableFile(resolvedChargePointId, filePath);
                                LOG.info("📥 Registered diagnostic file for download: {} (Extracted: {}, Resolved: {}, Final size: {} bytes)",
                                        zipFile.getName(), extractedChargePointId, resolvedChargePointId, zipFile.length());
                            } else {
                                LOG.warn("Download service not available, cannot register file: {}", zipFile.getName());
                            }
                        }
                    }

                    // Check every 10 seconds
                    Thread.sleep(10000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                } catch (Exception e) {
                    LOG.error("Error in diagnostic file monitor: {}", e.getMessage());
                }
            }
            LOG.info("Diagnostic file monitor stopped");
        });

        monitorThread.setDaemon(true);
        monitorThread.setName("DiagnosticFileMonitor");
        monitorThread.start();
    }

    /**
     * Generate standardized file name: ChargePointID_diagnostics_YYYY_MM_DD_HH_MM_SS.zip
     */
    private String generateStandardizedFileName(String originalFileName) {
        // 获取当前时间
        java.time.LocalDateTime now = java.time.LocalDateTime.now();
        java.time.format.DateTimeFormatter formatter = java.time.format.DateTimeFormatter.ofPattern("yyyy_MM_dd_HH_mm_ss");
        String timestamp = now.format(formatter);

        // 尝试从进度跟踪器获取充电桩ID
        String chargePointId = "UnknownCP";
        if (progressTracker != null) {
            chargePointId = progressTracker.getChargePointIdForFile(originalFileName);
        } else {
            // 如果没有进度跟踪器，使用传统方法提取
            chargePointId = extractChargePointId(originalFileName);
        }

        // 生成新文件名：充电桩ID_diagnostics_年_月_日_时_分_秒.zip
        String newFileName = chargePointId + "_diagnostics_" + timestamp + ".zip";

        LOG.info("📝 Standardized file name: {} -> {}", originalFileName, newFileName);
        return newFileName;
    }

    /**
     * Extract charge point ID from original file name or use a default pattern
     */
    private String extractChargePointId(String originalFileName) {
        LOG.info("🔍 Extracting charge point ID from file: {}", originalFileName);

        // 常见的充电桩ID模式
        // 1. 如果文件名包含CP开头的模式，提取CP后面的数字
        if (originalFileName.matches(".*CP\\d+.*")) {
            java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("(CP\\d+)");
            java.util.regex.Matcher matcher = pattern.matcher(originalFileName);
            if (matcher.find()) {
                String result = matcher.group(1);
                LOG.info("✅ Found CP pattern: {}", result);
                return result;
            }
        }

        // 2. 检查是否有已知的充电桩ID模式（从进度跟踪器的映射中）
        if (progressTracker != null) {
            // 获取所有正在跟踪的充电桩ID
            for (String chargeBoxId : progressTracker.getAllTrackedChargePoints()) {
                if (originalFileName.contains(chargeBoxId)) {
                    LOG.info("✅ Found tracked charge point ID: {}", chargeBoxId);
                    return chargeBoxId;
                }
            }
        }

        // 3. 如果文件名包含下划线分隔的时间戳，取第一部分作为ID
        if (originalFileName.contains("_")) {
            String[] parts = originalFileName.split("_");
            if (parts.length > 0 && parts[0].length() > 0) {
                // 如果第一部分看起来像充电桩ID（包含字母和数字），使用它
                if (parts[0].matches(".*[A-Za-z].*") && parts[0].length() <= 20) {
                    LOG.info("✅ Using first part as charge point ID: {}", parts[0]);
                    return parts[0];
                }
            }
        }

        // 4. 如果无法提取，使用原文件名的前缀
        String nameWithoutExtension = originalFileName;
        if (originalFileName.toLowerCase().endsWith(".zip")) {
            nameWithoutExtension = originalFileName.substring(0, originalFileName.length() - 4);
        }

        // 限制长度并清理特殊字符
        String cleanName = nameWithoutExtension.replaceAll("[^A-Za-z0-9]", "");
        if (cleanName.length() > 15) {
            cleanName = cleanName.substring(0, 15);
        }

        String result = cleanName.isEmpty() ? "UnknownCP" : cleanName;
        LOG.info("⚠️ Using fallback charge point ID: {}", result);
        return result;
    }

    /**
     * Extract charge point ID from standardized file name
     * Expected format: ChargePointID_diagnostics_YYYY_MM_DD_HH_MM_SS.zip
     */
    private String extractChargePointIdFromFileName(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "UnknownCP";
        }

        // Remove .zip extension
        String nameWithoutExtension = fileName;
        if (fileName.toLowerCase().endsWith(".zip")) {
            nameWithoutExtension = fileName.substring(0, fileName.length() - 4);
        }

        // Split by underscore and get the first part (should be charge point ID)
        String[] parts = nameWithoutExtension.split("_");
        if (parts.length > 0 && !parts[0].isEmpty()) {
            String chargePointId = parts[0];
            LOG.info("✅ Extracted charge point ID from file name: {} -> {}", fileName, chargePointId);
            return chargePointId;
        }

        // Fallback
        LOG.warn("⚠️ Could not extract charge point ID from file name: {}, using fallback", fileName);
        return "UnknownCP";
    }

    /**
     * 使用动态学习的映射服务解析充电桩ID
     * 自动学习和处理标识不匹配的情况
     *
     * @param extractedId 从文件名提取的充电桩ID
     * @param fileName 原始文件名
     * @return 解析后的正确充电桩ID
     */
    private String resolveChargePointIdWithMapping(String extractedId, String fileName) {
        if (identityMappingService == null) {
            LOG.debug("🔍 Identity mapping service not available, using extracted ID: {}", extractedId);
            return extractedId;
        }

        LOG.info("🔍 Resolving charge point ID: {} from file: {}", extractedId, fileName);

        // 查找当前活跃的诊断任务对应的充电桩ID
        String connectedChargeBoxId = findActiveChargeBoxId();

        if (connectedChargeBoxId != null) {
            // 使用动态学习的映射服务进行智能解析
            String resolvedId = identityMappingService.resolveChargePointIdentity(fileName, connectedChargeBoxId);
            LOG.info("🎓 Dynamic learning resolved: {} -> {} (file: {}, connected: {})",
                    extractedId, resolvedId, fileName, connectedChargeBoxId);
            return resolvedId;
        } else {
            LOG.warn("⚠️ No active charge box found for dynamic learning");

            // 如果没有活跃任务，检查是否有已学习的映射
            String mappedId = identityMappingService.getDevOpsIdentity(extractedId);
            if (!mappedId.equals(extractedId)) {
                LOG.info("🔄 Using learned mapping: {} -> {} (file: {})", extractedId, mappedId, fileName);
                return mappedId;
            }
        }

        LOG.info("🔍 No mapping available, using extracted ID: {} (file: {})", extractedId, fileName);
        return extractedId;
    }

    /**
     * 查找当前活跃的充电桩ID
     * 通过检查进度跟踪器中的活跃任务来确定
     *
     * @return 活跃的充电桩ID，如果找不到则返回null
     */
    private String findActiveChargeBoxId() {
        if (progressTracker == null) {
            return null;
        }

        // 获取所有正在跟踪的充电桩
        for (String chargeBoxId : progressTracker.getAllTrackedChargePoints()) {
            // 检查该充电桩的进度状态
            DiagnosticsProgressTracker.DiagnosticsProgress progress = progressTracker.getProgress(chargeBoxId);
            if (progress != null && !progress.getStatus().equals("Completed")) {
                LOG.debug("🔍 Found active charge box with ongoing diagnostics: {}", chargeBoxId);
                return chargeBoxId;
            }
        }

        LOG.debug("🔍 No active charge box found in progress tracker");
        return null;
    }
}