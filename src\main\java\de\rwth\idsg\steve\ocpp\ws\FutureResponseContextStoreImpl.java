/*
 * SteV<PERSON> - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.ocpp.ws;

import de.rwth.idsg.steve.ocpp.ws.data.FutureResponseContext;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.web.socket.WebSocketSession;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.BiFunction;

/**
 * Presumption: The responses must be sent using the same connection as the requests!
 *
 * <AUTHOR> Goekay <<EMAIL>>
 * @since 21.03.2015
 */
@Slf4j
@Service
public class FutureResponseContextStoreImpl implements FutureResponseContextStore {

    // We store for each chargeBox connection, multiple pairs of (messageId, context)
    // (session, (messageId, context))
    private final Map<WebSocketSession, Map<String, FutureResponseContext>> lookupTable = new ConcurrentHashMap<>();

    @Override
    public void addSession(WebSocketSession session) {
        addIfAbsent(session);
    }

    @Override
    public void removeSession(WebSocketSession session) {
        log.debug("Deleting the store for sessionId '{}'", session.getId());
        lookupTable.remove(session);
    }

    @Override
    public void clearSessionAndFailPendingRequests(WebSocketSession session, Exception cause) {
        Map<String, FutureResponseContext> pending = lookupTable.remove(session);
        if (pending != null && !pending.isEmpty()) {
            log.warn("Session '{}' closed. Failing {} pending requests.", session.getId(), pending.size());
            for (Map.Entry<String, FutureResponseContext> entry : pending.entrySet()) {
                String messageId = entry.getKey();
                FutureResponseContext context = entry.getValue();
                log.warn("Failing pending request with callId='{}' due to session close.", messageId);
                context.getTask().failed(context.getChargeBoxId(), cause);
            }
        }
    }

    @Override
    public void add(WebSocketSession session, String messageId, FutureResponseContext context) {
        Map<String, FutureResponseContext> map = addIfAbsent(session);
        map.put(messageId, context);
        log.info(">>>> [STORE_ADD] Stored callId='{}' for sessionId='{}'. New store size: {}",
                messageId, session.getId(), map.size());
    }

    @Override
    public FutureResponseContext get(WebSocketSession session, String messageId) {
        RemoveFunction removeFunction = new RemoveFunction(messageId);
        lookupTable.computeIfPresent(session, removeFunction);

        if (removeFunction.removedContext == null) {
            log.warn(">>>> [STORE_GET] Attempted to get callId='{}' for sessionId='{}', but it was NOT found.",
                    messageId, session.getId());
        }

        return removeFunction.removedContext;
    }

    @Override
    public String dumpState() {
        StringBuilder sb = new StringBuilder("FutureResponseContextStore state:\n");
        if (lookupTable.isEmpty()) {
            sb.append("  <empty>");
            return sb.toString();
        }
        lookupTable.forEach((session, pendingMap) -> {
            sb.append(String.format("  - Session '%s': %d pending requests %s\n",
                    session.getId(),
                    pendingMap.size(),
                    pendingMap.keySet()));
        });
        return sb.toString();
    }

    private Map<String, FutureResponseContext> addIfAbsent(WebSocketSession session) {
        return lookupTable.computeIfAbsent(session, innerSession -> {
            log.debug("Creating new store for sessionId '{}'", innerSession.getId());
            return new ConcurrentHashMap<>();
        });
    }

    @RequiredArgsConstructor(access = AccessLevel.PRIVATE)
    private static class RemoveFunction implements
            BiFunction<WebSocketSession, Map<String, FutureResponseContext>, Map<String, FutureResponseContext>> {

        private final String messageId;
        @Nullable private FutureResponseContext removedContext;

        @Override
        public Map<String, FutureResponseContext> apply(WebSocketSession session,
                                                        Map<String, FutureResponseContext> map) {
            removedContext = map.remove(messageId);
            if (removedContext != null) {
                log.info(">>>> [STORE_REMOVE] Removed callId='{}' from session '{}'. New size: {}",
                        messageId, session.getId(), map.size());
            }
            return map;
        }
    }
}
