/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.service.impl;

import de.rwth.idsg.steve.repository.UserRepository;
import de.rwth.idsg.steve.repository.dto.User;
import de.rwth.idsg.steve.service.UserService;
import de.rwth.idsg.steve.web.dto.UserDTO;
import de.rwth.idsg.steve.web.dto.UserForm;
import de.rwth.idsg.steve.web.dto.UserQueryForm;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 用户服务实现
 */
@Slf4j
@Service
public class UserServiceImpl implements UserService {

    @Autowired
    private UserRepository userRepository;

    @Override
    public List<User.Overview> getOverview(UserQueryForm form) {
        return userRepository.getOverview(form);
    }

    @Override
    public User.Details getDetails(int userPk) {
        return userRepository.getDetails(userPk);
    }

    @Override
    public void add(UserForm form) {
        userRepository.add(form);
    }

    @Override
    public void update(UserForm form) {
        userRepository.update(form);
    }

    @Override
    public void delete(int userPk) {
        userRepository.delete(userPk);
    }

    @Override
    public List<UserDTO> getUsers() {
        // 简化实现，只返回空列表
        return new ArrayList<>();
    }

    @Override
    public UserForm getUser(int userPk) {
        // 简化实现，创建一个新的空表单
        UserForm form = new UserForm();
        form.setUserPk(userPk);
        return form;
    }

    @Override
    public void addEnterpriseUser(UserForm form) {
        userRepository.add(form);
    }

    @Override
    public void addCarUser(UserForm form) {
        userRepository.add(form);
    }

    @Override
    public void updateUser(UserForm form) {
        userRepository.update(form);
    }

    @Override
    public void deleteUser(int userPk) {
        userRepository.delete(userPk);
    }

    @Override
    public List<Integer> getUserIdsByName(String userName) {
        if (userName == null || userName.trim().isEmpty()) {
            return Collections.emptyList();
        }
        return userRepository.getUserIdsByName(userName);
    }

    @Override
    public Integer getWebUserId(String userName) {
        if (userName == null || userName.trim().isEmpty()) {
            return null;
        }
        return userRepository.getWebUserId(userName);
    }

    @Override
    public String getUserName(int userPk) {
        return userRepository.getUserName(userPk);
    }
} 