/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.web.security;

import de.rwth.idsg.steve.service.PermissionService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.HandlerMapping;

import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Interceptor to check if a charge box owner has access to a specific charge box.
 *
 * @since 1.0.8
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ChargeBoxOwnerAuthorizationInterceptor implements HandlerInterceptor {

    private final PermissionService permissionService;
    
    // Pattern to extract chargeBoxId from URL paths (excluding delete which uses chargeBoxPk)
    private static final Pattern CHARGE_BOX_ID_PATTERN = Pattern.compile(".*/chargepoints/(details|update)/([^/]+).*");
    private static final Pattern OPERATIONS_PATTERN = Pattern.compile(".*/operations/([^/]+)/([^/]+).*");

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String path = request.getRequestURI();

        // Admin can access everything
        if (permissionService.isAdmin()) {
            return true;
        }

        // Factory operators can access charge boxes but not restricted pages
        if (permissionService.isOperatorFactory()) {
            // Block access to restricted pages for Factory Operator
            if (path.contains("/ocppTags") ||
                path.contains("/chargingProfiles") ||
                path.contains("/reservations") ||
                path.contains("/transactions") ||
                path.contains("/connectorStatus") ||
                path.contains("/operations/v1.6")) {
                throw new AccessDeniedException("Factory Operator does not have permission to access this page");
            }
            return true;
        }
        
        // For charge box owners, we need to check if they own the charge box
        if (permissionService.isOperatorOwner()) {
            // Block access to restricted pages for Charge Box Owner
            if (path.contains("/ownerAssignments") ||
                path.contains("/operations/faults") ||
                path.contains("/notification") ||
                path.contains("/settings") ||
                path.contains("/log") ||
                path.contains("/about")) {
                throw new AccessDeniedException("Charge Box Owner does not have permission to access this page");
            }

            // 允许访问故障报告页面
            if (path.contains("/operations/report-fault") ||
                path.contains("/operations/fault-details") ||
                path.contains("/operations/update-fault")) {
                return true;
            }
            
            String chargeBoxId = extractChargeBoxIdFromRequest(request);
            
            if (chargeBoxId != null) {
                if (!permissionService.canViewChargeBox(chargeBoxId)) {
                    throw new AccessDeniedException("You do not have permission to access this charge point");
                }
            }
            
            // 检查chargeBoxPk路径变量
            Integer chargeBoxPk = extractChargeBoxPkFromRequest(request);
            if (chargeBoxPk != null) {
                log.info("=== ChargeBoxOwnerAuthorizationInterceptor Debug ===");
                log.info("Request path: {}", request.getRequestURI());
                log.info("Extracted chargeBoxPk: {}", chargeBoxPk);
                log.info("Current user is operator owner: {}", permissionService.isOperatorOwner());

                boolean canView = permissionService.canViewChargeBoxByPk(chargeBoxPk);
                log.info("canViewChargeBoxByPk result: {}", canView);

                if (!canView) {
                    log.error("Access denied for chargeBoxPk: {} by user", chargeBoxPk);
                    throw new AccessDeniedException("You do not have permission to access this charge point");
                }

                log.info("Access granted for chargeBoxPk: {}", chargeBoxPk);
            }
        }
        
        return true;
    }
    
    /**
     * Extract chargeBoxId from request URL or parameters
     *
     * @param request The HTTP request
     * @return The extracted chargeBoxId or null if not found
     */
    private String extractChargeBoxIdFromRequest(HttpServletRequest request) {
        String path = request.getRequestURI();
        
        // Check if it's a charge point details/delete/update URL
        Matcher matcher = CHARGE_BOX_ID_PATTERN.matcher(path);
        if (matcher.matches()) {
            return matcher.group(2);
        }
        
        // Check if it's an operations URL with chargeBoxId parameter
        String chargeBoxId = request.getParameter("chargeBoxId");
        if (chargeBoxId != null) {
            return chargeBoxId;
        }
        
        // Check path variables
        @SuppressWarnings("unchecked")
        Map<String, String> pathVariables = (Map<String, String>) request.getAttribute(HandlerMapping.URI_TEMPLATE_VARIABLES_ATTRIBUTE);
        if (pathVariables != null && pathVariables.containsKey("chargeBoxId")) {
            return pathVariables.get("chargeBoxId");
        }
        
        return null;
    }
    
    /**
     * 从请求中提取充电桩主键
     *
     * @param request HTTP请求
     * @return 充电桩主键，如果未找到则返回null
     */
    private Integer extractChargeBoxPkFromRequest(HttpServletRequest request) {
        // 检查路径变量
        @SuppressWarnings("unchecked")
        Map<String, String> pathVariables = (Map<String, String>) request.getAttribute(HandlerMapping.URI_TEMPLATE_VARIABLES_ATTRIBUTE);
        if (pathVariables != null && pathVariables.containsKey("chargeBoxPk")) {
            try {
                return Integer.parseInt(pathVariables.get("chargeBoxPk"));
            } catch (NumberFormatException e) {
                log.warn("Invalid chargeBoxPk format: {}", pathVariables.get("chargeBoxPk"));
                return null;
            }
        }
        
        return null;
    }
}