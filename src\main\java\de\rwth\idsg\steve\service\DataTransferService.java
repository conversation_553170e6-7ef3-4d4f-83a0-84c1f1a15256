package de.rwth.idsg.steve.service;

import de.rwth.idsg.steve.web.dto.DataTransferForm;

import java.util.concurrent.CompletionStage;

public interface DataTransferService {

    /**
     * Asynchronously reads the configuration from a charge point via OCPP DataTransfer.
     *
     * @param chargeBoxId The ID of the charge point.
     * @return A CompletionStage that will complete with a DataTransferForm DTO
     *         containing the charge point's configuration.
     */
    CompletionStage<DataTransferForm> readConfiguration(String chargeBoxId);

    /**
     * Asynchronously writes the configuration to a charge point via OCPP DataTransfer.
     *
     * @param form The DTO containing the configuration to write.
     * @return A CompletionStage that will complete with the status of the operation.
     *         For simplicity, let's return a Boolean: true for success, false for failure.
     */
    CompletionStage<Boolean> writeConfiguration(DataTransferForm form);
} 