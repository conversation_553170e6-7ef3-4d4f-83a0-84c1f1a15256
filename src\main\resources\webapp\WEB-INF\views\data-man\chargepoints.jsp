<%--

    SteV<PERSON> - SteckdosenVerwaltung - https://github.com/steve-community/steve
    Copyright (C) 2013-2025 SteVe Community Team
    All Rights Reserved.

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.

--%>
<%@ include file="../00-header.jsp" %>
<style>
    .table-custom th {
        text-align: left; /* Header text left-aligned */
        vertical-align: middle;
    }
    .table-custom td {
        text-align: center; /* Default cell text center-aligned */
        vertical-align: middle;
    }
    /* Removed specific left-align for chargebox-id, firmware-version, owner */
    /* They will now inherit center alignment from .table-custom td */

    /* Pagination Styles */
    .pagination {
        display: flex; /* Use flexbox for horizontal layout */
        justify-content: flex-end; /* Align items to the right */
        padding-left: 0;
        list-style: none;
    }
    .pagination .page-item .page-link {
        color: #495057; /* Darker text for page links */
        border-radius: 0.25rem; /* Slightly rounded corners */
        margin: 0 2px; /* Add small margin between page items */
    }
    .pagination .page-item.active .page-link {
        z-index: 3;
        color: #fff;
        background-color: #007bff; /* Standard primary color for active page */
        border-color: #007bff;
    }
    .pagination .page-item:not(.disabled) .page-link:hover {
        background-color: #e9ecef; /* Light grey background on hover */
        border-color: #dee2e6;
    }
    .pagination .page-item.disabled .page-link {
        color: #6c757d;
        pointer-events: none;
        background-color: #fff;
        border-color: #dee2e6;
    }

    /* Enforce margin between specific filter elements with higher specificity */
    div#overview .search-panel .row .col-md-auto.me-md-2 {
        margin-right: 1rem !important;
    }
    div#overview .search-panel .row .col-md-auto.me-md-2 + .col-md-auto {
        margin-left: 0.5rem !important;
    }

    .page-size-selector {
        display: flex;
        align-items: center;
    }

    .page-size-selector label {
        margin-right: 0.5rem;
        white-space: nowrap;
    }

    .filter-item {
        margin-right: 20px !important; /* Force consistent spacing */
        flex: 1; /* Allow items to grow and fill space */
    }

    .status-link {
        text-decoration: none;
        color: inherit;
    }

    .status-link:hover {
        text-decoration: underline;
    }

    /* Custom Dropdown Styles */
    .actions-menu-container {
        position: relative;
        display: inline-block;
    }

    .actions-menu {
        display: none;
        position: absolute;
        right: 0;
        background-color: #f9f9f9;
        min-width: 160px;
        box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
        z-index: 1050; /* Increased z-index to ensure it's on top */
        border: 1px solid #ccc;
        border-radius: 4px;
        list-style: none;
        padding: 0;
        margin: 0;
    }
    
    .actions-menu a, .actions-menu button {
        color: black;
        padding: 8px 12px;
        text-decoration: none;
        display: block;
        width: 100%;
        text-align: center;
        background: none;
        border: none;
        cursor: pointer;
    }

    .actions-menu a:hover, .actions-menu button:hover {
        background-color: #f1f1f1;
    }
    
    .actions-menu .dropdown-divider {
        height: 1px;
        margin: .5rem 0;
        overflow: hidden;
        background-color: #e9ecef;
    }

    /* Dropdown action button styles */
    .dropdown-item-form {
        padding: 0;
        margin: 0;
    }

    .dropdown-item-form .dropdown-item {
        background: none;
        border: none;
        color: #212529; /* Standard dropdown item text color */
        text-align: left;
        width: 100%;
        padding: .25rem 1.5rem; /* Match default dropdown item padding */
        cursor: pointer;
    }

    .dropdown-item-form .dropdown-item:hover {
        background-color: #f8f9fa; /* Standard dropdown hover color */
        color: #16181b;
    }

    /* Custom Modal Styles */
    .modal {
        display: none; 
        position: fixed; 
        z-index: 1000; 
        left: 0;
        top: 0;
        width: 100%; 
        height: 100%; 
        overflow: auto; 
        background-color: rgba(0,0,0,0.4);
    }

    .modal-content {
        background-color: #fefefe;
        margin: 15% auto; 
        padding: 20px;
        border: 1px solid #888;
        width: 30%; 
        border-radius: 5px;
        box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2);
    }
    
    .modal-header {
        padding-bottom: 10px;
        border-bottom: 1px solid #ddd;
    }

    .modal-header h2 {
        margin: 0;
    }

    .modal-body {
        padding: 15px 0;
    }
    
    .modal-footer {
        padding-top: 10px;
        border-top: 1px solid #ddd;
        text-align: right;
    }
    
    .modal-footer .btn {
        margin-left: 10px;
    }

    .close {
        color: #aaa;
        float: right;
        font-size: 28px;
        font-weight: bold;
    }

    .close:hover,
    .close:focus {
        color: black;
        text-decoration: none;
        cursor: pointer;
    }

    .firmware-date-input {
        min-width: 150px !important; /* Ensure the date input is wide enough */
    }

    /* Styles for progress bar in modal */
    .progress-container {
        width: 100%;
        background-color: #f3f3f3;
        border-radius: 5px;
        margin-top: 10px;
    }

    .progress-bar {
        width: 0%;
        height: 20px;
        background-color: #4caf50;
        text-align: center;
        line-height: 20px;
        color: white;
        border-radius: 5px;
    }

    /* Modal styles */
    .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        overflow: auto;
        background-color: rgba(0,0,0,0.4);
    }

    .modal-content {
        background-color: #fefefe;
        margin: 5% auto;
        padding: 0;
        border: 1px solid #888;
        border-radius: 0.375rem;
        width: 80%;
        max-width: 800px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .modal-header {
        padding: 1rem 1.5rem;
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        border-radius: 0.375rem 0.375rem 0 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .modal-header h2 {
        margin: 0;
        color: #495057;
        font-size: 1.25rem;
    }

    .modal-body {
        padding: 1.5rem;
    }

    .modal-footer {
        padding: 1rem 1.5rem;
        background-color: #f8f9fa;
        border-top: 1px solid #dee2e6;
        border-radius: 0 0 0.375rem 0.375rem;
        display: flex;
        justify-content: flex-end;
        gap: 0.5rem;
    }

    .close {
        color: #aaa;
        font-size: 28px;
        font-weight: bold;
        cursor: pointer;
        background: none;
        border: none;
        padding: 0;
        line-height: 1;
    }

    .close:hover,
    .close:focus {
        color: #000;
        text-decoration: none;
    }

    .badge {
        display: inline-block;
        padding: 0.25em 0.4em;
        font-size: 75%;
        font-weight: 700;
        line-height: 1;
        text-align: center;
        white-space: nowrap;
        vertical-align: baseline;
        border-radius: 0.25rem;
    }

    .badge-success {
        color: #fff;
        background-color: #28a745;
    }

    .badge-warning {
        color: #212529;
        background-color: #ffc107;
    }

    .badge-danger {
        color: #fff;
        background-color: #dc3545;
    }

    .badge-secondary {
        color: #fff;
        background-color: #6c757d;
    }
</style>
<script type="text/javascript">
    // Page data for redirection logic, from Spring model
    const chargePointPage = {
        number: ${chargePointPage.number},
        numberOfElements: ${chargePointPage.numberOfElements}
    };

    // Helper function to get selections from sessionStorage
    function getSelections() {
        var selections = sessionStorage.getItem('chargePointSelections');
        return selections ? new Set(JSON.parse(selections)) : new Set();
    }

    // Helper function to save selections to sessionStorage
    function saveSelections(selections) {
        sessionStorage.setItem('chargePointSelections', JSON.stringify(Array.from(selections)));
    }

    // Function to sync checkbox states with stored selections
    function syncCheckboxes() {
        var selections = getSelections();
        $('input[name="chargePointIds"]').each(function() {
            if ($(this).prop('disabled')) {
                $(this).prop('checked', false);
            } else {
                $(this).prop('checked', selections.has($(this).val()));
            }
        });

        var allOnPage = $('input[name="chargePointIds"]:not(:disabled)');
        var allSelected = allOnPage.length > 0 && allOnPage.filter(':checked').length === allOnPage.length;
        $('#selectAll').prop('checked', allSelected);
    }

    $(document).ready(function() {
        // Clear selection storage if navigating from a different page.
        // This prevents "sticky" selections across different user tasks.
        var referrer = document.referrer;
        var chargePointUrlPart = "${ctxPath}/manager/chargepoints";
        if (!referrer || !referrer.includes(chargePointUrlPart)) {
            sessionStorage.removeItem('chargePointSelections');
        }

        <%@ include file="../snippets/sortable.js" %>

        // --- Flash Message Pop-up ---
        <c:if test="${not empty successMessage}">
            alert("Task initiated successfully. You can monitor the progress on the Firmware Update Status page.");
        </c:if>
        <c:if test="${not empty errorMessage}">
            alert("Error: ${errorMessage}");
        </c:if>
        <c:if test="${not empty warningMessage}">
            alert("Warning: ${warningMessage}");
        </c:if>

        // Initial sync on page load
        syncCheckboxes();

        // "Select All" functionality
        $('#selectAll').on('click', function() {
            var selections = getSelections();
            if (this.checked) {
                // If checking, fetch all online IDs and add them
                $.ajax({
                    url: "${ctxPath}/manager/chargepoints/online-ids",
                    type: "GET",
                    success: function(onlineIds) {
                        onlineIds.forEach(function(id) { selections.add(id); });
                        saveSelections(selections);
                        syncCheckboxes();
                        alert(onlineIds.length + " online charge points selected across all pages.");
                    },
                    error: function() {
                        alert("Could not fetch list of online charge points.");
                        $('#selectAll').prop('checked', false);
                    }
                });
            } else {
                // If unchecking, clear all selections
                selections.clear();
                saveSelections(selections);
                syncCheckboxes();
            }
        });

        // Individual checkbox functionality
        $(document).on('change', 'input[name="chargePointIds"]', function() {
            var selections = getSelections();
            var chargeBoxId = $(this).val();
            if (this.checked) {
                selections.add(chargeBoxId);
            } else {
                selections.delete(chargeBoxId);
            }
            saveSelections(selections);
            syncCheckboxes();
        });

        // Form submission logic
        $("#batchUpdateForm").on('submit', function(e) {
            e.preventDefault();
            var form = $(this);
            var selections = getSelections();

            if (selections.size === 0) {
                alert("Please select at least one charge point.");
                return;
            }

            var selectedFirmware = $('#firmwareFileSelect').val();
            if (!selectedFirmware) {
                alert("Please select a firmware file.");
                return;
            }

            // Clear old hidden inputs and add current selections
            form.find('input[name="chargePointIds"][type="hidden"]').remove();
            selections.forEach(function(id) {
                $('<input>').attr({
                    type: 'hidden',
                    name: 'chargePointIds',
                    value: id
                }).appendTo(form);
            });

            // Clear selections from storage after adding them to the form for submission
            sessionStorage.removeItem('chargePointSelections');

            form.get(0).submit();
        });

        $('#pageSizeSelector').on('change', function() {
            var form = $(this).closest('form');
            // Reset to the first page when changing page size
            var pageInput = form.find('input[name="page"]');
            if (pageInput.length > 0) {
                pageInput.val(0);
            } else {
                form.append('<input type="hidden" name="page" value="0">');
            }
            form.submit();
        });

        // --- Custom Dropdown Logic ---
        // Use event delegation for dynamically added elements
        $(document).on('click', '.actions-btn', function(event) {
            event.stopPropagation(); // Prevent document click from closing it immediately
            // Close all other open menus
            $('.actions-menu').not($(this).next('.actions-menu')).hide();
            // Toggle the current menu
            $(this).next('.actions-menu').toggle();
        });

        // Close dropdown when clicking outside
        $(document).on('click', function(event) {
            if (!$(event.target).closest('.actions-menu-container').length) {
                $('.actions-menu').hide();
            }
        });

        // --- Custom Modal Logic ---
        var modal = $('#deleteConfirmationModal');
        var formToSubmit = null;
        var actionUrl = '';
        var chargeBoxPkToDelete = null;

        // CSRF token for AJAX requests
        var csrfToken = $("meta[name='_csrf']").attr("content");
        var csrfHeader = $("meta[name='_csrf_header']").attr("content");
        var headers = {};
        headers[csrfHeader] = csrfToken;

        // When the user clicks the delete button in the dropdown
        $(document).on('click', '.delete-btn', function(event) {
            event.preventDefault(); // Prevent the form from submitting immediately
            chargeBoxPkToDelete = $(this).data('chargebox-pk');
            modal.show();
        });

        // When the user clicks on "Confirm Delete" in the modal
        $('#confirmDelete').on('click', function() {
            if (chargeBoxPkToDelete) {
                var deletingButton = $(this);
                deletingButton.prop('disabled', true).text('Deleting...');
                var deleteUrl = "${ctxPath}/manager/chargepoints/delete/" + chargeBoxPkToDelete;

                $.ajax({
                    url: deleteUrl, // Use the constructed URL
                    type: 'DELETE',
                    headers: headers,
                    success: function() {
                        var urlParams = new URLSearchParams(window.location.search);
                        var currentPage = chargePointPage.number;
                        var elementsOnPage = chargePointPage.numberOfElements;

                        if (elementsOnPage === 1 && currentPage > 0) {
                            urlParams.set('page', currentPage - 1);
                        } else {
                            // a page refresh is enough, but to be explicit:
                            urlParams.set('page', currentPage);
                        }

                        // Redirect to the potentially updated page URL
                        window.location.href = window.location.pathname + '?' + urlParams.toString();
                    },
                    error: function(jqXHR) {
                        // On error, show message and re-enable button
                        var errorMessage = "An unknown error occurred.";
                        if (jqXHR.responseText) {
                            errorMessage = jqXHR.responseText;
                        }
                        alert('Error deleting charge point: ' + errorMessage);
                        deletingButton.prop('disabled', false).text('Confirm Delete');
                        chargeBoxPkToDelete = null;
                    }
                });
            }
        });

        // When the user clicks on <span> (x), or cancel button, close the modal
        $('.close, #cancelDelete').on('click', function() {
            modal.hide();
            chargeBoxPkToDelete = null;
        });

        // When the user clicks anywhere outside of the modal, close it
        $(window).on('click', function(event) {
            if (event.target == modal[0]) {
                modal.hide();
                chargeBoxPkToDelete = null;
            }
        });

        // --- Get Diagnostics Modal Logic ---
        var diagnosticsModal = $('#getDiagnosticsModal');
        var chargeBoxIdForDiagnostics = null;

        // When the user clicks the Get Diagnostics button in the dropdown
        $(document).on('click', '.get-diagnostics-btn', function(event) {
            event.preventDefault();
            chargeBoxIdForDiagnostics = $(this).data('chargebox-id');
            $('#diagnosticsChargePointId').text(chargeBoxIdForDiagnostics);
            diagnosticsModal.show();
        });

        // When the user clicks on "Perform" in the diagnostics modal
        $('#confirmDiagnostics').on('click', function() {
            if (chargeBoxIdForDiagnostics) {
                var performButton = $(this);
                performButton.prop('disabled', true).text('Processing...');

                // Create a form and submit it
                var form = $('<form>', {
                    'method': 'POST',
                    'action': "${ctxPath}/manager/chargepoints/" + chargeBoxIdForDiagnostics + "/getDiagnostics"
                });

                // Add CSRF token
                form.append($('<input>', {
                    'type': 'hidden',
                    'name': '${_csrf.parameterName}',
                    'value': '${_csrf.token}'
                }));

                // Add parameters if they exist
                var retries = $('#retries').val();
                var retryInterval = $('#retryInterval').val();
                var startDate = $('#startDate').val();
                var stopDate = $('#stopDate').val();

                if (retries) {
                    form.append($('<input>', {
                        'type': 'hidden',
                        'name': 'retries',
                        'value': retries
                    }));
                }

                if (retryInterval) {
                    form.append($('<input>', {
                        'type': 'hidden',
                        'name': 'retryInterval',
                        'value': retryInterval
                    }));
                }

                if (startDate) {
                    form.append($('<input>', {
                        'type': 'hidden',
                        'name': 'startDate',
                        'value': startDate
                    }));
                }

                if (stopDate) {
                    form.append($('<input>', {
                        'type': 'hidden',
                        'name': 'stopDate',
                        'value': stopDate
                    }));
                }

                // Append form to body and submit
                form.appendTo('body').submit();
            }
        });

        // When the user clicks on <span> (x), or cancel button, close the diagnostics modal
        $('.close, #cancelDiagnostics').on('click', function() {
            diagnosticsModal.hide();
            chargeBoxIdForDiagnostics = null;
        });

        // When the user clicks anywhere outside of the diagnostics modal, close it
        $(window).on('click', function(event) {
            if (event.target == diagnosticsModal[0]) {
                diagnosticsModal.hide();
                chargeBoxIdForDiagnostics = null;
            }
        });

        // Batch update via modal
        $('#batch-update-btn').on('click', function() {
            // ... (existing code for batch update)
        });

        // Bulk Import Modal Handling
        $(document).ready(function() {
            var modal = $('#bulk-import-modal');
            var btn = $('#bulk-import-btn');
            var span = modal.find('.close');

            btn.on('click', function() {
                modal.show();
            });

            span.on('click', function() {
                modal.hide();
                // Reset form and progress on close
                $('#bulk-import-form')[0].reset();
                $('#import-progress-container').hide();
                $('#import-progress-bar').css('width', '0%').text('0%');
                $('#import-status').text('');
            });

            $(window).on('click', function(event) {
                if ($(event.target).is(modal)) {
                    modal.hide();
                }
            });

            $('#bulk-import-form').on('submit', function(e) {
                e.preventDefault();
                var formData = new FormData(this);
                var fileInput = $('#ini-file')[0];

                if (fileInput.files.length === 0) {
                    alert('Please select a file.');
                    return;
                }

                let csrfToken;
                let csrfHeader;

                // on document ready
                $(function () {
                    // get csrf token from meta tags
                    csrfToken = $("meta[name='_csrf']").attr("content");
                    csrfHeader = $("meta[name='_csrf_header']").attr("content");
                });

                $('#import-progress-container').show();
                $('#import-status').text('Uploading file...');

                $.ajax({
                    url: '<c:url value="/manager/chargepoints/import"/>',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    headers: {
                       [csrfHeader]: csrfToken
                    },
                    success: function(response) {
                        const taskId = response.taskId;
                        $('#import-status').text('Upload successful. Processing file... Task ID: ' + taskId);
                        if (taskId) {
                            pollProgress(taskId);
                        } else {
                             $('#import-status').text('Error: Could not start import process.');
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        $('#import-status').text('Upload failed: ' + errorThrown);
                    }
                });
            });

            function pollProgress(taskId) {
                var interval = setInterval(function() {
                    $.ajax({
                        url: '<c:url value="/manager/chargepoints/import/progress/"/>' + taskId,
                        type: 'GET',
                        success: function(progress) {
                            if (progress.total > 0) {
                                var percentage = Math.round((progress.processed / progress.total) * 100);
                                $('#import-progress-bar').css('width', percentage + '%').text(percentage + '%');
                                $('#import-status').text('Processed ' + progress.processed + ' of ' + progress.total + ' entries.');
                            }

                            if (progress.finished) {
                                clearInterval(interval);
                                var summary = 'Import finished. Added: ' + progress.added + '. Already existed: ' + progress.existed + '.';
                                $('#import-status').text(summary);
                                // Optionally, refresh the page after a delay
                                setTimeout(function() {
                                    location.reload();
                                }, 2000);
                            }
                        },
                        error: function() {
                            clearInterval(interval);
                            $('#import-status').text('Error checking progress. Please try again.');
                        }
                    });
                }, 1000);
            }
        });
    });
</script>
<div class="content-container">
    <div class="page-title">Charge Point Management</div>

    <%-- Unknown Charge Points Section --%>
    <c:if test="${userRole == 'ADMIN' or userRole == 'OPERATOR_FACTORY'}">
        <%-- Simplified conditional display of unknown section based on userRole from model --%>
        <sec:authorize access="hasAnyAuthority('ADMIN', 'OPERATOR_FACTORY')">
            <%-- Retaining original sec:authorize for actual security, model attribute is for convenience --%>
            <div>
            <section><span id="unknown" style="cursor: pointer">
            Unknown Charge Points
            <a class="tooltip" href="#"><img src="${ctxPath}/static/images/info.png" style="vertical-align:middle">
                <span>A list of charge points that attempted to connect and send a boot notification but were not present in database.</span>
            </a>
            </span></section>
            <div id="unknownTable" style="display: none">
                <table class="res add-margin-bottom table-custom">
                    <thead>
                    <tr>
                        <th data-sort="string">ChargeBox ID</th>
                        <th data-sort="int"># of Attempts</th>
                        <th data-sort="date">Last Attempt</th>
                        <th>Actions</th>
                    </tr>
                    </thead>
                    <tbody>
                    <c:forEach items="${unknownList}" var="item">
                        <tr>
                            <td><encode:forHtml value="${item.key}" /></td>
                            <td>${item.numberOfAttempts}</td>
                            <td data-sort-value="${item.lastAttemptTimestamp.millis}">${item.lastAttemptTimestamp}</td>
                            <td>
                                <form:form cssClass="inline" action="${ctxPath}/manager/chargepoints/unknown/add/${item.key}/" method="post">
                                    <input type="submit" class="btn btn-sm btn-success" value="Add"/>
                                </form:form>
                                &nbsp;
                                <form:form cssClass="inline" action="${ctxPath}/manager/chargepoints/unknown/remove/${item.key}/" method="post">
                                    <input type="submit" class="btn btn-sm btn-warning" value="Forget"/>
                                </form:form>
                            </td>
                        </tr>
                    </c:forEach>
                    <c:if test="${empty unknownList}">
                        <tr><td colspan="4">No unknown charge points.</td></tr>
                    </c:if>
                    </tbody>
                </table>
            </div>
            </div>
        </sec:authorize>
    </c:if>

    <section><span>
    Charge Point Overview
    <a class="tooltip" href="#"><img src="${ctxPath}/static/images/info.png" style="vertical-align:middle">
        <span>
        <sec:authorize access="hasAnyAuthority('ADMIN', 'OPERATOR_FACTORY')">
        Deleting a charge point causes losing all related information including transactions, reservations, connector status and connector meter values.
        </sec:authorize>
        <sec:authorize access="hasAuthority('OPERATOR_OWNER')">
        You can only see charge points assigned to you.
        </sec:authorize>
        </span>
    </a>
    </span></section>
    <div id="overview">
        <div class="search-panel p-3 mb-3 border rounded bg-light">
            <form:form action="${ctxPath}/manager/chargepoints/query" method="get" modelAttribute="params">
                <div class="d-flex flex-wrap">
                    <!-- Uniformly spaced filter items -->
                    <div class="filter-item">
                        <label for="chargeBoxId" class="form-label">ChargeBox ID:</label>
                        <form:input path="chargeBoxId" id="chargeBoxId" class="form-control form-control-sm"/>
                    </div>
                    <div class="filter-item">
                        <label for="heartbeatPeriod" class="form-label">Heartbeat:</label>
                        <form:select path="heartbeatPeriod" id="heartbeatPeriod" class="form-control form-control-sm">
                            <form:options items="${heartbeatPeriod}" itemLabel="value" itemValue="name"/>
                        </form:select>
                    </div>
                    <sec:authorize access="hasAnyAuthority('ADMIN', 'OPERATOR_FACTORY')">
                        <div class="filter-item">
                            <label for="owner" class="form-label">Owner:</label>
                            <form:select path="owner" id="owner" class="form-control form-control-sm">
                                <form:option value="" label="-- All Owners --"/>
                                <c:forEach items="${ownerList}" var="owner">
                                    <form:option value="${owner.username}" label="${owner.displayName}"/>
                                </c:forEach>
                            </form:select>
                        </div>
                    </sec:authorize>
                    <div class="filter-item">
                        <label for="chargingStationPk" class="form-label">Charging Station:</label>
                        <form:select path="chargingStationPk" id="chargingStationPk" class="form-control form-control-sm">
                            <form:option value="" label="-- All Stations --"/>
                            <c:forEach items="${chargeStationList}" var="station">
                                <form:option value="${station.chargingStationPk}" label="${station.displayName}"/>
                            </c:forEach>
                        </form:select>
                    </div>
                    <div class="filter-item">
                        <label for="lastUpgradeStatus" class="form-label">Upgrade Status:</label>
                        <form:select path="lastUpgradeStatus" cssClass="form-select form-select-sm">
                            <form:option value="" label="All"/>
                            <form:option value="Upgrade succeeded" label="Succeeded"/>
                            <form:option value="Upgrading" label="Upgrading"/>
                            <form:option value="Upgrade failed" label="Failed"/>
                        </form:select>
                    </div>
                    <div class="filter-item">
                       <label for="pageSizeSelector" class="form-label">Show:</label>
                       <select id="pageSizeSelector" name="size" class="form-select form-select-sm">
                           <c:forEach items="${availablePageSizes}" var="size">
                               <option value="${size}" ${size == chargePointPage.size ? 'selected' : ''}>${size}</option>
                           </c:forEach>
                       </select>
                    </div>
                    <div class="filter-item">
                        <label>&nbsp;</label> <!-- Spacer for button alignment -->
                        <div class="d-flex">
                            <button type="submit" class="btn btn-primary btn-sm flex-grow-1">Search</button>
                            <a href="${ctxPath}/manager/chargepoints/add" class="btn btn-success btn-sm ms-2 flex-grow-1">Add</a>
                            <button id="bulk-import-btn" type="button" class="btn btn-info btn-sm ms-2 flex-grow-1">Bulk Import</button>
                        </div>
                    </div>
                </div>
            </form:form>
        </div>

        <form:form id="batchUpdateForm" action="${ctxPath}/manager/chargepoints/batch-update" method="post">
            <div class="batch-update-panel p-3 mb-3 border rounded bg-light">
                <div class="row gx-3 align-items-end">
                    <div class="col-md-4">
                        <label for="firmwareFileSelect" class="form-label"><b>Select Firmware for Batch Update:</b></label>
                        <div class="input-group">
                            <select id="firmwareFileSelect" name="firmwareFile" class="form-control form-control-sm">
                                <option value="">-- Please Select --</option>
                                <c:forEach items="${firmwareFiles}" var="file">
                                    <option value="${file}">${file}</option>
                                </c:forEach>
                            </select>
                            <sec:authorize access="hasAnyAuthority('ADMIN', 'OPERATOR_FACTORY')">
                                <div class="input-group-append">
                                    <button type="button" class="btn btn-danger btn-sm" id="deleteFirmwareBtn"
                                            title="Manage firmware files" onclick="openFirmwareDeleteModal()">
                                        <i class="fa fa-trash"></i> Delete
                                    </button>
                                </div>
                            </sec:authorize>
                        </div>
                    </div>
                    <div class="col-md-auto">
                        <button type="submit" class="btn btn-warning btn-sm">Upgrade Selected</button>
                    </div>

                </div>
            </div>

            <div class="table-responsive">
                <table id="chargePointTable" class="table table-striped table-hover table-bordered table-custom">
                    <thead>
                    <tr>
                        <th style="width: 3%; text-align: center;"><input type="checkbox" id="selectAll" title="Select/Deselect ALL online charge points on ALL pages."></th>
                        <th class="sorting" data-sort="string">ChargeBox ID</th>
                        <th class="sorting" data-sort="string">Status</th>
                        <th class="sorting" data-sort="date">Last Heartbeat</th>
                        <th class="sorting" data-sort="string">Charging Station</th>
                        <th class="sorting" data-sort="string">Firmware Version</th>
                        <th class="sorting" data-sort="string">Firmware Update Timestamp</th>
                        <th class="sorting" data-sort="string">Last Upgrade Status</th>
                        <th class="sorting" data-sort="string">Owner</th>
                        <th class="sorting" data-sort="float">Charging Success Rate</th>
                        <th class="sorting" data-sort="int">Total Sessions</th>
                        <th style="text-align: center;">Actions</th>
                    </tr>
                    </thead>
                    <tbody>
                    <c:forEach items="${cpList}" var="cp">
                        <tr>
                            <td style="text-align: center;">
                                <input type="checkbox" name="chargePointIds" value="${cp.chargeBoxId}" <c:if test="${!cp.online}">disabled title="Not Online"</c:if>/>
                            </td>
                            <td>${cp.chargeBoxId}</td>
                            <td>
                                <c:choose>
                                    <c:when test="${cp.online}">
                                        <span style="color: green; text-decoration: underline;">Online</span>
                                    </c:when>
                                    <c:otherwise>
                                        <span style="color: red; text-decoration: underline;">Offline</span>
                                    </c:otherwise>
                                </c:choose>
                            </td>
                            <td data-sort-value="${cp.lastHeartbeatTimestampDT.millis}">${cp.lastHeartbeatTimestamp}</td>
                            <td>
                                <c:choose>
                                    <c:when test="${not empty cp.chargingStationName}">
                                        <a href="${ctxPath}/manager/chargestations/details/${cp.chargingStationPk}" title="View Charging Station Details">
                                            ${cp.chargingStationName}
                                        </a>
                                    </c:when>
                                    <c:otherwise>
                                        <span style="color: #999;">Not Assigned</span>
                                    </c:otherwise>
                                </c:choose>
                            </td>
                            <td>${cp.firmwareVersion}</td>
                            <td>${cp.firmwareUpdateTimestamp}</td>
                            <td>
                                <c:if test="${not empty cp.lastUpgradeStatus}">
                                    <a href="${ctxPath}/manager/firmwareUpdate?chargeBoxId=${cp.chargeBoxId}" class="status-link">
                                        <c:choose>
                                            <c:when test="${cp.lastUpgradeStatus == 'Upgrade succeeded'}">
                                                <span style="color: green;">${cp.lastUpgradeStatus}</span>
                                            </c:when>
                                            <c:when test="${cp.lastUpgradeStatus == 'Upgrading'}">
                                                <span style="color: #FFBF00;">${cp.lastUpgradeStatus}</span>
                                            </c:when>
                                            <c:when test="${cp.lastUpgradeStatus == 'Upgrade failed'}">
                                                <span style="color: red;">${cp.lastUpgradeStatus}</span>
                                            </c:when>
                                            <c:otherwise>
                                                ${cp.lastUpgradeStatus}
                                            </c:otherwise>
                                        </c:choose>
                                    </a>
                                </c:if>
                            </td>
                            <td>${cp.owner}</td>
                            <td data-sort-value="${cp.successRateDecimal}" style="text-align: center;">
                                <c:choose>
                                    <c:when test="${cp.totalChargingSessions > 0}">
                                        <span title="Successful: ${cp.successfulChargingSessions} / Total: ${cp.totalChargingSessions}">
                                            <c:choose>
                                                <c:when test="${cp.successRateDecimal >= 90}">
                                                    <span style="color: green; font-weight: bold;">${cp.successRate}</span>
                                                </c:when>
                                                <c:when test="${cp.successRateDecimal >= 70}">
                                                    <span style="color: #FFBF00; font-weight: bold;">${cp.successRate}</span>
                                                </c:when>
                                                <c:otherwise>
                                                    <span style="color: red; font-weight: bold;">${cp.successRate}</span>
                                                </c:otherwise>
                                            </c:choose>
                                        </span>
                                    </c:when>
                                    <c:otherwise>
                                        <span style="color: #999;">N/A</span>
                                    </c:otherwise>
                                </c:choose>
                            </td>
                            <td style="text-align: center;">
                                <c:choose>
                                    <c:when test="${cp.totalChargingSessions > 0}">
                                        <span style="font-weight: bold;">${cp.totalChargingSessions}</span>
                                    </c:when>
                                    <c:otherwise>
                                        <span style="color: #999;">0</span>
                                    </c:otherwise>
                                </c:choose>
                            </td>
                            <td style="text-align: center;">
                                <div class="actions-menu-container">
                                    <button type="button" class="btn btn-sm btn-secondary actions-btn">More</button>
                                    <div class="actions-menu">
                                        <a href="${ctxPath}/manager/chargepoints/details/${cp.chargeBoxId}">Details</a>
                                        <a href="${ctxPath}/manager/chargepoints/datatransfer/${cp.chargeBoxId}">Data Transfer</a>
                                        <c:choose>
                                            <c:when test="${cp.online}">
                                                <button type="button" class="get-diagnostics-btn dropdown-item" data-chargebox-id="${cp.chargeBoxId}">Get Diagnostics</button>
                                            </c:when>
                                            <c:otherwise>
                                                <button type="button" class="dropdown-item" disabled style="color: #999; cursor: not-allowed;" title="Charge point is offline">Get Diagnostics</button>
                                            </c:otherwise>
                                        </c:choose>
                                        <sec:authorize access="hasAnyAuthority('ADMIN', 'OPERATOR_FACTORY', 'OPERATOR_OWNER')">
                                            <div class="dropdown-divider"></div>
                                            <button type="button" class="delete-btn dropdown-item" data-chargebox-pk="${cp.chargeBoxPk}">Delete</button>
                                        </sec:authorize>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    </c:forEach>
                    <c:if test="${empty cpList}">
                        <tr>
                            <td colspan="11" class="text-center">No charge points found.</td>
                        </tr>
                    </c:if>
                    </tbody>
                </table>
            </div>
        </form:form>

        <%-- Pagination Controls --%>
        <c:if test="${chargePointPage != null && chargePointPage.totalPages > 0}">
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div>
                    Showing ${chargePointPage.number * chargePointPage.size + 1} 
                    to ${chargePointPage.number * chargePointPage.size + chargePointPage.numberOfElements} 
                    of ${chargePointPage.totalElements} entries
                </div>
                <nav aria-label="Page navigation">
                    <ul class="pagination mb-0">
                        <%-- Previous Page Link --%>
                        <li class="page-item <c:if test='${chargePointPage.number == 0}'>disabled</c:if>">
                            <c:url value="/manager/chargepoints/query" var="prevUrl">
                                <c:if test="${not empty params.chargeBoxId}"><c:param name="chargeBoxId" value="${params.chargeBoxId}"/></c:if>
                                <c:if test="${not empty params.heartbeatPeriod}"><c:param name="heartbeatPeriod" value="${params.heartbeatPeriod}"/></c:if>
                                <c:if test="${not empty params.lastUpgradeStatus}"><c:param name="lastUpgradeStatus" value="${params.lastUpgradeStatus}"/></c:if>
                                <c:if test="${not empty params.owner}"><c:param name="owner" value="${params.owner}"/></c:if>
                                <c:if test="${not empty params.chargingStationPk}"><c:param name="chargingStationPk" value="${params.chargingStationPk}"/></c:if>
                                <c:param name="page" value="${chargePointPage.number - 1}"/>
                                <c:param name="size" value="${chargePointPage.size}"/>
                            </c:url>
                            <a class="page-link" href="${prevUrl}">Previous</a>
                        </li>

                        <%-- Page Number Links --%>
                        <c:forEach begin="0" end="${chargePointPage.totalPages - 1}" var="i">
                            <li class="page-item <c:if test='${chargePointPage.number == i}'>active</c:if>">
                                <c:url value="/manager/chargepoints/query" var="pageUrl">
                                    <c:if test="${not empty params.chargeBoxId}"><c:param name="chargeBoxId" value="${params.chargeBoxId}"/></c:if>
                                    <c:if test="${not empty params.heartbeatPeriod}"><c:param name="heartbeatPeriod" value="${params.heartbeatPeriod}"/></c:if>
                                    <c:if test="${not empty params.lastUpgradeStatus}"><c:param name="lastUpgradeStatus" value="${params.lastUpgradeStatus}"/></c:if>
                                    <c:if test="${not empty params.owner}"><c:param name="owner" value="${params.owner}"/></c:if>
                                    <c:if test="${not empty params.chargingStationPk}"><c:param name="chargingStationPk" value="${params.chargingStationPk}"/></c:if>
                                    <c:param name="page" value="${i}"/>
                                    <c:param name="size" value="${chargePointPage.size}"/>
                                </c:url>
                                <a class="page-link" href="${pageUrl}">${i + 1}</a>
                            </li>
                        </c:forEach>

                        <%-- Next Page Link --%>
                        <li class="page-item <c:if test='${chargePointPage.number >= chargePointPage.totalPages - 1}'>disabled</c:if>">
                            <c:url value="/manager/chargepoints/query" var="nextUrl">
                                <c:if test="${not empty params.chargeBoxId}"><c:param name="chargeBoxId" value="${params.chargeBoxId}"/></c:if>
                                <c:if test="${not empty params.heartbeatPeriod}"><c:param name="heartbeatPeriod" value="${params.heartbeatPeriod}"/></c:if>
                                <c:if test="${not empty params.lastUpgradeStatus}"><c:param name="lastUpgradeStatus" value="${params.lastUpgradeStatus}"/></c:if>
                                <c:if test="${not empty params.owner}"><c:param name="owner" value="${params.owner}"/></c:if>
                                <c:if test="${not empty params.chargingStationPk}"><c:param name="chargingStationPk" value="${params.chargingStationPk}"/></c:if>
                                <c:param name="page" value="${chargePointPage.number + 1}"/>
                                <c:param name="size" value="${chargePointPage.size}"/>
                            </c:url>
                            <a class="page-link" href="${nextUrl}">Next</a>
                        </li>
                    </ul>
                </nav>
            </div>
        </c:if>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteConfirmationModal" class="modal">
  <div class="modal-content">
    <div class="modal-header">
      <span class="close">&times;</span>
      <h2>Confirm Deletion</h2>
    </div>
    <div class="modal-body">
      <p>Are you sure you want to delete this charge point? This action cannot be undone.</p>
    </div>
    <div class="modal-footer">
      <button id="cancelDelete" type="button" class="btn btn-secondary">Cancel</button>
      <button id="confirmDelete" type="button" class="btn btn-danger">Confirm Delete</button>
    </div>
  </div>
</div>

<!-- Get Diagnostics Modal -->
<div id="getDiagnosticsModal" class="modal">
  <div class="modal-content">
    <div class="modal-header">
      <span class="close">&times;</span>
      <h2>Get Diagnostics</h2>
    </div>
    <div class="modal-body">
      <p><strong>Charge Point:</strong> <span id="diagnosticsChargePointId"></span></p>
      <div class="info-message" style="background-color: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
        <p><strong>Diagnostic logs will be automatically saved to the built-in FTP server logs directory</strong></p>
        <p><strong>Date Range:</strong> If not specified, will retrieve logs from yesterday.
           You can specify Start/Stop dates to retrieve logs from specific days (e.g., 2025-07-15 to 2025-07-17 will get logs from 15th, 16th, and 17th).</p>
      </div>
      <form id="getDiagnosticsForm">
        <table class="userInput">
          <tr><td>Retries (integer):</td><td><input type="number" id="retries" name="retries" placeholder="optional" /></td></tr>
          <tr><td>Retry Interval (integer):</td><td><input type="number" id="retryInterval" name="retryInterval" placeholder="optional" /></td></tr>
          <tr><td>Start Date:</td>
              <td>
                  <input type="date" id="startDate" name="startDate" placeholder="optional (e.g., 2025-07-15)" />
              </td>
          </tr>
          <tr><td>Stop Date:</td>
              <td>
                  <input type="date" id="stopDate" name="stopDate" placeholder="optional (e.g., 2025-07-17)" />
              </td>
          </tr>
        </table>
      </form>
    </div>
    <div class="modal-footer">
      <button id="cancelDiagnostics" type="button" class="btn btn-secondary">Cancel</button>
      <button id="confirmDiagnostics" type="button" class="btn btn-primary">Perform</button>
    </div>
  </div>
</div>

<!-- Bulk Import Modal -->
<div id="bulk-import-modal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <span class="close">&times;</span>
            <h2>Bulk Import Charge Points</h2>
        </div>
        <div class="modal-body">
            <form id="bulk-import-form" enctype="multipart/form-data">
                <div class="form-group">
                    <label for="ini-file">Select .ini file:</label>
                    <input type="file" id="ini-file" name="file" class="form-control" accept=".ini" required>
                </div>
                <button type="submit" class="btn btn-primary">Upload</button>
            </form>
            <div id="import-progress-container" style="display:none;">
                <p id="import-status"></p>
                <div class="progress-container">
                    <div id="import-progress-bar" class="progress-bar">0%</div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // This script block is placed at the end of the body to ensure all elements are loaded.
    $(document).ready(function() {
        // --- Custom Dropdown Logic (Append to Body) ---
        function closeExternalDropdowns() {
            var openMenu = $('body > .actions-menu');
            if (openMenu.length) {
                var container = openMenu.data('original-container');
                if (container) {
                    openMenu.hide().removeClass('show').detach().appendTo(container);
                } else {
                    openMenu.remove(); // Failsafe
                }
            }
        }

        $(document).on('click', '.actions-btn', function(event) {
            event.stopPropagation();

            var menu = $(this).siblings('.actions-menu');
            var isAlreadyOpen = menu.hasClass('show');

            closeExternalDropdowns();

            if (!isAlreadyOpen) {
                var container = $(this).closest('.actions-menu-container');
                menu.data('original-container', container);

                var buttonRect = this.getBoundingClientRect();

                menu.detach().appendTo('body');

                // Position menu to the top-right of the button
                menu.css({
                    position: 'absolute',
                    top: (buttonRect.top + window.scrollY) + 'px',
                    left: (buttonRect.left + window.scrollX) + 'px',
                    zIndex: 9999,
                    minWidth: $(this).outerWidth() + 'px' // Ensure menu is at least as wide as button
                }).addClass('show').show();
            }
        });

        // Close dropdown when clicking anywhere else
        $(document).on('click', function(event) {
            // Close if click is not on the menu itself or a button that opens a menu
            if (!$(event.target).closest('.actions-menu').length && !$(event.target).closest('.actions-btn').length) {
                closeExternalDropdowns();
            }
        });
    });
</script>

<script type="text/javascript">
    $(function () {
        const csrfToken = $("meta[name='_csrf']").attr("content");
        const csrfHeader = $("meta[name='_csrf_header']").attr("content");

        // --- Delete Modal ---
        // handleDeleteModal("charge point", function (chargeBoxPk) {
        //     let url = "${ctxPath}/manager/chargepoints/" + chargeBoxPk + "/delete";
        //     window.location.href = url;
        // });

        // --- Bulk Import Modal ---
        const bulkImportModal = document.getElementById('bulk-import-modal');
        const bulkImportBtn = document.getElementById('bulk-import-btn');
        const closeBtn = bulkImportModal.querySelector('.close');

        bulkImportBtn.onclick = function () {
            bulkImportModal.style.display = 'block';
            // Reset form on open
            $('#bulk-import-form')[0].reset();
            $('#import-status').text('');
            $('#import-progress-bar').css('width', '0%').text('');
            $('#import-progress-container').hide();
        };

        closeBtn.onclick = function () {
            bulkImportModal.style.display = 'sm';
        };

        window.onclick = function (event) {
            if (event.target == bulkImportModal) {
                bulkImportModal.style.display = 'sm';
            }
        };

        $('#bulk-import-form').on('submit', function (e) {
            e.preventDefault();

            const fileInput = $('#ini-file')[0];
            if (fileInput.files.length === 0) {
                $('#import-status').text('Please select a file to upload.');
                return;
            }

            $('#import-progress-container').show();
            const formData = new FormData(this);

            $.ajax({
                url: '${ctxPath}/manager/chargepoints/import',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                headers: {
                    [csrfHeader]: csrfToken
                },
                success: function (response) {
                    const taskId = response.taskId;
                    $('#import-status').text('Upload successful. Processing file... Task ID: ' + taskId);
                    if (taskId) {
                        pollProgress(taskId);
                    } else {
                        $('#import-status').text('Error: Could not get task ID.');
                    }
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    $('#import-status').text('Upload failed: ' + errorThrown);
                }
            });
        });

        function pollProgress(taskId) {
            const interval = setInterval(function () {
                $.ajax({
                    url: '${ctxPath}/manager/chargepoints/import/progress/' + taskId,
                    type: 'GET',
                    success: function (progress) {
                        const percentage = progress.total > 0 ? (progress.processed / progress.total) * 100 : 0;
                        $('#import-progress-bar').css('width', percentage + '%').text(Math.round(percentage) + '%');
                        
                        let statusText = `Processed: ${progress.processed}/${progress.total} | Added: ${progress.added} | Existed: ${progress.existed}`;
                        $('#import-status').text(statusText);

                        if (progress.finished) {
                            clearInterval(interval);
                            $('#import-status').append(' | Import complete.');
                        }
                    },
                    error: function () {
                        clearInterval(interval);
                        $('#import-status').text('Error fetching progress. Please check server logs.');
                    }
                });
            }, 2000); // Poll every 2 seconds
        }
    });

    // --- Firmware File Management ---
    $(document).ready(function() {
        // Handle select all checkbox
        $('#selectAllFirmware').on('change', function() {
            const isChecked = $(this).is(':checked');
            $('.firmware-file-checkbox').prop('checked', isChecked);
            updateDeleteButtonState();
        });

        // Handle individual checkbox changes
        $(document).on('change', '.firmware-file-checkbox', function() {
            updateDeleteButtonState();
            updateSelectAllState();
        });

        // Handle delete confirmation
        $('#confirmDeleteFirmware').on('click', function() {
            const selectedFiles = getSelectedFiles();
            if (selectedFiles.length === 0) {
                alert('Please select at least one file to delete');
                return;
            }

            const fileList = selectedFiles.join(', ');
            if (confirm(`Are you sure you want to delete ${selectedFiles.length} file(s)?\n\n${fileList}\n\nThis action cannot be undone.`)) {
                deleteFirmwareFiles(selectedFiles);
            }
        });
    });

    // Modal control functions
    function openFirmwareDeleteModal() {
        document.getElementById('firmwareDeleteModal').style.display = 'block';
        loadFirmwareFiles();
    }

    function closeFirmwareDeleteModal() {
        document.getElementById('firmwareDeleteModal').style.display = 'none';
    }

    // Close modal when clicking outside of it
    window.onclick = function(event) {
        const modal = document.getElementById('firmwareDeleteModal');
        if (event.target == modal) {
            closeFirmwareDeleteModal();
        }
    }

    function loadFirmwareFiles() {
        $('#firmwareDeleteLoading').show();
        $('#firmwareDeleteContent').hide();
        $('#firmwareDeleteError').hide();

        $.ajax({
            url: '${ctxPath}/manager/chargepoints/firmware-files',
            type: 'GET',
            success: function(response) {
                if (response.success) {
                    displayFirmwareFiles(response.files);
                    $('#firmwareDeleteLoading').hide();
                    $('#firmwareDeleteContent').show();
                } else {
                    showDeleteError('Failed to load firmware files: ' + (response.errorMessage || 'Unknown error'));
                }
            },
            error: function(xhr, status, error) {
                showDeleteError('Error loading firmware files: ' + error);
            }
        });
    }

    function displayFirmwareFiles(files) {
        const tbody = $('#firmwareFilesList');
        tbody.empty();

        if (files.length === 0) {
            tbody.append('<tr><td colspan="6" class="text-center">No firmware files found</td></tr>');
            return;
        }

        files.forEach(function(file) {
            const statusBadge = getStatusBadge(file.uploadStatus);
            const fileSize = formatFileSize(file.fileSize);
            const uploadTime = formatDateTime(file.uploadTime);

            const row = '<tr>' +
                '<td>' +
                    '<input type="checkbox" class="firmware-file-checkbox" value="' + file.filename + '">' +
                '</td>' +
                '<td title="' + file.filename + '">' + truncateText(file.filename, 30) + '</td>' +
                '<td>' + (file.fileType || 'Unknown') + '</td>' +
                '<td>' + statusBadge + '</td>' +
                '<td>' + uploadTime + '</td>' +
                '<td>' + fileSize + '</td>' +
                '</tr>';
            tbody.append(row);
        });

        updateDeleteButtonState();
    }

    function getStatusBadge(status) {
        switch (status) {
            case 'SUCCESS':
                return '<span class="badge badge-success">Installed</span>';
            case 'PENDING':
                return '<span class="badge badge-warning">Pending</span>';
            case 'FAILED':
                return '<span class="badge badge-danger">Failed</span>';
            default:
                return '<span class="badge badge-secondary">Unknown</span>';
        }
    }

    function formatFileSize(bytes) {
        if (!bytes) return 'Unknown';
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    }

    function formatDateTime(dateTime) {
        if (!dateTime) return 'Unknown';
        // Handle different date formats
        try {
            const date = new Date(dateTime);
            return date.toLocaleString();
        } catch (e) {
            return dateTime;
        }
    }

    function truncateText(text, maxLength) {
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    }

    function updateDeleteButtonState() {
        const selectedCount = $('.firmware-file-checkbox:checked').length;
        $('#confirmDeleteFirmware').prop('disabled', selectedCount === 0);
    }

    function updateSelectAllState() {
        const totalCheckboxes = $('.firmware-file-checkbox').length;
        const checkedCheckboxes = $('.firmware-file-checkbox:checked').length;

        if (checkedCheckboxes === 0) {
            $('#selectAllFirmware').prop('indeterminate', false).prop('checked', false);
        } else if (checkedCheckboxes === totalCheckboxes) {
            $('#selectAllFirmware').prop('indeterminate', false).prop('checked', true);
        } else {
            $('#selectAllFirmware').prop('indeterminate', true);
        }
    }

    function getSelectedFiles() {
        const selected = [];
        $('.firmware-file-checkbox:checked').each(function() {
            selected.push($(this).val());
        });
        return selected;
    }

    function deleteFirmwareFiles(filenames) {
        $('#confirmDeleteFirmware').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Deleting...');

        // Get CSRF token from meta tag or form
        var token = $("meta[name='_csrf']").attr("content");
        var header = $("meta[name='_csrf_header']").attr("content");

        // If meta tags don't exist, try to get from hidden input
        if (!token) {
            token = $("input[name='_csrf']").val();
        }

        var ajaxData = { filenames: filenames };
        var ajaxHeaders = {};

        // Add CSRF token to request
        if (token && header) {
            ajaxHeaders[header] = token;
        } else if (token) {
            ajaxData['_csrf'] = token;
        }

        $.ajax({
            url: '${ctxPath}/manager/chargepoints/firmware-files/delete',
            type: 'POST',
            data: ajaxData,
            headers: ajaxHeaders,
            success: function(response) {
                if (response.success) {
                    alert(response.message || 'Delete successful');
                    closeFirmwareDeleteModal();
                    // Refresh the page to update firmware dropdown
                    location.reload();
                } else {
                    alert('Delete failed: ' + (response.errorMessage || 'Unknown error'));
                }
            },
            error: function(xhr, status, error) {
                console.log('Delete error:', xhr.responseText);
                alert('Error deleting files: ' + error + ' (Status: ' + xhr.status + ')');
            },
            complete: function() {
                $('#confirmDeleteFirmware').prop('disabled', false).html('<i class="fa fa-trash"></i> Delete Selected');
            }
        });
    }

    function showDeleteError(message) {
        $('#firmwareDeleteLoading').hide();
        $('#firmwareDeleteContent').hide();
        $('#firmwareDeleteError').text(message).show();
    }


</script>

<!-- Firmware File Delete Modal -->
<div id="firmwareDeleteModal" class="modal" style="display: none;">
    <div class="modal-content" style="width: 80%; max-width: 800px;">
        <div class="modal-header">
            <span class="close" onclick="closeFirmwareDeleteModal()">&times;</span>
            <h2>Manage Firmware Files</h2>
        </div>
        <div class="modal-body">
            <div id="firmwareDeleteLoading" class="text-center" style="display: none;">
                <i class="fa fa-spinner fa-spin"></i> Loading firmware files...
            </div>
            <div id="firmwareDeleteContent" style="display: none;">
                <p>Select firmware files to delete. This will remove both the database record and the physical file.</p>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="selectAllFirmware"> Select All
                    </label>
                </div>
                <div class="table-responsive">
                    <table class="table table-striped table-sm">
                        <thead>
                            <tr>
                                <th width="50">Select</th>
                                <th>Filename</th>
                                <th>Type</th>
                                <th>Status</th>
                                <th>Upload Time</th>
                                <th>Size</th>
                            </tr>
                        </thead>
                        <tbody id="firmwareFilesList">
                            <!-- Files will be loaded here -->
                        </tbody>
                    </table>
                </div>
            </div>
            <div id="firmwareDeleteError" class="alert alert-danger" style="display: none;"></div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closeFirmwareDeleteModal()">Cancel</button>
            <button type="button" class="btn btn-danger" id="confirmDeleteFirmware" disabled>
                <i class="fa fa-trash"></i> Delete Selected
            </button>
        </div>
    </div>
</div>

<%@ include file="../00-footer.jsp" %>