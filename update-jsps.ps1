$files = @(
    "CancelReservation.jsp",
    "ClearCache.jsp",
    "ClearChargingProfile.jsp",
    "DataTransfer.jsp",
    "GetCompositeSchedule.jsp",
    "GetCompositeScheduleResponse.jsp",
    "GetConfiguration.jsp",
    "GetDiagnostics.jsp",
    "GetLocalListVersion.jsp",
    "RemoteStartTransaction.jsp",
    "RemoteStopTransaction.jsp",
    "ReserveNow.jsp",
    "Reset.jsp",
    "SendLocalList.jsp",
    "SetChargingProfile.jsp",
    "TriggerMessage.jsp",
    "UnlockConnector.jsp",
    "UpdateFirmware.jsp"
)

foreach ($file in $files) {
    $filePath = "D:\Git\EVSE_OMS\src\main\resources\webapp\WEB-INF\views\op16\$file"
    
    # 读取文件内容
    $content = Get-Content -Path $filePath -Raw
    
    # 使用正则表达式替换左侧菜单部分
    $newContent = $content -replace '<div class="left-menu">[\s\S]*?<\/div>\r?\n<div class="op16-content">', '<%@ include file="../snippets/ocppv16-subnav.jsp" %>\r\n<div>'
    
    # 写入文件
    Set-Content -Path $filePath -Value $newContent
    
    Write-Host "Updated $file"
}