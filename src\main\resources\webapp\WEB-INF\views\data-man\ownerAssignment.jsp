<%--
    SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
    Copyright (C) 2013-2025 SteVe Community Team
    All Rights Reserved.

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.
--%>
<%@ include file="../00-header.jsp" %>
<script type="text/javascript">
    $(document).ready(function() {
        var table = $(".res").stupidtable({
            "date": function (a, b) {
                var aDate = (parseInt(a) || 0);
                var bDate = (parseInt(b) || 0);
                return aDate - bDate;
            }
        });

        table.on("aftertablesort", function (event, data) {
            var th = $(this).find("th");
            th.find(".arrow").remove();
            var dir = $.fn.stupidtable.dir;

            // https://en.wikipedia.org/wiki/Geometric_Shapes
            var arrow = data.direction === dir.ASC ? "&#9650;" : "&#9660;";
            th.eq(data.column).append('<span class="arrow" style="float: right">' + arrow + '</span>');
        });
    });
</script>
<div class="content">
    <div class="content-container">
        <div class="page-title">
            <h2>Owner Assignments</h2>
        </div>
        
        <section><span>Charge Box Owner Assignments</span></section>

        <form action="${ctxPath}/manager/ownerAssignments/query" method="get">
            <table class="userInput">
                <tr>
                    <td>Owner Username:</td>
                    <td><input type="text" name="username" value="${params.username}"/></td>
                </tr>
                <tr>
                    <td></td>
                    <td id="add_space">
                        <input type="submit" value="Get">
                    </td>
                </tr>
            </table>
        </form>

        <br>

        <div class="table-responsive">
            <table class="res action">
                <thead>
                    <tr>
                        <th data-sort="int">User ID</th>
                        <th data-sort="string">Username</th>
                        <th data-sort="int">Assigned Charge Boxes</th>
                        <th></th>
                    </tr>
                </thead>
                <tbody>
                <c:forEach items="${owners}" var="owner">
                    <tr>
                        <td>${owner.userPk}</td>
                        <td>${owner.username}</td>
                        <td>${owner.chargeBoxCount}</td>
                        <td>
                            <form action="${ctxPath}/manager/ownerAssignments/details/${owner.userPk}" method="get">
                                <input type="submit" class="blueSubmit" value="Manage Assignments">
                            </form>
                        </td>
                    </tr>
                </c:forEach>
                </tbody>
            </table>
        </div>
    </div>
</div>
<%@ include file="../00-footer.jsp" %> 