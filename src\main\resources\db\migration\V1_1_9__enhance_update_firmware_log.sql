-- 增强固件更新日志表
ALTER TABLE `update_firmware_log` 
    ADD COLUMN `last_updated` TIMESTAMP NULL DEFAULT NULL COMMENT '最后更新时间' AFTER `sending_time`,
    ADD COLUMN `firmware_status` VARCHAR(50) NULL DEFAULT NULL COMMENT '固件升级状态' AFTER `status`,
    ADD COLUMN `batch_id` INT(11) NULL DEFAULT NULL COMMENT '批处理ID';

-- 创建固件升级批处理表
CREATE TABLE IF NOT EXISTS `update_firmware_batch` (
    `batch_id` INT(11) NOT NULL AUTO_INCREMENT,
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `total_count` INT(11) NOT NULL DEFAULT 0,
    `success_count` INT(11) NOT NULL DEFAULT 0,
    `pending_count` INT(11) NOT NULL DEFAULT 0,
    `error_count` INT(11) NOT NULL DEFAULT 0,
    `status` VARCHAR(50) NOT NULL DEFAULT 'INPROGRESS',
    `created_by` VARCHAR(255) NULL,
    PRIMARY KEY (`batch_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='固件升级批处理';

-- 添加外键关系
ALTER TABLE `update_firmware_log` 
    ADD CONSTRAINT `fk_update_firmware_log_batch`
    FOREIGN KEY (`batch_id`)
    REFERENCES `update_firmware_batch` (`batch_id`)
    ON DELETE SET NULL
    ON UPDATE NO ACTION; 