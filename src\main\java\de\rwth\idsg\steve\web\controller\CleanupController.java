/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.web.controller;

import de.rwth.idsg.steve.service.DiagnosticsCleanupService;
import de.rwth.idsg.steve.service.TaskStoreCleanupService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 清理服务控制器
 * 提供手动触发清理操作的API接口（用于测试和管理）
 * 
 * <AUTHOR> Community Team
 * @since 2025.01
 */
@Slf4j
@RestController
@RequestMapping("/manager/api/cleanup")
@PreAuthorize("hasRole('ADMIN')")
public class CleanupController {
    
    @Autowired
    private DiagnosticsCleanupService diagnosticsCleanupService;
    
    @Autowired
    private TaskStoreCleanupService taskStoreCleanupService;
    
    /**
     * 手动触发诊断任务清理
     */
    @PostMapping("/diagnostics")
    public ResponseEntity<Map<String, Object>> manualDiagnosticsCleanup() {
        log.info("🔧 Manual diagnostics cleanup requested via API");
        
        try {
            int cleanedTasks = diagnosticsCleanupService.manualCleanup();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("cleanedTasks", cleanedTasks);
            response.put("message", "Diagnostics cleanup completed successfully");
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("❌ Error during manual diagnostics cleanup: {}", e.getMessage(), e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("error", e.getMessage());
            response.put("message", "Diagnostics cleanup failed");
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 手动触发TaskStore清理
     */
    @PostMapping("/taskstore")
    public ResponseEntity<Map<String, Object>> manualTaskStoreCleanup() {
        log.info("🔧 Manual TaskStore cleanup requested via API");
        
        try {
            int cleanedTasks = taskStoreCleanupService.manualTaskStoreCleanup();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("cleanedTasks", cleanedTasks);
            response.put("message", "TaskStore cleanup completed successfully");
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("❌ Error during manual TaskStore cleanup: {}", e.getMessage(), e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("error", e.getMessage());
            response.put("message", "TaskStore cleanup failed");
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 获取清理服务状态
     */
    @PostMapping("/status")
    public ResponseEntity<Map<String, Object>> getCleanupStatus() {
        try {
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("diagnosticsStatus", diagnosticsCleanupService.getStatus());
            response.put("taskStoreStatus", taskStoreCleanupService.getStatus());
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("❌ Error getting cleanup status: {}", e.getMessage(), e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("error", e.getMessage());
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.internalServerError().body(response);
        }
    }
}
