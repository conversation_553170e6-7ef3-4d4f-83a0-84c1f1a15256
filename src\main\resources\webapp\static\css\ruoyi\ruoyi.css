/**
 * EVSE_OMS通用css样式
 * 基于RuoYi-Vue样式改造
 */

/** 基础通用 **/
.pt5 { padding-top: 5px; }
.pr5 { padding-right: 5px; }
.pb5 { padding-bottom: 5px; }
.mt5 { margin-top: 5px; }
.mr5 { margin-right: 5px; }
.mb5 { margin-bottom: 5px; }
.mb8 { margin-bottom: 8px; }
.ml5 { margin-left: 5px; }
.mt10 { margin-top: 10px; }
.mr10 { margin-right: 10px; }
.mb10 { margin-bottom: 10px; }
.ml10 { margin-left: 10px; }
.mt20 { margin-top: 20px; }
.mr20 { margin-right: 20px; }
.mb20 { margin-bottom: 20px; }
.ml20 { margin-left: 20px; }

.h1, .h2, .h3, .h4, .h5, .h6, h1, h2, h3, h4, h5, h6 {
  font-family: inherit;
  font-weight: 500;
  line-height: 1.1;
  color: inherit;
}

/** 表单布局 **/
.form-header {
  font-size: 15px;
  color: #409EFF;
  border-bottom: 1px solid #ddd;
  margin: 8px 10px 25px 10px;
  padding-bottom: 5px
}

/** 表格布局 **/
.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

/** 主题色应用 **/
.text-primary { color: #409EFF; }
.text-success { color: #67C23A; }
.text-info { color: #909399; }
.text-warning { color: #E6A23C; }
.text-danger { color: #F56C6C; }
.text-muted { color: #888888; }

/** 按钮样式 **/
.btn-primary {
  background-color: #409EFF;
  color: #fff;
  border: 1px solid #409EFF;
}
.btn-primary:hover {
  background-color: #66b1ff;
  border: 1px solid #66b1ff;
}
.btn-success {
  background-color: #67C23A;
  color: #fff;
  border: 1px solid #67C23A;
}
.btn-success:hover {
  background-color: #85ce61;
  border: 1px solid #85ce61;
}
.btn-warning {
  background-color: #E6A23C;
  color: #fff;
  border: 1px solid #E6A23C;
}
.btn-warning:hover {
  background-color: #ebb563;
  border: 1px solid #ebb563;
}
.btn-danger {
  background-color: #F56C6C;
  color: #fff;
  border: 1px solid #F56C6C;
}
.btn-danger:hover {
  background-color: #f78989;
  border: 1px solid #f78989;
}

/** 卡片样式 **/
.card-box {
  margin-bottom: 10px;
  background: #fff;
  border-radius: 2px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  padding: 15px;
}
.card-header {
  padding: 14px 15px 7px;
  font-weight: bold;
  font-size: 14px;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 10px;
}
.card-body {
  padding: 15px 20px 20px 20px;
}

/** 列表样式 **/
.list-group {
  padding-left: 0px;
  list-style: none;
}
.list-group-item {
  border-bottom: 1px solid #e7eaec;
  border-top: 1px solid #e7eaec;
  margin-bottom: -1px;
  padding: 11px 0px;
  font-size: 13px;
}
.list-group-striped > .list-group-item:nth-of-type(odd) {
  background-color: #f8f8f9;
}

/** 图片样式 **/
.img-circle {
  border-radius: 50%;
}
.img-lg {
  width: 120px;
  height: 120px;
}
.pull-right {
  float: right !important;
}