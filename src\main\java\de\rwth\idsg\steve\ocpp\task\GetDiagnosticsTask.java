/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.ocpp.task;

import de.rwth.idsg.steve.ocpp.CommunicationTask;
import de.rwth.idsg.steve.ocpp.OcppCallback;
import de.rwth.idsg.steve.web.dto.ocpp.GetDiagnosticsParams;
import lombok.extern.slf4j.Slf4j;

import jakarta.xml.ws.AsyncHandler;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import static de.rwth.idsg.steve.utils.DateTimeUtils.toDateTime;

/**
 * <AUTHOR> Goekay <<EMAIL>>
 * @since 09.03.2018
 */
@Slf4j
public class GetDiagnosticsTask extends CommunicationTask<GetDiagnosticsParams, String> {

    // 存储等待文件传输完成的充电桩
    private static final ConcurrentMap<String, GetDiagnosticsTask> pendingTasks = new ConcurrentHashMap<>();

    public GetDiagnosticsTask(GetDiagnosticsParams params) {
        super(params);
    }

    /**
     * 获取等待文件传输完成的任务
     */
    public static GetDiagnosticsTask getPendingTask(String chargeBoxId) {
        GetDiagnosticsTask task = pendingTasks.get(chargeBoxId);
        log.debug("🔍 Looking for pending task for {}: {}", chargeBoxId, task != null ? "found" : "not found");
        return task;
    }

    /**
     * 获取所有等待的任务（用于调试）
     */
    public static void logAllPendingTasks() {
        log.info("📋 Current pending GetDiagnostics tasks: {}", pendingTasks.size());
        for (String chargeBoxId : pendingTasks.keySet()) {
            log.info("  - {}", chargeBoxId);
        }
    }

    /**
     * 当文件传输完成时调用此方法来完成任务
     */
    public void completeTaskForChargeBox(String chargeBoxId, String fileName) {
        log.info("🎉 Completing GetDiagnostics task for {}: file ready for download: {}", chargeBoxId, fileName);

        // 调用addNewResponse来真正标记任务完成
        addNewResponse(chargeBoxId, fileName);

        // 调用回调通知其他组件
        success(chargeBoxId, fileName);

        // 从等待列表中移除
        pendingTasks.remove(chargeBoxId);

        log.info("✅ GetDiagnostics task completed for {}", chargeBoxId);
    }

    @Override
    public OcppCallback<String> defaultCallback() {
        return new GetDiagnosticsCallback();
    }

    /**
     * 特殊的回调类，不在收到OCPP响应时立即完成任务
     */
    private class GetDiagnosticsCallback extends DefaultOcppCallback<String> {
        @Override
        public void success(String chargeBoxId, String fileName) {
            log.info("📨 Received GetDiagnostics OCPP response from {}: fileName={}", chargeBoxId, fileName);

            // 不调用addNewResponse，避免任务被标记为完成
            // 而是将任务添加到等待列表中，等待文件传输完成
            pendingTasks.put(chargeBoxId, GetDiagnosticsTask.this);

            log.info("⏳ Task for {} is now waiting for file transmission to complete", chargeBoxId);
            logGetDiagnostics(chargeBoxId, "OCPP Response Received", fileName != null ? fileName : "No file name returned");
        }

        @Override
        public void failed(String chargeBoxId, Exception e) {
            log.error("❌ GetDiagnostics OCPP request failed for {}: {}", chargeBoxId, e.getMessage());
            addNewError(chargeBoxId, e.getMessage());
            logGetDiagnostics(chargeBoxId, "Failed", e.getMessage());
        }
    }

    @Override
    public ocpp.cp._2010._08.GetDiagnosticsRequest getOcpp12Request() {
        return new ocpp.cp._2010._08.GetDiagnosticsRequest()
                .withLocation(params.getLocation())
                .withRetries(params.getRetries())
                .withRetryInterval(params.getRetryInterval())
                .withStartTime(toDateTime(params.getStart()))
                .withStopTime(toDateTime(params.getStop()));
    }

    @Override
    public ocpp.cp._2012._06.GetDiagnosticsRequest getOcpp15Request() {
        return new ocpp.cp._2012._06.GetDiagnosticsRequest()
                .withLocation(params.getLocation())
                .withRetries(params.getRetries())
                .withRetryInterval(params.getRetryInterval())
                .withStartTime(toDateTime(params.getStart()))
                .withStopTime(toDateTime(params.getStop()));
    }

    @Override
    public ocpp.cp._2015._10.GetDiagnosticsRequest getOcpp16Request() {
        return new ocpp.cp._2015._10.GetDiagnosticsRequest()
                .withLocation(params.getLocation())
                .withRetries(params.getRetries())
                .withRetryInterval(params.getRetryInterval())
                .withStartTime(toDateTime(params.getStart()))
                .withStopTime(toDateTime(params.getStop()));
    }

    @Override
    public AsyncHandler<ocpp.cp._2010._08.GetDiagnosticsResponse> getOcpp12Handler(String chargeBoxId) {
        return res -> {
            try {
                String fileName = res.get().getFileName();
                log.info("Received GetDiagnostics response for OCPP 1.2 from {}: fileName={}", chargeBoxId, fileName);

                // 使用新的回调机制，不立即完成任务
                GetDiagnosticsCallback callback = new GetDiagnosticsCallback();
                callback.success(chargeBoxId, fileName);
            } catch (Exception e) {
                log.error("Error processing GetDiagnostics response for OCPP 1.2 from {}: {}", chargeBoxId, e.getMessage());
                failed(chargeBoxId, e);
                logGetDiagnostics(chargeBoxId, "Failed", e.getMessage());
            }
        };
    }

    @Override
    public AsyncHandler<ocpp.cp._2012._06.GetDiagnosticsResponse> getOcpp15Handler(String chargeBoxId) {
        return res -> {
            try {
                String fileName = res.get().getFileName();
                log.info("Received GetDiagnostics response for OCPP 1.5 from {}: fileName={}", chargeBoxId, fileName);

                // 使用新的回调机制，不立即完成任务
                GetDiagnosticsCallback callback = new GetDiagnosticsCallback();
                callback.success(chargeBoxId, fileName);
            } catch (Exception e) {
                log.error("Error processing GetDiagnostics response for OCPP 1.5 from {}: {}", chargeBoxId, e.getMessage());
                failed(chargeBoxId, e);
                logGetDiagnostics(chargeBoxId, "Failed", e.getMessage());
            }
        };
    }

    @Override
    public AsyncHandler<ocpp.cp._2015._10.GetDiagnosticsResponse> getOcpp16Handler(String chargeBoxId) {
        return res -> {
            try {
                String fileName = res.get().getFileName();
                log.info("Received GetDiagnostics response for OCPP 1.6 from {}: fileName={}", chargeBoxId, fileName);

                // 使用新的回调机制，不立即完成任务
                GetDiagnosticsCallback callback = new GetDiagnosticsCallback();
                callback.success(chargeBoxId, fileName);
            } catch (Exception e) {
                log.error("Error processing GetDiagnostics response for OCPP 1.6 from {}: {}", chargeBoxId, e.getMessage());
                failed(chargeBoxId, e);
                logGetDiagnostics(chargeBoxId, "Failed", e.getMessage());
            }
        };
    }

    /**
     * 记录GetDiagnostics操作日志
     */
    private void logGetDiagnostics(String chargeBoxId, String status, String response) {
        log.info("GetDiagnostics operation for chargeBoxId={}: status={}, response={}, location={}",
                chargeBoxId, status, response, params.getLocation());

        if ("OCPP Response Received".equals(status)) {
            log.info("📨 GetDiagnostics OCPP response received for {}: Diagnostic file '{}' will be uploaded to FTP server",
                    chargeBoxId, response);
            log.info("📁 Expected file location: ftp_files/{}", response);
            log.info("⏳ Please wait for the charge point to upload the diagnostic file...");
        } else if ("Success".equals(status)) {
            log.info("✅ GetDiagnostics SUCCESS for {}: Diagnostic file '{}' will be uploaded to FTP server",
                    chargeBoxId, response);
            log.info("📁 Expected file location: ftp_files/{}", response);
            log.info("⏳ Please wait for the charge point to upload the diagnostic file...");
        } else {
            log.error("❌ GetDiagnostics FAILED for {}: {}", chargeBoxId, response);
        }
    }
}
