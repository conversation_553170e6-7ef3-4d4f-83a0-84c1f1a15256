<%--

    St<PERSON><PERSON><PERSON> - SteckdosenVerwaltung - https://github.com/steve-community/steve
    Copyright (C) 2013-2025 SteVe Community Team
    All Rights Reserved.

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.

--%>
<%@ page contentType="text/html" pageEncoding="utf-8" language="java" trimDirectiveWhitespaces="true" %>
<%@ taglib uri="jakarta.tags.core" prefix="c" %>

<%@ include file="00-context.jsp" %>

<!DOCTYPE html>
<html>
<head>
	<link rel="icon" href="${ctxPath}/static/images/favicon.ico" type="image/x-icon" />
	<link rel="shortcut icon" href="${ctxPath}/static/images/favicon.ico" type="image/x-icon" />
	<link rel="stylesheet" type="text/css" href="${ctxPath}/static/css/style.css">
    <link rel="stylesheet" type="text/css" href="${ctxPath}/static/css/button-fix.css">
    <link rel="stylesheet" type="text/css" href="${ctxPath}/static/css/layout-fix.css">
    <script type="text/javascript" src="${ctxPath}/static/js/button-fix.js" ></script>
	<title>EVSE Management System</title>
    <style>
        body {
            background-color: #f0f2f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            padding: 0;
            font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .login-container {
            width: 400px;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
            padding: 30px;
            box-sizing: border-box;
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .login-logo {
            font-size: 24px;
            font-weight: bold;
            color: #1e1e2d;
            margin-bottom: 10px;
        }
        
        .login-title {
            font-size: 18px;
            color: #6c7293;
            margin-top: 0;
        }
        
        .login-form input[type="text"], 
        .login-form input[type="password"] {
            width: 100%;
            padding: 12px;
            margin-bottom: 20px;
            border: 1px solid #ebedf2;
            border-radius: 4px;
            box-sizing: border-box;
            transition: border-color 0.3s;
        }
        
        .login-form input[type="text"]:focus, 
        .login-form input[type="password"]:focus {
            border-color: #3699ff;
            outline: none;
        }
        
        .login-form input[type="submit"] {
            width: 100%;
            padding: 12px;
            background: #3699ff;
            color: #fff;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: background 0.3s;
        }
        
        .login-form input[type="submit"]:hover {
            background: #1e88e5;
        }
        
        .error-message {
            background-color: #f8d7da;
            color: #721c24;
            padding: 12px;
            margin-bottom: 20px;
            border-radius: 4px;
            text-align: center;
        }
        
        .login-form label {
            display: block;
            margin-bottom: 8px;
            color: #464e5f;
            font-weight: 500;
        }
        
        .top-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background-color: #1e1e2d;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            font-weight: bold;
            z-index: 100;
            box-shadow: 0 1px 4px rgba(0,0,0,.1);
        }
    </style>
</head>
<body>
<div class="top-header">
    EVSE Management System
</div>

<c:if test="${param.error != null}">
    <div class="login-container">
        <div class="login-header">
            <div class="login-logo">Sign In</div>
            <h2 class="login-title">Please enter your credentials</h2>
        </div>
        <div class="error-message">Your username or password is incorrect</div>
        <form class="login-form" method="POST" action="${ctxPath}/manager/signin">
            <div>
                <label for="username">Username</label>
                <input type="text" name="username" id="username" required />
            </div>
            <div>
                <label for="password">Password</label>
                <input type="password" name="password" id="password" required />
            </div>
            <div style="margin-top: 30px;">
                <input type="submit" value="Sign In">
            </div>
            <input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}"/>
        </form>
    </div>
</c:if>
<c:if test="${param.error == null}">
    <div class="login-container">
        <div class="login-header">
            <div class="login-logo">Sign In</div>
            <h2 class="login-title">Please enter your credentials</h2>
        </div>
        <form class="login-form" method="POST" action="${ctxPath}/manager/signin">
            <div>
                <label for="username">Username</label>
                <input type="text" name="username" id="username" required />
            </div>
            <div>
                <label for="password">Password</label>
                <input type="password" name="password" id="password" required />
            </div>
            <div style="margin-top: 30px;">
                <input type="submit" value="Sign In">
            </div>
            <input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}"/>
        </form>
    </div>
</c:if>
</body>
</html>
