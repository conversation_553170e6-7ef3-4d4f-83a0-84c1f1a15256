/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.service;

import de.rwth.idsg.steve.SteveConfiguration;
import de.rwth.idsg.steve.repository.FirmwareFileRepository;
import de.rwth.idsg.steve.repository.dto.FirmwareFile;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;

/**
 * 固件文件管理服务
 */
@Slf4j
@Service
public class FirmwareFileService {

    @Autowired
    private FirmwareFileRepository firmwareFileRepository;

    /**
     * Get all firmware filenames (for display in dropdown)
     * @return All firmware filenames with their status
     */
    public List<String> getAllFirmwareFilenames() {
        return firmwareFileRepository.getAllFirmwareFilenames();
    }

    /**
     * Get all firmware files with detailed information
     * @return All firmware files with status and metadata
     */
    public List<FirmwareFile> getAllFirmwareFiles() {
        return firmwareFileRepository.getAllFirmwareFiles();
    }

    /**
     * Get all successful firmware filenames (for backward compatibility)
     * @return Successfully installed firmware filenames
     */
    public List<String> getSuccessfulFirmwareFilenames() {
        return firmwareFileRepository.getSuccessfulFirmwareFilenames();
    }

    /**
     * 获取所有上传成功的固件文件详细信息
     * @return 成功上传的固件文件列表
     */
    public List<FirmwareFile> getSuccessfulFirmwareFiles() {
        return firmwareFileRepository.getSuccessfulFirmwareFiles();
    }

    /**
     * Record pending firmware file upload (file uploaded, waiting for installation confirmation)
     * @param filename File name
     * @param fileSize File size in bytes
     * @param userPk User ID who uploaded the file
     * @return Record ID
     */
    @Transactional
    public Integer recordPendingUpload(String filename, Long fileSize, Integer userPk) {
        FirmwareFile.FileType fileType = FirmwareFile.getFileTypeFromFilename(filename);

        FirmwareFile firmwareFile = FirmwareFile.builder()
                .filename(filename)
                .fileType(fileType)
                .uploadTime(DateTime.now())
                .uploadStatus(FirmwareFile.UploadStatus.PENDING)
                .uploadedByUserPk(userPk)
                .fileSize(fileSize)
                .description("Uploaded via Web interface, awaiting installation confirmation")
                .build();

        Integer recordId = firmwareFileRepository.addFirmwareFile(firmwareFile);
        log.info("Recorded pending firmware file upload: {} (ID: {}, size: {} bytes, user: {})",
                filename, recordId, fileSize, userPk);

        return recordId;
    }

    /**
     * Record successful firmware file installation
     * @param filename File name
     * @param fileSize File size in bytes
     * @param userPk User ID who uploaded the file
     * @return Record ID
     */
    @Transactional
    public Integer recordSuccessfulUpload(String filename, Long fileSize, Integer userPk) {
        FirmwareFile.FileType fileType = FirmwareFile.getFileTypeFromFilename(filename);
        
        FirmwareFile firmwareFile = FirmwareFile.builder()
                .filename(filename)
                .fileType(fileType)
                .uploadTime(DateTime.now())
                .uploadStatus(FirmwareFile.UploadStatus.SUCCESS)
                .uploadedByUserPk(userPk)
                .fileSize(fileSize)
                .description("Uploaded via Web interface")
                .build();

        Integer recordId = firmwareFileRepository.addFirmwareFile(firmwareFile);
        log.info("Recorded successful firmware file upload: {} (ID: {}, size: {} bytes, user: {})",
                filename, recordId, fileSize, userPk);
        
        return recordId;
    }

    /**
     * Update firmware file status from PENDING to SUCCESS when installation is confirmed
     * @param filename File name
     * @return true if update was successful, false otherwise
     */
    @Transactional
    public boolean markFirmwareAsInstalled(String filename) {
        try {
            int updated = firmwareFileRepository.updateUploadStatus(filename,
                    FirmwareFile.UploadStatus.PENDING,
                    FirmwareFile.UploadStatus.SUCCESS,
                    "Firmware installation confirmed by charge point");

            if (updated > 0) {
                log.info("Successfully marked firmware as installed: {}", filename);
                return true;
            } else {
                log.warn("No pending firmware record found to mark as installed: {}", filename);
                return false;
            }
        } catch (Exception e) {
            log.error("Failed to mark firmware as installed: " + filename, e);
            return false;
        }
    }

    /**
     * Update firmware file status from PENDING to FAILED when installation fails
     * @param filename File name
     * @param errorMessage Error message
     * @return true if update was successful, false otherwise
     */
    @Transactional
    public boolean markFirmwareAsFailed(String filename, String errorMessage) {
        try {
            int updated = firmwareFileRepository.updateUploadStatus(filename,
                    FirmwareFile.UploadStatus.PENDING,
                    FirmwareFile.UploadStatus.FAILED,
                    "Firmware installation failed: " + errorMessage);

            if (updated > 0) {
                log.info("Successfully marked firmware as failed: {} ({})", filename, errorMessage);
                return true;
            } else {
                log.warn("No pending firmware record found to mark as failed: {}", filename);
                return false;
            }
        } catch (Exception e) {
            log.error("Failed to mark firmware as failed: " + filename, e);
            return false;
        }
    }

    /**
     * Delete firmware file (both database record and physical file)
     * @param filename File name to delete
     * @return true if deletion was successful, false otherwise
     */
    @Transactional
    public boolean deleteFirmwareFile(String filename) {
        try {
            // First delete from database
            int deletedRecords = firmwareFileRepository.deleteFirmwareFile(filename);

            if (deletedRecords > 0) {
                // Then cleanup physical file
                cleanupPhysicalFile(filename);
                log.info("Successfully deleted firmware file: {} ({} database records)", filename, deletedRecords);
                return true;
            } else {
                log.warn("No database records found for firmware file: {}", filename);
                return false;
            }
        } catch (Exception e) {
            log.error("Failed to delete firmware file: " + filename, e);
            return false;
        }
    }

    /**
     * Record failed firmware file upload and cleanup physical file
     * @param filename File name
     * @param userPk User ID who attempted the upload
     * @param errorMessage Error message
     */
    @Transactional
    public void recordFailedUpload(String filename, Integer userPk, String errorMessage) {
        // 清理物理文件
        cleanupPhysicalFile(filename);
        
        // 可选：记录失败的上传尝试（用于审计）
        FirmwareFile.FileType fileType = FirmwareFile.getFileTypeFromFilename(filename);
        
        FirmwareFile firmwareFile = FirmwareFile.builder()
                .filename(filename)
                .fileType(fileType)
                .uploadTime(DateTime.now())
                .uploadStatus(FirmwareFile.UploadStatus.FAILED)
                .uploadedByUserPk(userPk)
                .description("Upload failed: " + errorMessage)
                .build();

        firmwareFileRepository.addFirmwareFile(firmwareFile);
        log.warn("Recorded failed firmware file upload: {} (user: {}, error: {})", filename, userPk, errorMessage);
    }

    /**
     * 检查文件名是否已存在
     * @param filename 文件名
     * @return 如果存在返回true
     */
    public boolean existsByFilename(String filename) {
        return firmwareFileRepository.existsByFilename(filename);
    }

    /**
     * 根据文件名获取固件文件信息
     * @param filename 文件名
     * @return 固件文件信息，如果不存在返回null
     */
    public FirmwareFile getFirmwareFileByFilename(String filename) {
        return firmwareFileRepository.getFirmwareFileByFilename(filename);
    }

    /**
     * 清理物理文件
     * @param filename 文件名
     * @return 是否删除成功
     */
    private boolean cleanupPhysicalFile(String filename) {
        try {
            String firmwarePath = SteveConfiguration.CONFIG.getPhysicalFirmwareUploadPath();
            Path filePath = Paths.get(firmwarePath, filename);
            File file = filePath.toFile();

            if (file.exists()) {
                boolean deleted = file.delete();
                if (deleted) {
                    log.info("Successfully deleted physical file: {}", filePath);
                } else {
                    log.warn("Failed to delete physical file: {}", filePath);
                }
                return deleted;
            } else {
                log.info("Physical file does not exist, no need to delete: {}", filePath);
                return true;
            }

        } catch (Exception e) {
            log.error("Error occurred while cleaning up physical file: " + filename, e);
            return false;
        }
    }

    /**
     * 创建测试数据 - 仅用于测试目的
     */
    @Transactional
    public void createTestData() {
        try {
            // 创建一些测试固件文件记录
            FirmwareFile file1 = FirmwareFile.builder()
                .filename("test_firmware_v1.0.bin")
                .fileType(FirmwareFile.FileType.FIRMWARE)
                .uploadStatus(FirmwareFile.UploadStatus.SUCCESS)
                .fileSize(1024000L)
                .description("Test firmware v1.0 - Successfully installed")
                .build();
            firmwareFileRepository.addFirmwareFile(file1);

            FirmwareFile file2 = FirmwareFile.builder()
                .filename("test_firmware_v2.0.bin")
                .fileType(FirmwareFile.FileType.FIRMWARE)
                .uploadStatus(FirmwareFile.UploadStatus.PENDING)
                .fileSize(2048000L)
                .description("Test firmware v2.0 - Pending installation")
                .build();
            firmwareFileRepository.addFirmwareFile(file2);

            FirmwareFile file3 = FirmwareFile.builder()
                .filename("test_app_v1.0.apk")
                .fileType(FirmwareFile.FileType.APPLICATION)
                .uploadStatus(FirmwareFile.UploadStatus.FAILED)
                .fileSize(5120000L)
                .description("Test application v1.0 - Installation failed")
                .build();
            firmwareFileRepository.addFirmwareFile(file3);

            log.info("Test firmware file data created successfully");
        } catch (Exception e) {
            log.error("Failed to create test data", e);
        }
    }

    /**
     * 同步物理目录和数据库记录
     * 这个方法可以用于修复数据不一致的情况
     */
    @Transactional
    public void syncPhysicalFilesWithDatabase() {
        try {
            String firmwarePath = SteveConfiguration.CONFIG.getPhysicalFirmwareUploadPath();
            File firmwareDir = new File(firmwarePath);
            
            if (!firmwareDir.exists() || !firmwareDir.isDirectory()) {
                log.warn("Firmware directory does not exist or is not a directory: {}", firmwarePath);
                return;
            }
            
            File[] files = firmwareDir.listFiles();
            if (files == null) {
                return;
            }
            
            for (File file : files) {
                if (file.isFile()) {
                    String filename = file.getName();
                    if (!existsByFilename(filename)) {
                        log.info("Found unrecorded physical file, deleting: {}", filename);
                        file.delete();
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("Error occurred while syncing physical files with database", e);
        }
    }
}
