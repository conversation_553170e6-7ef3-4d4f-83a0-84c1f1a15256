-- 创建固件更新日志表
CREATE TABLE IF NOT EXISTS `update_firmware_log` (
    `log_id` INT(11) NOT NULL AUTO_INCREMENT,
    `location` VARCHAR(255) NOT NULL COMMENT '固件位置URI',
    `retries` INT(11) NULL DEFAULT NULL COMMENT '重试次数',
    `retry_interval` INT(11) NULL DEFAULT NULL COMMENT '重试间隔',
    `retrieve_datetime` DATETIME NOT NULL COMMENT '检索日期/时间',
    `sending_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发送时间',
    `charge_box_id` VARCHAR(255) NOT NULL COMMENT '充电桩ID',
    `status` VARCHAR(50) NULL DEFAULT NULL COMMENT '状态信息',
    `response` VARCHAR(255) NULL DEFAULT NULL COMMENT '响应内容',
    PRIMARY KEY (`log_id`),
    INDEX `update_firmware_log_charge_box_id_idx` (`charge_box_id`),
    INDEX `update_firmware_log_sending_time_idx` (`sending_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='固件更新日志';

-- 添加与充电桩表的外键关系
ALTER TABLE `update_firmware_log` 
ADD CONSTRAINT `fk_update_firmware_log_charge_box`
  FOREIGN KEY (`charge_box_id`)
  REFERENCES `charge_box` (`charge_box_id`)
  ON DELETE CASCADE
  ON UPDATE NO ACTION; 