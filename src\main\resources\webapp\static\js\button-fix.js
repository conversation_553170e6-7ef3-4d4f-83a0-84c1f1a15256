/**
 * 按钮样式修复脚本
 * 在页面加载时自动应用样式到所有按钮元素
 */
document.addEventListener('DOMContentLoaded', function() {
    // 为所有的提交按钮添加btn类
    var submitButtons = document.querySelectorAll('input[type="submit"]');
    submitButtons.forEach(function(button) {
        if (!button.className.includes('btn')) {
            // 根据按钮的现有类添加对应的样式
            if (button.className.includes('blueSubmit')) {
                button.classList.add('btn', 'btn-blue');
            } else if (button.className.includes('redSubmit')) {
                button.classList.add('btn', 'btn-red');
            } else {
                button.classList.add('btn', 'btn-primary');
            }
        }
    });

    // 为所有的普通按钮添加btn类
    var normalButtons = document.querySelectorAll('input[type="button"]');
    normalButtons.forEach(function(button) {
        if (!button.className.includes('btn')) {
            button.classList.add('btn', 'btn-standard');
        }
    });

    // 修复表格中的按钮宽度问题
    var tableButtons = document.querySelectorAll('table input[type="button"], table input[type="submit"]');
    tableButtons.forEach(function(button) {
        button.style.width = 'auto';
        button.style.minWidth = '80px';
        button.style.overflow = 'visible';
        button.style.whiteSpace = 'normal';
    });
}); 