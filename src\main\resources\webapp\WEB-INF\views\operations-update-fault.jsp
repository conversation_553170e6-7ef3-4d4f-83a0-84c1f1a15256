<%--
    SteV<PERSON> - SteckdosenVerwaltung - https://github.com/steve-community/steve
    Copyright (C) 2013-2025 SteVe Community Team
    All Rights Reserved.

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.
--%>
<%@ include file="00-header.jsp" %>
<%@ include file="00-op-bind-errors.jsp" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>

<style>
    .update-form-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0 12px;
    }
    
    .update-form-table td {
        padding: 8px;
        vertical-align: top;
    }
    
    .update-form-table td:first-child {
        width: 180px;
        text-align: right;
        padding-right: 15px;
        font-weight: bold;
    }
    
    .update-form-table select,
    .update-form-table textarea {
        width: 90%;
        padding: 8px;
        border-radius: 4px;
        border: 1px solid #ccc;
    }
    
    .update-form-table textarea {
        min-height: 120px;
    }
    
    .form-buttons {
        margin-top: 20px;
    }
    
    .form-buttons input[type="submit"] {
        margin-right: 10px;
        padding: 8px 16px;
        background-color: #337ab7;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    }
    
    .btn {
        display: inline-block;
        padding: 6px 12px;
        margin-bottom: 0;
        font-weight: 400;
        text-align: center;
        white-space: nowrap;
        vertical-align: middle;
        touch-action: manipulation;
        cursor: pointer;
        background-image: none;
        border: 1px solid transparent;
        border-radius: 4px;
        text-decoration: none;
        color: #333;
        background-color: #fff;
        border-color: #ccc;
    }
    
    .info-section {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
    }
    
    .info-table {
        width: 100%;
    }
    
    .info-table td {
        padding: 8px;
    }
    
    .info-table td:first-child {
        width: 180px;
        font-weight: bold;
    }
    
    .status-new {
        color: #d9534f;
        font-weight: bold;
    }
    
    .status-in-progress {
        color: #f0ad4e;
        font-weight: bold;
    }
    
    .status-resolved {
        color: #5cb85c;
        font-weight: bold;
    }
    
    .note-help-text {
        color: #777;
        font-size: 0.9em;
        margin-top: 5px;
    }
    
    .required-note {
        color: #d9534f;
        display: none;
    }
</style>

<div class="content">
    <div>
        <section><span>Update Fault Status</span></section>
        
        <div class="info-section">
            <table class="info-table">
                <tr>
                    <td>Fault ID:</td>
                    <td>${issue.issueId}</td>
                </tr>
                <tr>
                    <td>Charge Point:</td>
                    <td>${issue.chargeBoxId}</td>
                </tr>
                <tr>
                    <td>Report Time:</td>
                    <td><fmt:formatDate value="${issue.reportTime}" pattern="yyyy-MM-dd HH:mm:ss" /></td>
                </tr>
                <tr>
                    <td>Fault Description:</td>
                    <td>${issue.faultDescription}</td>
                </tr>
                <tr>
                    <td>Error Code:</td>
                    <td>${issue.ocppErrorCode}</td>
                </tr>
                <tr>
                    <td>Report Type:</td>
                    <td>${issue.isAutoReported ? 'Automatic' : 'Manual'}</td>
                </tr>
                <tr>
                    <td>Reporter:</td>
                    <td>${issue.reporterUsername}</td>
                </tr>
                <tr>
                    <td>Current Status:</td>
                    <td>
                        <c:choose>
                            <c:when test="${issue.status eq 'NEW'}"><span class="status-new">New</span></c:when>
                            <c:when test="${issue.status eq 'IN_PROGRESS'}"><span class="status-in-progress">In Progress</span></c:when>
                            <c:when test="${issue.status eq 'RESOLVED'}"><span class="status-resolved">Resolved</span></c:when>
                            <c:otherwise>${issue.status}</c:otherwise>
                        </c:choose>
                    </td>
                </tr>
            </table>
        </div>
        
        <div class="info-section">
            <h3>Fault Images</h3>
            <div class="image-gallery">
                <c:if test="${empty issue.imagePaths}">
                    <p>No uploaded images</p>
                </c:if>
                <c:if test="${not empty issue.imagePaths}">
                    <div style="display: flex; flex-wrap: wrap; gap: 10px;">
                        <c:forEach items="${issue.imagePaths}" var="imagePath">
                            <div style="margin: 5px; text-align: center;">
                                <c:choose>
                                    <%-- 处理完整路径格式，如 uploads/issue-images/filename --%>
                                    <c:when test="${fn:startsWith(imagePath, 'uploads/')}">
                                        <a href="${ctxPath}/static/${imagePath}" target="_blank">
                                            <img src="${ctxPath}/static/${imagePath}" 
                                                alt="Fault Image" style="max-width: 200px; max-height: 150px; border: 1px solid #ddd; padding: 3px;" />
                                        </a>
                                    </c:when>
                                    <%-- 处理以/开头的路径格式，如 /uploads/issues/... --%>
                                    <c:when test="${fn:startsWith(imagePath, '/')}">
                                        <a href="${ctxPath}/static${imagePath}" target="_blank">
                                            <img src="${ctxPath}/static${imagePath}" 
                                                alt="Fault Image" style="max-width: 200px; max-height: 150px; border: 1px solid #ddd; padding: 3px;" />
                                        </a>
                                    </c:when>
                                    <%-- 处理其他格式（默认是纯文件名） --%>
                                    <c:otherwise>
                                        <a href="${ctxPath}/static/images/${imagePath}" target="_blank">
                                            <img src="${ctxPath}/static/images/${imagePath}" 
                                                alt="Fault Image" style="max-width: 200px; max-height: 150px; border: 1px solid #ddd; padding: 3px;" />
                                        </a>
                                    </c:otherwise>
                                </c:choose>
                            </div>
                        </c:forEach>
                    </div>
                </c:if>
            </div>
        </div>
        
        <!-- 添加维护记录显示区域 -->
        <div class="info-section">
            <h3>Maintenance Records</h3>
            <c:if test="${empty maintenanceRecords}">
                <p>No maintenance records</p>
            </c:if>
            <c:if test="${not empty maintenanceRecords}">
                <style>
                    /* 自定义维护记录表格样式 */
                    table.maintenance-records {
                        border-collapse: collapse;
                        width: 100%;
                        margin-bottom: 15px;
                    }
                    table.maintenance-records th {
                        text-align: center;
                        padding: 8px;
                        background: #fff;
                        border-bottom: double #CCC;
                    }
                    table.maintenance-records tr { 
                        border-bottom: 1px solid #CCC; 
                        height: 60px; /* 增大每行高度为原来的三倍 */
                    }
                    table.maintenance-records tr:nth-child(odd){ background: #fcfaf2; }
                    table.maintenance-records tr:nth-child(even):hover, 
                    table.maintenance-records tr:nth-child(odd):hover { background: #ebf4f9; }
                    table.maintenance-records td {
                        padding: 8px;
                        text-align: center;
                        vertical-align: middle;
                        word-wrap: break-word;
                    }
                    /* 修改各列宽度比例 */
                    table.maintenance-records th:nth-child(1), 
                    table.maintenance-records td:nth-child(1) {
                        width: 10%; /* 减少ID列宽度 */
                    }
                    table.maintenance-records th:nth-child(2), 
                    table.maintenance-records td:nth-child(2) {
                        width: 20%; /* 减少Maintenance Time列宽度 */
                    }
                    table.maintenance-records th:nth-child(3), 
                    table.maintenance-records td:nth-child(3) {
                        width: 15%; /* 减少Maintainer列宽度 */
                    }
                    table.maintenance-records th:nth-child(4), 
                    table.maintenance-records td:nth-child(4) {
                        width: 55%; /* 增加Maintenance Content列宽度 */
                        text-align: left; /* 内容左对齐 */
                    }
                    /* 分页样式 */
                    .pagination {
                        display: flex;
                        justify-content: center;
                        margin-top: 15px;
                    }
                    .pagination a {
                        padding: 5px 10px;
                        margin: 0 5px;
                        border: 1px solid #ccc;
                        text-decoration: none;
                        color: #333;
                    }
                    .pagination a.active {
                        background-color: #337ab7;
                        color: white;
                        border-color: #337ab7;
                    }
                    .pagination a:hover:not(.active) {
                        background-color: #ddd;
                    }
                </style>
                
                <table class="maintenance-records">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Maintenance Time</th>
                            <th>Maintainer</th>
                            <th>Maintenance Content</th>
                        </tr>
                    </thead>
                    <tbody id="maintenanceTableBody">
                        <c:set var="recordsPerPage" value="5" />
                        <c:forEach items="${maintenanceRecords}" var="record" varStatus="status">
                            <c:set var="pageNum" value="${(status.index - (status.index % recordsPerPage)) / recordsPerPage + 1}" />
                            <tr class="record-row" data-page="${pageNum}">
                                <td><c:out value="${record.recordId}" /></td>
                                <td>
                                    <fmt:formatDate value="${record.maintenanceTime}" pattern="yyyy-MM-dd HH:mm:ss" />
                                </td>
                                <td><c:out value="${record.maintainerUsername}" /></td>
                                <td><c:out value="${record.maintenanceDescription}" /></td>
                            </tr>
                        </c:forEach>
                    </tbody>
                </table>
                
                <!-- 添加分页控件 -->
                <div id="paginationContainer" class="pagination">
                    <!-- 分页链接将通过JavaScript动态生成 -->
                </div>
                
                <script>
                    // 在文档加载完成后执行
                    $(document).ready(function() {
                        // 确保jQuery正确加载
                        console.log("Document ready, initializing pagination");
                        
                        // 首先隐藏所有行
                        $('.record-row').hide();
                        
                        // 初始化分页
                        initPagination();
                        
                        // 显示第一页
                        showPage(1);
                    });
                    
                    // 初始化分页函数
                    function initPagination() {
                        console.log("Initializing pagination");
                        // 获取实际行数
                        var totalRecords = $('.record-row').length;
                        console.log("Total records found: " + totalRecords);
                        
                        // 每页显示5条记录
                        var recordsPerPage = 5;
                        var totalPages = Math.ceil(totalRecords / recordsPerPage);
                        console.log("Total pages: " + totalPages);
                        
                        // 生成分页HTML
                        var paginationHtml = '';
                        
                        // 只有当总页数大于1时才显示分页
                        if (totalPages > 1) {
                            for (var i = 1; i <= totalPages; i++) {
                                paginationHtml += '<a href="javascript:void(0)" onclick="showPage(' + i + ')" ' + 
                                                 (i === 1 ? 'class="active"' : '') + '>' + i + '</a>';
                            }
                            $('#paginationContainer').html(paginationHtml);
                        } else {
                            // 如果只有一页，隐藏分页容器
                            $('#paginationContainer').hide();
                        }
                        
                        // 手动设置每行的data-page属性
                        $('.record-row').each(function(index) {
                            var pageNum = Math.floor(index / recordsPerPage) + 1;
                            $(this).attr('data-page', pageNum);
                            console.log("Set row " + index + " to page " + pageNum);
                        });
                    }
                    
                    // 显示指定页码的记录
                    function showPage(pageNum) {
                        console.log("Showing page: " + pageNum);
                        
                        // 隐藏所有行
                        $('.record-row').hide();
                        
                        // 显示当前页的行
                        $('.record-row[data-page="' + pageNum + '"]').show();
                        
                        // 更新活动页码样式
                        $('.pagination a').removeClass('active');
                        $('.pagination a:nth-child(' + pageNum + ')').addClass('active');
                    }
                </script>
            </c:if>
        </div>
        
        <form:form action="${ctxPath}/manager/operations/update-fault/${issue.issueId}" method="post" 
                  modelAttribute="updateForm" id="updateForm">
            
            <table class="update-form-table">
                <tr>
                    <td></td>
                    <td>
                        <form:hidden path="issueId" value="${issue.issueId}" />
                    </td>
                </tr>
                <tr>
                    <td><form:label path="status">Update Status: <span class="required-field">*</span></form:label></td>
                    <td>
                        <form:select path="status" id="statusSelect">
                            <c:forEach items="${statuses}" var="status">
                                <form:option value="${status}" label="${status eq 'NEW' ? 'New' : status eq 'IN_PROGRESS' ? 'In Progress' : status eq 'RESOLVED' ? 'Resolved' : status.value()}" />
                            </c:forEach>
                        </form:select>
                        <form:errors path="status" cssClass="error" />
                    </td>
                </tr>
                <tr>
                    <td>
                        <form:label path="note" id="noteLabel">Maintenance Note:</form:label>
                        <span class="required-field required-note" id="noteRequired">*</span>
                    </td>
                    <td>
                        <form:textarea path="note" rows="5" cols="50" id="noteTextarea"
                                     placeholder="Please enter the maintenance process, results or other notes" />
                        <div class="note-help-text" id="noteHelpText">
                            Please describe the maintenance process and results
                        </div>
                        <div class="note-help-text required-note" id="resolvedNoteHelpText">
                            <strong>When status is "Resolved", you must provide detailed solution information</strong>
                        </div>
                        <form:errors path="note" cssClass="error" />
                    </td>
                </tr>
                <tr>
                    <td></td>
                    <td class="form-buttons">
                        <input type="submit" value="Update Status" id="submitButton">
                        <a href="${ctxPath}/manager/operations/faults" class="btn">Return to List</a>
                    </td>
                </tr>
            </table>
        </form:form>
    </div>
</div>

<script>
    $(document).ready(function() {
        // Check form status and set whether the note field is required
        function checkStatus() {
            var status = $('#statusSelect').val();
            var noteTextarea = $('#noteTextarea');
            
            if (status === 'RESOLVED') {
                // If the status is "Resolved", show the required mark and help text
                $('#noteRequired').show();
                $('#resolvedNoteHelpText').show();
                noteTextarea.attr('required', 'required');
            } else {
                // If the status is not "Resolved", hide the required mark and help text
                $('#noteRequired').hide();
                $('#resolvedNoteHelpText').hide();
                noteTextarea.removeAttr('required');
            }
        }
        
        // Check initial status on page load
        checkStatus();
        
        // Check when status changes
        $('#statusSelect').change(function() {
            checkStatus();
            // 不要自动填充维护记录，保留用户当前输入的内容
        });
        
        // Validate before form submission
        $('#updateForm').submit(function(e) {
            var status = $('#statusSelect').val();
            var noteValue = $('#noteTextarea').val().trim();
            
            if (status === 'RESOLVED' && noteValue === '') {
                alert('When status is "Resolved", you must provide maintenance notes!');
                e.preventDefault();
                $('#noteTextarea').focus();
                return false;
            }
            
            return true;
        });

        // 清空默认带有"Status updated to:"开头的文本
        var noteTextarea = $('#noteTextarea');
        var currentText = noteTextarea.val();
        if (currentText && currentText.trim().startsWith('Status updated to:')) {
            noteTextarea.val('');
        }
    });
</script>

<%@ include file="00-footer.jsp" %> 