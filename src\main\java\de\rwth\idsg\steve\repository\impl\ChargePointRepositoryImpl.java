/*
 * SteV<PERSON> - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.repository.impl;

import de.rwth.idsg.steve.SteveException;
import de.rwth.idsg.steve.ocpp.OcppProtocol;
import de.rwth.idsg.steve.repository.AddressRepository;
import de.rwth.idsg.steve.repository.ChargePointRepository;
import de.rwth.idsg.steve.repository.ChargingSuccessRepository;
import de.rwth.idsg.steve.repository.dto.ChargePoint;
import de.rwth.idsg.steve.repository.dto.ChargePointSelect;
import de.rwth.idsg.steve.repository.dto.ChargingSuccessStats;
import de.rwth.idsg.steve.repository.dto.ConnectorStatus;
import de.rwth.idsg.steve.utils.DateTimeUtils;
import de.rwth.idsg.steve.web.dto.ChargePointForm;
import de.rwth.idsg.steve.web.dto.ChargePointQueryForm;
import de.rwth.idsg.steve.web.dto.ConnectorStatusForm;
import de.rwth.idsg.steve.web.dto.UserRole;
import jooq.steve.db.tables.records.AddressRecord;
import jooq.steve.db.tables.records.ChargeBoxRecord;
import lombok.extern.slf4j.Slf4j;
import ocpp.cs._2015._10.RegistrationStatus;
import org.joda.time.DateTime;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record5;
import org.jooq.Result;
import org.jooq.SelectConditionStep;
import org.jooq.SelectQuery;
import org.jooq.Table;
import org.jooq.exception.DataAccessException;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.concurrent.CompletableFuture;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static de.rwth.idsg.steve.utils.CustomDSL.date;
import static de.rwth.idsg.steve.utils.CustomDSL.includes;
import static jooq.steve.db.tables.ChargeBox.CHARGE_BOX;
import static jooq.steve.db.tables.Connector.CONNECTOR;
import static jooq.steve.db.tables.ConnectorStatus.CONNECTOR_STATUS;
import static jooq.steve.db.tables.ViewOwnerChargeBoxes.VIEW_OWNER_CHARGE_BOXES;
import static jooq.steve.db.tables.User.USER;
import static jooq.steve.db.tables.WebUser.WEB_USER;
import static jooq.steve.db.tables.UserChargeBox.USER_CHARGE_BOX;
import static jooq.steve.db.tables.ViewChargeBoxWithOwner.VIEW_CHARGE_BOX_WITH_OWNER;

/**
 * <AUTHOR> Goekay <<EMAIL>>
 * @since 14.08.2014
 */
@Slf4j
@Repository
public class ChargePointRepositoryImpl implements ChargePointRepository {

    private final DSLContext ctx;
    private final AddressRepository addressRepository;

    @Autowired
    private ChargingSuccessRepository chargingSuccessRepository;

    @Autowired
    public ChargePointRepositoryImpl(DSLContext ctx, AddressRepository addressRepository) {
        this.ctx = ctx;
        this.addressRepository = addressRepository;
    }

    @Override
    public Optional<String> getRegistrationStatus(String chargeBoxId) {
        String status = ctx.select(CHARGE_BOX.REGISTRATION_STATUS)
                           .from(CHARGE_BOX)
                           .where(CHARGE_BOX.CHARGE_BOX_ID.eq(chargeBoxId))
                           .fetchOne(CHARGE_BOX.REGISTRATION_STATUS);

        return Optional.ofNullable(status);
    }

    @Override
    public List<ChargePointSelect> getChargePointSelect(OcppProtocol ocppProtocol, List<String> inStatusFilter, List<String> chargeBoxIdFilter) {
        if (ocppProtocol == null) {
            return new ArrayList<>();
        }

        Condition condition = CHARGE_BOX.OCPP_PROTOCOL.eq(ocppProtocol.getCompositeValue());
        
        if (!CollectionUtils.isEmpty(inStatusFilter)) {
            condition = condition.and(CHARGE_BOX.REGISTRATION_STATUS.in(inStatusFilter));
        }

        if (!CollectionUtils.isEmpty(chargeBoxIdFilter)) {
            condition = condition.and(CHARGE_BOX.CHARGE_BOX_ID.in(chargeBoxIdFilter));
        }

        return ctx.select(CHARGE_BOX.CHARGE_BOX_ID, CHARGE_BOX.ENDPOINT_ADDRESS)
                    .from(CHARGE_BOX)
                    .where(condition)
                    .fetch()
                    .map(r -> new ChargePointSelect(ocppProtocol, r.value1(), r.value2()));
    }

    @Override
    public List<String> getChargeBoxIds() {
        return ctx.select(CHARGE_BOX.CHARGE_BOX_ID)
                  .from(CHARGE_BOX)
                  .fetch(CHARGE_BOX.CHARGE_BOX_ID);
    }

    @Override
    public Map<String, Integer> getChargeBoxIdPkPair(List<String> chargeBoxIdList) {
        return ctx.select(CHARGE_BOX.CHARGE_BOX_ID, CHARGE_BOX.CHARGE_BOX_PK)
                  .from(CHARGE_BOX)
                  .where(CHARGE_BOX.CHARGE_BOX_ID.in(chargeBoxIdList))
                  .fetchMap(CHARGE_BOX.CHARGE_BOX_ID, CHARGE_BOX.CHARGE_BOX_PK);
    }

    @Override
    public List<ChargePoint.Overview> getOverview(ChargePointQueryForm form) {
        try {
            log.debug("Getting charge point overview (non-paged) with form: {}", form);
            Page<ChargePoint.Overview> page = getOverview(form, Pageable.unpaged()); // Or a PageRequest.of(0, Integer.MAX_VALUE) if unpaged is not desired
            return page.getContent();
        } catch (Exception e) {
            log.error("Error in non-paged getOverview: {}", e.getMessage(), e);
            return List.of();
        }
    }

    @Override
    public Page<ChargePoint.Overview> getOverview(ChargePointQueryForm form, Pageable pageable) {
        try {
            log.debug("Getting charge point overview (paged) with form: {} and pageable: {}", form, pageable);

            ChargePointQueryForm queryForm = (form == null) ? new ChargePointQueryForm() : form;
            log.debug("Using queryForm: {}", queryForm);

            // Select fields including new ones
            // Assuming VIEW_CHARGE_BOX_WITH_OWNER has 'owner_username' for the owner and can be joined on 'charge_box_id'
            Field<?>[] selectFields = {
                CHARGE_BOX.CHARGE_BOX_PK,
                CHARGE_BOX.CHARGE_BOX_ID,
                CHARGE_BOX.DESCRIPTION,
                CHARGE_BOX.OCPP_PROTOCOL,
                CHARGE_BOX.LAST_HEARTBEAT_TIMESTAMP,
                CHARGE_BOX.FW_VERSION,
                CHARGE_BOX.FW_UPDATE_TIMESTAMP,
                CHARGE_BOX.LAST_UPGRADE_STATUS,
                VIEW_CHARGE_BOX_WITH_OWNER.OWNER_USERNAME.as("owner"), // Updated to use OWNER_USERNAME
                DSL.field("charging_station.charging_station_pk", Integer.class).as("charging_station_pk"),
                DSL.field("charging_station.station_name", String.class).as("station_name")
            };

            SelectQuery<?> selectQuery = ctx.selectQuery();
            selectQuery.addSelect(selectFields);
            selectQuery.addFrom(CHARGE_BOX);
            selectQuery.addJoin(
                VIEW_CHARGE_BOX_WITH_OWNER,
                org.jooq.JoinType.LEFT_OUTER_JOIN,
                VIEW_CHARGE_BOX_WITH_OWNER.CHARGE_BOX_ID.eq(CHARGE_BOX.CHARGE_BOX_ID) // Updated join condition
            );
            selectQuery.addJoin(
                DSL.table("charging_station"),
                org.jooq.JoinType.LEFT_OUTER_JOIN,
                DSL.field("charging_station.charging_station_pk", Integer.class).eq(DSL.field("charge_box.charging_station_pk", Integer.class))
            );

            // Apply filters from ChargePointQueryForm
            applyQueryFormFilters(selectQuery, queryForm);

            // Get total count for pagination
            int totalCount = ctx.fetchCount(selectQuery);

            // Apply pagination and ordering
            selectQuery.addOrderBy(CHARGE_BOX.CHARGE_BOX_PK.asc()); // Default sort
            if (pageable.isPaged()) {
                selectQuery.addLimit(pageable.getPageSize());
                selectQuery.addOffset(pageable.getOffset());
            }
            
            Result<?> result = selectQuery.fetch();

            List<ChargePoint.Overview> overviewList = result.map(r -> {
                try {
                    DateTime lastHeartbeat = r.get(CHARGE_BOX.LAST_HEARTBEAT_TIMESTAMP);
                    boolean isOnline = false;
                    if (lastHeartbeat != null) {
                        isOnline = lastHeartbeat.isAfter(DateTime.now().minusMinutes(5));
                    }

                    // 获取充电成功率统计数据
                    String chargeBoxId = r.get(CHARGE_BOX.CHARGE_BOX_ID);
                    ChargingSuccessStats successStats = chargingSuccessRepository.getSuccessStats(chargeBoxId).orElse(null);

                    int totalSessions = 0;
                    int successfulSessions = 0;
                    String successRateStr = "0.00%";
                    java.math.BigDecimal successRateDecimal = java.math.BigDecimal.ZERO;

                    if (successStats != null) {
                        totalSessions = successStats.getTotalSessions();
                        successfulSessions = successStats.getSuccessfulSessions();
                        successRateStr = successStats.getFormattedSuccessRate();
                        successRateDecimal = successStats.getSuccessRate();
                    }

                    return ChargePoint.Overview.builder()
                            .chargeBoxPk(r.get(CHARGE_BOX.CHARGE_BOX_PK))
                            .chargeBoxId(chargeBoxId)
                            .description(r.get(CHARGE_BOX.DESCRIPTION))
                            .ocppProtocol(r.get(CHARGE_BOX.OCPP_PROTOCOL))
                            .lastHeartbeatTimestampDT(lastHeartbeat)
                            .lastHeartbeatTimestamp(DateTimeUtils.humanize(lastHeartbeat))
                            .firmwareVersion(r.get(CHARGE_BOX.FW_VERSION))
                            .firmwareUpdateTimestamp(r.get(CHARGE_BOX.FW_UPDATE_TIMESTAMP) == null ? null : r.get(CHARGE_BOX.FW_UPDATE_TIMESTAMP).toString()) // Handle potential null and convert to string
                            .lastUpgradeStatus(r.get(CHARGE_BOX.LAST_UPGRADE_STATUS))
                            .owner(r.get("owner", String.class)) // Get owner by alias
                            .chargingStationPk(r.get("charging_station_pk", Integer.class))
                            .chargingStationName(r.get("station_name", String.class))
                            .online(isOnline)
                            .totalChargingSessions(totalSessions)
                            .successfulChargingSessions(successfulSessions)
                            .successRate(successRateStr)
                            .successRateDecimal(successRateDecimal)
                            .build();
                } catch (Exception e) {
                    log.error("Error mapping charge point record for paged overview: {}", e.getMessage(), e);
                    return null;
                }
            })
            .stream()
            .filter(cp -> cp != null)
            .collect(Collectors.toList());

            log.debug("Found {} charge points for the current page", overviewList.size());
            return new PageImpl<>(overviewList, pageable, totalCount);

        } catch (Exception e) {
            log.error("Error in paged getOverview: {}", e.getMessage(), e);
            return Page.empty(pageable);
        }
    }
    
    // Helper method to apply filters from ChargePointQueryForm
    // This largely reuses logic from the old getOverviewInternal method
    private void applyQueryFormFilters(SelectQuery<?> selectQuery, ChargePointQueryForm form) {
        if (form.isSetOcppVersion()) {
            selectQuery.addConditions(CHARGE_BOX.OCPP_PROTOCOL.like(form.getOcppVersion().getValue() + "_%"));
        }
        if (form.isSetDescription()) {
            selectQuery.addConditions(includes(CHARGE_BOX.DESCRIPTION, form.getDescription()));
        }
        if (form.isSetChargeBoxId()) {
            selectQuery.addConditions(includes(CHARGE_BOX.CHARGE_BOX_ID, form.getChargeBoxId()));
        }
        if (form.isSetNote()) {
            selectQuery.addConditions(includes(CHARGE_BOX.NOTE, form.getNote()));
        }
        if (form.isSetLastUpgradeStatus()) {
            selectQuery.addConditions(CHARGE_BOX.LAST_UPGRADE_STATUS.eq(form.getLastUpgradeStatus()));
        }
        if (form.isSetOwner()) {
            selectQuery.addConditions(VIEW_CHARGE_BOX_WITH_OWNER.OWNER_USERNAME.like("%" + form.getOwner() + "%"));
        }
        if (form.isSetChargingStationPk()) {
            selectQuery.addConditions(DSL.field("charging_station_pk", Integer.class).eq(form.getChargingStationPk()));
        }
        // New filters
        if (form.isSetFirmwareVersion()) {
            selectQuery.addConditions(includes(CHARGE_BOX.FW_VERSION, form.getFirmwareVersion()));
        }
        if (form.isSetFirmwareUpdateTimestamp()) {
            // Assuming firmwareUpdateTimestamp is a string search for now.
            // If it's a date range, this logic needs to be more complex.
            // For simplicity, using 'includes' like other string fields.
            selectQuery.addConditions(includes(CHARGE_BOX.FW_UPDATE_TIMESTAMP.cast(String.class), form.getFirmwareUpdateTimestamp()));
        }

        if (form.getHeartbeatPeriod() != null) {
            // Revert to using Joda Time with CustomDSL.date wrapper for heartbeat period filtering
            switch (form.getHeartbeatPeriod()) {
                case ALL:
                    break;
                case TODAY:
                    selectQuery.addConditions(
                            date(CHARGE_BOX.LAST_HEARTBEAT_TIMESTAMP).eq(date(DateTime.now()))
                    );
                    break;
                case YESTERDAY:
                    selectQuery.addConditions(
                            date(CHARGE_BOX.LAST_HEARTBEAT_TIMESTAMP).eq(date(DateTime.now().minusDays(1)))
                    );
                    break;
                case EARLIER: // Assuming EARLIER was a valid QueryPeriodType option
                    selectQuery.addConditions(
                            date(CHARGE_BOX.LAST_HEARTBEAT_TIMESTAMP).lessThan(date(DateTime.now().minusDays(1)))
                    );
                    break;
                // If LAST_7_DAYS was a new enum member, ensure it's handled or remove if not present
                // case LAST_7_DAYS: 
                //    selectQuery.addConditions(
                //        date(CHARGE_BOX.LAST_HEARTBEAT_TIMESTAMP).between(date(DateTime.now().minusDays(6)), date(DateTime.now()))
                //    );
                //    break;
                default:
                    // It's good practice to log or handle unexpected enum values if possible,
                    // but throwing an exception might be too aggressive if "ALL" or other fallbacks are intended.
                    // For now, matching original behavior of not having a default throw for unhandled cases in this specific switch.
                    log.warn("Unknown or unhandled HeartbeatPeriod type in query: {}", form.getHeartbeatPeriod());
                    break;
            }
        }
    }

    @Override
    public ChargePoint.Details getDetails(int chargePointPk) {
        ChargeBoxRecord cbr = ctx.selectFrom(CHARGE_BOX)
                                 .where(CHARGE_BOX.CHARGE_BOX_PK.equal(chargePointPk))
                                 .fetchOne();

        if (cbr == null) {
            throw new SteveException("Charge point not found");
        }

        AddressRecord ar = addressRepository.get(ctx, cbr.getAddressPk());

        return new ChargePoint.Details(cbr, ar);
    }

    @Override
    public List<ConnectorStatus> getChargePointConnectorStatus(ConnectorStatusForm form) {
        // find out the latest timestamp for each connector
        Field<Integer> t1Pk = CONNECTOR_STATUS.CONNECTOR_PK.as("t1_pk");
        Field<DateTime> t1TsMax = DSL.max(CONNECTOR_STATUS.STATUS_TIMESTAMP).as("t1_ts_max");
        Table<?> t1 = ctx.select(t1Pk, t1TsMax)
                         .from(CONNECTOR_STATUS)
                         .groupBy(CONNECTOR_STATUS.CONNECTOR_PK)
                         .asTable("t1");

        // get the status table with latest timestamps only
        Field<Integer> t2Pk = CONNECTOR_STATUS.CONNECTOR_PK.as("t2_pk");
        Field<DateTime> t2Ts = CONNECTOR_STATUS.STATUS_TIMESTAMP.as("t2_ts");
        Field<String> t2Status = CONNECTOR_STATUS.STATUS.as("t2_status");
        Field<String> t2Error = CONNECTOR_STATUS.ERROR_CODE.as("t2_error");
        Table<?> t2 = ctx.selectDistinct(t2Pk, t2Ts, t2Status, t2Error)
                         .from(CONNECTOR_STATUS)
                         .join(t1)
                            .on(CONNECTOR_STATUS.CONNECTOR_PK.equal(t1.field(t1Pk)))
                            .and(CONNECTOR_STATUS.STATUS_TIMESTAMP.equal(t1.field(t1TsMax)))
                         .asTable("t2");

        // https://github.com/steve-community/steve/issues/691
        Condition chargeBoxCondition = CHARGE_BOX.REGISTRATION_STATUS.eq(RegistrationStatus.ACCEPTED.value());

        if (form != null && form.getChargeBoxId() != null) {
            chargeBoxCondition = chargeBoxCondition.and(CHARGE_BOX.CHARGE_BOX_ID.eq(form.getChargeBoxId()));
        }

        final Condition statusCondition;
        if (form == null || form.getStatus() == null) {
            statusCondition = DSL.noCondition();
        } else {
            statusCondition = t2.field(t2Status).eq(form.getStatus());
        }

        return ctx.select(
                        CHARGE_BOX.CHARGE_BOX_PK,
                        CONNECTOR.CHARGE_BOX_ID,
                        CONNECTOR.CONNECTOR_ID,
                        t2.field(t2Ts),
                        t2.field(t2Status),
                        t2.field(t2Error),
                        CHARGE_BOX.OCPP_PROTOCOL)
                  .from(t2)
                  .join(CONNECTOR)
                        .on(CONNECTOR.CONNECTOR_PK.eq(t2.field(t2Pk)))
                  .join(CHARGE_BOX)
                        .on(CHARGE_BOX.CHARGE_BOX_ID.eq(CONNECTOR.CHARGE_BOX_ID))
                  .where(chargeBoxCondition, statusCondition)
                  .orderBy(t2.field(t2Ts).desc())
                  .fetch()
                  .map(r -> ConnectorStatus.builder()
                                           .chargeBoxPk(r.value1())
                                           .chargeBoxId(r.value2())
                                           .connectorId(r.value3())
                                           .timeStamp(DateTimeUtils.humanize(r.value4()))
                                           .statusTimestamp(r.value4())
                                           .status(r.value5())
                                           .errorCode(r.value6())
                                           .ocppProtocol(r.value7() == null ? null : OcppProtocol.fromCompositeValue(r.value7()))
                                           .build()
                  );
    }

    @Override
    public List<Integer> getNonZeroConnectorIds(String chargeBoxId) {
        return ctx.select(CONNECTOR.CONNECTOR_ID)
                  .from(CONNECTOR)
                  .where(CONNECTOR.CHARGE_BOX_ID.equal(chargeBoxId))
                  .and(CONNECTOR.CONNECTOR_ID.notEqual(0))
                  .fetch(CONNECTOR.CONNECTOR_ID);
    }

    @Override
    public void addChargePointList(List<String> chargeBoxIdList) {
        List<ChargeBoxRecord> batch = chargeBoxIdList.stream()
                                                     .map(s -> ctx.newRecord(CHARGE_BOX)
                                                                  .setChargeBoxId(s)
                                                                  .setInsertConnectorStatusAfterTransactionMsg(false))
                                                     .collect(Collectors.toList());

        ctx.batchInsert(batch).execute();

        // 为每个充电桩初始化成功率统计数据
        for (String chargeBoxId : chargeBoxIdList) {
            try {
                chargingSuccessRepository.initializeSuccessStats(chargeBoxId);
            } catch (Exception e) {
                log.warn("Failed to initialize charging success stats for chargeBoxId: {}", chargeBoxId, e);
            }
        }
    }

    @Override
    public int addChargePoint(ChargePointForm form) {
        return ctx.transactionResult(configuration -> {
            DSLContext ctx = DSL.using(configuration);
            try {
                Integer addressId = addressRepository.updateOrInsert(ctx, form.getAddress());
                int chargeBoxPk = addChargePointInternal(ctx, form, addressId);

                // 异步初始化充电成功率统计数据，避免阻塞主流程
                CompletableFuture.runAsync(() -> {
                    try {
                        chargingSuccessRepository.initializeSuccessStats(form.getChargeBoxId());
                        log.debug("Successfully initialized charging success stats for chargeBoxId: {}", form.getChargeBoxId());
                    } catch (Exception e) {
                        log.warn("Failed to initialize charging success stats for chargeBoxId: {}", form.getChargeBoxId(), e);
                    }
                });

                return chargeBoxPk;

            } catch (DataAccessException e) {
                throw new SteveException("Failed to add the charge point with chargeBoxId '%s'",
                        form.getChargeBoxId(), e);
            }
        });
    }

    @Override
    public void updateChargePoint(ChargePointForm form) {
        ctx.transaction(configuration -> {
            DSLContext ctx = DSL.using(configuration);
            try {
                Integer addressId = addressRepository.updateOrInsert(ctx, form.getAddress());
                updateChargePointInternal(ctx, form, addressId);

                // 处理拥有者分配更新
                if (form.getOwnerUserPk() != null) {
                    // 先删除现有的分配关系
                    ctx.deleteFrom(USER_CHARGE_BOX)
                       .where(USER_CHARGE_BOX.CHARGE_BOX_PK.eq(form.getChargeBoxPk()))
                       .execute();

                    // 添加新的分配关系
                    ctx.insertInto(USER_CHARGE_BOX)
                       .set(USER_CHARGE_BOX.WEB_USER_PK, form.getOwnerUserPk())
                       .set(USER_CHARGE_BOX.CHARGE_BOX_PK, form.getChargeBoxPk())
                       .execute();

                    log.info("Updated charge box {} owner to user (userPk: {})",
                            form.getChargeBoxId(), form.getOwnerUserPk());
                } else {
                    // 如果没有选择拥有者，删除现有的分配关系
                    int deletedRows = ctx.deleteFrom(USER_CHARGE_BOX)
                                        .where(USER_CHARGE_BOX.CHARGE_BOX_PK.eq(form.getChargeBoxPk()))
                                        .execute();
                    if (deletedRows > 0) {
                        log.info("Removed owner assignment for charge box {}", form.getChargeBoxId());
                    }
                }

            } catch (DataAccessException e) {
                throw new SteveException("Failed to update the charge point with chargeBoxId '%s'",
                        form.getChargeBoxId(), e);
            }
        });
    }

    @Override
    public void deleteChargePoint(int chargeBoxPk) {
        log.debug("Starting deletion process for charge box pk: {}", chargeBoxPk);

        ctx.transaction(configuration -> {
            DSLContext ctx = DSL.using(configuration);
            try {
                // 首先检查充电桩是否存在
                boolean exists = ctx.fetchExists(
                    ctx.selectOne()
                       .from(CHARGE_BOX)
                       .where(CHARGE_BOX.CHARGE_BOX_PK.eq(chargeBoxPk))
                );

                if (!exists) {
                    log.warn("Charge box with pk {} does not exist", chargeBoxPk);
                    throw new SteveException("Charge box does not exist");
                }

                log.debug("Charge box pk {} exists, proceeding with deletion", chargeBoxPk);

                // 删除地址
                log.debug("Deleting address for charge box pk: {}", chargeBoxPk);
                addressRepository.delete(ctx, selectAddressId(chargeBoxPk));
                log.debug("Address deleted successfully for charge box pk: {}", chargeBoxPk);

                // 删除充电桩本身
                log.debug("Deleting charge box record for pk: {}", chargeBoxPk);
                deleteChargePointInternal(ctx, chargeBoxPk);
                log.debug("Charge box record deleted successfully for pk: {}", chargeBoxPk);

            } catch (DataAccessException e) {
                log.error("Database error while deleting charge box pk {}: {}", chargeBoxPk, e.getMessage(), e);
                throw new SteveException("Failed to delete the charge point: " + e.getMessage(), e);
            } catch (Exception e) {
                log.error("Unexpected error while deleting charge box pk {}: {}", chargeBoxPk, e.getMessage(), e);
                throw new SteveException("Failed to delete the charge point: " + e.getMessage(), e);
            }
        });

        log.info("Successfully completed deletion process for charge box pk: {}", chargeBoxPk);
    }

    @Override
    public Integer getChargeBoxPkFromChargeBoxId(String chargeBoxId) {
        return ctx.selectFrom(CHARGE_BOX)
                  .where(CHARGE_BOX.CHARGE_BOX_ID.eq(chargeBoxId))
                  .fetchOne(CHARGE_BOX.CHARGE_BOX_PK);
    }

    @Override
    public List<String> getChargeBoxIdsRequiringReconnect() {
        return ctx.select(CHARGE_BOX.CHARGE_BOX_ID)
                  .from(CHARGE_BOX)
                  .where(CHARGE_BOX.RECONNECT_REQUIRED.eq(true))
                  .fetch(CHARGE_BOX.CHARGE_BOX_ID);
    }

    @Override
    public void resetReconnectFlags() {
        ctx.update(CHARGE_BOX)
           .set(CHARGE_BOX.RECONNECT_REQUIRED, false)
           .where(CHARGE_BOX.RECONNECT_REQUIRED.eq(true))
           .execute();
    }

    @Override
    public void resetReconnectFlag(String chargeBoxId) {
        ctx.update(CHARGE_BOX)
           .set(CHARGE_BOX.RECONNECT_REQUIRED, false)
           .set(CHARGE_BOX.IN_TRANSACTION, false)
           .where(CHARGE_BOX.CHARGE_BOX_ID.eq(chargeBoxId))
           .execute();
    }

    @Override
    public boolean isReconnectRequired(String chargeBoxId) {
        Record1<Boolean> record = ctx.select(CHARGE_BOX.RECONNECT_REQUIRED)
                                    .from(CHARGE_BOX)
                                    .where(CHARGE_BOX.CHARGE_BOX_ID.eq(chargeBoxId))
                                    .fetchOne();
        
        return record != null && Boolean.TRUE.equals(record.value1());
    }

    @Override
    public List<String> getOnlineChargeBoxIds() {
        DateTime fiveMinutesAgo = DateTime.now().minusMinutes(5);

        return ctx.select(CHARGE_BOX.CHARGE_BOX_ID)
                  .from(CHARGE_BOX)
                  .where(CHARGE_BOX.REGISTRATION_STATUS.eq(RegistrationStatus.ACCEPTED.value()))
                  .and(CHARGE_BOX.LAST_HEARTBEAT_TIMESTAMP.isNotNull())
                  .and(CHARGE_BOX.LAST_HEARTBEAT_TIMESTAMP.greaterThan(fiveMinutesAgo))
                  .fetch(CHARGE_BOX.CHARGE_BOX_ID);
    }

    @Override
    public List<ChargePointSelect> getChargePointSelectByOwner(Integer webUserPk) {
        // 管理员角色获取所有充电桩
        if (isAdmin(webUserPk)) {
            return getChargePointsEx();
        }

        // 普通用户获取自己拥有的充电桩
        return ctx.select(CHARGE_BOX.CHARGE_BOX_ID, CHARGE_BOX.OCPP_PROTOCOL, CHARGE_BOX.ENDPOINT_ADDRESS)
                 .from(CHARGE_BOX)
                 .join(USER_CHARGE_BOX).on(USER_CHARGE_BOX.CHARGE_BOX_PK.eq(CHARGE_BOX.CHARGE_BOX_PK))
                 .where(USER_CHARGE_BOX.WEB_USER_PK.eq(webUserPk))
                 .orderBy(CHARGE_BOX.CHARGE_BOX_ID)
                 .fetch(r -> {
                     String protocol = r.value2();
                     OcppProtocol ocppProtocol = protocol != null ?
                         OcppProtocol.fromCompositeValue(protocol) : OcppProtocol.V_16_JSON;

                     return new ChargePointSelect(
                             ocppProtocol,
                             r.value1(),
                             r.value3()
                     );
                 });
    }
    
    private boolean isAdmin(Integer userPk) {
        jooq.steve.db.enums.WebUserUserRole role = ctx.select(WEB_USER.USER_ROLE)
                        .from(WEB_USER)
                        .where(WEB_USER.WEB_USER_PK.eq(userPk))
                        .fetchOneInto(jooq.steve.db.enums.WebUserUserRole.class);
        return jooq.steve.db.enums.WebUserUserRole.ADMIN.equals(role) || 
               jooq.steve.db.enums.WebUserUserRole.OPERATOR_FACTORY.equals(role);
    }

    @Override
    public List<ChargePoint.Overview> getOwnerChargeBoxes(int webUserPk, ChargePointQueryForm form) {
        try {
            log.debug("Getting charge boxes for owner with PK: {}, form: {}", webUserPk, form);
            
            // 创建一个默认的查询表单，避免NPE
            ChargePointQueryForm queryForm = (form == null) ? new ChargePointQueryForm() : form;
            
            SelectQuery selectQuery = ctx.selectQuery();
            selectQuery.addFrom(VIEW_OWNER_CHARGE_BOXES);
            selectQuery.addSelect(
                    VIEW_OWNER_CHARGE_BOXES.CHARGE_BOX_PK,
                    VIEW_OWNER_CHARGE_BOXES.CHARGE_BOX_ID,
                    VIEW_OWNER_CHARGE_BOXES.DESCRIPTION,
                    VIEW_OWNER_CHARGE_BOXES.OCPP_PROTOCOL,
                    VIEW_OWNER_CHARGE_BOXES.LAST_HEARTBEAT_TIMESTAMP
            );
            
            // 根据web_user_pk过滤只属于该桩主的充电桩
            selectQuery.addConditions(VIEW_OWNER_CHARGE_BOXES.WEB_USER_PK.eq(webUserPk));
            
            // 添加其他查询条件
            if (queryForm.isSetOcppVersion()) {
                selectQuery.addConditions(VIEW_OWNER_CHARGE_BOXES.OCPP_PROTOCOL.like(queryForm.getOcppVersion().getValue() + "_"));
            }
            
            if (queryForm.isSetDescription()) {
                selectQuery.addConditions(includes(VIEW_OWNER_CHARGE_BOXES.DESCRIPTION, queryForm.getDescription()));
            }
            
            if (queryForm.isSetChargeBoxId()) {
                selectQuery.addConditions(includes(VIEW_OWNER_CHARGE_BOXES.CHARGE_BOX_ID, queryForm.getChargeBoxId()));
            }
            
            if (queryForm.isSetNote()) {
                selectQuery.addConditions(includes(VIEW_OWNER_CHARGE_BOXES.NOTE, queryForm.getNote()));
            }
            
            switch (queryForm.getHeartbeatPeriod()) {
                case ALL:
                    break;
                case TODAY:
                    selectQuery.addConditions(
                            date(VIEW_OWNER_CHARGE_BOXES.LAST_HEARTBEAT_TIMESTAMP).eq(date(DateTime.now()))
                    );
                    break;
                case YESTERDAY:
                    selectQuery.addConditions(
                            date(VIEW_OWNER_CHARGE_BOXES.LAST_HEARTBEAT_TIMESTAMP).eq(date(DateTime.now().minusDays(1)))
                    );
                    break;
                case EARLIER:
                    selectQuery.addConditions(
                            date(VIEW_OWNER_CHARGE_BOXES.LAST_HEARTBEAT_TIMESTAMP).lt(date(DateTime.now().minusDays(1)))
                    );
                    break;
                default:
                    throw new SteveException("Unknown heartbeat period: " + queryForm.getHeartbeatPeriod());
            }
            
            // 执行查询
            Result<Record5<Integer, String, String, String, DateTime>> result = selectQuery.fetch();
            
            // 转换结果
            return result.map(r -> ChargePoint.Overview.builder()
                            .chargeBoxPk(r.value1())
                            .chargeBoxId(r.value2())
                            .description(r.value3())
                            .ocppProtocol(r.value4())
                            .lastHeartbeatTimestampDT(r.value5())
                            .lastHeartbeatTimestamp(DateTimeUtils.humanize(r.value5()))
                            .build());
        } catch (Exception e) {
            log.error("Error in getOwnerChargeBoxes: {}", e.getMessage(), e);
            return List.of(); // 返回空列表
        }
    }

    // 额外的辅助方法，用于获取所有充电桩
    private List<ChargePointSelect> getChargePointsEx() {
        return ctx.select(CHARGE_BOX.CHARGE_BOX_ID, CHARGE_BOX.OCPP_PROTOCOL, CHARGE_BOX.ENDPOINT_ADDRESS)
                .from(CHARGE_BOX)
                .orderBy(CHARGE_BOX.CHARGE_BOX_ID)
                .fetch(r -> {
                    String protocol = r.value2();
                    OcppProtocol ocppProtocol = protocol != null ?
                        OcppProtocol.fromCompositeValue(protocol) : OcppProtocol.V_16_JSON;

                    return new ChargePointSelect(
                            ocppProtocol,
                            r.value1(),
                            r.value3()
                    );
                });
    }

    @Override
    public List<ChargePointSelect> getChargePointSelectList() {
        return ctx.select(CHARGE_BOX.CHARGE_BOX_ID, CHARGE_BOX.DESCRIPTION, CHARGE_BOX.OCPP_PROTOCOL, CHARGE_BOX.ENDPOINT_ADDRESS)
                  .from(CHARGE_BOX)
                  .fetch()
                  .map(r -> {
                      String protocol = r.value3();
                      OcppProtocol ocppProtocol = protocol != null ?
                          OcppProtocol.fromCompositeValue(protocol) : OcppProtocol.V_16_JSON;

                      return new ChargePointSelect(
                              ocppProtocol,
                              r.value1(),
                              r.value4() != null ? r.value4() : "-",
                              r.value2()
                      );
                  });
    }

    @Override
    public ChargePoint.Details getDetailsByChargeBoxId(String chargeBoxId) {
        ChargeBoxRecord cbr = ctx.selectFrom(CHARGE_BOX)
                                 .where(CHARGE_BOX.CHARGE_BOX_ID.eq(chargeBoxId))
                                 .fetchOne();

        if (cbr == null) {
            return null;
        }

        AddressRecord ar = addressRepository.get(ctx, cbr.getAddressPk());

        return new ChargePoint.Details(cbr, ar);
    }

    @Override
    public void updateLastUpgradeStatus(String chargeBoxId, String status) {
        ctx.update(CHARGE_BOX)
           .set(CHARGE_BOX.LAST_UPGRADE_STATUS, status)
           .where(CHARGE_BOX.CHARGE_BOX_ID.eq(chargeBoxId))
           .execute();
    }

    @Override
    public Optional<OcppProtocol> findOcppProtocolByChargeBoxId(String chargeBoxId) {
        String protocolString = ctx.select(CHARGE_BOX.OCPP_PROTOCOL)
                                   .from(CHARGE_BOX)
                                   .where(CHARGE_BOX.CHARGE_BOX_ID.eq(chargeBoxId))
                                   .fetchOne(CHARGE_BOX.OCPP_PROTOCOL);

        return Optional.ofNullable(protocolString)
                       .map(OcppProtocol::fromCompositeValue);
    }

    @Override
    public boolean isOnline(String chargeBoxId) {
        DateTime lastHeartbeat = ctx.select(CHARGE_BOX.LAST_HEARTBEAT_TIMESTAMP)
                                    .from(CHARGE_BOX)
                                    .where(CHARGE_BOX.CHARGE_BOX_ID.eq(chargeBoxId))
                                    .fetchOne(CHARGE_BOX.LAST_HEARTBEAT_TIMESTAMP);

        if (lastHeartbeat == null) {
            return false;
        }

        // Consider online if the last heartbeat was within the last 5 minutes
        return lastHeartbeat.isAfter(DateTime.now().minusMinutes(5));
    }

    // -------------------------------------------------------------------------
    // Helpers
    // -------------------------------------------------------------------------

    private SelectConditionStep<Record1<Integer>> selectAddressId(int chargeBoxPk) {
        return ctx.select(CHARGE_BOX.ADDRESS_PK)
                  .from(CHARGE_BOX)
                  .where(CHARGE_BOX.CHARGE_BOX_PK.eq(chargeBoxPk));
    }

    private int addChargePointInternal(DSLContext ctx, ChargePointForm form, Integer addressPk) {
        return ctx.insertInto(CHARGE_BOX)
                  .set(CHARGE_BOX.CHARGE_BOX_ID, form.getChargeBoxId())
                  .set(CHARGE_BOX.DESCRIPTION, form.getDescription())
                  .set(CHARGE_BOX.LOCATION_LATITUDE, form.getLocationLatitude())
                  .set(CHARGE_BOX.LOCATION_LONGITUDE, form.getLocationLongitude())
                  .set(CHARGE_BOX.INSERT_CONNECTOR_STATUS_AFTER_TRANSACTION_MSG, form.getInsertConnectorStatusAfterTransactionMsg())
                  .set(CHARGE_BOX.REGISTRATION_STATUS, form.getRegistrationStatus())
                  .set(CHARGE_BOX.NOTE, form.getNote())
                  .set(CHARGE_BOX.ADMIN_ADDRESS, form.getAdminAddress())
                  .set(CHARGE_BOX.ADDRESS_PK, addressPk)
                  .set(DSL.field("charging_station_pk", Integer.class), form.getChargingStationPk())
                  .returning(CHARGE_BOX.CHARGE_BOX_PK)
                  .fetchOne()
                  .getChargeBoxPk();
    }

    private void updateChargePointInternal(DSLContext ctx, ChargePointForm form, Integer addressPk) {
        ctx.update(CHARGE_BOX)
           .set(CHARGE_BOX.DESCRIPTION, form.getDescription())
           .set(CHARGE_BOX.LOCATION_LATITUDE, form.getLocationLatitude())
           .set(CHARGE_BOX.LOCATION_LONGITUDE, form.getLocationLongitude())
           .set(CHARGE_BOX.INSERT_CONNECTOR_STATUS_AFTER_TRANSACTION_MSG, form.getInsertConnectorStatusAfterTransactionMsg())
           .set(CHARGE_BOX.REGISTRATION_STATUS, form.getRegistrationStatus())
           .set(CHARGE_BOX.NOTE, form.getNote())
           .set(CHARGE_BOX.ADMIN_ADDRESS, form.getAdminAddress())
           .set(CHARGE_BOX.ADDRESS_PK, addressPk)
           .set(DSL.field("charging_station_pk", Integer.class), form.getChargingStationPk())
           .where(CHARGE_BOX.CHARGE_BOX_PK.equal(form.getChargeBoxPk()))
           .execute();
    }

    private void deleteChargePointInternal(DSLContext ctx, int chargeBoxPk) {
        log.debug("Executing delete query for charge box pk: {}", chargeBoxPk);

        int deletedRows = ctx.delete(CHARGE_BOX)
                             .where(CHARGE_BOX.CHARGE_BOX_PK.equal(chargeBoxPk))
                             .execute();

        log.debug("Delete query executed, {} rows affected for charge box pk: {}", deletedRows, chargeBoxPk);

        if (deletedRows == 0) {
            log.warn("No rows were deleted for charge box pk: {}", chargeBoxPk);
            throw new SteveException("No charge box was deleted - it may have already been removed");
        }
    }

    @Override
    public List<ChargePoint.Overview> getChargePointsByStation(Integer chargingStationPk) {
        if (chargingStationPk == null) {
            return Collections.emptyList();
        }

        try {
            log.debug("Getting charge points for station: {}", chargingStationPk);

            // Select fields
            Field<?>[] selectFields = {
                CHARGE_BOX.CHARGE_BOX_PK,
                CHARGE_BOX.CHARGE_BOX_ID,
                CHARGE_BOX.DESCRIPTION,
                CHARGE_BOX.OCPP_PROTOCOL,
                CHARGE_BOX.LAST_HEARTBEAT_TIMESTAMP,
                CHARGE_BOX.FW_VERSION,
                CHARGE_BOX.FW_UPDATE_TIMESTAMP,
                CHARGE_BOX.LAST_UPGRADE_STATUS,
                VIEW_CHARGE_BOX_WITH_OWNER.OWNER_USERNAME.as("owner"),
                DSL.field("charging_station_pk", Integer.class).as("charging_station_pk"),
                DSL.field("station_name", String.class).as("station_name")
            };

            SelectQuery<?> selectQuery = ctx.selectQuery();
            selectQuery.addSelect(selectFields);
            selectQuery.addFrom(CHARGE_BOX);
            selectQuery.addJoin(
                VIEW_CHARGE_BOX_WITH_OWNER,
                org.jooq.JoinType.LEFT_OUTER_JOIN,
                VIEW_CHARGE_BOX_WITH_OWNER.CHARGE_BOX_ID.eq(CHARGE_BOX.CHARGE_BOX_ID)
            );

            // Filter by charging station
            selectQuery.addConditions(DSL.field("charging_station_pk", Integer.class).eq(chargingStationPk));

            // Order by charge box ID
            selectQuery.addOrderBy(CHARGE_BOX.CHARGE_BOX_ID.asc());

            Result<?> result = selectQuery.fetch();

            return result.map(r -> {
                try {
                    DateTime lastHeartbeat = r.get(CHARGE_BOX.LAST_HEARTBEAT_TIMESTAMP);
                    boolean isOnline = false;
                    if (lastHeartbeat != null) {
                        isOnline = lastHeartbeat.isAfter(DateTime.now().minusMinutes(5));
                    }

                    // 获取充电成功率统计数据
                    String chargeBoxId = r.get(CHARGE_BOX.CHARGE_BOX_ID);
                    ChargingSuccessStats successStats = chargingSuccessRepository.getSuccessStats(chargeBoxId).orElse(null);

                    int totalSessions = 0;
                    int successfulSessions = 0;
                    String successRateStr = "0.00%";
                    java.math.BigDecimal successRateDecimal = java.math.BigDecimal.ZERO;

                    if (successStats != null) {
                        totalSessions = successStats.getTotalSessions();
                        successfulSessions = successStats.getSuccessfulSessions();
                        successRateStr = successStats.getFormattedSuccessRate();
                        successRateDecimal = successStats.getSuccessRate();
                    }

                    return ChargePoint.Overview.builder()
                            .chargeBoxPk(r.get(CHARGE_BOX.CHARGE_BOX_PK))
                            .chargeBoxId(chargeBoxId)
                            .description(r.get(CHARGE_BOX.DESCRIPTION))
                            .ocppProtocol(r.get(CHARGE_BOX.OCPP_PROTOCOL))
                            .lastHeartbeatTimestampDT(lastHeartbeat)
                            .lastHeartbeatTimestamp(DateTimeUtils.humanize(lastHeartbeat))
                            .firmwareVersion(r.get(CHARGE_BOX.FW_VERSION))
                            .firmwareUpdateTimestamp(r.get(CHARGE_BOX.FW_UPDATE_TIMESTAMP) == null ? null : r.get(CHARGE_BOX.FW_UPDATE_TIMESTAMP).toString())
                            .lastUpgradeStatus(r.get(CHARGE_BOX.LAST_UPGRADE_STATUS))
                            .owner(r.get("owner", String.class))
                            .online(isOnline)
                            .totalChargingSessions(totalSessions)
                            .successfulChargingSessions(successfulSessions)
                            .successRate(successRateStr)
                            .successRateDecimal(successRateDecimal)
                            .build();
                } catch (Exception e) {
                    log.error("Error mapping charge point record for station {}: {}", chargingStationPk, e.getMessage(), e);
                    return null;
                }
            })
            .stream()
            .filter(cp -> cp != null)
            .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("Error getting charge points for station {}: {}", chargingStationPk, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public void batchUpdateChargingStation(List<Integer> chargeBoxPks, Integer chargingStationPk) {
        if (chargeBoxPks == null || chargeBoxPks.isEmpty()) {
            return;
        }

        try {
            log.debug("Batch updating charging station for {} charge points to station: {}", chargeBoxPks.size(), chargingStationPk);

            ctx.update(CHARGE_BOX)
               .set(DSL.field("charging_station_pk", Integer.class), chargingStationPk)
               .where(CHARGE_BOX.CHARGE_BOX_PK.in(chargeBoxPks))
               .execute();

            log.info("Successfully updated {} charge points to station: {}", chargeBoxPks.size(), chargingStationPk);
        } catch (Exception e) {
            log.error("Error batch updating charging station: {}", e.getMessage(), e);
            throw new SteveException("Failed to batch update charging station", e);
        }
    }

    @Override
    public List<ChargePointSelect> getUnassignedChargePoints() {
        try {
            return ctx.select(CHARGE_BOX.CHARGE_BOX_ID,
                             CHARGE_BOX.DESCRIPTION,
                             CHARGE_BOX.OCPP_PROTOCOL)
                      .from(CHARGE_BOX)
                      .where(DSL.field("charging_station_pk", Integer.class).isNull())
                      .orderBy(CHARGE_BOX.CHARGE_BOX_ID)
                      .fetch()
                      .map(record -> {
                          String protocol = record.get(CHARGE_BOX.OCPP_PROTOCOL);
                          OcppProtocol ocppProtocol = protocol != null ?
                              OcppProtocol.fromCompositeValue(protocol) : null;

                          return new ChargePointSelect(
                              ocppProtocol != null ? ocppProtocol : OcppProtocol.V_16_SOAP,
                              record.get(CHARGE_BOX.CHARGE_BOX_ID),
                              "-", // endpoint address placeholder
                              record.get(CHARGE_BOX.DESCRIPTION)
                          );
                      });
        } catch (Exception e) {
            log.error("Error getting unassigned charge points: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<ChargePointSelect> getUnassignedChargePointsByOwner(Integer webUserPk) {
        try {
            if (webUserPk == null) {
                return List.of();
            }

            return ctx.select(CHARGE_BOX.CHARGE_BOX_ID,
                             CHARGE_BOX.DESCRIPTION,
                             CHARGE_BOX.OCPP_PROTOCOL)
                      .from(CHARGE_BOX)
                      .join(USER_CHARGE_BOX).on(USER_CHARGE_BOX.CHARGE_BOX_PK.eq(CHARGE_BOX.CHARGE_BOX_PK))
                      .where(DSL.field("charging_station_pk", Integer.class).isNull())
                      .and(USER_CHARGE_BOX.WEB_USER_PK.eq(webUserPk))
                      .orderBy(CHARGE_BOX.CHARGE_BOX_ID)
                      .fetch()
                      .map(record -> {
                          String protocol = record.get(CHARGE_BOX.OCPP_PROTOCOL);
                          OcppProtocol ocppProtocol = protocol != null ?
                              OcppProtocol.fromCompositeValue(protocol) : null;

                          return new ChargePointSelect(
                              ocppProtocol != null ? ocppProtocol : OcppProtocol.V_16_SOAP,
                              record.get(CHARGE_BOX.CHARGE_BOX_ID),
                              "-", // endpoint address placeholder
                              record.get(CHARGE_BOX.DESCRIPTION)
                          );
                      });
        } catch (Exception e) {
            log.error("Error fetching unassigned charge points for user {}: {}", webUserPk, e.getMessage(), e);
            return List.of();
        }
    }
}
