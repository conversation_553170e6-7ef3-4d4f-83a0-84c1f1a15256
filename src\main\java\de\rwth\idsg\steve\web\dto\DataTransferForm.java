package de.rwth.idsg.steve.web.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class DataTransferForm {

    private String chargeBoxId;
    private boolean isOnline;

    // General settings from image
    private Integer dcMaxVoltage;
    private Integer dcMinVoltage;
    private Integer dcMaxCurrent;

    private Integer moduleNumber;
    private String dcModuleType;
    private String appVersion;

    // Control Board 1
    private Integer cb1VoltageK;
    private Integer cb1VoltageB;
    private Integer cb1CurrentK;
    private Integer cb1CurrentB;
    private String cb1Version;
    private boolean cb1Gun1Enabled;
    private String cb1Gun1ChannelType;
    private boolean cb1Gun2Enabled;
    private String cb1Gun2ChannelType;

    // Control Board 2
    private Integer cb2VoltageK;
    private Integer cb2VoltageB;
    private Integer cb2CurrentK;
    private Integer cb2CurrentB;
    private String cb2Version;
    private boolean cb2Gun1Enabled;
    private String cb2Gun1ChannelType;
    private boolean cb2Gun2Enabled;
    private String cb2Gun2ChannelType;
}