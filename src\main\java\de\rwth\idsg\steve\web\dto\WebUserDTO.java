/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.web.dto;

import jooq.steve.db.tables.records.WebUserRecord;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 用于在前端页面展示用户信息的DTO类
 */
@Getter
@Setter
@ToString
public class WebUserDTO {
    private int userPk;
    private String username;
    private String userRole;
    private boolean enabled;
    
    /**
     * 从WebUserRecord创建WebUserDTO
     */
    public static WebUserDTO fromRecord(WebUserRecord record) {
        if (record == null) {
            return null;
        }
        
        WebUserDTO dto = new WebUserDTO();
        dto.setUserPk(record.getWebUserPk());
        dto.setUsername(record.getUsername());
        dto.setUserRole(record.getUserRole() != null ? record.getUserRole().name() : null);
        dto.setEnabled(record.getEnabled());
        return dto;
    }
} 