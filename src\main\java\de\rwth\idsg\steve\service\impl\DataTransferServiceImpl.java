package de.rwth.idsg.steve.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import de.rwth.idsg.steve.ocpp.OcppCallback;
import de.rwth.idsg.steve.ocpp.OcppProtocol;
import de.rwth.idsg.steve.ocpp.task.DataTransferTask;
import de.rwth.idsg.steve.ocpp.ws.data.OcppJsonError;
import de.rwth.idsg.steve.repository.ChargePointRepository;
import de.rwth.idsg.steve.repository.dto.ChargePointSelect;
import de.rwth.idsg.steve.service.ChargePointServiceClient;
import de.rwth.idsg.steve.service.DataTransferService;
import de.rwth.idsg.steve.web.dto.DataTransferForm;
import de.rwth.idsg.steve.web.dto.ocpp.DataTransferParams;
import lombok.extern.slf4j.Slf4j;
import ocpp.cs._2015._10.DataTransferStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.stream.Stream;

@Slf4j
@Service
public class DataTransferServiceImpl implements DataTransferService {

    private final ChargePointServiceClient chargePointServiceClient;
    private final ChargePointRepository chargePointRepository;
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Autowired
    public DataTransferServiceImpl(ChargePointServiceClient chargePointServiceClient,
                                   ChargePointRepository chargePointRepository) {
        this.chargePointServiceClient = chargePointServiceClient;
        this.chargePointRepository = chargePointRepository;
    }

    @Override
    public CompletionStage<DataTransferForm> readConfiguration(String chargeBoxId) {
        CompletableFuture<DataTransferForm> future = new CompletableFuture<>();

        OcppProtocol protocol = chargePointRepository.findOcppProtocolByChargeBoxId(chargeBoxId)
                .orElseThrow(() -> new IllegalArgumentException("Cannot determine OCPP protocol for " + chargeBoxId));

        try {
            ObjectNode dataNode = objectMapper.createObjectNode();
            // Using a stream to build the request payload for reading all keys
            Stream.of(
                    "appVersion", "controlBoardVersion", "dcModuleType", "moduleNumber", "dcMaxVoltage",
                    "dcMinVoltage", "dcMaxCurrent", "channelsConfig", "calibrationVoltage", "calibrationCurrent"
            ).forEach(key -> dataNode.put(key, "***"));

            DataTransferParams params = new DataTransferParams();
            params.setChargePointSelectList(Collections.singletonList(new ChargePointSelect(protocol, chargeBoxId)));
            params.setVendorId("setec002"); // Read operation
            params.setData(dataNode.toString());

            log.info("Sending DataTransfer read request to {}: {}", chargeBoxId, params.getData());

            chargePointServiceClient.dataTransfer(params, new OcppCallback<>() {
                @Override
                public void success(String chargeBoxId, DataTransferTask.ResponseWrapper response) {
                    if (DataTransferStatus.ACCEPTED.value().equals(response.getStatus()) && response.getData() != null) {
                        try {
                            log.info(">>>> [RAW] Received DataTransfer response from {}: {}", chargeBoxId, response.getData());

                            JsonNode data = objectMapper.readTree(response.getData());
                            DataTransferForm form = mapResponseToForm(chargeBoxId, data);
                            log.info(">>>> [MAPPED] Mapped response to DTO for {}: {}", chargeBoxId, form);

                            future.complete(form);

                        } catch (Exception e) {
                            log.error("Failed to process DataTransfer response for {}", chargeBoxId, e);
                            future.completeExceptionally(e);
                        }
                    } else {
                        String errorMessage = String.format("Received non-Accepted status ('%s') or null data from %s", response.getStatus(), chargeBoxId);
                        log.warn(errorMessage);
                        future.completeExceptionally(new RuntimeException(errorMessage));
                    }
                }

                @Override
                public void success(String chargeBoxId, OcppJsonError error) {
                    String errorMsg = String.format("DataTransfer read failed for %s with OCPP error: %s", chargeBoxId, error);
                    log.warn(errorMsg);
                    future.completeExceptionally(new RuntimeException(errorMsg));
                }

                @Override
                public void failed(String chargeBoxId, Exception e) {
                    log.error("DataTransfer read failed for {}", chargeBoxId, e);
                    future.completeExceptionally(e);
                }
            });

        } catch (Exception e) {
            log.error("Failed to initiate DataTransfer read for {}", chargeBoxId, e);
            future.completeExceptionally(e);
        }

        return future;
    }

    @Override
    public CompletionStage<Boolean> writeConfiguration(DataTransferForm form) {
        CompletableFuture<Boolean> future = new CompletableFuture<>();

        OcppProtocol protocol = chargePointRepository.findOcppProtocolByChargeBoxId(form.getChargeBoxId())
                .orElseThrow(() -> new IllegalArgumentException("Cannot determine OCPP protocol for " + form.getChargeBoxId()));

        try {
            Map<String, Object> requestData = mapFormToRequest(form);
            if (requestData.isEmpty()) {
                // Nothing to update
                future.complete(true);
                return future;
            }

            String jsonData = objectMapper.writeValueAsString(requestData);

            DataTransferParams params = new DataTransferParams();
            params.setChargePointSelectList(Collections.singletonList(new ChargePointSelect(protocol, form.getChargeBoxId())));
            params.setVendorId("setec001"); // Write operation
            params.setData(jsonData);
            
            log.info("Sending DataTransfer write request to {}: {}", form.getChargeBoxId(), params.getData());

            chargePointServiceClient.dataTransfer(params, new OcppCallback<>() {
                @Override
                public void success(String chargeBoxId, DataTransferTask.ResponseWrapper response) {
                    boolean success = DataTransferStatus.ACCEPTED.value().equals(response.getStatus());
                    future.complete(success);
                }

                @Override
                public void success(String chargeBoxId, OcppJsonError error) {
                    log.warn("DataTransfer write for {} failed with OCPP error: {}", chargeBoxId, error);
                    future.complete(false);
                }

                @Override
                public void failed(String chargeBoxId, Exception e) {
                    log.error("DataTransfer write failed for {}", chargeBoxId, e);
                    future.complete(false);
                }
            });

        } catch (Exception e) {
            log.error("Failed to initiate DataTransfer write for {}", form.getChargeBoxId(), e);
            future.completeExceptionally(e);
        }

        return future;
    }

    private DataTransferForm mapResponseToForm(String chargeBoxId, JsonNode data) {
        DataTransferForm form = new DataTransferForm();
        form.setChargeBoxId(chargeBoxId);

        // Map simple fields
        if (data.has("dcMaxVoltage")) form.setDcMaxVoltage(data.get("dcMaxVoltage").asInt());
        if (data.has("dcMinVoltage")) form.setDcMinVoltage(data.get("dcMinVoltage").asInt());
        if (data.has("dcMaxCurrent")) form.setDcMaxCurrent(data.get("dcMaxCurrent").asInt());
        if (data.has("moduleNumber")) form.setModuleNumber(data.get("moduleNumber").asInt());
        if (data.has("dcModuleType")) form.setDcModuleType(String.valueOf(data.get("dcModuleType").asInt()));
        if (data.has("appVersion")) form.setAppVersion(data.get("appVersion").asText());

        // Handle nested array for controlBoardVersion
        if (data.has("controlBoardVersion") && data.get("controlBoardVersion").isArray()) {
            for (JsonNode node : data.get("controlBoardVersion")) {
                int board = node.get("controlBoard").asInt();
                if (board == 1) {
                    if (node.has("controlBoardVersion")) form.setCb1Version(node.get("controlBoardVersion").asText());
                } else if (board == 2) {
                    if (node.has("controlBoardVersion")) form.setCb2Version(node.get("controlBoardVersion").asText());
                }
            }
        }

        // Handle nested array for calibrationVoltage
        if (data.has("calibrationVoltage") && data.get("calibrationVoltage").isArray()) {
            for (JsonNode node : data.get("calibrationVoltage")) {
                int board = node.get("controlBoard").asInt();
                if (board == 1) {
                    if (node.has("voltageK")) form.setCb1VoltageK(node.get("voltageK").asInt());
                    if (node.has("voltageB")) form.setCb1VoltageB(node.get("voltageB").asInt());
                } else if (board == 2) {
                    if (node.has("voltageK")) form.setCb2VoltageK(node.get("voltageK").asInt());
                    if (node.has("voltageB")) form.setCb2VoltageB(node.get("voltageB").asInt());
                }
            }
        }

        // Handle nested array for calibrationCurrent
        if (data.has("calibrationCurrent") && data.get("calibrationCurrent").isArray()) {
            for (JsonNode node : data.get("calibrationCurrent")) {
                int board = node.get("controlBoard").asInt();
                if (board == 1) {
                    if (node.has("currentK")) form.setCb1CurrentK(node.get("currentK").asInt());
                    if (node.has("currentB")) form.setCb1CurrentB(node.get("currentB").asInt());
                } else if (board == 2) {
                    if (node.has("currentK")) form.setCb2CurrentK(node.get("currentK").asInt());
                    if (node.has("currentB")) form.setCb2CurrentB(node.get("currentB").asInt());
                }
            }
        }

        // Handle channelsConfig
        if (data.has("channelsConfig") && data.get("channelsConfig").isArray()) {
            for (JsonNode node : data.get("channelsConfig")) {
                int connectorId = node.path("connectorId").asInt(0);
                int controlBoard = node.path("controlBoard").asInt(0);
                boolean enabled = node.path("enable").asBoolean(false);
                String channelType = node.path("channelType").asText("");

                if (controlBoard == 1) {
                    if (connectorId == 1) {
                        form.setCb1Gun1Enabled(enabled);
                        form.setCb1Gun1ChannelType(channelType);
                    } else if (connectorId == 2) {
                        form.setCb1Gun2Enabled(enabled);
                        form.setCb1Gun2ChannelType(channelType);
                    }
                } else if (controlBoard == 2) {
                    if (connectorId == 1) {
                        form.setCb2Gun1Enabled(enabled);
                        form.setCb2Gun1ChannelType(channelType);
                    } else if (connectorId == 2) {
                        form.setCb2Gun2Enabled(enabled);
                        form.setCb2Gun2ChannelType(channelType);
                    }
                }
            }
        }
        return form;
    }

    private Map<String, Object> mapFormToRequest(DataTransferForm form) {
        Map<String, Object> requestMap = new HashMap<>();

        // Add simple fields from the form, whether they are null or not
        requestMap.put("dcMaxVoltage", form.getDcMaxVoltage());
        requestMap.put("dcMinVoltage", form.getDcMinVoltage());
        requestMap.put("dcMaxCurrent", form.getDcMaxCurrent());
        requestMap.put("moduleNumber", form.getModuleNumber());

        if (form.getDcModuleType() != null && !form.getDcModuleType().isEmpty()) {
            try {
                requestMap.put("dcModuleType", Integer.parseInt(form.getDcModuleType()));
            } catch (NumberFormatException e) {
                log.warn("Invalid dcModuleType value: '{}', sending as null.", form.getDcModuleType());
                requestMap.put("dcModuleType", null);
            }
        } else {
            requestMap.put("dcModuleType", null);
        }

        // Build calibrationVoltage object and add if not empty
        List<Map<String, Object>> calVoltageList = new ArrayList<>();
        Map<String, Object> cb1Volt = new HashMap<>();
        cb1Volt.put("controlBoard", 1);
        if (form.getCb1VoltageK() != null) cb1Volt.put("voltageK", form.getCb1VoltageK());
        if (form.getCb1VoltageB() != null) cb1Volt.put("voltageB", form.getCb1VoltageB());
        if (cb1Volt.size() > 1) calVoltageList.add(cb1Volt);

        Map<String, Object> cb2Volt = new HashMap<>();
        cb2Volt.put("controlBoard", 2);
        if (form.getCb2VoltageK() != null) cb2Volt.put("voltageK", form.getCb2VoltageK());
        if (form.getCb2VoltageB() != null) cb2Volt.put("voltageB", form.getCb2VoltageB());
        if (cb2Volt.size() > 1) calVoltageList.add(cb2Volt);

        if (!calVoltageList.isEmpty()) requestMap.put("calibrationVoltage", calVoltageList);

        // Build calibrationCurrent object and add if not empty
        List<Map<String, Object>> calCurrentList = new ArrayList<>();
        Map<String, Object> cb1Curr = new HashMap<>();
        cb1Curr.put("controlBoard", 1);
        if (form.getCb1CurrentK() != null) cb1Curr.put("currentK", form.getCb1CurrentK());
        if (form.getCb1CurrentB() != null) cb1Curr.put("currentB", form.getCb1CurrentB());
        if (cb1Curr.size() > 1) calCurrentList.add(cb1Curr);

        Map<String, Object> cb2Curr = new HashMap<>();
        cb2Curr.put("controlBoard", 2);
        if (form.getCb2CurrentK() != null) cb2Curr.put("currentK", form.getCb2CurrentK());
        if (form.getCb2CurrentB() != null) cb2Curr.put("currentB", form.getCb2CurrentB());
        if (cb2Curr.size() > 1) calCurrentList.add(cb2Curr);
        
        if (!calCurrentList.isEmpty()) requestMap.put("calibrationCurrent", calCurrentList);
        
        // Build channelsConfig array and add if not empty
        List<Map<String, Object>> channelsConfigList = new ArrayList<>();
        
        Map<Integer, Integer> gunToBoardMap = new HashMap<>(); // connectorId -> controlBoard
        if (form.isCb1Gun1Enabled()) {
            gunToBoardMap.put(1, 1);
        } else if(form.isCb2Gun1Enabled()) {
            gunToBoardMap.put(1, 2);
        }

        if (form.isCb1Gun2Enabled()) {
            gunToBoardMap.put(2, 1);
        } else if (form.isCb2Gun2Enabled()) {
            gunToBoardMap.put(2, 2);
        }

        for (Map.Entry<Integer, Integer> entry : gunToBoardMap.entrySet()) {
            int connectorId = entry.getKey();
            int boardId = entry.getValue();
            Map<String, Object> channel = new HashMap<>();
            channel.put("connectorId", connectorId);
            channel.put("controlBoard", boardId);
            channel.put("enable", true);

            String channelType = "";
            if (boardId == 1) {
                channelType = (connectorId == 1) ? form.getCb1Gun1ChannelType() : form.getCb1Gun2ChannelType();
            } else { // boardId == 2
                channelType = (connectorId == 1) ? form.getCb2Gun1ChannelType() : form.getCb2Gun2ChannelType();
            }
            channel.put("channelType", channelType);
            channelsConfigList.add(channel);
        }

        if (!channelsConfigList.isEmpty()) {
            requestMap.put("channelsConfig", channelsConfigList);
        }
        
        return requestMap;
    }
} 