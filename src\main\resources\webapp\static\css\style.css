@charset "UTF-8";
@font-face{font-family:DroidSerif; src:url(DroidSerif.ttf);}

html, body { height: 100%; overflow: auto; }
#diffElements span a:link, #diffElements span  span a:visited { color:#000; text-decoration:none; cursor: default;}
#diffElements span a:hover, #diffElements span a:active { color:#ce2828; text-decoration:none; cursor: default;}
#diffElements input, #diffElements select {width: 130px;}
a:link, a:visited {color: #000; text-decoration: underline}
a:hover, a:active {color: #af6021; text-decoration: underline}

body {
	font-family: Verdana, sans-serif;
	font-size: 12px;
	background: #e0e6e9;
	padding: 0;
	margin: 0;
}
section {
	margin-bottom: 12px;
    background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAIAAACQd1PeAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAytpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNS1jMDE0IDc5LjE1MTQ4MSwgMjAxMy8wMy8xMy0xMjowOToxNSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnN0UmVmPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VSZWYjIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6MEI2QkFDMDQ3MDA1MTFFMzhFRTdFMUM3MjQzQjM3QzEiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6MEI2QkFDMDM3MDA1MTFFMzhFRTdFMUM3MjQzQjM3QzEiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIChNYWNpbnRvc2gpIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5kaWQ6MTczM2IzZGQtYTIxNS00MDk4LThhM2EtYzE1MzkyZGUzZmQwIiBzdFJlZjpkb2N1bWVudElEPSJ4bXAuZGlkOjE3MzNiM2RkLWEyMTUtNDA5OC04YTNhLWMxNTM5MmRlM2ZkMCIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/Ptw6u3kAAAAPSURBVHjaYjhz5gxAgAEABMwCZTQYvMQAAAAASUVORK5CYII=') repeat-x 50% 50%;
}
section span { color:#5c5c5c; background:#fff; padding-right:10px; font-family:DroidSerif; font-size: 14px; font-weight: normal;}
ul hr { color: #CCC; background: #CCC; 	height: 1px; border: 0;}
table { width: 100%;}
table.action td:last-child {
	border-left: 1px solid #CCC;
}
table.action th:last-child {
	border-left: 1px solid #CCC;
}
table.userInput th:first-child {
	text-align: right;
}
table.userInput td:first-child {
	text-align: right;
	width: 45%;
	padding: 3px;
}
table.userInputFullPage td:first-child {width: 50%; text-align: right; padding: 3px;}
table.sll { padding-bottom: 40px; }
table.res {
	border-collapse: collapse;
	table-layout:fixed;
}
table.res th {
	text-align: center;
	padding: 5px;
	background: #fff;
	border-bottom: double #CCC;
}
table.res th[data-sort],
#chargePointTable th[data-sort] {
	cursor:pointer;
}
table.res tr { border-bottom: 1px solid #CCC; }
table.res tr:nth-child(odd){ background: #fcfaf2; }

table.res tr:nth-child(even):hover {  background: #ebf4f9; }
table.res tr:nth-child(odd):hover {  background: #ebf4f9; }

table.res td {
	padding: 5px;
	text-align: center;
	width: auto;
	word-wrap: break-word;
}
/*** Table for charge point details ***/
table.cpd {
	border-collapse: collapse;
	width: 70%;
}
table.cpd th {
	text-align: left;
	padding: 5px 5px 5px 15px;
	background: #fff;
	border-bottom: double #CCC;
}
table.cpd tr { 
	border-bottom: 1px solid #CCC; 
}
table.cpd td {
	padding: 5px;
	text-align: center;
}
table.cpd td:first-child {
	text-align: left;
	width: 200px;
	padding-left: 15px;
	background: #f7f7f7;
}
/*** Fin ***/
select {
	width: 70%;
	font-size: 12px;
	margin: 1px 0 1px 0;
	padding: 2px;
	outline: none;
	border: 1px solid #CCC;
	border-radius: 0;
  	-webkit-border-radius: 0;
	box-sizing: border-box;
}
select option { font-size: 12px; }
input[type="text"], input[type="number"], input[type="password"], input[type="text"].dateTimePicker{
	appearance: none;
	-webkit-appearance: none;
	margin: 1px 3px 1px 0;
	padding: 3px;
	border: 1px solid #CCC;
	border-radius: 0;
 	width: 70%;
	font-size: 12px;
	box-sizing: border-box;
}

input[type="button"] {
	margin: 0 0 5px 5px;
    width: 100px;
	font-size:12px;
	height: 25px;
	-moz-border-radius:3px;
	-webkit-border-radius:3px;
	border-radius:3px;
	background:-webkit-gradient(linear, left top, left bottom, color-stop(0.05, #ededed), color-stop(1, #dfdfdf));
	background:-moz-linear-gradient(top, #ededed 5%, #dfdfdf 100%);
	background:-webkit-linear-gradient(top, #ededed 5%, #dfdfdf 100%);
	background:-o-linear-gradient(top, #ededed 5%, #dfdfdf 100%);
	background:-ms-linear-gradient(top, #ededed 5%, #dfdfdf 100%);
	background:linear-gradient(to bottom, #ededed 5%, #dfdfdf 100%);
	filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#ededed', endColorstr='#dfdfdf',GradientType=0);
	background-color:#ededed;
	border:1px solid #b0b0b0;
	display:inline-block;
	color:#575757;
	text-decoration:none;
	box-sizing: border-box;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}
input[type="button"]:hover {
	background:-webkit-gradient(linear, left top, left bottom, color-stop(0.05, #dfdfdf), color-stop(1, #ededed));
	background:-moz-linear-gradient(top, #dfdfdf 5%, #ededed 100%);
	background:-webkit-linear-gradient(top, #dfdfdf 5%, #ededed 100%);
	background:-o-linear-gradient(top, #dfdfdf 5%, #ededed 100%);
	background:-ms-linear-gradient(top, #dfdfdf 5%, #ededed 100%);
	background:linear-gradient(to bottom, #dfdfdf 5%, #ededed 100%);
	filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#dfdfdf', endColorstr='#ededed',GradientType=0);
	background-color:#dfdfdf;
}
input[type="button"]:active, input[type="submit"]:active {
	position:relative;
	top:1px;
}
input[type="submit"] {
	min-width: 100px;
	font-size: 12px;
	background: #397079;
	color: #fff;
    cursor: pointer;
	height: 25px;
	border: 0;
	border-radius: 3px;
	-moz-border-radius: 3px;
	-webkit-border-radius: 3px;
	box-sizing: border-box;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	padding: 0 8px;
}
input[type="submit"]:hover {
	background:#29575f;
}
input[type="submit"].blueSubmit {
	background:#397ac2;
	box-sizing: border-box;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}
input[type="submit"].blueSubmit:hover {
	background:#1e63b0;
}
input[type="submit"].redSubmit {
	background:#c14848;
	box-sizing: border-box;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}
input[type="submit"].redSubmit:hover {
	background:#af3232;
}
textarea {
	margin: 1px 3px 1px 0;
	padding: 3px;
	border: 1px solid #CCC;
	border-radius: 0;
	width: 70%;
	height: 6em; /* 6 rows */
	font-size: 12px;
	font-family: Verdana, sans-serif;
	resize: vertical;
	box-sizing: border-box;
}
#add_space { padding-top: 20px; }
.add-margin-bottom { margin-bottom: 20px; }
/*************** top menu navigation div ***************/
ul.navigation {
	color: #ccc;
	margin:0;
	position:relative;
	float:right;
}
ul.navigation li {
	display:inline;
	margin:0;
	padding:0;
	float:left;
	position:relative;
}

ul.navigation li a {
	padding-left:10px;
	padding-right:10px;
	line-height: 35px;
	text-decoration:none;
	display:inline-block;
	cursor: pointer;
}
ul.navigation li a:link, ul.navigation li a:visited { color:#CCC; }
ul.navigation li a:hover, ul.navigation li a:active { color:#fff; }
ul.navigation li:hover > ul {
	visibility:visible;
	opacity:1;
}
ul.navigation ul {
	list-style: none;
    margin: 0;
    padding: 0;    
    visibility:hidden;
    opacity:0;
    position: absolute;
    z-index: 99999;
    text-align: center;
}
ul.navigation ul li {
	background:#000;
	width:100%;
}
ul.navigation ul li a {
	text-decoration:none;
	display:inline-block;
    width:100%;
    padding: 0;
}
/*************** fin ***************/
.left-menu {
	float: left;
	width: 230px;
	height: 100%;
	margin: 0;
	padding: 0; 
	list-style-type: none;
}
.left-menu ul {
	list-style: none;
	margin: 0;
	padding: 0;
}
.left-menu li a {
	color:#000;
	display: block;
	voice-family: inherit;
	text-decoration: none;
    outline: none;
	background: #fff;
	padding: 7px;
}
.left-menu li a:link{
	color:#000;
	background: #fff;
}
.left-menu li a:hover{
	color:#000;
	background: #cddecf;
	border-radius: 2px 0 0 2px;
}
.left-menu li a.highlight {
	color:#000;
	background: #CCC;
	border-radius: 2px 0 0 2px;
}
.op-content {
	min-height: 350px;
	margin: 0 0 0 230px;
	padding: 0 0 0 12px;
	border-left: 1px solid #CCC;
}
.op15-content {
	min-height: 470px;
	margin: 0 0 0 230px;
	padding: 0 0 0 12px;
	border-left: 1px solid #CCC;
}
.op16-content {
	min-height: 650px;
	margin: 0;
	padding: 0;
	border-left: none;
}
.main { height: auto; min-height: 100%; }
.main-wrapper {
	height: auto; 
}
.top-banner {
	width: 100%;
	height: 80px;
	border-radius: 0;
	background: #71797d;
}
.top-menu {
	height: 35px;
	border-radius: 0;
	background: #000;
}
.container {
	width: 80%;
	margin-right: auto;
	margin-left: auto;
	*zoom: 1;
}
.content {
	position: relative;
	width: 80%;
	border: 1px solid #CCC;
	border-radius: 4px;
	background: #fff;
	margin: 12px auto;
	padding: 12px;
/* 	overflow: auto; so the size of the wrapper is always the size of the longest content */
}
.submit-button {
	margin-top: 20px;
}
.right-content { margin: 0 0 0 230px; border-left: 1px solid #CCC; padding-left:12px; }
.right-content div { display:none; }
.right-content div:first-child { display:block; }
.info, .warning, .error { text-align: left; border: 1px solid; margin-bottom: 10px; padding:15px 30px 15px 30px; }
.info { color: #00529B; background-color: #daecf4; }
.warning { color: #9F6000; background-color: #FEEFB3; }
.error {
    background-color: #f8d7da;
    color: #721c24;
    padding: 10px;
    margin-bottom: 20px;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
}
input:disabled { background-color:#dddddd; }
input[type="submit"]:disabled, input[type="submit"]:hover:disabled, input[type="submit"]:active:disabled {
	cursor: not-allowed;
	background:-webkit-gradient(linear, left top, left bottom, from(#a5a5a5), to(#5b5b5b));
	background:-moz-linear-gradient(top,  #a5a5a5,  #5b5b5b);
	background:-o-linear-gradient(top, #a5a5a5, #5b5b5b);
	background:-ms-linear-gradient(top, #a5a5a5, #5b5b5b);
}

a.tooltip { color: #5c5c5c; position: relative; text-decoration: none; }
a.tooltip span { display: none !important; }
a.tooltip:hover {cursor:default;}
a.tooltip:hover span {
  font-family: Verdana, sans-serif !important;
  position: absolute !important;
  z-index: 1001 !important;
  display: block !important;
  width: 250px !important;
  top: 2em !important;
  left: 1em !important;
  border: 1px solid #ccc !important;
  color: #00529B !important;
  background-color: #daecf4 !important;
  font-size: 12px !important;
  padding: 0.5em !important;
  border-radius: 4px !important;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15) !important;
}

/* 修复表格中tooltip的定位问题 */
td a.tooltip:hover span {
  top: -0.5em !important;
  left: 2em !important;
}

/* 修复flex容器中tooltip的定位问题 */
.form-control-plaintext + a.tooltip:hover span,
.form-check + a.tooltip:hover span {
  top: -1em !important;
  left: 1em !important;
}

/* 修复绝对定位的tooltip */
a.tooltip[style*="position: absolute"]:hover span {
  top: 1.5em !important;
  left: -200px !important;
}

/* 修复select框旁边的tooltip */
.form-control + a.tooltip:hover span {
  top: -2em !important;
  left: -200px !important;
}

/* 修复flex布局中tooltip在select框左边的情况 */
div[style*="display: flex"] a.tooltip:hover span {
  top: 2em !important;
  left: 0 !important;
  width: 200px !important;
}

.tileWrapper { text-align: center; }
.tileRow1 { width: 220px; }

.tileWrapper a:link, .tileWrapper a:visited {
	margin: 10px;
	padding-top: 15px;
	padding-bottom: 15px;
	border: 3px solid #fff;
	border-radius: 4px;
	display: inline-block;
	box-shadow: 2px 2px 5px #969696;
	font-size: 14px;
	font-weight: bold;
	vertical-align: top;
	text-decoration: none;
	background-color: #f7f7f7;
	color: #504f50;
}
.tileWrapper a:hover, .tileWrapper a:active {
	background-color: #eeeeee;
}
span.baseTable { 
	display: table;  
	text-align:left; 
	width:100%; 
	margin-top: 10px;
}
span.baseRow { 
	display: table-row; 
	font-weight: normal;
	line-height: 30px;
}
span.baseCell { 
	display: table-cell; 
}
span.baseCell:first-child {
	width:62%;
	text-align: right; 
}
span.baseCell:nth-child(2) {
	padding-left: 5px;
}
span.base { 
	margin-top: 10px;
	margin-bottom: 10px;
	display: block;
	font-weight: normal;
}
span.formatNumber {
	font-size: 18px;
	color: #a36b83;
	font-weight: bold;
}
table.res th.sorting-asc, table.res th.sorting-desc,
#chargePointTable th.sorting-asc, #chargePointTable th.sorting-desc {
	background: #CCC;
	border-radius: 5px 5px 0 0;
}
input, select, textarea {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
}
.inline {
	display:inline-block;
}

/* 为Owner Assignment页面添加特定样式 */
.userDetails {
    width: 100%;
    margin-bottom: 20px;
    border-collapse: collapse;
}

.userDetails td {
    padding: 5px 10px;
    border-bottom: 1px solid #ddd;
}

.userDetails td:first-child {
    font-weight: bold;
    width: 200px;
}

.info {
    margin-bottom: 30px;
}

.info h3 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #214478;
}

/* Improve table readability */
#assignedTable, #unassignedTable {
    width: 100%;
    margin-bottom: 15px;
}

#assignedTable th, #unassignedTable th,
#assignedTable td, #unassignedTable td {
    padding: 8px;
    text-align: left;
}

#assignedTable th, #unassignedTable th {
    background-color: #f2f2f2;
}

/* Error message style */
.error {
    background-color: #f8d7da;
    color: #721c24;
    padding: 10px;
    margin-bottom: 20px;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
}

/* Ensure the checkboxes and buttons are vertically centered */
th input[type="checkbox"] {
    vertical-align: middle;
    margin-right: 5px;
}

/* Ensure table contents are vertically centered */
tr td {
    vertical-align: middle;
}

/* File upload button style */
.file-input-container {
    position: relative;
    margin-bottom: 10px;
}

.file-input-container input[type="file"] {
    padding: 8px;
    border-radius: 4px;
    border: 1px solid #ccc;
    background-color: #f9f9f9;
    width: auto;
}

.file-input-container input[type="file"]::-webkit-file-upload-button {
    background-color: #4e73df;
    color: white;
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    margin-right: 10px;
    cursor: pointer;
}

.file-input-container input[type="file"]::-webkit-file-upload-button:hover {
    background-color: #375bc8;
}

.image-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 15px;
    padding: 15px;
    border: 1px dashed #ccc;
    min-height: 50px;
    background-color: #f9f9f9;
}

/* OCPP v1.6导航样式 */
.ocpp-v16-subnav {
    margin-bottom: 20px;
    background-color: #f5f5f5;
    border-radius: 4px;
    padding: 10px;
    border: 1px solid #ddd;
}

.ocpp-v16-subnav ul {
    display: flex;
    flex-wrap: wrap;
    list-style: none;
    padding: 0;
    margin: 0;
}

.ocpp-v16-subnav li {
    margin: 5px;
}

.ocpp-v16-subnav a {
    display: block;
    padding: 8px 15px;
    background-color: #fff;
    color: #337ab7;
    text-decoration: none;
    border-radius: 4px;
    border: 1px solid #ddd;
    transition: all 0.3s ease;
}

.ocpp-v16-subnav a:hover, 
.ocpp-v16-subnav a.active {
    background-color: #337ab7;
    color: #fff;
    border-color: #337ab7;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .ocpp-v16-subnav ul {
        flex-direction: column;
    }
    
    .ocpp-v16-subnav a {
        text-align: center;
    }
}

/* 优化ChangeConfiguration页面的表单布局 */
.config-form-container {
    width: 90%;
    padding: 20px 0 20px 50px;
    margin: 0;
}

.config-form {
    width: 100%;
}

.config-form td:first-child {
    width: 25%;
    text-align: right;
    padding-right: 20px;
    vertical-align: middle;
}

.config-form td:nth-child(2) {
    width: 70%;
    text-align: left;
    padding-left: 10px;
}

.config-form select,
.config-form input[type="text"] {
    width: 80%;
    min-width: 300px;
}

.config-form .submit-button {
    text-align: right;
    margin-top: 15px;
}

/* ChangeConfiguration页面特定样式 */
.change-config-page {
    border-left: none !important;
    margin-left: 0 !important;
}

.change-config-page .config-form-container {
    padding-left: 30px;
}


/* Styles for firmwareUpdate.jsp Parameters section */
.parameters-section .form-group {
    margin-bottom: 15px;
}

.parameters-section .form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.parameters-section .form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-sizing: border-box; /* Ensures padding and border don't add to the width */
}

.parameters-section .form-control-file {
    display: block;
    width: 100%;
    padding: 8px 12px;
    /* Basic styling for file input, can be further improved if needed */
}

.parameters-section .submit-group {
    margin-top: 20px;
    text-align: right; /* Align button to the right, or 'left'/'center' as preferred */
}

/* Ensure the .btn-primary class from existing styles is applied correctly */
.parameters-section .submit-button .btn-primary {
    padding: 10px 20px;
    /* Add other styles if needed to match existing buttons */
}

/* Adjust dateTimePicker width if it's too narrow */
.parameters-section .dateTimePicker {
    width: auto; /* Or a specific width like '200px' if needed */
}


/* Ensure dateTimePicker within parameters section also takes full width */
.parameters-section .form-control.dateTimePicker {
    width: 100% !important; /* Force width to 100% */
}
