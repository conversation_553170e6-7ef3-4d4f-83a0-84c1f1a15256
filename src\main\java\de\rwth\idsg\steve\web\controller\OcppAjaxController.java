/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.web.controller;

import de.rwth.idsg.steve.SteveConfiguration;
import de.rwth.idsg.steve.ocpp.OcppCallback;
import de.rwth.idsg.steve.ocpp.RequestResult;
import de.rwth.idsg.steve.ocpp.ws.data.OcppJsonError;
import de.rwth.idsg.steve.repository.UpdateFirmwareLogRepository;
import de.rwth.idsg.steve.service.ChargePointServiceClient;
import de.rwth.idsg.steve.service.DiagnosticsTaskManager;
import de.rwth.idsg.steve.service.DiagnosticsTaskInfo;
import de.rwth.idsg.steve.service.FirmwareFileService;
import de.rwth.idsg.steve.service.PermissionService;
import de.rwth.idsg.steve.utils.DateTimeUtils;
import de.rwth.idsg.steve.web.dto.ocpp.ClearChargingProfileParams;
import de.rwth.idsg.steve.web.dto.ocpp.GetCompositeScheduleParams;
import de.rwth.idsg.steve.web.dto.ocpp.GetDiagnosticsParams;
import de.rwth.idsg.steve.web.dto.ocpp.SetChargingProfileParams;
import de.rwth.idsg.steve.web.dto.ocpp.TriggerMessageParams;
import de.rwth.idsg.steve.web.dto.ocpp.UpdateFirmwareParams;
import de.rwth.idsg.steve.web.dto.UpdateFirmwareLog;
import lombok.extern.slf4j.Slf4j;
import ocpp.cp._2015._10.GetCompositeScheduleResponse;
import ocpp.cs._2015._10.ChargePointStatus;
import org.joda.time.DateTime;
import org.jooq.DSLContext;
import org.jooq.Record;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.util.StringUtils;

import jakarta.servlet.ServletContext;
import jakarta.validation.Valid;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static org.jooq.impl.DSL.field;
import static org.jooq.impl.DSL.table;
import de.rwth.idsg.steve.SteveConfiguration;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.Enumeration;

/**
 * REST控制器，用于处理OCPP操作的AJAX请求
 */
@Slf4j
@RestController
@RequestMapping(value = "/manager/operations/ajax/{opVersion}")
public class OcppAjaxController {

    @Autowired private ChargePointServiceClient chargePointServiceClient;
    @Autowired private UpdateFirmwareLogRepository updateFirmwareLogRepository;
    @Autowired private DSLContext ctx;
    @Autowired private ServletContext servletContext;
    @Autowired private DiagnosticsTaskManager diagnosticsTaskManager;
    @Autowired private FirmwareFileService firmwareFileService;
    @Autowired private PermissionService permissionService;

    // -------------------------------------------------------------------------\
    // Paths
    // -------------------------------------------------------------------------

    private static final String TRIGGER_MESSAGE_PATH = "/TriggerMessage";
    private static final String CLEAR_CHARGING_PATH = "/ClearChargingProfile";
    private static final String SET_CHARGING_PATH = "/SetChargingProfile";
    private static final String GET_COMPOSITE_PATH = "/GetCompositeSchedule";
    private static final String UPDATE_FIRMWARE_PATH = "/UpdateFirmware";
    private static final String GET_DIAGNOSTICS_PATH = "/GetDiagnostics";
    // private static final String FIRMWARE_UPLOAD_DIR = "/uploaded_firmware/"; // No longer used for physical path

    // -------------------------------------------------------------------------\
    // Http methods
    // -------------------------------------------------------------------------

    @RequestMapping(value = TRIGGER_MESSAGE_PATH, method = RequestMethod.POST,
                  produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Map<String, Object>> triggerMessage(@Valid TriggerMessageParams params) {
        try {
            CompletableFuture<Boolean> future = new CompletableFuture<>();

            OcppCallback<String> callback = new OcppCallback<String>() {
                @Override
                public void success(String chargeBoxId, String response) {
                    log.info("TriggerMessage success for {}: {}", chargeBoxId, response);
                    future.complete(true);
                }

                @Override
                public void success(String chargeBoxId, OcppJsonError error) {
                    log.error("TriggerMessage received JSON error for {}: {}", chargeBoxId, error.toString());
                    future.complete(false);
                }

                @Override
                public void failed(String chargeBoxId, Exception e) {
                    log.error("TriggerMessage failed for {}: {}", chargeBoxId, e.getMessage());
                    future.complete(false);
                }
            };

            chargePointServiceClient.triggerMessage(params, callback);

            boolean success = false;
            try {
                success = future.get(15, TimeUnit.SECONDS);
            } catch (InterruptedException | ExecutionException | TimeoutException e) {
                log.error("Error waiting for TriggerMessage response: {}", e.getMessage());
                future.complete(false);
            }

            Map<String, Object> result = new HashMap<>();
            result.put("success", success);
            if (!success) {
                result.put("errorMessage", "发送触发消息请求失败");
            } else {
                result.put("message", "操作成功完成");
            }
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("处理AJAX请求时发生异常", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("errorMessage", "处理请求时发生错误: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }

    @RequestMapping(value = SET_CHARGING_PATH, method = RequestMethod.POST,
                  produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Map<String, Object>> setChargingProfile(@Valid SetChargingProfileParams params) {
        try {
            CompletableFuture<Boolean> future = new CompletableFuture<>();

            OcppCallback<String> callback = new OcppCallback<String>() {
                @Override
                public void success(String chargeBoxId, String response) {
                    log.info("SetChargingProfile success for {}: {}", chargeBoxId, response);
                    future.complete(true);
                }

                @Override
                public void success(String chargeBoxId, OcppJsonError error) {
                    log.error("SetChargingProfile received JSON error for {}: {}", chargeBoxId, error.toString());
                    future.complete(false);
                }

                @Override
                public void failed(String chargeBoxId, Exception e) {
                    log.error("SetChargingProfile failed for {}: {}", chargeBoxId, e.getMessage());
                    future.complete(false);
                }
            };

            chargePointServiceClient.setChargingProfile(params, callback);

            boolean success = false;
            try {
                success = future.get(15, TimeUnit.SECONDS);
            } catch (InterruptedException | ExecutionException | TimeoutException e) {
                log.error("Error waiting for SetChargingProfile response: {}", e.getMessage());
                future.complete(false);
            }

            Map<String, Object> result = new HashMap<>();
            result.put("success", success);
            if (!success) {
                result.put("errorMessage", "设置充电配置文件请求失败");
            } else {
                result.put("message", "操作成功完成");
            }
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("处理AJAX请求时发生异常", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("errorMessage", "处理请求时发生错误: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }

    @RequestMapping(value = CLEAR_CHARGING_PATH, method = RequestMethod.POST,
                  produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Map<String, Object>> clearChargingProfile(@Valid ClearChargingProfileParams params) {
         try {
            CompletableFuture<Boolean> future = new CompletableFuture<>();

            OcppCallback<String> callback = new OcppCallback<String>() {
                @Override
                public void success(String chargeBoxId, String response) {
                    log.info("ClearChargingProfile success for {}: {}", chargeBoxId, response);
                    future.complete(true);
                }

                @Override
                public void success(String chargeBoxId, OcppJsonError error) {
                    log.error("ClearChargingProfile received JSON error for {}: {}", chargeBoxId, error.toString());
                    future.complete(false);
                }

                @Override
                public void failed(String chargeBoxId, Exception e) {
                    log.error("ClearChargingProfile failed for {}: {}", chargeBoxId, e.getMessage());
                    future.complete(false);
                }
            };

            chargePointServiceClient.clearChargingProfile(params, callback);

            boolean success = false;
            try {
                success = future.get(15, TimeUnit.SECONDS);
            } catch (InterruptedException | ExecutionException | TimeoutException e) {
                log.error("Error waiting for ClearChargingProfile response: {}", e.getMessage());
                future.complete(false);
            }

            Map<String, Object> result = new HashMap<>();
            result.put("success", success);
            if (!success) {
                result.put("errorMessage", "清除充电配置文件请求失败");
            } else {
                result.put("message", "操作成功完成");
            }
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("处理AJAX请求时发生异常", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("errorMessage", "处理请求时发生错误: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }

    @RequestMapping(value = GET_COMPOSITE_PATH, method = RequestMethod.POST,
                  produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Map<String, Object>> getCompositeSchedule(@Valid GetCompositeScheduleParams params) {
        try {
            CompletableFuture<Boolean> future = new CompletableFuture<>();

            OcppCallback<GetCompositeScheduleResponse> callback = new OcppCallback<GetCompositeScheduleResponse>() {
                @Override
                public void success(String chargeBoxId, GetCompositeScheduleResponse response) {
                    log.info("GetCompositeSchedule success for {}: {}", chargeBoxId, response);
                    future.complete(true);
                }

                @Override
                public void success(String chargeBoxId, OcppJsonError error) {
                    log.error("GetCompositeSchedule received JSON error for {}: {}", chargeBoxId, error.toString());
                    future.complete(false);
                }

                @Override
                public void failed(String chargeBoxId, Exception e) {
                    log.error("GetCompositeSchedule failed for {}: {}", chargeBoxId, e.getMessage());
                    future.complete(false);
                }
            };

            chargePointServiceClient.getCompositeSchedule(params, callback);

            boolean success = false;
            try {
                success = future.get(15, TimeUnit.SECONDS);
            } catch (InterruptedException | ExecutionException | TimeoutException e) {
                log.error("Error waiting for GetCompositeSchedule response: {}", e.getMessage());
                future.complete(false);
            }

            Map<String, Object> result = new HashMap<>();
            result.put("success", success);
            if (!success) {
                result.put("errorMessage", "获取综合时间表请求失败");
            } else {
                result.put("message", "操作成功完成");
            }
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("处理AJAX请求时发生异常", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("errorMessage", "处理请求时发生错误: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }

    @RequestMapping(value = UPDATE_FIRMWARE_PATH, method = RequestMethod.POST,
                  produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Map<String, Object>> updateFirmware(@Valid UpdateFirmwareParams params,
                                                              @RequestParam("firmwareFile") MultipartFile firmwareFile) {
        log.info("UpdateFirmware request received. Params: {}, File: {}", params, firmwareFile.getOriginalFilename());

        if (firmwareFile.isEmpty()) {
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("errorMessage", "Firmware file is empty.");
            return ResponseEntity.badRequest().body(result);
        }

        String fileName = StringUtils.cleanPath(firmwareFile.getOriginalFilename());
        Integer currentUserPk = null;

        try {
            // 获取当前用户ID
            currentUserPk = permissionService.getCurrentUser().getWebUserPk();
        } catch (Exception e) {
            log.warn("Failed to get current user for firmware upload", e);
        }

        try {
            // Use physical path from configuration
            String uploadDirPath = SteveConfiguration.CONFIG.getPhysicalFirmwareUploadPath();
            log.info("Attempting to use physical upload path: {}", uploadDirPath);

            Path uploadPath = Paths.get(uploadDirPath);
            if (Files.notExists(uploadPath)) {
                Files.createDirectories(uploadPath);
                log.info("Upload directory {} created.", uploadDirPath);
            } else {
                log.info("Upload directory {} already exists.", uploadDirPath);
            }

            Path destinationPath = uploadPath.resolve(fileName);
            log.info("Attempting to copy uploaded file {} to path: {}", fileName, destinationPath);

            Files.copy(firmwareFile.getInputStream(), destinationPath, StandardCopyOption.REPLACE_EXISTING);
            log.info("Successfully copied file {} to {}", fileName, destinationPath);

            // 处理固件更新请求
            ResponseEntity<Map<String, Object>> response = processFirmwareUpdate(params, destinationPath.toFile());

            // 检查固件更新是否成功
            Map<String, Object> responseBody = response.getBody();
            boolean isSuccess = responseBody != null && Boolean.TRUE.equals(responseBody.get("success"));

            if (isSuccess) {
                // Firmware update request sent successfully, record as pending
                try {
                    firmwareFileService.recordPendingUpload(fileName, firmwareFile.getSize(), currentUserPk);
                    log.info("Successfully recorded pending firmware file upload: {}", fileName);
                } catch (Exception e) {
                    log.error("Failed to record pending firmware upload: " + fileName, e);
                    // Don't affect main flow, just log error
                }
            } else {
                // 固件更新失败，记录失败并清理文件
                String errorMessage = responseBody != null ? (String) responseBody.get("errorMessage") : "Unknown error";
                try {
                    firmwareFileService.recordFailedUpload(fileName, currentUserPk, errorMessage);
                    log.info("Recorded failed firmware upload and cleaned up file: {}", fileName);
                } catch (Exception e) {
                    log.error("Failed to record failed firmware upload: " + fileName, e);
                }
            }

            return response;

        } catch (IOException e) {
            log.error("Failed to upload firmware file", e);

            // 记录上传失败
            try {
                firmwareFileService.recordFailedUpload(fileName, currentUserPk, "File upload failed: " + e.getMessage());
            } catch (Exception ex) {
                log.error("Failed to record failed firmware upload: " + fileName, ex);
            }

            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("errorMessage", "Failed to upload firmware file: " + e.getMessage());
            return ResponseEntity.status(500).body(result);
        }
    }

    /**
     * This method contains the core logic for processing a firmware update request.
     * It can be called from different controllers, either with a freshly uploaded file
     * or with a file that already exists on the server.
     *
     * @param params       The parameters for the UpdateFirmware request.
     * @param firmwareFile The firmware file on the server's file system.
     * @return A ResponseEntity with the outcome of the operation.
     */
    public ResponseEntity<Map<String, Object>> processFirmwareUpdate(@Valid UpdateFirmwareParams params,
                                                                     File firmwareFile) {
        Map<String, Object> result = new ConcurrentHashMap<>();
        String location;

        try {
            String ftpIp = SteveConfiguration.CONFIG.getFtpIp();
            if ("auto".equalsIgnoreCase(ftpIp) || !StringUtils.hasText(ftpIp)) {
                log.info("Attempting to dynamically determine internal IP for FTP as ftpIp property is 'auto'.");
                ftpIp = getInternalIpAddress();
                if (ftpIp == null) {
                    throw new IOException("Could not automatically determine a site-local IP address for FTP.");
                }
                log.info("Dynamically determined internal IP for FTP: {}", ftpIp);
            }

            int ftpPort = SteveConfiguration.CONFIG.getFtpPort();
            String ftpUser = SteveConfiguration.CONFIG.getFtpUsername();
            String ftpPassword = SteveConfiguration.CONFIG.getFtpPassword();
            // Use ftpBasePath and ensure it is correctly formatted
            String ftpBasePath = SteveConfiguration.CONFIG.getFtpBasePath();
            if (!ftpBasePath.startsWith("/")) {
                ftpBasePath = "/" + ftpBasePath;
            }
            if (!ftpBasePath.endsWith("/")) {
                ftpBasePath = ftpBasePath + "/";
            }

            location = String.format("ftp://%s:%s@%s:%d%s%s", ftpUser, ftpPassword, ftpIp, ftpPort, ftpBasePath, firmwareFile.getName());
            log.info("Firmware file accessible at FTP URL: {}", location);
        } catch (IOException e) {
            log.error("Failed to construct FTP URL", e);
            result.put("success", false);
            result.put("errorMessage", "Failed to construct FTP URL: " + e.getMessage());
            return ResponseEntity.status(500).body(result);
        }

        params.setLocation(location);
        log.info("Processing UpdateFirmware request post-file-upload. Location: {}, Params: {}", location, params);

        List<de.rwth.idsg.steve.repository.dto.ChargePointSelect> chargePointList = params.getChargePointSelectList();
        if (chargePointList == null || chargePointList.isEmpty()) {
            result.put("success", false);
            result.put("errorMessage", "No charge points specified.");
            return ResponseEntity.badRequest().body(result);
        }

        int totalCount = chargePointList.size();
        Integer batchId = (totalCount > 1) ? createUpdateFirmwareBatch(totalCount) : null;
        if (batchId != null) {
            result.put("batchId", batchId);
        }

        final AtomicInteger successCount = new AtomicInteger(0);
        final AtomicInteger errorCount = new AtomicInteger(0);
        final CompletableFuture<Void> allDone = new CompletableFuture<>();
        final Map<String, String> individualResults = new ConcurrentHashMap<>();

        OcppCallback<String> callback = new OcppCallback<String>() {
            private final AtomicInteger pendingCount = new AtomicInteger(totalCount);

            @Override
            public void success(String cbId, String response) {
                log.info("UpdateFirmware command successfully sent to {}: {}", cbId, response);
                successCount.incrementAndGet();
                individualResults.put(cbId, "Success: " + response);
                recordFirmwareUpdateLog(batchId, params, cbId, "Sent", response);
                if (pendingCount.decrementAndGet() == 0) {
                    allDone.complete(null);
                }
            }

            @Override
            public void success(String cbId, OcppJsonError error) {
                String errorMessage = "Received OCPP error: " + error.getErrorCode() + " - " + error.getErrorDescription();
                log.error("UpdateFirmware failed for {}: {}", cbId, errorMessage);
                errorCount.incrementAndGet();
                individualResults.put(cbId, "Failed: " + errorMessage);
                recordFirmwareUpdateLog(batchId, params, cbId, "Error", errorMessage);
                if (pendingCount.decrementAndGet() == 0) {
                    allDone.complete(null);
                }
            }

            @Override
            public void failed(String cbId, Exception e) {
                String exceptionMessage = "Exception: " + e.getMessage();
                log.error("UpdateFirmware failed for {}", cbId, e);
                errorCount.incrementAndGet();
                individualResults.put(cbId, "Failed: " + exceptionMessage);
                recordFirmwareUpdateLog(batchId, params, cbId, "Error", exceptionMessage);
                if (pendingCount.decrementAndGet() == 0) {
                    allDone.complete(null);
                }
            }
        };

        int taskId = chargePointServiceClient.updateFirmware(params, callback);

        // We don't wait for allDone here in a web request context to avoid blocking.
        // The front-end can poll for status using the batchId if necessary.

        String message = String.format("UpdateFirmware commands initiated for %d stations (Task ID: %d, Batch ID: %s).",
                totalCount, taskId, batchId != null ? batchId : "N/A");

        result.put("success", true);
        result.put("message", message);
        result.put("results", individualResults);
        return ResponseEntity.ok(result);
    }

    @RequestMapping(value = GET_DIAGNOSTICS_PATH, method = RequestMethod.POST,
                  produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Map<String, Object>> getDiagnostics(@Valid GetDiagnosticsParams params,
                                                              @RequestParam(value = "diagnosticFile", required = false) MultipartFile diagnosticFile,
                                                              HttpServletRequest request) {
        log.info("🚀🚀🚀 AJAX GetDiagnostics request received! URL: {}, Method: {}", request.getRequestURL(), request.getMethod());
        log.info("🚀🚀🚀 Request parameters: {}", request.getParameterMap().keySet());
        log.info("GetDiagnostics request received. Params: {}, File: {}", params,
                diagnosticFile != null ? diagnosticFile.getOriginalFilename() : "none");

        Map<String, Object> result = new HashMap<>();

        // If a diagnostic file is uploaded, save it directly to logs directory
        if (diagnosticFile != null && !diagnosticFile.isEmpty()) {
            try {
                // Use physical path from configuration for diagnostics
                String uploadDirPath = SteveConfiguration.CONFIG.getPhysicalDiagnosticsUploadPath();
                log.info("Attempting to use physical diagnostics upload path: {}", uploadDirPath);

                Path uploadPath = Paths.get(uploadDirPath);
                if (Files.notExists(uploadPath)) {
                    Files.createDirectories(uploadPath);
                    log.info("Diagnostics upload directory {} created.", uploadDirPath);
                } else {
                    log.info("Diagnostics upload directory {} already exists.", uploadDirPath);
                }

                String fileName = StringUtils.cleanPath(diagnosticFile.getOriginalFilename());
                // Add timestamp to avoid filename conflicts
                String timestamp = java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
                String nameWithoutExt = fileName.contains(".") ? fileName.substring(0, fileName.lastIndexOf(".")) : fileName;
                String extension = fileName.contains(".") ? fileName.substring(fileName.lastIndexOf(".")) : "";
                String finalFileName = nameWithoutExt + "_" + timestamp + extension;

                Path destinationPath = uploadPath.resolve(finalFileName);
                log.info("Attempting to copy uploaded diagnostic file {} to path: {}", finalFileName, destinationPath);

                Files.copy(diagnosticFile.getInputStream(), destinationPath, StandardCopyOption.REPLACE_EXISTING);
                log.info("Successfully copied diagnostic file {} to {}", finalFileName, destinationPath);

                result.put("success", true);
                result.put("message", "Diagnostic file uploaded successfully: " + finalFileName);
                result.put("fileName", finalFileName);
                return ResponseEntity.ok(result);

            } catch (IOException e) {
                log.error("Failed to upload diagnostic file", e);
                result.put("success", false);
                result.put("errorMessage", "Failed to upload diagnostic file: " + e.getMessage());
                return ResponseEntity.status(500).body(result);
            }
        }

        // If no file uploaded, proceed with traditional GetDiagnostics request to charge point
        List<de.rwth.idsg.steve.repository.dto.ChargePointSelect> chargePointList = params.getChargePointSelectList();
        if (chargePointList == null || chargePointList.isEmpty()) {
            result.put("success", false);
            result.put("errorMessage", "No charge points specified and no diagnostic file uploaded.");
            return ResponseEntity.badRequest().body(result);
        }

        try {
            // Generate FTP location for charge point to upload diagnostics
            String location = generateGetDiagnosticsLocation();
            params.setLocation(location);

            // 处理日期范围
            if (params.getStartDate() != null && params.getStopDate() != null) {
                log.info("Date range specified: {} to {}", params.getStartDate(), params.getStopDate());
                // 将日期转换为日期时间范围
                params.setStart(params.getStartDate().toLocalDateTime(org.joda.time.LocalTime.MIDNIGHT));
                params.setStop(params.getStopDate().toLocalDateTime(new org.joda.time.LocalTime(23, 59, 59)));
                log.info("Converted to time range: {} to {}", params.getStart(), params.getStop());
            }

            // 检查是否有单个充电桩的请求，如果是，尝试复用现有任务
            log.info("🔧 OcppAjaxController.getDiagnostics: checking for task reuse, chargePointCount={}",
                    chargePointList.size());

            if (chargePointList.size() == 1) {
                String chargeBoxId = chargePointList.get(0).getChargeBoxId();
                log.info("🔧 Single charge point request for: {}", chargeBoxId);

                DiagnosticsTaskInfo existingTask = diagnosticsTaskManager.getValidTask(chargeBoxId);

                if (existingTask != null) {
                    log.info("🔄 Reusing existing diagnostics task for {}: taskId={}, remaining={}min",
                            chargeBoxId, existingTask.getTaskId(), existingTask.getRemainingMinutes());

                    String reuseMessage = String.format(
                        "Found existing diagnostics task for charge point %s. Task ID: %d. " +
                        "Remaining time: %d minutes. Using existing task.",
                        chargeBoxId, existingTask.getTaskId(), existingTask.getRemainingMinutes());

                    result.put("success", true);
                    result.put("message", reuseMessage);
                    result.put("taskId", existingTask.getTaskId());
                    result.put("reused", true);
                    return ResponseEntity.ok(result);
                } else {
                    log.info("🔧 No existing valid task found for {}, will create new task", chargeBoxId);
                }
            } else {
                log.info("🔧 Multiple charge points selected, skipping task reuse logic");
            }

            int taskId = chargePointServiceClient.getDiagnostics(params);

            // 注册新任务到任务管理器（仅对单个充电桩）
            if (chargePointList.size() == 1) {
                String chargeBoxId = chargePointList.get(0).getChargeBoxId();
                log.info("🔧 Registering new task for {}: taskId={}", chargeBoxId, taskId);
                diagnosticsTaskManager.registerTask(chargeBoxId, taskId);
            } else {
                log.info("🔧 Multiple charge points, not registering task in manager");
            }

            String message = String.format("GetDiagnostics request sent successfully to %d charge point(s). Task ID: %d. " +
                    "Diagnostic files will be uploaded to the FTP server logs directory.",
                    chargePointList.size(), taskId);

            result.put("success", true);
            result.put("message", message);
            result.put("taskId", taskId);
            result.put("reused", false);
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("Failed to process GetDiagnostics request", e);
            result.put("success", false);
            result.put("errorMessage", "Failed to process GetDiagnostics request: " + e.getMessage());
            return ResponseEntity.status(500).body(result);
        }
    }

    private String generateGetDiagnosticsLocation() throws IOException {
        String ftpIp = SteveConfiguration.CONFIG.getFtpIp();

        if ("auto".equalsIgnoreCase(ftpIp) || !StringUtils.hasText(ftpIp)) {
            log.info("Attempting to dynamically determine internal IP for FTP as ftpIp property is 'auto'.");
            ftpIp = getInternalIpAddress();
            if (ftpIp == null) {
                throw new IOException("Could not automatically determine a site-local IP address for FTP.");
            }
            log.info("Dynamically determined internal IP for FTP: {}", ftpIp);
        }

        int ftpPort = SteveConfiguration.CONFIG.getFtpPort();
        String ftpUser = SteveConfiguration.CONFIG.getFtpUsername();
        String ftpPassword = SteveConfiguration.CONFIG.getFtpPassword();

        // Use /logs/ path for diagnostic logs
        String ftpLogsPath = "/logs/";

        String location = String.format("ftp://%s:%s@%s:%d%s", ftpUser, ftpPassword, ftpIp, ftpPort, ftpLogsPath);
        log.info("Generated GetDiagnostics FTP URL: {}", location);
        return location;
    }

    // Helper method to get internal IP address
    private String getInternalIpAddress() throws SocketException {
        Enumeration<NetworkInterface> nics = NetworkInterface.getNetworkInterfaces();
        while (nics.hasMoreElements()) {
            NetworkInterface nic = nics.nextElement();
            if (nic.isUp() && !nic.isLoopback() && !nic.isVirtual()) {
                Enumeration<InetAddress> addrs = nic.getInetAddresses();
                while (addrs.hasMoreElements()) {
                    InetAddress addr = addrs.nextElement();
                    if (addr instanceof java.net.Inet4Address && addr.isSiteLocalAddress()) {
                        log.info("Found site-local IPv4 address: {} for interface: {}", addr.getHostAddress(), nic.getDisplayName());
                        return addr.getHostAddress();
                    }
                }
            }
        }
        // Fallback: if no site-local found, try to find any non-loopback IPv4
        log.warn("No site-local IPv4 address found. Attempting to find first non-loopback IPv4 address.");
        nics = NetworkInterface.getNetworkInterfaces(); // Re-iterate
        while (nics.hasMoreElements()) {
            NetworkInterface nic = nics.nextElement();
            if (nic.isUp() && !nic.isLoopback() && !nic.isVirtual()) {
                Enumeration<InetAddress> addrs = nic.getInetAddresses();
                while (addrs.hasMoreElements()) {
                    InetAddress addr = addrs.nextElement();
                    if (addr instanceof java.net.Inet4Address) {
                        log.info("Using first available non-loopback IPv4 address: {} for interface: {}", addr.getHostAddress(), nic.getDisplayName());
                        return addr.getHostAddress();
                    }
                }
            }
        }
        log.error("Could not determine a suitable internal IP address.");
        return null;
    }

    // createUpdateFirmwareBatch, recordFirmwareUpdateLog, updateBatchStatus methods remain as they were in the provided file content.
    // I will copy them from the provided file content to ensure they are unchanged if they were correct.
    // Based on the mcp_desktop-commander_read_file output, these methods are:
    
    /**
     * 创建固件升级批处理记录
     *
     * @param totalCount 批处理中的总数量
     * @return 批处理ID
     */
    private Integer createUpdateFirmwareBatch(int totalCount) {
        try {
            // 使用最小必要字段创建批处理记录，避免可能的问题
            Record record = ctx.insertInto(table("update_firmware_batch"))
                    .set(field("total_count"), totalCount)
                    .set(field("pending_count"), totalCount)
                    .set(field("success_count"), 0)
                    .set(field("error_count"), 0)
                    .set(field("status"), "INPROGRESS")
                    .set(field("created_by"), "system") // Assuming system user
                    .returning(field("batch_id", Integer.class))
                    .fetchOne();
                    
            if (record == null) {
                log.debug("No batch record created, will continue without batch tracking");
                return null;
            }
            
            Integer batchId = record.getValue(field("batch_id", Integer.class));
            log.info("Successfully created update_firmware_batch record with ID: {}", batchId);
            return batchId;
        } catch (Exception e) {
            log.debug("Could not create batch record due to: {}. Will continue without batch tracking", e.getMessage());
            return null;
        }
    }
    
    /**
     * 记录固件升级日志并关联到批处理
     */
    private void recordFirmwareUpdateLog(Integer batchId, UpdateFirmwareParams params, 
                                        String chargeBoxId, String status, String response) {
        try {
            // Check if params or essential fields are null to prevent NullPointerException
            if (params == null || params.getLocation() == null || params.getRetrieve() == null) {
                log.error("Cannot record firmware update log due to null params or essential fields for chargeBoxId: {}", chargeBoxId);
                return;
            }

            DateTime retrieveDateTime = DateTimeUtils.toDateTime(params.getRetrieve());

            if (batchId == null) {
                int logId = updateFirmwareLogRepository.insert(
                    params.getLocation(),
                    params.getRetries(),
                    params.getRetryInterval(),
                    retrieveDateTime,
                    chargeBoxId,
                    status,
                    response
                );
                if (logId > 0) {
                    log.debug("Successfully recorded firmware update log for chargeBoxId={}, logId={}", chargeBoxId, logId);
                }
                return;
            }
            
            Record checkRecord = ctx.select(field("batch_id"))
                              .from(table("update_firmware_batch"))
                              .where(field("batch_id").eq(batchId))
                              .fetchOne();
            
            if (checkRecord == null) {
                log.warn("Cannot associate log with batch - batch record with ID {} not found. Recording log without batch association.", batchId);
                updateFirmwareLogRepository.insert(
                    params.getLocation(), params.getRetries(), params.getRetryInterval(),
                    retrieveDateTime, chargeBoxId, status, response);
                return;
            }
            
            Record record = ctx.insertInto(table("update_firmware_log"))
                    .set(field("location"), params.getLocation())
                    .set(field("retries"), params.getRetries())
                    .set(field("retry_interval"), params.getRetryInterval())
                    .set(field("retrieve_datetime"), retrieveDateTime)
                    .set(field("charge_box_id"), chargeBoxId)
                    .set(field("status"), status)
                    .set(field("response"), response)
                    .set(field("batch_id"), batchId)
                    .returning(field("log_id", Integer.class))
                    .fetchOne();
            
            if (record == null) {
                log.debug("Could not insert log record with batch association for chargeBoxId={}, will try without batch association", chargeBoxId);
                 updateFirmwareLogRepository.insert( // Fallback
                    params.getLocation(), params.getRetries(), params.getRetryInterval(),
                    retrieveDateTime, chargeBoxId, status, response);
            } else {
                log.debug("Successfully recorded firmware update log with batch association for chargeBoxId={}, logId={}, batchId={}", 
                        chargeBoxId, record.getValue(field("log_id", Integer.class)), batchId);
            }
        } catch (Exception e) {
            log.error("Error recording firmware update log for chargeBoxId {}: {}", chargeBoxId, e.getMessage(), e);
        }
    }
    
    /**
     * 更新批处理状态
     */
    private void updateBatchStatus(Integer batchId, int successCount, int errorCount, int pendingCount) {
        if (batchId == null) {
            log.debug("Skipping batch status update as batchId is null");
            return;
        }
        
        try {
            Record checkRecord = ctx.select(field("batch_id"))
                              .from(table("update_firmware_batch"))
                              .where(field("batch_id").eq(batchId))
                              .fetchOne();
            
            if (checkRecord == null) {
                log.warn("Cannot update batch status - batch record with ID {} not found", batchId);
                return;
            }
            
            String status = "INPROGRESS";
            if (pendingCount <= 0) { // Ensure pendingCount cannot be negative
                pendingCount = 0; // Correct if somehow negative
                status = errorCount == 0 ? "COMPLETED" : "COMPLETED_WITH_ERRORS";
            }
            
            int result = ctx.update(table("update_firmware_batch"))
                .set(field("success_count"), successCount)
                .set(field("error_count"), errorCount)
                .set(field("pending_count"), pendingCount)
                .set(field("status"), status)
                .where(field("batch_id").eq(batchId))
                .execute();
                
            if (result > 0) {
                log.info("Successfully updated batch status for batch ID: {}, new status: {}, success: {}, error: {}, pending: {}", batchId, status, successCount, errorCount, pendingCount);
            } else {
                log.warn("Failed to update batch status for batch ID: {}. No rows affected.", batchId);
            }
        } catch (Exception e) {
            log.error("Error updating batch status for batch ID {}: {}", batchId, e.getMessage(), e);
        }
    }
}
