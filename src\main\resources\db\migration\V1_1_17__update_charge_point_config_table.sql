-- Drop unused gun mapping columns
ALTER TABLE charge_point_config DROP COLUMN cb1_gun1_mapping;
ALTER TABLE charge_point_config DROP COLUMN cb1_gun2_mapping;
ALTER TABLE charge_point_config DROP COLUMN cb2_gun1_mapping;
ALTER TABLE charge_point_config DROP COLUMN cb2_gun2_mapping;
ALTER TABLE charge_point_config DROP COLUMN cb1_gun1_enabled;
ALTER TABLE charge_point_config DROP COLUMN cb1_gun2_enabled;
ALTER TABLE charge_point_config DROP COLUMN cb2_gun1_enabled;
ALTER TABLE charge_point_config DROP COLUMN cb2_gun2_enabled;

-- Add new columns to match the DataTransferForm DTO
ALTER TABLE charge_point_config ADD COLUMN dc_max_power DOUBLE PRECISION;
ALTER TABLE charge_point_config ADD COLUMN auto_charge BOOLEAN;
ALTER TABLE charge_point_config ADD COLUMN energy_save BOOLEAN;
ALTER TABLE charge_point_config ADD COLUMN control_board_version_combined VARCHAR(255);
ALTER TABLE charge_point_config ADD COLUMN door_state_check BOOLEAN;
ALTER TABLE charge_point_config ADD COLUMN single_dc_module_current DOUBLE PRECISION;
ALTER TABLE charge_point_config ADD COLUMN firmware_version VARCHAR(255);
ALTER TABLE charge_point_config ADD COLUMN ocpp_charger_id VARCHAR(255);
ALTER TABLE charge_point_config ADD COLUMN ocpp_server_url VARCHAR(255);
ALTER TABLE charge_point_config ADD COLUMN module_output_voltage DOUBLE PRECISION;
ALTER TABLE charge_point_config ADD COLUMN soc INTEGER;
ALTER TABLE charge_point_config ADD COLUMN module_info_upload BOOLEAN;
ALTER TABLE charge_point_config ADD COLUMN plug_and_start BOOLEAN;
ALTER TABLE charge_point_config ADD COLUMN module_output_voltage_a_full_power DOUBLE PRECISION;
ALTER TABLE charge_point_config ADD COLUMN module_output_voltage_b_full_power DOUBLE PRECISION;
ALTER TABLE charge_point_config ADD COLUMN read_external_meter BOOLEAN;
ALTER TABLE charge_point_config ADD COLUMN detailed_status_notification BOOLEAN;
ALTER TABLE charge_point_config ADD COLUMN rgb_flow_light BOOLEAN;
ALTER TABLE charge_point_config ADD COLUMN id_tag_is_mac_address BOOLEAN;
ALTER TABLE charge_point_config ADD COLUMN nls_em28_scanner VARCHAR(255);
ALTER TABLE charge_point_config ADD COLUMN check_chademoplugin VARCHAR(255);
ALTER TABLE charge_point_config ADD COLUMN channels_config TEXT; 