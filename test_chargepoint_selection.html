<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Charge Point Selection</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-info {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .form-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .multi-select-container {
            position: relative;
            width: 100%;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        .selected-options {
            padding: 10px;
            cursor: pointer;
            min-height: 20px;
            line-height: 1.5;
            background-color: #fff;
        }
        .options-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            border: 1px solid #ccc;
            border-top: none;
            background-color: white;
            z-index: 1000;
            max-height: 200px;
            overflow-y: auto;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .options-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .options-list li {
            padding: 8px 10px;
            cursor: pointer;
        }
        .options-list li:hover {
            background-color: #f0f0f0;
        }
        .options-list li input[type="checkbox"] {
            margin-right: 8px;
        }
        .submit-button {
            text-align: center;
            margin-top: 15px;
        }
        .submit-button input[type="submit"] {
            background-color: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        .debug-output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Charge Point Selection for GetDiagnostics</h1>
        
        <div class="test-info">
            <h3>Test Purpose</h3>
            <p>This page tests the charge point selection functionality to ensure it works correctly.</p>
            <p><strong>Current Issue:</strong> Form shows "No charge points selected" even when points are selected.</p>
            <p><strong>Expected Fix:</strong> JavaScript should properly update the hidden select element.</p>
        </div>

        <form id="testForm" method="post" action="javascript:void(0);">
            <div class="form-section">
                <h3>Charge Points Selection Test</h3>
                
                <div class="multi-select-container">
                    <div class="selected-options" tabindex="0">Click to select Charge Points</div>
                    <div class="options-dropdown" style="display: none;">
                        <ul class="options-list">
                            <li>
                                <input type="checkbox" id="cp-CP001" name="selectedChargePoints" value="V_16_SOAP;CP001;http://example.com/cp001">
                                <label for="cp-CP001">CP001 (OCPP 1.6 SOAP)</label>
                            </li>
                            <li>
                                <input type="checkbox" id="cp-CP002" name="selectedChargePoints" value="V_16_SOAP;CP002;http://example.com/cp002">
                                <label for="cp-CP002">CP002 (OCPP 1.6 SOAP)</label>
                            </li>
                            <li>
                                <input type="checkbox" id="cp-CP005" name="selectedChargePoints" value="V_16_JSON;CP005;ws://example.com/cp005">
                                <label for="cp-CP005">CP005 (OCPP 1.6 JSON)</label>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- This is the critical hidden select that must be updated -->
                    <select name="chargePointSelectList" multiple="true" style="display:none;" id="hiddenSelect">
                        <option value="V_16_SOAP;CP001;http://example.com/cp001">CP001</option>
                        <option value="V_16_SOAP;CP002;http://example.com/cp002">CP002</option>
                        <option value="V_16_JSON;CP005;ws://example.com/cp005">CP005</option>
                    </select>
                </div>
                
                <div class="submit-button">
                    <input type="submit" value="Test Selection" onclick="testSelection(event)">
                </div>
            </div>
        </form>
        
        <div id="debugOutput" class="debug-output" style="display: none;"></div>
        <div id="successMessage" class="success" style="display: none;"></div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing charge point selection...');
            
            const container = document.querySelector('.multi-select-container');
            const selectedOptionsDisplay = container.querySelector('.selected-options');
            const dropdown = container.querySelector('.options-dropdown');
            const optionsList = container.querySelector('.options-list');
            const hiddenSelect = document.querySelector('select[name="chargePointSelectList"]');

            console.log('Elements found:', {
                container: !!container,
                selectedOptionsDisplay: !!selectedOptionsDisplay,
                dropdown: !!dropdown,
                optionsList: !!optionsList,
                hiddenSelect: !!hiddenSelect
            });

            if (!hiddenSelect) {
                console.error('CRITICAL: Hidden select element not found!');
                return;
            }

            // Toggle dropdown visibility
            selectedOptionsDisplay.addEventListener('click', function() {
                const isVisible = dropdown.style.display !== 'none';
                dropdown.style.display = isVisible ? 'none' : 'block';
                console.log('Dropdown toggled:', !isVisible);
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(event) {
                if (!container.contains(event.target)) {
                    dropdown.style.display = 'none';
                }
            });

            // Prevent dropdown from closing when clicking inside it
            dropdown.addEventListener('click', function(event) {
                event.stopPropagation();
            });
            
            // Update selection function - CRITICAL FUNCTION
            function updateSelection() {
                console.log('updateSelection() called');
                
                const selectedValues = [];
                const displayTexts = [];
                const checkedBoxes = optionsList.querySelectorAll('input[type="checkbox"]:checked');
                
                console.log('Found checked boxes:', checkedBoxes.length);
                
                checkedBoxes.forEach(function(checkbox) {
                    selectedValues.push(checkbox.value);
                    const labelElement = checkbox.parentElement.querySelector('label[for="' + checkbox.id + '"]');
                    if (labelElement) {
                         displayTexts.push(labelElement.textContent);
                    }
                    console.log('Selected:', checkbox.value);
                });

                // CRITICAL: Update hidden select
                console.log('Updating hidden select...');
                Array.from(hiddenSelect.options).forEach(function(option) {
                    option.selected = false;
                });
                
                let updatedCount = 0;
                selectedValues.forEach(function(value) {
                    const optionToSelect = Array.from(hiddenSelect.options).find(opt => opt.value === value);
                    if (optionToSelect) {
                        optionToSelect.selected = true;
                        updatedCount++;
                        console.log('Selected option in hidden select:', value);
                    } else {
                        console.warn('Option not found in hidden select:', value);
                    }
                });
                
                console.log('Updated', updatedCount, 'options in hidden select');
                
                // Dispatch change event
                var event = new Event('change', { bubbles: true });
                hiddenSelect.dispatchEvent(event);

                // Update display
                if (displayTexts.length > 0) {
                    selectedOptionsDisplay.textContent = displayTexts.join(', ');
                } else {
                    selectedOptionsDisplay.textContent = 'Click to select Charge Points';
                }
                
                // Verify selection
                const selectedOptions = Array.from(hiddenSelect.selectedOptions);
                console.log('Hidden select now has', selectedOptions.length, 'selected options');
                selectedOptions.forEach(opt => console.log('  -', opt.value));
            }

            // Event listeners for checkboxes
            optionsList.querySelectorAll('input[type="checkbox"]').forEach(function(checkbox) {
                checkbox.addEventListener('change', function() {
                    console.log('Checkbox changed:', this.id, this.checked);
                    updateSelection();
                });
            });
            
            console.log('Charge point selection initialized successfully');
        });
        
        function testSelection(event) {
            event.preventDefault();
            
            console.log('=== FORM SUBMISSION TEST ===');
            
            const form = document.getElementById('testForm');
            const formData = new FormData(form);
            const hiddenSelect = document.querySelector('select[name="chargePointSelectList"]');
            
            let debugInfo = 'Form Submission Test Results:\n\n';
            
            // Check FormData
            debugInfo += '1. FormData entries:\n';
            for (let [key, value] of formData.entries()) {
                debugInfo += `   ${key}: ${value}\n`;
            }
            
            // Check hidden select
            const selectedOptions = Array.from(hiddenSelect.selectedOptions);
            debugInfo += `\n2. Hidden Select Status:\n`;
            debugInfo += `   Total options: ${hiddenSelect.options.length}\n`;
            debugInfo += `   Selected options: ${selectedOptions.length}\n`;
            debugInfo += `   Selected values:\n`;
            selectedOptions.forEach(opt => {
                debugInfo += `     - ${opt.value}\n`;
            });
            
            // Check what would be sent
            debugInfo += `\n3. What would be sent to server:\n`;
            const chargePointSelectList = selectedOptions.map(opt => opt.value);
            debugInfo += `   chargePointSelectList: ${JSON.stringify(chargePointSelectList)}\n`;
            debugInfo += `   Array length: ${chargePointSelectList.length}\n`;
            
            // Show results
            const debugOutput = document.getElementById('debugOutput');
            debugOutput.textContent = debugInfo;
            debugOutput.style.display = 'block';
            
            // Show success/failure
            const successMessage = document.getElementById('successMessage');
            if (chargePointSelectList.length > 0) {
                successMessage.innerHTML = `<strong>✅ SUCCESS!</strong><br>Selected ${chargePointSelectList.length} charge point(s). The form should work correctly now.`;
                successMessage.style.display = 'block';
            } else {
                successMessage.innerHTML = `<strong>❌ FAILURE!</strong><br>No charge points selected. There's still an issue with the JavaScript.`;
                successMessage.style.backgroundColor = '#f8d7da';
                successMessage.style.borderColor = '#f5c6cb';
                successMessage.style.color = '#721c24';
                successMessage.style.display = 'block';
            }
            
            console.log('Test completed. Results:', {
                formDataEntries: Array.from(formData.entries()),
                selectedOptions: selectedOptions.length,
                chargePointSelectList: chargePointSelectList
            });
        }
    </script>
</body>
</html>
