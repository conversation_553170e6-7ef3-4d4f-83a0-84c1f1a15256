# Just to be backwards compatible with previous versions, this is set to "steve",
# since there might be already configured chargepoints expecting the older path.
# Otherwise, might as well be changed to something else or be left empty.
#
context.path = steve

# Database configuration
#
db.ip = localhost
db.port = 3306
db.schema = stevedb
db.user = root
db.password = 123456

# Credentials for Web interface access
#
auth.user = admin
auth.password = 1234

# The header key and value for Web API access using API key authorization.
# Both must be set for Web APIs to be enabled. Otherwise, we will block all calls.
#
webapi.key = STEVE-API-KEY
webapi.value =

# Jetty configuration
#
server.host = 0.0.0.0
server.gzip.enabled = true

# Jetty HTTP configuration
#
http.enabled = true
http.port = 8080

# Jetty HTTPS configuration
#
https.enabled = false
https.port = 8443
keystore.path =
keystore.password =

# When the WebSocket/Json charge point opens more than one WebSocket connection,
# we need a mechanism/strategy to select one of them for outgoing requests.
# For allowed values see de.rwth.idsg.steve.ocpp.ws.custom.WsSessionSelectStrategyEnum.
#
ws.session.select.strategy = ALWAYS_LAST

# if BootNotification messages arrive (SOAP) or WebSocket connection attempts are made (JSON) from unknown charging
# stations, we reject these charging stations, because stations with these chargeBoxIds were NOT inserted into database
# beforehand. by setting this property to true, this behaviour can be modified to automatically insert unknown
# stations into database and accept their requests.
#
# CAUTION: setting this property to true is very dangerous, because we will accept EVERY BootNotification or WebSocket
# connection attempt from ANY sender as long as the sender knows the URL and sends a valid message.
#
auto.register.unknown.stations = false

# if this field is set, it will take precedence over the default regex we are using in
# de.rwth.idsg.steve.web.validation.ChargeBoxIdValidator.REGEX to validate the format of the chargeBoxId values
#
charge-box-id.validation.regex =

# Firmware and FTP Configuration
steve.firmware.physical-upload-path=ftp_files/firmware/
steve.diagnostics.physical-upload-path=ftp_files/logs/
steve.firmware.ftp.enabled=true
steve.firmware.ftp.username=user
steve.firmware.ftp.password=123
steve.firmware.ftp.ip=***********
steve.firmware.ftp.port=9988
steve.firmware.ftp.base.path=/firmware/
steve.firmware.ftp.log-path=ftp_files/logs/ftp-server.log
steve.firmware.ftp.passive.external-address=***********
steve.firmware.ftp.passive.ports=50000-51000

# Diagnostic File Handling Configuration
# Set to true to enable legacy file monitoring as backup for charge points that upload to FTP root
# When enabled, files uploaded to FTP root will be automatically moved to /logs/ directory
# This is needed for charge points that don't properly handle the /logs/ path in FTP URL
steve.ftp.enable-legacy-file-monitor=true

# Diagnostics Task Management Configuration
# Time in minutes after which a diagnostics task expires and gets cleaned up
steve.diagnostics.task.expiry-minutes=10
# Interval in minutes for automatic cleanup of expired tasks and files
steve.diagnostics.cleanup.interval-minutes=5
# Interval in minutes for automatic cleanup of finished tasks in TaskStore
# Should be same or shorter than diagnostics expiry time to avoid orphaned tasks
steve.taskstore.cleanup.interval-minutes=5

### DO NOT MODIFY ###
steve.version = ${project.version}
git.describe = ${git.commit.id.describe}
db.sql.logging = false
profile = prod
