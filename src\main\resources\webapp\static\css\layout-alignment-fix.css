/**
 * 布局对齐修复样式
 * 专门解决EVSE_OMS项目中的元素对齐和排版问题
 */

/* ===== 搜索面板特殊对齐修复 ===== */

/* 确保搜索面板中最后一行的按钮与输入框底部对齐 */
.search-panel .row:last-child .col-md-4:last-child {
    display: flex;
    align-items: flex-end;
}

.search-panel .row:last-child .col-md-4:last-child .form-group {
    width: 100%;
    margin-bottom: 0;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
}

/* 确保按钮高度与输入框一致 */
.search-panel .btn {
    height: 38px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 100px;
}

/* ===== 表单元素统一高度 ===== */
.form-control,
.btn,
select.form-control {
    height: 38px;
    line-height: 1.5;
    box-sizing: border-box;
}

/* 日期输入框特殊处理 */
input[type="date"].form-control {
    height: 38px;
    padding: 7px 12px;
}

/* ===== 表格对齐优化 ===== */
.table th {
    text-align: left;
    vertical-align: middle;
    font-weight: 600;
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
}

.table td {
    vertical-align: middle;
    text-align: center;
}

/* 特定列左对齐 */
.table td:first-child,
.table th:first-child {
    text-align: left;
}

/* ===== 按钮组对齐 ===== */
.btn-group {
    display: flex;
    gap: 8px;
    align-items: center;
}

.btn-group .btn {
    margin: 0;
    flex: 0 0 auto;
}

/* ===== 表单行对齐 ===== */
.form-row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px;
    align-items: flex-end;
}

.form-row .form-group {
    margin-bottom: 0;
}

/* ===== 内容区域对齐 ===== */
.content-container {
    padding: 25px;
    background-color: #fff;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
    margin-bottom: 25px;
}

/* ===== 页面标题对齐 ===== */
.page-title {
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ebedf2;
    font-size: 20px;
    font-weight: 600;
    color: #181c32;
}

/* ===== 面包屑导航对齐 ===== */
.breadcrumb {
    background-color: transparent;
    padding: 0 0 15px 0;
    margin-bottom: 20px;
    border-bottom: 1px solid #ebedf2;
    display: flex;
    align-items: center;
}

.breadcrumb a {
    color: #6c7293;
    text-decoration: none;
}

.breadcrumb a:hover {
    color: #3699ff;
    text-decoration: none;
}

.breadcrumb .separator {
    margin: 0 8px;
    color: #6c7293;
}

/* ===== 卡片布局对齐 ===== */
.card {
    background-color: #fff;
    border: 1px solid #ebedf2;
    border-radius: 6px;
    margin-bottom: 25px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

.card-header {
    padding: 15px 20px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #ebedf2;
    font-weight: 600;
    color: #181c32;
}

.card-body {
    padding: 20px;
}

/* ===== 工具提示对齐 ===== */
.tooltip-icon {
    width: 16px;
    height: 16px;
    vertical-align: middle;
    margin-left: 5px;
}

/* ===== 响应式对齐修复 ===== */
@media (max-width: 768px) {
    .search-panel .row {
        flex-direction: column;
    }
    
    .search-panel .col-md-4 {
        flex: 0 0 100%;
        max-width: 100%;
        margin-bottom: 15px;
    }
    
    .search-panel .row:last-child .col-md-4:last-child {
        align-items: stretch;
    }
    
    .btn-group {
        flex-direction: column;
        gap: 10px;
    }
    
    .btn-group .btn {
        width: 100%;
    }
}

/* ===== 特殊页面对齐修复 ===== */

/* 充电站详情页面表单对齐 */
.userInput {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 25px;
}

.userInput th {
    background-color: #f8f9fa;
    padding: 15px;
    text-align: left;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}

.userInput td {
    padding: 12px 15px;
    border-bottom: 1px solid #dee2e6;
    vertical-align: middle;
}

.userInput td:first-child {
    width: 200px;
    font-weight: 500;
    background-color: #f8f9fa;
    text-align: left;
}

.userInput input,
.userInput select,
.userInput textarea {
    width: 100%;
    max-width: 400px;
}

/* 提交按钮区域对齐 */
.submit-button {
    text-align: left;
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid #ebedf2;
}

.submit-button .btn {
    margin-right: 10px;
    margin-bottom: 10px;
}

/* ===== 分页对齐 ===== */
.pagination {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #ebedf2;
}

.pagination .page-info {
    margin-right: 15px;
    color: #6c757d;
    font-size: 14px;
}

/* ===== 操作按钮对齐 ===== */
.action-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
    justify-content: center;
}

.action-buttons .btn {
    min-width: 80px;
}

/* ===== 状态指示器对齐 ===== */
.status-indicator {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.status-online {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-offline {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status-unknown {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

/* ===== 数据表格特殊对齐 ===== */
.table-custom th,
.table-custom td {
    text-align: center;
    vertical-align: middle;
}

.table-custom th:first-child,
.table-custom td:first-child {
    text-align: left;
}

.table-custom .text-center {
    text-align: center !important;
}

.table-custom .text-left {
    text-align: left !important;
}

.table-custom .text-right {
    text-align: right !important;
}

/* ===== 清除浮动 ===== */
.clearfix::before,
.clearfix::after {
    content: " ";
    display: table;
}

.clearfix::after {
    clear: both;
}

/* ===== 隐藏元素 ===== */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}
