/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.web.controller;

import de.rwth.idsg.steve.SteveConfiguration;
import de.rwth.idsg.steve.ocpp.OcppProtocol;
import de.rwth.idsg.steve.ocpp.OcppVersion;
import de.rwth.idsg.steve.repository.ChargePointRepository;
import de.rwth.idsg.steve.repository.UpdateFirmwareLogRepository;
import de.rwth.idsg.steve.repository.dto.ConnectorStatus;
import de.rwth.idsg.steve.service.ChargePointHelperService;
import de.rwth.idsg.steve.service.DiagnosticsProgressTracker;
import de.rwth.idsg.steve.service.DiagnosticsTaskManager;
import de.rwth.idsg.steve.service.DiagnosticsTaskInfo;
import de.rwth.idsg.steve.service.ChargePointServiceClient;
import de.rwth.idsg.steve.utils.ConnectorStatusCountFilter;
import de.rwth.idsg.steve.utils.ConnectorStatusFilter;
import de.rwth.idsg.steve.web.dto.ConnectorStatusForm;
import de.rwth.idsg.steve.web.dto.UpdateFirmwareLogQueryForm;
import de.rwth.idsg.steve.web.dto.ocpp.GetDiagnosticsParams;
import de.rwth.idsg.steve.web.dto.ocpp.UpdateFirmwareParams;
import ocpp.cs._2015._10.RegistrationStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;

import java.util.List;
import java.util.Arrays;
import de.rwth.idsg.steve.ocpp.OcppVersion;
import ocpp.cs._2015._10.RegistrationStatus;
import de.rwth.idsg.steve.web.dto.UpdateFirmwareLog;

/**
 * <AUTHOR> AI
 */
@Slf4j
@Controller
@RequestMapping(value = "/manager", method = RequestMethod.GET)
public class DataManagementController {

    @Autowired private ChargePointRepository chargePointRepository;
    @Autowired private ChargePointHelperService chargePointHelperService;
    @Autowired private UpdateFirmwareLogRepository updateFirmwareLogRepository;
    @Autowired private DiagnosticsProgressTracker progressTracker;
    @Autowired private DiagnosticsTaskManager diagnosticsTaskManager;
    @Autowired private ChargePointServiceClient chargePointServiceClient;

    private static final String PARAMS = "params";
    private static final String LOG_QUERY_PARAMS = "logQueryParams";

    // -------------------------------------------------------------------------
    // Path for firmware update
    // -------------------------------------------------------------------------
    private static final String FIRMWARE_UPDATE_PATH = "/firmwareUpdate";



    // -------------------------------------------------------------------------
    // Paths for connector status
    // -------------------------------------------------------------------------

    private static final String CONNECTOR_STATUS_PATH = "/data-man/connectorStatus";
    private static final String CONNECTOR_STATUS_QUERY_PATH = "/data-man/connectorStatus/query";

    /**
     * Connector Status
     */
    @PreAuthorize("hasAuthority('ADMIN')")
    @RequestMapping(value = CONNECTOR_STATUS_PATH)
    public String getConnectorStatus(Model model) {
        return getConnectorStatusQuery(new ConnectorStatusForm(), model);
    }

    @PreAuthorize("hasAuthority('ADMIN')")
    @RequestMapping(value = CONNECTOR_STATUS_QUERY_PATH)
    public String getConnectorStatusQuery(@ModelAttribute(PARAMS) ConnectorStatusForm params, Model model) {
        model.addAttribute("cpList", chargePointRepository.getChargeBoxIds());
        model.addAttribute("statusValues", ConnectorStatusCountFilter.ALL_STATUS_VALUES);
        model.addAttribute(PARAMS, params);

        List<ConnectorStatus> latestList = chargePointHelperService.getChargePointConnectorStatus(params);
        List<ConnectorStatus> filteredList = ConnectorStatusFilter.filterAndPreferZero(latestList);
        model.addAttribute("connectorStatusList", filteredList);
        return "data-man/connectorStatus";
    }

    /**
     * Firmware Update Page
     */
    @RequestMapping(value = FIRMWARE_UPDATE_PATH)
    public String getFirmwareUpdatePage(@ModelAttribute(PARAMS) UpdateFirmwareParams params,
                                        @ModelAttribute(LOG_QUERY_PARAMS) UpdateFirmwareLogQueryForm logQueryParams,
                                        BindingResult logQueryParamsBindingResult,
                                        @PageableDefault(size = 10, sort = "sendingTime", direction = Sort.Direction.DESC) Pageable pageable,
                                        Model model) {
        model.addAttribute(PARAMS, params);
        List<RegistrationStatus> inStatusFilter = Arrays.asList(RegistrationStatus.ACCEPTED, RegistrationStatus.PENDING);
        model.addAttribute("ocppCpList", chargePointHelperService.getChargePoints(OcppVersion.V_16, inStatusFilter));
        model.addAttribute("opVersion", "v1.6");

        Page<UpdateFirmwareLog> firmwareLogPage = updateFirmwareLogRepository.findAll(logQueryParams, pageable);
        model.addAttribute("firmwareLogPage", firmwareLogPage);
        model.addAttribute("logList", firmwareLogPage.getContent());
        model.addAttribute("count", firmwareLogPage.getTotalElements());

        model.addAttribute("chargeBoxIdListForLogs", chargePointRepository.getChargeBoxIds());
        model.addAttribute("chargePointDetailsPath", "/manager/chargepoints/details/");

        return "data-man/firmwareUpdate";
    }



    /**
     * Handle Get Diagnostics for Single Charge Point
     */
    @PostMapping("/chargepoints/{chargeBoxId}/getDiagnostics")
    public String getSingleChargepointDiagnostics(@PathVariable("chargeBoxId") String chargeBoxId,
                                                 @RequestParam(value = "retries", required = false) Integer retries,
                                                 @RequestParam(value = "retryInterval", required = false) Integer retryInterval,
                                                 @RequestParam(value = "startDate", required = false) String startDate,
                                                 @RequestParam(value = "stopDate", required = false) String stopDate,
                                                 HttpServletRequest request) {

        log.info("🚀🚀🚀 ChargePoints Get Diagnostics request received for: {}", chargeBoxId);
        log.info("🚀🚀🚀 Parameters - retries: {}, retryInterval: {}, startDate: {}, stopDate: {}",
                retries, retryInterval, startDate, stopDate);

        // 检查是否有现有任务可以复用
        log.info("🔧 DataManagementController.getSingleChargepointDiagnostics: checking for task reuse for {}", chargeBoxId);

        DiagnosticsTaskInfo existingTask = diagnosticsTaskManager.getValidTask(chargeBoxId);
        if (existingTask != null) {
            log.info("🔄 Reusing existing diagnostics task for {}: taskId={}", chargeBoxId, existingTask.getTaskId());

            // 添加复用消息到session
            String reuseMessage = String.format(
                "Found existing GetDiagnostics task for charge point %s (Task ID: %d). " +
                "Reusing the existing task instead of creating a new one. You can monitor progress in real-time.",
                chargeBoxId, existingTask.getTaskId());

            request.getSession().setAttribute("successMessage", reuseMessage);
            return "redirect:/manager/operations/tasks/" + existingTask.getTaskId();
        }

        // 创建参数对象
        GetDiagnosticsParams params = new GetDiagnosticsParams();
        params.setRetries(retries);
        params.setRetryInterval(retryInterval);

        // 处理日期范围
        if (startDate != null && !startDate.isEmpty() && stopDate != null && !stopDate.isEmpty()) {
            try {
                org.joda.time.LocalDate start = org.joda.time.LocalDate.parse(startDate);
                org.joda.time.LocalDate stop = org.joda.time.LocalDate.parse(stopDate);
                params.setStart(start.toLocalDateTime(org.joda.time.LocalTime.MIDNIGHT));
                params.setStop(stop.toLocalDateTime(new org.joda.time.LocalTime(23, 59, 59)));
            } catch (Exception e) {
                // 如果日期解析失败，使用默认值
                org.joda.time.LocalDate yesterday = org.joda.time.LocalDate.now().minusDays(1);
                params.setStart(yesterday.toLocalDateTime(org.joda.time.LocalTime.MIDNIGHT));
                params.setStop(yesterday.toLocalDateTime(new org.joda.time.LocalTime(23, 59, 59)));
            }
        } else {
            // 默认获取昨天一整天的日志
            org.joda.time.LocalDate yesterday = org.joda.time.LocalDate.now().minusDays(1);
            params.setStart(yesterday.toLocalDateTime(org.joda.time.LocalTime.MIDNIGHT));
            params.setStop(yesterday.toLocalDateTime(new org.joda.time.LocalTime(23, 59, 59)));
        }

        // 设置FTP位置
        String ftpLocation = String.format("ftp://%s:%s@%s:%d/logs/",
            SteveConfiguration.CONFIG.getFtpUsername(),
            SteveConfiguration.CONFIG.getFtpPassword(),
            SteveConfiguration.CONFIG.getFtpIp(),
            SteveConfiguration.CONFIG.getFtpPort());
        params.setLocation(ftpLocation);

        // 设置充电桩选择列表
        List<de.rwth.idsg.steve.repository.dto.ChargePointSelect> cpSelectList = new ArrayList<>();
        de.rwth.idsg.steve.repository.dto.ChargePointSelect cpSelect =
            new de.rwth.idsg.steve.repository.dto.ChargePointSelect(OcppProtocol.V_16_JSON, chargeBoxId);
        cpSelectList.add(cpSelect);
        params.setChargePointSelectList(cpSelectList);

        // 启动进度跟踪
        progressTracker.startTracking(chargeBoxId);
        progressTracker.simulateProgress(chargeBoxId);

        int taskId = chargePointServiceClient.getDiagnostics(params);

        // 注册新任务到任务管理器
        log.info("🔧 Registering new task for {}: taskId={}", chargeBoxId, taskId);
        diagnosticsTaskManager.registerTask(chargeBoxId, taskId);

        // 添加成功消息到session
        String successMessage = String.format(
            "GetDiagnostics request sent successfully to charge point %s. Task ID: %d. " +
            "Diagnostic files will be uploaded to the FTP server. You can monitor progress in real-time. " +
            "📥 Reminder: Once ready, you can download the diagnostic file multiple times within 10 minutes. " +
            "The task and file will be automatically cleaned up after 10 minutes to save server space.",
            chargeBoxId, taskId);

        request.getSession().setAttribute("successMessage", successMessage);

        // 重定向到任务详情页面
        return "redirect:/manager/operations/tasks/" + taskId;
    }
}