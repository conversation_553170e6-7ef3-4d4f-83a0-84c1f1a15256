/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.repository.impl;

import de.rwth.idsg.steve.repository.ChargerIssueRepository;
import de.rwth.idsg.steve.repository.dto.maintenance.EnumReportStatus;
import de.rwth.idsg.steve.web.dto.ChargerIssueDTO;
import de.rwth.idsg.steve.web.dto.ChargerIssueForm;
import de.rwth.idsg.steve.web.dto.ChargerIssueStatus;
import de.rwth.idsg.steve.web.dto.FaultForm;
import de.rwth.idsg.steve.web.dto.MaintenanceRecordDTO;
import de.rwth.idsg.steve.web.dto.MaintenanceRecordForm;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.jooq.DSLContext;
import org.jooq.Field;
import org.jooq.Record;
import org.jooq.Record1;
import org.jooq.Result;
import org.jooq.SelectConditionStep;
import org.jooq.SelectJoinStep;
import org.jooq.SelectQuery;
import org.jooq.SelectSeekStep1;
import org.jooq.SelectWhereStep;
import org.jooq.impl.DSL;
import org.jooq.Condition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import de.rwth.idsg.steve.web.dto.SortOrder;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static jooq.steve.db.tables.ChargerIssue.CHARGER_ISSUE;
import static jooq.steve.db.tables.ChargeBox.CHARGE_BOX;
import static jooq.steve.db.tables.WebUser.WEB_USER;
import static jooq.steve.db.tables.IssueImage.ISSUE_IMAGE;
import static jooq.steve.db.tables.IssueMaintenanceRecord.ISSUE_MAINTENANCE_RECORD;
import static jooq.steve.db.tables.User.USER;
import static jooq.steve.db.tables.UserChargeBox.USER_CHARGE_BOX;
import static org.jooq.impl.DSL.select;
import static org.jooq.impl.DSL.trueCondition;

/**
 * 充电桩故障仓库实现类
 */
@Slf4j
@Repository
public class ChargerIssueRepositoryImpl implements ChargerIssueRepository {

    @Autowired
    private DSLContext ctx;
    
    /**
     * 将DTO枚举转换为数据库枚举
     */
    private jooq.steve.db.enums.ChargerIssueStatus toDbEnum(ChargerIssueStatus status) {
        if (status == null) return null;
        return jooq.steve.db.enums.ChargerIssueStatus.valueOf(status.name());
    }
    
    /**
     * 将数据库枚举转换为DTO枚举
     */
    private ChargerIssueStatus fromDbEnum(jooq.steve.db.enums.ChargerIssueStatus status) {
        if (status == null) return null;
        return ChargerIssueStatus.valueOf(status.name());
    }

    /**
     * 将DTO状态转换为仓库状态枚举
     */
    private de.rwth.idsg.steve.repository.dto.maintenance.EnumReportStatus dtoStatusToEnumReportStatus(ChargerIssueStatus status) {
        if (status == null) return null;
        return de.rwth.idsg.steve.repository.dto.maintenance.EnumReportStatus.valueOf(status.name());
    }
    
    /**
     * 将仓库状态枚举转换为DTO状态
     */
    private ChargerIssueStatus enumReportStatusToDtoStatus(de.rwth.idsg.steve.repository.dto.maintenance.EnumReportStatus status) {
        if (status == null) return null;
        return ChargerIssueStatus.valueOf(status.name());
    }

    @Override
    public List<ChargerIssueDTO> getIssues() {
        Result<Record> result = ctx.select()
                      .from(CHARGER_ISSUE)
                      .fetch();
        return extractIssuesFromResult(result);
    }

    @Override
    public List<ChargerIssueDTO> getIssuesByStatus(ChargerIssueStatus status) {
        Result<Record> result = ctx.select()
                      .from(CHARGER_ISSUE)
                      .where(CHARGER_ISSUE.STATUS.eq(toDbEnum(status)))
                      .fetch();
        return extractIssuesFromResult(result);
    }

    @Override
    public List<ChargerIssueDTO> getIssuesByChargeBoxId(String chargeBoxId) {
        return this.ctx.selectFrom(CHARGER_ISSUE)
                .where(CHARGER_ISSUE.CHARGE_BOX_PK.eq(
                        select(CHARGE_BOX.CHARGE_BOX_PK)
                                .from(CHARGE_BOX)
                                .where(CHARGE_BOX.CHARGE_BOX_ID.eq(chargeBoxId))
                ))
                .fetch(r -> buildChargerIssueDTO(r));
    }

    @Override
    public List<ChargerIssueDTO> getIssuesByChargeBoxPk(int chargeBoxPk) {
        Result<Record> result = ctx.select()
                      .from(CHARGER_ISSUE)
                      .where(CHARGER_ISSUE.CHARGE_BOX_PK.eq(chargeBoxPk))
                      .fetch();
        return extractIssuesFromResult(result);
    }

    @Override
    public ChargerIssueDTO getIssue(Integer issueId) {
        Record record = ctx.selectFrom(CHARGER_ISSUE)
                          .where(CHARGER_ISSUE.ISSUE_ID.eq(issueId))
                          .fetchOne();

        if (record == null) {
            return null;
        }

        ChargerIssueDTO dto = buildChargerIssueDTO(record);
        dto.setImagePaths(getIssueImages(issueId));
        return dto;
    }

    @Override
    public int createIssue(ChargerIssueForm form, Integer reporterUserPk) {
        // 确保使用用户提交的时间，如果没有提供，则使用当前时间
        DateTime reportTime = form.getReportTime() != null ? new DateTime(form.getReportTime()) : new DateTime();
        
        Record1<Integer> record = ctx.insertInto(CHARGER_ISSUE)
                .set(CHARGER_ISSUE.CHARGE_BOX_PK, form.getChargeBoxPk())
                .set(CHARGER_ISSUE.REPORTER_USER_PK, reporterUserPk)
                .set(CHARGER_ISSUE.FAULT_DESCRIPTION, form.getFaultDescription())
                .set(CHARGER_ISSUE.OCPP_ERROR_CODE, form.getOcppErrorCode())
                .set(CHARGER_ISSUE.IS_AUTO_REPORTED, form.getIsAutoReported() != null && form.getIsAutoReported())
                .set(CHARGER_ISSUE.REPORT_TIME, reportTime)
                .set(CHARGER_ISSUE.VENDOR_ERROR_CODE, form.getVendorErrorCode())
                .set(CHARGER_ISSUE.VENDOR_ID, form.getVendorId())
                .returningResult(CHARGER_ISSUE.ISSUE_ID)
                .fetchOne();

        return record.value1();
    }

    @Override
    public void updateIssue(ChargerIssueForm form) {
        DateTime resolveTime = null;
        if (form.getStatus() == ChargerIssueStatus.RESOLVED) {
            resolveTime = new DateTime();
        }
                                
        ctx.update(CHARGER_ISSUE)
           .set(CHARGER_ISSUE.FAULT_DESCRIPTION, form.getFaultDescription())
           .set(CHARGER_ISSUE.OCPP_ERROR_CODE, form.getOcppErrorCode())
           .set(CHARGER_ISSUE.STATUS, form.getStatus() != null ? toDbEnum(form.getStatus()) : null)
           .set(CHARGER_ISSUE.RESOLVE_DESCRIPTION, form.getResolveDescription())
           .set(CHARGER_ISSUE.RESOLVE_TIME, resolveTime)
           .set(CHARGER_ISSUE.VENDOR_ERROR_CODE, form.getVendorErrorCode())
           .set(CHARGER_ISSUE.VENDOR_ID, form.getVendorId())
           .where(CHARGER_ISSUE.ISSUE_ID.eq(form.getIssueId()))
           .execute();
    }

    @Override
    public void updateIssueStatus(int issueId, ChargerIssueStatus status) {
        DateTime resolveTime = null;
        if (status == ChargerIssueStatus.RESOLVED) {
            resolveTime = new DateTime();
        }
                                
        ctx.update(CHARGER_ISSUE)
           .set(CHARGER_ISSUE.STATUS, toDbEnum(status))
           .set(CHARGER_ISSUE.RESOLVE_TIME, resolveTime)
           .where(CHARGER_ISSUE.ISSUE_ID.eq(issueId))
           .execute();
    }

    @Override
    public void resolveIssue(int issueId, String resolveDescription) {
        DateTime resolveTime = new DateTime();
        
        ctx.update(CHARGER_ISSUE)
           .set(CHARGER_ISSUE.STATUS, toDbEnum(ChargerIssueStatus.RESOLVED))
           .set(CHARGER_ISSUE.RESOLVE_DESCRIPTION, resolveDescription)
           .set(CHARGER_ISSUE.RESOLVE_TIME, resolveTime)
           .where(CHARGER_ISSUE.ISSUE_ID.eq(issueId))
           .execute();
    }

    @Override
    public void addIssueImage(int issueId, String imagePath) {
        ctx.insertInto(ISSUE_IMAGE)
           .set(ISSUE_IMAGE.ISSUE_ID, issueId)
           .set(ISSUE_IMAGE.IMAGE_PATH, imagePath)
           .execute();
    }

    @Override
    public List<String> getIssueImages(int issueId) {
        Result<Record1<String>> result = ctx.select(ISSUE_IMAGE.IMAGE_PATH)
                                         .from(ISSUE_IMAGE)
                                         .where(ISSUE_IMAGE.ISSUE_ID.eq(issueId))
                                         .fetch();

        if (result.isEmpty()) {
            return Collections.emptyList();
        }

        return result.stream()
                    .map(r -> r.value1())
                    .collect(Collectors.toList());
    }

    @Override
    public int addMaintenanceRecord(MaintenanceRecordForm form, Integer maintainerUserPk) {
        Record1<Integer> record = ctx.insertInto(ISSUE_MAINTENANCE_RECORD)
                .set(ISSUE_MAINTENANCE_RECORD.ISSUE_ID, form.getIssueId())
                .set(ISSUE_MAINTENANCE_RECORD.MAINTAINER_USER_PK, maintainerUserPk)
                .set(ISSUE_MAINTENANCE_RECORD.MAINTENANCE_DESCRIPTION, form.getMaintenanceDescription())
                .returningResult(ISSUE_MAINTENANCE_RECORD.RECORD_ID)
                .fetchOne();

        if (form.getNewStatus() != null) {
            if (form.getNewStatus() == ChargerIssueStatus.RESOLVED) {
                resolveIssue(form.getIssueId(), form.getResolveDescription());
            } else {
                updateIssueStatus(form.getIssueId(), form.getNewStatus());
            }
        }

        return record.value1();
    }

    @Override
    public List<MaintenanceRecordDTO> getMaintenanceRecords(int issueId) {
        Result<Record> result = ctx.select()
                                .from(ISSUE_MAINTENANCE_RECORD)
                                .leftJoin(WEB_USER)
                                .on(ISSUE_MAINTENANCE_RECORD.MAINTAINER_USER_PK.eq(WEB_USER.WEB_USER_PK))
                                .where(ISSUE_MAINTENANCE_RECORD.ISSUE_ID.eq(issueId))
                                .orderBy(ISSUE_MAINTENANCE_RECORD.MAINTENANCE_TIME.desc())
                                .fetch();

        List<MaintenanceRecordDTO> records = new ArrayList<>();
        for (Record r : result) {
            DateTime maintenanceTime = r.get(ISSUE_MAINTENANCE_RECORD.MAINTENANCE_TIME);
            
            MaintenanceRecordDTO dto = MaintenanceRecordDTO.builder()
                    .recordId(r.get(ISSUE_MAINTENANCE_RECORD.RECORD_ID))
                    .issueId(r.get(ISSUE_MAINTENANCE_RECORD.ISSUE_ID))
                    .maintenanceTime(maintenanceTime != null ? maintenanceTime.toDate() : null)
                    .maintainerUserPk(r.get(ISSUE_MAINTENANCE_RECORD.MAINTAINER_USER_PK))
                    .maintainerUsername(r.get(WEB_USER.USERNAME))
                    .maintenanceDescription(r.get(ISSUE_MAINTENANCE_RECORD.MAINTENANCE_DESCRIPTION))
                    .build();
            records.add(dto);
        }

        return records;
    }

    @Override
    public int createAutoIssue(int chargeBoxPk, String ocppErrorCode, String faultDescription) {
        return createAutoIssue(chargeBoxPk, ocppErrorCode, faultDescription, null, null);
    }

    @Override
    public int createAutoIssue(int chargeBoxPk, String ocppErrorCode, String faultDescription, String vendorErrorCode, String vendorId) {
        Record1<Integer> record = ctx.insertInto(CHARGER_ISSUE)
                .set(CHARGER_ISSUE.CHARGE_BOX_PK, chargeBoxPk)
                .set(CHARGER_ISSUE.FAULT_DESCRIPTION, faultDescription)
                .set(CHARGER_ISSUE.OCPP_ERROR_CODE, ocppErrorCode)
                .set(CHARGER_ISSUE.IS_AUTO_REPORTED, true)
                .set(CHARGER_ISSUE.VENDOR_ERROR_CODE, vendorErrorCode)
                .set(CHARGER_ISSUE.VENDOR_ID, vendorId)
                .returningResult(CHARGER_ISSUE.ISSUE_ID)
                .fetchOne();

        return record.value1();
    }

    @Override
    public List<ChargerIssueDTO> getFilteredIssues(FaultForm form) {
        SelectJoinStep<Record> selectQuery = ctx.select()
            .from(CHARGER_ISSUE);

        // 构建条件
        Condition whereCondition = DSL.trueCondition();

        // 按充电桩ID过滤
        if (form.isChargeBoxIdSet()) {
            whereCondition = whereCondition.and(CHARGER_ISSUE.CHARGE_BOX_PK.eq(
                    select(CHARGE_BOX.CHARGE_BOX_PK)
                            .from(CHARGE_BOX)
                            .where(CHARGE_BOX.CHARGE_BOX_ID.eq(form.getChargeBoxId()))
            ));
        }

        // 按故障状态过滤
        if (form.isStatusSet()) {
            whereCondition = whereCondition.and(CHARGER_ISSUE.STATUS.eq(toDbEnum(form.getStatus())));
        }

        // 按用户ID过滤
        if (form.isUserIdSet()) {
            whereCondition = whereCondition.and(CHARGER_ISSUE.REPORTER_USER_PK.eq(form.getUserId()));
        }

        // 按日期范围过滤
        if (form.isStartDateSet()) {
            whereCondition = whereCondition.and(CHARGER_ISSUE.REPORT_TIME.ge(new DateTime(form.getStartDate())));
        }

        if (form.isEndDateSet()) {
            whereCondition = whereCondition.and(CHARGER_ISSUE.REPORT_TIME.le(new DateTime(form.getEndDate())));
        }

        // 应用WHERE条件
        SelectConditionStep<Record> query = selectQuery.where(whereCondition);

        // 排序逻辑
        String sortField = form.getSortField();
        SortOrder sortOrder = form.getSortOrder();
        
        Field<?> orderField;
        if ("chargeBoxId".equals(sortField)) {
            orderField = CHARGER_ISSUE.CHARGE_BOX_PK;
        } else if ("status".equals(sortField)) {
            orderField = CHARGER_ISSUE.STATUS;
        } else if ("reportTime".equals(sortField)) {
            orderField = CHARGER_ISSUE.REPORT_TIME;
        } else {
            orderField = CHARGER_ISSUE.ISSUE_ID;
        }
        
        Result<Record> result;
        // 应用排序
        if (sortOrder == SortOrder.ASC) {
            result = query.orderBy(orderField.asc()).fetch();
        } else {
            result = query.orderBy(orderField.desc()).fetch();
        }

        // 返回结果
        return extractIssuesFromResult(result);
    }

    private List<ChargerIssueDTO> extractIssuesFromResult(Result<Record> result) {
        if (result.isEmpty()) {
            return Collections.emptyList();
        }

        List<ChargerIssueDTO> issues = new ArrayList<>();
        for (Record r : result) {
            ChargerIssueDTO dto = buildChargerIssueDTO(r);
            issues.add(dto);
        }

        return issues;
    }

    private ChargerIssueDTO buildChargerIssueDTO(Record r) {
        int issueId = r.get(CHARGER_ISSUE.ISSUE_ID);

        // Get charge box detail
        Record chargeBoxRecord = ctx.select(CHARGE_BOX.CHARGE_BOX_ID, CHARGE_BOX.DESCRIPTION)
                                   .from(CHARGE_BOX)
                                   .where(CHARGE_BOX.CHARGE_BOX_PK.eq(r.get(CHARGER_ISSUE.CHARGE_BOX_PK)))
                                   .fetchOne();

        // Get reporter user detail
        Record reporterRecord = null;
        if (r.get(CHARGER_ISSUE.REPORTER_USER_PK) != null) {
            reporterRecord = ctx.select(WEB_USER.USERNAME)
                               .from(WEB_USER)
                               .where(WEB_USER.WEB_USER_PK.eq(r.get(CHARGER_ISSUE.REPORTER_USER_PK)))
                               .fetchOne();
        }

        String chargeBoxId = chargeBoxRecord != null ? chargeBoxRecord.get(CHARGE_BOX.CHARGE_BOX_ID) : null;
        String reporterUsername = reporterRecord != null ? reporterRecord.get(WEB_USER.USERNAME) : null;
        
        DateTime reportTime = r.get(CHARGER_ISSUE.REPORT_TIME);
        DateTime resolveTime = r.get(CHARGER_ISSUE.RESOLVE_TIME);

        ChargerIssueDTO dto = ChargerIssueDTO.builder()
                .issueId(issueId)
                .chargeBoxId(chargeBoxId)
                .chargeBoxPk(r.get(CHARGER_ISSUE.CHARGE_BOX_PK))
                .reportTime(reportTime != null ? reportTime.toDate() : null)
                .reporterUserPk(r.get(CHARGER_ISSUE.REPORTER_USER_PK))
                .reporterUsername(reporterUsername)
                .isAutoReported(r.get(CHARGER_ISSUE.IS_AUTO_REPORTED))
                .ocppErrorCode(r.get(CHARGER_ISSUE.OCPP_ERROR_CODE))
                .faultDescription(r.get(CHARGER_ISSUE.FAULT_DESCRIPTION))
                .status(fromDbEnum(r.get(CHARGER_ISSUE.STATUS)))
                .resolveDescription(r.get(CHARGER_ISSUE.RESOLVE_DESCRIPTION))
                .resolveTime(resolveTime != null ? resolveTime.toDate() : null)
                .vendorErrorCode(r.get(CHARGER_ISSUE.VENDOR_ERROR_CODE))
                .vendorId(r.get(CHARGER_ISSUE.VENDOR_ID))
                .build();
                
        // 设置图片路径
        dto.setImagePaths(getIssueImages(issueId));
        
        return dto;
    }

    @Override
    public int getIssueCountByStatus(ChargerIssueStatus status) {
        return ctx.selectCount()
                  .from(CHARGER_ISSUE)
                  .where(CHARGER_ISSUE.STATUS.eq(toDbEnum(status)))
                  .fetchOne(0, int.class);
    }

    @Override
    public int getIssueCountByStatusAndUser(ChargerIssueStatus status, Integer webUserPk) {
        return ctx.selectCount()
                  .from(CHARGER_ISSUE)
                  .join(USER_CHARGE_BOX).on(USER_CHARGE_BOX.CHARGE_BOX_PK.eq(CHARGER_ISSUE.CHARGE_BOX_PK))
                  .where(CHARGER_ISSUE.STATUS.eq(toDbEnum(status)))
                  .and(USER_CHARGE_BOX.WEB_USER_PK.eq(webUserPk))
                  .fetchOne(0, int.class);
    }
}