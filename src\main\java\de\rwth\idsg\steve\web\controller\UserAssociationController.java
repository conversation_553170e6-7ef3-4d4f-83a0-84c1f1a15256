/*
 * SteV<PERSON> - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.web.controller;

import de.rwth.idsg.steve.repository.UserRepository;
import de.rwth.idsg.steve.repository.WebUserRepository;
import de.rwth.idsg.steve.repository.dto.User;
import de.rwth.idsg.steve.service.WebUserService;
import de.rwth.idsg.steve.web.dto.UserAssociationDTO;
import de.rwth.idsg.steve.web.dto.UserQueryForm;
import jooq.steve.db.tables.records.UserRecord;
import jooq.steve.db.tables.records.WebUserRecord;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.ArrayList;
import java.util.List;

/**
 * 用户关联管理控制器
 * 用于管理user表和web_user表之间的关联关系
 */
@Slf4j
@Controller
@RequestMapping(value = "/manager/user-associations")
@PreAuthorize("hasAuthority('ADMIN')")
@RequiredArgsConstructor
public class UserAssociationController {

    private final WebUserService webUserService;
    private final WebUserRepository webUserRepository;
    private final UserRepository userRepository;

    /**
     * 显示用户关联概览页面
     */
    @RequestMapping(method = RequestMethod.GET)
    public String getOverview(Model model) {
        try {
            List<UserAssociationDTO> associations = buildAssociationList();
            model.addAttribute("associations", associations);
            
            // 获取所有未关联的user记录，用于关联选择
            List<User.Overview> availableUsers = userRepository.getOverview(new UserQueryForm());
            model.addAttribute("availableUsers", availableUsers);
            
            return "data-man/user-associations";
        } catch (Exception e) {
            log.error("Error loading user associations: {}", e.getMessage(), e);
            model.addAttribute("errorMessage", "Error loading user associations: " + e.getMessage());
            return "data-man/user-associations";
        }
    }

    /**
     * 建立用户关联
     */
    @RequestMapping(value = "/associate", method = RequestMethod.POST)
    public String associateUser(@RequestParam("webUserPk") int webUserPk,
                               @RequestParam("userPk") int userPk,
                               Model model) {
        try {
            log.info("Associating web_user_pk {} with user_pk {}", webUserPk, userPk);
            webUserService.associateWithUser(webUserPk, userPk);
            model.addAttribute("successMessage", "User association successful");
        } catch (Exception e) {
            log.error("Error associating users: {}", e.getMessage(), e);
            model.addAttribute("errorMessage", "Error associating users: " + e.getMessage());
        }
        return getOverview(model);
    }

    /**
     * 解除用户关联
     */
    @RequestMapping(value = "/disassociate/{webUserPk}", method = RequestMethod.POST)
    public String disassociateUser(@PathVariable("webUserPk") int webUserPk, Model model) {
        try {
            log.info("Disassociating web_user_pk {}", webUserPk);
            webUserService.disassociateFromUser(webUserPk);
            model.addAttribute("successMessage", "User association removed");
        } catch (Exception e) {
            log.error("Error disassociating user: {}", e.getMessage(), e);
            model.addAttribute("errorMessage", "Error removing user association: " + e.getMessage());
        }
        return getOverview(model);
    }

    /**
     * 构建用户关联列表
     */
    private List<UserAssociationDTO> buildAssociationList() {
        List<UserAssociationDTO> associations = new ArrayList<>();
        
        // 获取所有web_user记录
        List<WebUserRecord> webUsers = webUserRepository.getAllUsers();
        
        for (WebUserRecord webUser : webUsers) {
            UserAssociationDTO dto = UserAssociationDTO.builder()
                .webUserPk(webUser.getWebUserPk())
                .webUsername(webUser.getUsername())
                .webUserRole(de.rwth.idsg.steve.web.dto.UserRole.valueOf(webUser.getUserRole().name()))
                .webUserEnabled(webUser.getEnabled())
                .build();
            
            // 获取关联的user信息
            Integer associatedUserPk = webUserRepository.getAssociatedUserPk(webUser.getWebUserPk());
            if (associatedUserPk != null) {
                try {
                    User.Details userDetails = userRepository.getDetails(associatedUserPk);
                    if (userDetails != null) {
                        UserRecord userRecord = userDetails.getUserRecord();
                        dto.setAssociatedUserPk(associatedUserPk);
                        dto.setAssociatedUserName(userRecord.getFirstName() + " " + userRecord.getLastName());
                        dto.setAssociatedUserEmail(userRecord.getEMail());
                        dto.setAssociatedUserPhone(userRecord.getPhone());
                    }
                } catch (Exception e) {
                    log.warn("Error getting user details for user_pk {}: {}", associatedUserPk, e.getMessage());
                }
            }
            
            associations.add(dto);
        }
        
        return associations;
    }
}
