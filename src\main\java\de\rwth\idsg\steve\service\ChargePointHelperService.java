/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.service;

import com.google.common.util.concurrent.Striped;
import de.rwth.idsg.steve.ocpp.OcppProtocol;
import de.rwth.idsg.steve.ocpp.OcppTransport;
import de.rwth.idsg.steve.ocpp.OcppVersion;
import de.rwth.idsg.steve.ocpp.ws.AbstractWebSocketEndpoint;
import de.rwth.idsg.steve.ocpp.ws.data.SessionContext;
import de.rwth.idsg.steve.ocpp.ws.ocpp12.Ocpp12WebSocketEndpoint;
import de.rwth.idsg.steve.ocpp.ws.ocpp15.Ocpp15WebSocketEndpoint;
import de.rwth.idsg.steve.ocpp.ws.ocpp16.Ocpp16WebSocketEndpoint;
import de.rwth.idsg.steve.repository.ChargePointRepository;
import de.rwth.idsg.steve.repository.GenericRepository;
import de.rwth.idsg.steve.repository.dto.ChargePointSelect;
import de.rwth.idsg.steve.repository.dto.ConnectorStatus;
import de.rwth.idsg.steve.service.dto.UnidentifiedIncomingObject;
import de.rwth.idsg.steve.utils.ConnectorStatusCountFilter;
import de.rwth.idsg.steve.utils.DateTimeUtils;
import de.rwth.idsg.steve.repository.dto.ChargePoint;
import de.rwth.idsg.steve.web.dto.ChargePointQueryForm;
import de.rwth.idsg.steve.web.dto.ConnectorStatusForm;
import de.rwth.idsg.steve.web.dto.OcppJsonStatus;
import de.rwth.idsg.steve.web.dto.Statistics;
import de.rwth.idsg.steve.web.controller.ChargePointsController.ImportProgress;
import lombok.extern.slf4j.Slf4j;
import ocpp.cs._2015._10.RegistrationStatus;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Deque;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.locks.Lock;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import org.jooq.exception.IntegrityConstraintViolationException;

import static de.rwth.idsg.steve.SteveConfiguration.CONFIG;

/**
 * <AUTHOR> Goekay <<EMAIL>>
 * @since 24.03.2015
 */
@Slf4j
@Service
public class ChargePointHelperService {

    private final boolean autoRegisterUnknownStations = CONFIG.getOcpp().isAutoRegisterUnknownStations();
    private final Striped<Lock> isRegisteredLocks = Striped.lock(16);

    @Autowired private GenericRepository genericRepository;

    // SOAP-based charge points are stored in DB with an endpoint address
    @Autowired private ChargePointRepository chargePointRepository;

    // For WebSocket-based charge points, the active sessions are stored in memory
    @Autowired private Ocpp12WebSocketEndpoint ocpp12WebSocketEndpoint;
    @Autowired private Ocpp15WebSocketEndpoint ocpp15WebSocketEndpoint;
    @Autowired private Ocpp16WebSocketEndpoint ocpp16WebSocketEndpoint;

    private final UnidentifiedIncomingObjectService unknownChargePointService = new UnidentifiedIncomingObjectService(100);

    @Async
    public void importChargePointsFromIni(byte[] fileContent, String taskId, Map<String, ImportProgress> importTasks) {
        ImportProgress progress = importTasks.get(taskId);
        if (progress == null) {
            log.error("ImportProgress not found for task {}", taskId);
            return;
        }

        List<String> chargePointIdsInFile = new ArrayList<>();
        Pattern pattern = Pattern.compile("^\\s*\\[([^]]+)]\\s*$");

        try (ByteArrayInputStream byteStream = new ByteArrayInputStream(fileContent);
             BufferedReader reader = new BufferedReader(new InputStreamReader(byteStream))) {
            String line;
            while ((line = reader.readLine()) != null) {
                Matcher matcher = pattern.matcher(line);
                if (matcher.matches()) {
                    chargePointIdsInFile.add(matcher.group(1).trim());
                }
            }
        } catch (IOException e) {
            log.error("Error reading INI file for task {}", taskId, e);
            progress.setFinished(true);
            return;
        }

        // 去重处理，避免文件中包含重复的充电桩ID
        List<String> uniqueChargePointIds = chargePointIdsInFile.stream()
                .distinct()
                .collect(Collectors.toList());

        progress.setTotal(uniqueChargePointIds.size());
        List<String> newChargePoints = new ArrayList<>();

        for (String chargePointId : uniqueChargePointIds) {
            if (chargePointRepository.getChargeBoxPkFromChargeBoxId(chargePointId) != null) {
                progress.incrementExisted();
            } else {
                newChargePoints.add(chargePointId);
            }
            progress.incrementProcessed();
        }

        if (!newChargePoints.isEmpty()) {
            try {
                addChargePointsSafely(newChargePoints, progress);
            } catch (Exception e) {
                log.error("Error batch inserting new charge points for task {}", taskId, e);
            }
        }
        
        progress.setFinished(true);
    }
    
    /**
     * 获取充电桩所属机构/拥有者信息
     * 
     * @param chargeBoxPk 充电桩主键
     * @return 拥有者信息，若未找到则返回空字符串
     */
    public String getChargePointOwner(Integer chargeBoxPk) {
        if (chargeBoxPk == null) {
            return "";
        }
        
        try {
            // 这里简单返回一个静态值，实际应用中可以从数据库或其他服务获取
            // 后续可以通过查询 user_charge_box 表和 user 表来获取真实的拥有者信息
            return "默认运营商";
        } catch (Exception e) {
            log.error("获取充电桩拥有者信息失败: {}", chargeBoxPk, e);
            return "";
        }
    }

    public int addChargePoint(de.rwth.idsg.steve.web.dto.ChargePointForm form) {
        return chargePointRepository.addChargePoint(form);
    }

    public void addChargePoints(List<String> idList) {
        chargePointRepository.addChargePointList(idList);
    }

    /**
     * 安全地添加充电桩列表，处理可能的重复键异常
     *
     * @param idList 充电桩ID列表
     * @param progress 导入进度对象
     */
    private void addChargePointsSafely(List<String> idList, ImportProgress progress) {
        try {
            // 尝试批量插入
            chargePointRepository.addChargePointList(idList);
            progress.addAndGetAdded(idList.size());
            log.info("Successfully added {} charge points", idList.size());
        } catch (IntegrityConstraintViolationException e) {
            // 如果批量插入失败（可能由于重复键），则逐个插入
            log.warn("Batch insert failed due to constraint violation, falling back to individual inserts: {}", e.getMessage());
            addChargePointsIndividually(idList, progress);
        } catch (Exception e) {
            // 其他异常，记录错误但不中断处理
            log.error("Unexpected error during batch insert, falling back to individual inserts", e);
            addChargePointsIndividually(idList, progress);
        }
    }

    /**
     * 逐个添加充电桩，跳过已存在的充电桩
     *
     * @param idList 充电桩ID列表
     * @param progress 导入进度对象
     */
    private void addChargePointsIndividually(List<String> idList, ImportProgress progress) {
        int addedCount = 0;
        int skippedCount = 0;

        for (String chargePointId : idList) {
            try {
                // 再次检查是否已存在（可能在批量检查后被其他线程插入）
                if (chargePointRepository.getChargeBoxPkFromChargeBoxId(chargePointId) != null) {
                    skippedCount++;
                    log.debug("Charge point {} already exists, skipping", chargePointId);
                    continue;
                }

                // 尝试插入单个充电桩
                chargePointRepository.addChargePointList(Collections.singletonList(chargePointId));
                addedCount++;
                log.debug("Successfully added charge point {}", chargePointId);

            } catch (IntegrityConstraintViolationException e) {
                // 充电桩已存在（可能由于并发插入），跳过
                skippedCount++;
                log.debug("Charge point {} already exists (concurrent insert), skipping: {}", chargePointId, e.getMessage());
            } catch (Exception e) {
                // 其他错误，记录但继续处理下一个
                log.error("Failed to add charge point {}: {}", chargePointId, e.getMessage());
            }
        }

        progress.addAndGetAdded(addedCount);
        if (skippedCount > 0) {
            log.info("Added {} charge points, skipped {} existing charge points", addedCount, skippedCount);
        } else {
            log.info("Added {} charge points", addedCount);
        }
    }

    public Optional<RegistrationStatus> getRegistrationStatus(String chargeBoxId) {
        Lock l = isRegisteredLocks.get(chargeBoxId);
        l.lock();
        try {
            Optional<RegistrationStatus> status = getRegistrationStatusInternal(chargeBoxId);
            if (status.isEmpty()) {
                unknownChargePointService.processNewUnidentified(chargeBoxId);
            }
            return status;
        } finally {
            l.unlock();
        }
    }

    public Statistics getStats() {
        return getStatsByUser(null);
    }

    /**
     * 获取基于用户权限过滤的统计数据
     *
     * @param webUserPk 用户主键，如果为null则返回所有数据
     * @return 统计数据
     */
    public Statistics getStatsByUser(Integer webUserPk) {
        Statistics stats = genericRepository.getStatsByUser(webUserPk);

        // WebSocket连接数量暂时保持全局统计，因为这些是实时连接状态
        // TODO: 如果需要，可以后续添加基于用户的WebSocket连接过滤
        stats.setNumOcpp12JChargeBoxes(ocpp12WebSocketEndpoint.getNumberOfChargeBoxes());
        stats.setNumOcpp15JChargeBoxes(ocpp15WebSocketEndpoint.getNumberOfChargeBoxes());
        stats.setNumOcpp16JChargeBoxes(ocpp16WebSocketEndpoint.getNumberOfChargeBoxes());

        // 获取连接器状态，需要根据用户权限过滤
        List<ConnectorStatus> latestList;
        if (webUserPk != null) {
            // 对于特定用户，只获取他们拥有的充电桩的连接器状态
            latestList = getChargePointConnectorStatusByUser(webUserPk);
        } else {
            // 管理员或工厂运营商，获取所有连接器状态
            latestList = chargePointRepository.getChargePointConnectorStatus();
        }

        stats.setStatusCountMap(ConnectorStatusCountFilter.getStatusCountMap(latestList));

        return stats;
    }

    /**
     * 获取特定用户拥有的充电桩的连接器状态
     *
     * @param webUserPk 用户主键
     * @return 连接器状态列表
     */
    private List<ConnectorStatus> getChargePointConnectorStatusByUser(Integer webUserPk) {
        // 首先获取用户拥有的充电桩ID列表
        List<ChargePoint.Overview> userChargeBoxes = chargePointRepository.getOwnerChargeBoxes(webUserPk, new ChargePointQueryForm());
        List<String> chargeBoxIds = userChargeBoxes.stream()
                .map(ChargePoint.Overview::getChargeBoxId)
                .collect(Collectors.toList());

        if (chargeBoxIds.isEmpty()) {
            return Collections.emptyList();
        }

        // 获取所有连接器状态，然后过滤只属于用户的充电桩
        List<ConnectorStatus> allStatuses = chargePointRepository.getChargePointConnectorStatus();
        return allStatuses.stream()
                .filter(status -> chargeBoxIds.contains(status.getChargeBoxId()))
                .collect(Collectors.toList());
    }

    public List<ConnectorStatus> getChargePointConnectorStatus(ConnectorStatusForm params) {
        Map<String, Deque<SessionContext>> ocpp12Map = ocpp12WebSocketEndpoint.getACopy();
        Map<String, Deque<SessionContext>> ocpp15Map = ocpp15WebSocketEndpoint.getACopy();
        Map<String, Deque<SessionContext>> ocpp16Map = ocpp16WebSocketEndpoint.getACopy();

        Set<String> connectedJsonChargeBoxIds = new HashSet<>(extractIds(Arrays.asList(ocpp12Map, ocpp15Map, ocpp16Map)));

        List<ConnectorStatus> latestList = chargePointRepository.getChargePointConnectorStatus(params);

        // iterate over JSON stations and mark disconnected ones
        // https://github.com/steve-community/steve/issues/355
        //
        for (ConnectorStatus status : latestList) {
            OcppProtocol protocol = status.getOcppProtocol();
            if (protocol != null && protocol.getTransport() == OcppTransport.JSON) {
                status.setJsonAndDisconnected(!connectedJsonChargeBoxIds.contains(status.getChargeBoxId()));
            }
        }

        return latestList;
    }

    public List<OcppJsonStatus> getOcppJsonStatus() {
        Map<String, Deque<SessionContext>> ocpp12Map = ocpp12WebSocketEndpoint.getACopy();
        Map<String, Deque<SessionContext>> ocpp15Map = ocpp15WebSocketEndpoint.getACopy();
        Map<String, Deque<SessionContext>> ocpp16Map = ocpp16WebSocketEndpoint.getACopy();

        List<String> idList = extractIds(Arrays.asList(ocpp12Map, ocpp15Map, ocpp16Map));
        Map<String, Integer> primaryKeyLookup = chargePointRepository.getChargeBoxIdPkPair(idList);

        DateTime now = DateTime.now();
        List<OcppJsonStatus> returnList = new ArrayList<>();

        appendList(ocpp12Map, returnList, now, OcppVersion.V_12, primaryKeyLookup);
        appendList(ocpp15Map, returnList, now, OcppVersion.V_15, primaryKeyLookup);
        appendList(ocpp16Map, returnList, now, OcppVersion.V_16, primaryKeyLookup);
        return returnList;
    }

    public List<ChargePointSelect> getChargePoints(OcppVersion version) {
        return getChargePoints(version, Collections.singletonList(RegistrationStatus.ACCEPTED), Collections.emptyList());
    }

    public List<ChargePointSelect> getChargePoints(OcppVersion version, List<RegistrationStatus> inStatusFilter) {
        return getChargePoints(version, inStatusFilter, Collections.emptyList());
    }

    public List<ChargePointSelect> getChargePointsWithIds(OcppVersion version, List<String> chargeBoxIdFilter) {
        return getChargePoints(version, Collections.singletonList(RegistrationStatus.ACCEPTED), chargeBoxIdFilter);
    }

    public List<ChargePointSelect> getChargePoints(OcppVersion version, List<RegistrationStatus> inStatusFilter, List<String> chargeBoxIdFilter) {
        switch (version) {
            case V_12:
                return getChargePoints(OcppProtocol.V_12_SOAP, inStatusFilter, chargeBoxIdFilter, ocpp12WebSocketEndpoint);
            case V_15:
                return getChargePoints(OcppProtocol.V_15_SOAP, inStatusFilter, chargeBoxIdFilter, ocpp15WebSocketEndpoint);
            case V_16:
                return getChargePoints(OcppProtocol.V_16_SOAP, inStatusFilter, chargeBoxIdFilter, ocpp16WebSocketEndpoint);
            default:
                throw new IllegalArgumentException("Unknown OCPP version: " + version);
        }
    }

    public List<UnidentifiedIncomingObject> getUnknownChargePoints() {
        return unknownChargePointService.getObjects();
    }

    public void removeUnknown(List<String> chargeBoxIdList) {
        unknownChargePointService.removeAll(chargeBoxIdList);
    }

    // -------------------------------------------------------------------------
    // Helpers
    // -------------------------------------------------------------------------

    private Optional<RegistrationStatus> getRegistrationStatusInternal(String chargeBoxId) {
        // 1. exit if already registered
        Optional<String> status = chargePointRepository.getRegistrationStatus(chargeBoxId);
        if (status.isPresent()) {
            try {
                return Optional.ofNullable(RegistrationStatus.fromValue(status.get()));
            } catch (Exception e) {
                // in cases where the database entry (string) is altered, and therefore cannot be converted to enum
                log.error("Exception happened", e);
                return Optional.empty();
            }
        }

        // 2. ok, this chargeBoxId is unknown. exit if auto-register is disabled
        if (!autoRegisterUnknownStations) {
            return Optional.empty();
        }

        // 3. chargeBoxId is unknown and auto-register is enabled. insert chargeBoxId
        try {
            chargePointRepository.addChargePointList(Collections.singletonList(chargeBoxId));
            log.warn("Auto-registered unknown chargebox '{}'", chargeBoxId);
            return Optional.of(RegistrationStatus.ACCEPTED); // default db value is accepted
        } catch (Exception e) {
            log.error("Failed to auto-register unknown chargebox '" + chargeBoxId + "'", e);
            return Optional.empty();
        }
    }

    private List<ChargePointSelect> getChargePoints(OcppProtocol protocol, List<RegistrationStatus> inStatusFilter,
                                                    List<String> chargeBoxIdFilter, AbstractWebSocketEndpoint jsonEndpoint) {
        // soap stations
        //
        List<String> statusFilter = inStatusFilter.stream()
                                                  .map(RegistrationStatus::value)
                                                  .collect(Collectors.toList());

        List<ChargePointSelect> returnList = chargePointRepository.getChargePointSelect(protocol, statusFilter, chargeBoxIdFilter);

        // json stations
        //
        List<String> chargeBoxIdList = CollectionUtils.isEmpty(chargeBoxIdFilter)
            ? jsonEndpoint.getChargeBoxIdList()
            : jsonEndpoint.getChargeBoxIdList().stream().filter(chargeBoxIdFilter::contains).collect(Collectors.toList());

        var jsonProtocol = OcppProtocol.from(jsonEndpoint.getVersion(), OcppTransport.JSON);

        for (String chargeBoxId : chargeBoxIdList) {
            returnList.add(new ChargePointSelect(jsonProtocol, chargeBoxId));
        }

        return returnList;
    }

    private static List<String> extractIds(List<Map<String, Deque<SessionContext>>> ocppMaps) {
        return ocppMaps.stream()
                       .map(Map::keySet)
                       .flatMap(Collection::stream)
                       .collect(Collectors.toList());
    }

    private static void appendList(Map<String, Deque<SessionContext>> map, List<OcppJsonStatus> returnList,
                                   DateTime now, OcppVersion version, Map<String, Integer> primaryKeyLookup) {

        for (Map.Entry<String, Deque<SessionContext>> entry : map.entrySet()) {
            String chargeBoxId = entry.getKey();
            Deque<SessionContext> endpointDeque = entry.getValue();

            for (SessionContext ctx : endpointDeque) {
                DateTime openSince = ctx.getOpenSince();

                OcppJsonStatus status = OcppJsonStatus.builder()
                                                      .chargeBoxPk(primaryKeyLookup.get(chargeBoxId))
                                                      .chargeBoxId(chargeBoxId)
                                                      .connectedSinceDT(openSince)
                                                      .connectedSince(DateTimeUtils.humanize(openSince))
                                                      .connectionDuration(DateTimeUtils.timeElapsed(openSince, now))
                                                      .version(version)
                                                      .build();

                returnList.add(status);
            }
        }
    }
}
