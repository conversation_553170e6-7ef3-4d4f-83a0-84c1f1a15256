/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.web.dto;

import lombok.Builder;
import lombok.Getter;

import java.util.List;

/**
 * OCPP 1.5事务详情数据传输对象
 */
@Getter
@Builder
public class Ocpp15TransactionDetails {
    private final TransactionDetails transaction;
    private final List<MeterValue> values;

    @Getter
    @Builder
    public static class MeterValue {
        private final String timestamp;
        private final String value;
        private final String readingContext;
        private final String format;
        private final String measurand;
        private final String location;
        private final String unit;
    }
} 