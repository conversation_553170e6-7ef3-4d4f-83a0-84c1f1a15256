/**
 * Search Panel Styles - 已迁移到 unified-form-styles.css
 * 此文件保留用于向后兼容，主要样式已统一管理
 */

/* 保留一些特殊的搜索面板样式，避免与统一样式冲突 */

/* Form layout within search panel */
.search-panel form {
  width: 100%;
}

.search-panel .form-group {
  margin-bottom: 0;
}

.search-panel label {
  font-weight: 500;
  margin-bottom: 5px;
  display: block;
}

.search-panel .btn {
  margin-top: 5px;
  min-width: 120px;
}

/* Responsive grid system adjustments */
.search-panel .row {
  display: flex;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
}

.search-panel .col-md-4 {
  position: relative;
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  flex: 0 0 33.333333%;
  max-width: 33.333333%;
}

.search-panel .mb-3 {
  margin-bottom: 1rem !important;
}

.search-panel .d-flex {
  display: flex !important;
}

.search-panel .align-items-end {
  align-items: flex-end !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .search-panel .col-md-4 {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

/* Button styling */
.btn-standard {
  color: #333;
  background-color: #e2e6ea;
  border-color: #dae0e5;
}

.btn-standard:hover {
  color: #333;
  background-color: #d3d9df;
  border-color: #c8cfd6;
}

.btn-standard:focus, .btn-standard.focus {
  box-shadow: 0 0 0 0.2rem rgba(216, 217, 219, 0.5);
}

.btn-standard:active, .btn-standard.active {
  color: #333;
  background-color: #c8cfd6;
  border-color: #b9c1ca;
}

/* Text alignment classes */
.text-center {
  text-align: center !important;
} 