/*
 * SteV<PERSON> - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.web.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import java.io.File;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * Simple controller for downloading diagnostic files
 */
@Slf4j
@Controller
@RequestMapping("/manager/operations/download")
public class DiagnosticFileDownloadController {

    private static final String FTP_FILES_DIR = "ftp_files";

    @GetMapping("/{fileName}")
    public ResponseEntity<Resource> downloadFile(@PathVariable("fileName") String fileName) {
        log.info("Download request for file: {}", fileName);

        try {
            // 安全检查：只允许下载.zip文件
            if (!fileName.endsWith(".zip")) {
                log.warn("Invalid file type requested: {}", fileName);
                return ResponseEntity.badRequest().build();
            }

            // 安全检查：防止路径遍历攻击
            if (fileName.contains("..") || fileName.contains("/") || fileName.contains("\\")) {
                log.warn("Invalid file name with path traversal attempt: {}", fileName);
                return ResponseEntity.badRequest().build();
            }

            File actualFile = findActualFile(fileName);

            if (actualFile == null) {
                log.warn("File not found for request: {}", fileName);
                return ResponseEntity.notFound().build();
            }

            if (!actualFile.canRead()) {
                log.warn("File not readable: {}", actualFile.getAbsolutePath());
                return ResponseEntity.status(403).build();
            }

            Resource resource = new FileSystemResource(actualFile);

            log.info("Serving file: {} (size: {} bytes)", actualFile.getAbsolutePath(), actualFile.length());

            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + fileName + "\"")
                    .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE)
                    .header(HttpHeaders.CONTENT_LENGTH, String.valueOf(actualFile.length()))
                    .body(resource);

        } catch (Exception e) {
            log.error("Error downloading file {}: {}", fileName, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 查找实际的文件，支持原始文件名和重命名后的文件名
     */
    private File findActualFile(String requestedFileName) {
        Path ftpDir = Paths.get(FTP_FILES_DIR);
        File ftpDirFile = ftpDir.toFile();

        log.info("Looking for file: {} in directory: {}", requestedFileName, ftpDir.toAbsolutePath());

        if (!ftpDirFile.exists() || !ftpDirFile.isDirectory()) {
            log.warn("FTP directory does not exist: {}", ftpDir.toAbsolutePath());
            return null;
        }

        // 首先尝试直接匹配文件名
        File directMatch = new File(ftpDirFile, requestedFileName);
        if (directMatch.exists()) {
            log.info("Found direct match: {}", directMatch.getAbsolutePath());
            return directMatch;
        }

        // 如果直接匹配失败，尝试根据充电桩ID查找重命名后的文件
        // 从文件名中提取充电桩ID (例如: CP006_20250723_002124.zip -> CP006)
        String chargeBoxId = extractChargeBoxId(requestedFileName);
        if (chargeBoxId != null) {
            log.info("Extracted charge box ID: {} from requested file: {}", chargeBoxId, requestedFileName);

            // 先列出所有文件进行调试
            File[] allFiles = ftpDirFile.listFiles();
            if (allFiles != null) {
                log.info("All files in directory ({} files):", allFiles.length);
                for (File file : allFiles) {
                    if (file.isFile()) {
                        log.info("  - {} ({})", file.getName(), file.isFile() ? "file" : "dir");
                    }
                }
            }

            // 查找以该充电桩ID开头的诊断文件
            String pattern = chargeBoxId + "_diagnostics_";
            log.info("Looking for files with pattern: {}*.zip", pattern);

            File[] matchingFiles = ftpDirFile.listFiles((dir, name) -> {
                boolean matches = name.startsWith(pattern) && name.endsWith(".zip");
                log.info("Checking file: {} - matches: {}", name, matches);
                return matches;
            });

            if (matchingFiles != null && matchingFiles.length > 0) {
                log.info("Found {} matching files", matchingFiles.length);
                // 返回最新的文件（按修改时间排序）
                File latestFile = matchingFiles[0];
                for (File file : matchingFiles) {
                    if (file.lastModified() > latestFile.lastModified()) {
                        latestFile = file;
                    }
                }
                log.info("Selected latest file: {} (modified: {})", latestFile.getName(), latestFile.lastModified());
                return latestFile;
            } else {
                log.warn("No files found matching pattern: {}*.zip", pattern);
            }
        }

        log.warn("No matching file found for: {}", requestedFileName);
        return null;
    }

    /**
     * 从文件名中提取充电桩ID
     */
    private String extractChargeBoxId(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return null;
        }

        // 移除.zip扩展名
        String nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));

        // 按下划线分割，第一部分应该是充电桩ID
        String[] parts = nameWithoutExt.split("_");
        if (parts.length > 0) {
            return parts[0];
        }

        return null;
    }
}
