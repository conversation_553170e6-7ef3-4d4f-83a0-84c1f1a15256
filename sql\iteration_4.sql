-- Add station_id, location and web_user_pk fields to charging_station table
ALTER TABLE charging_station
ADD COLUMN station_id VARCHAR(255) NOT NULL DEFAULT '',
ADD COLUMN location VARCHAR(255) NULL,
ADD COLUMN web_user_pk INT NULL,
ADD FOREIGN KEY (web_user_pk) REFERENCES web_user(web_user_pk) ON DELETE SET NULL;

-- Create or update view_charging_stations view to include user information
DROP VIEW IF EXISTS view_charging_stations;
CREATE VIEW view_charging_stations AS
SELECT 
    cs.charging_station_pk,
    cs.station_id,
    cs.station_name,
    cs.operator_name,
    cs.location,
    cs.construction_date,
    cs.operation_date,
    cs.created_on,
    cs.last_updated_on,
    cs.web_user_pk,
    u.first_name,
    u.last_name,
    u.birth_day,
    u.sex,
    u.e_mail,
    u.phone
FROM 
    charging_station cs
LEFT JOIN 
    web_user wu ON cs.web_user_pk = wu.web_user_pk
LEFT JOIN 
    user u ON wu.web_user_pk = u.web_user_pk; 