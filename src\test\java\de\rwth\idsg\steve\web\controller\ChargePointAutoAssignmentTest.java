/*
 * SteV<PERSON> - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.web.controller;

import de.rwth.idsg.steve.repository.ChargePointRepository;
import de.rwth.idsg.steve.repository.ChargeStationRepository;
import de.rwth.idsg.steve.repository.UserChargeBoxRepository;
import de.rwth.idsg.steve.repository.dto.ChargeStation;
import de.rwth.idsg.steve.service.ChargePointHelperService;
import de.rwth.idsg.steve.service.PermissionService;
import de.rwth.idsg.steve.web.dto.ChargePointForm;
import jooq.steve.db.tables.records.WebUserRecord;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * Test for auto-assignment functionality when Charge Box Owner creates charge points and stations
 */
@ExtendWith(MockitoExtension.class)
public class ChargePointAutoAssignmentTest {

    @Mock
    private ChargePointRepository chargePointRepository;
    
    @Mock
    private ChargeStationRepository chargeStationRepository;
    
    @Mock
    private ChargePointHelperService chargePointHelperService;
    
    @Mock
    private PermissionService permissionService;
    
    @Mock
    private UserChargeBoxRepository userChargeBoxRepository;
    
    @Mock
    private Model model;
    
    @Mock
    private BindingResult bindingResult;

    @InjectMocks
    private ChargePointsController chargePointsController;
    
    @InjectMocks
    private ChargeStationController chargeStationController;

    private WebUserRecord mockUser;
    private ChargePointForm chargePointForm;
    private ChargeStation.Form chargeStationForm;

    @BeforeEach
    void setUp() {
        mockUser = new WebUserRecord();
        mockUser.setWebUserPk(123);
        mockUser.setUsername("testowner");
        
        chargePointForm = new ChargePointForm();
        chargePointForm.setChargeBoxId("TEST_CHARGE_BOX_001");
        chargePointForm.setRegistrationStatus("Accepted");
        chargePointForm.setInsertConnectorStatusAfterTransactionMsg(false);
        
        chargeStationForm = new ChargeStation.Form();
        chargeStationForm.setStationName("Test Station");
        chargeStationForm.setOperatorName("Test Operator");
        chargeStationForm.setLocation("Test Location");
    }

    @Test
    void testChargePointAutoAssignmentForOwner() throws Exception {
        // Given
        when(permissionService.isOperatorOwner()).thenReturn(true);
        when(permissionService.getCurrentUser()).thenReturn(mockUser);
        when(chargePointHelperService.addChargePoint(any(ChargePointForm.class))).thenReturn(456);
        when(bindingResult.hasErrors()).thenReturn(false);

        // When
        String result = chargePointsController.addSinglePost(chargePointForm, bindingResult, model);

        // Then
        verify(chargePointHelperService).addChargePoint(chargePointForm);
        verify(userChargeBoxRepository).assignChargeBoxToUser(123, 456);
        verify(chargePointHelperService).removeUnknown(any());
    }

    @Test
    void testChargePointNoAutoAssignmentForAdmin() throws Exception {
        // Given
        when(permissionService.isOperatorOwner()).thenReturn(false);
        when(chargePointHelperService.addChargePoint(any(ChargePointForm.class))).thenReturn(456);
        when(bindingResult.hasErrors()).thenReturn(false);

        // When
        String result = chargePointsController.addSinglePost(chargePointForm, bindingResult, model);

        // Then
        verify(chargePointHelperService).addChargePoint(chargePointForm);
        verify(userChargeBoxRepository, never()).assignChargeBoxToUser(anyInt(), anyInt());
        verify(chargePointHelperService).removeUnknown(any());
    }

    @Test
    void testChargeStationAutoAssignmentForOwner() throws Exception {
        // Given
        when(permissionService.isOperatorOwner()).thenReturn(true);
        when(permissionService.getCurrentUser()).thenReturn(mockUser);
        when(bindingResult.hasErrors()).thenReturn(false);

        // When
        String result = chargeStationController.addPost(chargeStationForm, bindingResult, model);

        // Then
        verify(chargeStationRepository).addChargeStation(chargeStationForm);
        // Verify that webUserPk was set to the current user's PK
        assert chargeStationForm.getWebUserPk().equals(123);
    }

    @Test
    void testChargeStationNoAutoAssignmentForAdmin() throws Exception {
        // Given
        when(permissionService.isOperatorOwner()).thenReturn(false);
        when(bindingResult.hasErrors()).thenReturn(false);

        // When
        String result = chargeStationController.addPost(chargeStationForm, bindingResult, model);

        // Then
        verify(chargeStationRepository).addChargeStation(chargeStationForm);
        // Verify that webUserPk was not set
        assert chargeStationForm.getWebUserPk() == null;
    }

    @Test
    void testChargePointAutoAssignmentHandlesException() throws Exception {
        // Given
        when(permissionService.isOperatorOwner()).thenReturn(true);
        when(permissionService.getCurrentUser()).thenReturn(mockUser);
        when(chargePointHelperService.addChargePoint(any(ChargePointForm.class))).thenReturn(456);
        when(bindingResult.hasErrors()).thenReturn(false);
        doThrow(new RuntimeException("Database error")).when(userChargeBoxRepository)
            .assignChargeBoxToUser(anyInt(), anyInt());

        // When
        String result = chargePointsController.addSinglePost(chargePointForm, bindingResult, model);

        // Then
        verify(chargePointHelperService).addChargePoint(chargePointForm);
        verify(userChargeBoxRepository).assignChargeBoxToUser(123, 456);
        // Should still complete successfully despite assignment failure
        verify(chargePointHelperService).removeUnknown(any());
    }
}
