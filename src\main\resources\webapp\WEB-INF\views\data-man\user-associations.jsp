<%--

    SteV<PERSON> - SteckdosenVerwaltung - https://github.com/steve-community/steve
    Copyright (C) 2013-2025 SteVe Community Team
    All Rights Reserved.

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.

--%>
<%@ include file="../00-header.jsp" %>
<script type="text/javascript">
    $(document).ready(function() {
        <%@ include file="../snippets/sortable.js" %>
    });
</script>

<div class="content-container">
    <div class="page-title">User Association Management</div>
    
    <!-- 成功/错误消息 -->
    <c:if test="${not empty successMessage}">
        <div class="alert alert-success">${successMessage}</div>
    </c:if>
    <c:if test="${not empty errorMessage}">
        <div class="alert alert-danger">${errorMessage}</div>
    </c:if>
    
    <section><span>User Association Overview</span></section>
    
    <div class="info-panel mb-4">
        <p><strong>Description:</strong> This page is used to manage the association between Web users (web_user table) and charging users (user table).</p>
        <ul>
            <li><strong>Associated:</strong> Web user is linked to a charging user and can manage corresponding charging business</li>
            <li><strong>Not Associated:</strong> Web user is only used for system management, with no corresponding charging user</li>
        </ul>
    </div>
    
    <div class="table-responsive">
        <table class="table table-striped table-hover table-bordered">
            <thead>
                <tr>
                    <th class="sorting" data-sort="int">Web User ID</th>
                    <th class="sorting" data-sort="string">Username</th>
                    <th class="sorting" data-sort="string">Role</th>
                    <th class="sorting" data-sort="string">Status</th>
                    <th class="sorting" data-sort="string">Association Status</th>
                    <th class="sorting" data-sort="string">Associated User</th>
                    <th class="text-center">Actions</th>
                </tr>
            </thead>
            <tbody>
            <c:forEach items="${associations}" var="assoc">
                <tr>
                    <td>${assoc.webUserPk}</td>
                    <td><strong>${assoc.webUsername}</strong></td>
                    <td>
                        <span class="badge badge-${assoc.webUserRole == 'ADMIN' ? 'danger' : 
                                                   assoc.webUserRole == 'OPERATOR_FACTORY' ? 'warning' : 'info'}">
                            ${assoc.webUserRole}
                        </span>
                    </td>
                    <td>
                        <span class="badge badge-${assoc.webUserEnabled ? 'success' : 'secondary'}">
                            ${assoc.webUserEnabled ? 'Enabled' : 'Disabled'}
                        </span>
                    </td>
                    <td>
                        <span class="badge badge-${assoc.associated ? 'success' : 'warning'}">
                            ${assoc.associationStatus}
                        </span>
                    </td>
                    <td>
                        <c:choose>
                            <c:when test="${assoc.associated}">
                                <div>
                                    <strong>${assoc.associatedUserName}</strong><br>
                                    <small class="text-muted">
                                        <c:if test="${not empty assoc.associatedUserEmail}">
                                            Email: ${assoc.associatedUserEmail}<br>
                                        </c:if>
                                        <c:if test="${not empty assoc.associatedUserPhone}">
                                            Phone: ${assoc.associatedUserPhone}
                                        </c:if>
                                    </small>
                                </div>
                            </c:when>
                            <c:otherwise>
                                <span class="text-muted">No Associated User</span>
                            </c:otherwise>
                        </c:choose>
                    </td>
                    <td class="text-center">
                        <c:choose>
                            <c:when test="${assoc.associated}">
                                <!-- Disassociate Button -->
                                <form action="${ctxPath}/manager/user-associations/disassociate/${assoc.webUserPk}"
                                      method="post" style="display: inline;">
                                    <input type="submit" class="btn btn-sm btn-warning" value="Disassociate"
                                           onclick="return confirm('Are you sure you want to disassociate?');">
                                    <input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}"/>
                                </form>
                            </c:when>
                            <c:otherwise>
                                <!-- Associate Dropdown -->
                                <form action="${ctxPath}/manager/user-associations/associate"
                                      method="post" style="display: inline;">
                                    <select name="userPk" class="form-control form-control-sm" style="width: auto; display: inline-block;">
                                        <option value="">Select User...</option>
                                        <c:forEach items="${availableUsers}" var="user">
                                            <option value="${user.userPk}">
                                                ${user.name} (${user.userPk})
                                            </option>
                                        </c:forEach>
                                    </select>
                                    <input type="hidden" name="webUserPk" value="${assoc.webUserPk}">
                                    <input type="submit" class="btn btn-sm btn-success" value="Associate">
                                    <input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}"/>
                                </form>
                            </c:otherwise>
                        </c:choose>
                    </td>
                </tr>
            </c:forEach>
            </tbody>
        </table>
    </div>
    
    <div class="mt-4">
        <h5>Statistics</h5>
        <div class="row">
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body text-center">
                        <h6 class="card-title">Total Web Users</h6>
                        <h4 class="text-primary">${associations.size()}</h4>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body text-center">
                        <h6 class="card-title">Associated Users</h6>
                        <h4 class="text-success">
                            <c:set var="associatedCount" value="0"/>
                            <c:forEach items="${associations}" var="assoc">
                                <c:if test="${assoc.associated}">
                                    <c:set var="associatedCount" value="${associatedCount + 1}"/>
                                </c:if>
                            </c:forEach>
                            ${associatedCount}
                        </h4>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body text-center">
                        <h6 class="card-title">Unassociated Users</h6>
                        <h4 class="text-warning">${associations.size() - associatedCount}</h4>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body text-center">
                        <h6 class="card-title">Association Rate</h6>
                        <h4 class="text-info">
                            <c:choose>
                                <c:when test="${associations.size() > 0}">
                                    <fmt:formatNumber value="${associatedCount * 100.0 / associations.size()}"
                                                    maxFractionDigits="1"/>%
                                </c:when>
                                <c:otherwise>0%</c:otherwise>
                            </c:choose>
                        </h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<%@ include file="../00-footer.jsp" %>
