/**
 * 布局控制脚本
 * 负责左侧导航菜单的展开/折叠等交互功能
 */
document.addEventListener('DOMContentLoaded', function() {
    // 左侧导航菜单子菜单展开/折叠
    const submenus = document.querySelectorAll('.left-sidebar ul li.submenu > a');
    submenus.forEach(function(submenu) {
        submenu.addEventListener('click', function(e) {
            e.preventDefault();
            const parent = this.parentNode;
            
            // 如果当前菜单已经是打开状态，则关闭它
            if(parent.classList.contains('open')) {
                parent.classList.remove('open');
            } else {
                // 先关闭其他所有已打开的子菜单
                document.querySelectorAll('.left-sidebar ul li.submenu.open').forEach(function(item) {
                    if(item !== parent) {
                        item.classList.remove('open');
                    }
                });
                
                // 打开当前菜单
                parent.classList.add('open');
            }
        });
    });
    
    // 设置当前活动菜单
    const setActiveMenu = function() {
        const currentPath = window.location.pathname;
        const links = document.querySelectorAll('.left-sidebar ul li a');
        
        links.forEach(function(link) {
            const href = link.getAttribute('href');
            if(href && currentPath.includes(href)) {
                link.classList.add('active');
                
                // 如果活动链接在子菜单中，则打开父菜单
                const parentSubmenu = link.closest('.submenu');
                if(parentSubmenu) {
                    parentSubmenu.classList.add('open');
                }
            }
        });
    };
    
    setActiveMenu();
    
    // 响应式导航收起按钮
    const sidebarToggle = document.querySelector('.sidebar-toggle');
    if(sidebarToggle) {
        sidebarToggle.addEventListener('click', function() {
            document.body.classList.toggle('sidebar-collapsed');
        });
    }
    
    // 点击内容区域时，在小屏幕上自动关闭导航菜单
    const mainContent = document.querySelector('.main-content');
    if(mainContent) {
        mainContent.addEventListener('click', function() {
            if(window.innerWidth < 992 && document.body.classList.contains('sidebar-expanded')) {
                document.body.classList.remove('sidebar-expanded');
            }
        });
    }
}); 