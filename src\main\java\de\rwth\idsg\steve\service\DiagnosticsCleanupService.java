/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 诊断任务清理服务
 * 定期清理过期的诊断任务和相关文件，防止系统资源浪费
 * 
 * <AUTHOR> Community Team
 * @since 2025.01
 */
@Slf4j
@Service
public class DiagnosticsCleanupService {
    
    @Autowired
    private DiagnosticsTaskManager taskManager;

    @Autowired
    private TaskStoreCleanupService taskStoreCleanupService;
    
    /**
     * 清理间隔时间（分钟）
     */
    @Value("${steve.diagnostics.cleanup.interval-minutes:5}")
    private int cleanupIntervalMinutes;
    
    /**
     * 服务启动时的初始化
     */
    @PostConstruct
    public void init() {
        log.info("🧹 DiagnosticsCleanupService initialized");
        log.info("📋 Task expiry time: {} minutes", taskManager.getTaskExpiryMinutes());
        log.info("⏰ Cleanup interval: {} minutes", cleanupIntervalMinutes);
        log.info("🔧 DiagnosticsTaskManager instance: {}", taskManager);

        // 启动时执行一次清理
        performCleanup();
    }
    
    /**
     * 定时清理过期的诊断任务
     * 使用Spring的@Scheduled注解，根据配置的间隔时间执行
     */
    @Scheduled(fixedRateString = "#{${steve.diagnostics.cleanup.interval-minutes:5} * 60 * 1000}")
    public void scheduledCleanup() {
        log.info("⏰ Scheduled cleanup triggered at {}",
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        performCleanup();
    }
    
    /**
     * 执行清理操作
     */
    private void performCleanup() {
        try {
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            log.info("🧹 Starting diagnostics cleanup at {}", timestamp);

            // 记录清理前的统计信息
            String statsBefore = taskManager.getTaskStatistics();
            log.info("📊 Before cleanup: {}", statsBefore);

            // 清理过期任务
            int cleanedTasks = taskManager.cleanupExpiredTasks();

            if (cleanedTasks > 0) {
                log.info("✅ Diagnostics cleanup completed: {} expired tasks cleaned", cleanedTasks);

                // 立即触发TaskStore清理，确保已完成的任务被移除
                log.info("🔄 Triggering immediate TaskStore cleanup after diagnostics cleanup");
                try {
                    int taskStoreCleanedTasks = taskStoreCleanupService.manualTaskStoreCleanup();
                    log.info("✅ TaskStore cleanup completed: {} finished tasks removed", taskStoreCleanedTasks);
                } catch (Exception taskStoreException) {
                    log.warn("⚠️ Error during immediate TaskStore cleanup: {}", taskStoreException.getMessage());
                }
            } else {
                log.info("✅ Diagnostics cleanup completed: no expired tasks found");
            }

            // 记录清理后的统计信息
            String statsAfter = taskManager.getTaskStatistics();
            log.info("📊 After cleanup: {}", statsAfter);

        } catch (Exception e) {
            log.error("❌ Error during diagnostics cleanup: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 手动触发清理（用于测试或管理界面）
     * 
     * @return 清理的任务数量
     */
    public int manualCleanup() {
        log.info("🔧 Manual cleanup triggered");
        performCleanup();
        return taskManager.cleanupExpiredTasks();
    }
    
    /**
     * 获取清理服务状态信息
     * 
     * @return 状态信息
     */
    public String getStatus() {
        return String.format("Cleanup service running - interval: %d min, task expiry: %d min", 
                cleanupIntervalMinutes, taskManager.getTaskExpiryMinutes());
    }
}
