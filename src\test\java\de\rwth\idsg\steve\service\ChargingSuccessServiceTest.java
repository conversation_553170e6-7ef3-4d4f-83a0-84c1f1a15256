package de.rwth.idsg.steve.service;

import de.rwth.idsg.steve.repository.ChargingSuccessRepository;
import de.rwth.idsg.steve.repository.dto.ChargingSession;
import de.rwth.idsg.steve.repository.dto.ChargingSuccessStats;
import de.rwth.idsg.steve.service.impl.ChargingSuccessServiceImpl;
import ocpp.cs._2015._10.ChargePointStatus;
import ocpp.cs._2015._10.MeterValue;
import ocpp.cs._2015._10.SampledValue;
import ocpp.cs._2015._10.Measurand;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 充电成功率统计服务测试类
 * 
 * <AUTHOR> Assistant
 */
@ExtendWith(MockitoExtension.class)
public class ChargingSuccessServiceTest {
    
    @Mock
    private ChargingSuccessRepository chargingSuccessRepository;
    
    @InjectMocks
    private ChargingSuccessServiceImpl chargingSuccessService;
    
    private static final String CHARGE_BOX_ID = "TEST_CHARGER_001";
    private static final int CONNECTOR_ID = 1;
    private static final int TRANSACTION_ID = 12345;
    private static final int SESSION_ID = 1;
    
    @BeforeEach
    void setUp() {
        // 设置默认的mock行为
        when(chargingSuccessRepository.createChargingSession(anyString(), anyInt(), any()))
                .thenReturn(SESSION_ID);
    }
    
    @Test
    void testHandleStatusNotification_Preparing() {
        // 测试处理Preparing状态
        chargingSuccessService.handleStatusNotification(
                CHARGE_BOX_ID, CONNECTOR_ID, ChargePointStatus.PREPARING, TRANSACTION_ID);
        
        // 验证创建了新的充电会话
        verify(chargingSuccessRepository).createChargingSession(CHARGE_BOX_ID, CONNECTOR_ID, TRANSACTION_ID);
        verify(chargingSuccessRepository).updateSessionStatus(SESSION_ID, ChargingSession.ChargingSessionStatus.PREPARING);
    }
    
    @Test
    void testHandleStatusNotification_Charging() {
        // 模拟找到活跃会话
        ChargingSession mockSession = ChargingSession.builder()
                .sessionId(SESSION_ID)
                .chargeBoxId(CHARGE_BOX_ID)
                .connectorId(CONNECTOR_ID)
                .transactionId(TRANSACTION_ID)
                .status(ChargingSession.ChargingSessionStatus.PREPARING)
                .hasPreparing(true)
                .hasCharging(false)
                .hasValidMeterValues(false)
                .hasFinishing(false)
                .meterValueCount(0)
                .validMeterValueCount(0)
                .build();
        
        when(chargingSuccessRepository.getSessionByTransactionId(TRANSACTION_ID))
                .thenReturn(Optional.of(mockSession));
        
        // 测试处理Charging状态
        chargingSuccessService.handleStatusNotification(
                CHARGE_BOX_ID, CONNECTOR_ID, ChargePointStatus.CHARGING, TRANSACTION_ID);
        
        // 验证更新了会话状态
        verify(chargingSuccessRepository).updateSessionStatus(SESSION_ID, ChargingSession.ChargingSessionStatus.CHARGING);
    }
    
    @Test
    void testHandleStatusNotification_Finishing() {
        // 模拟找到活跃会话
        ChargingSession mockSession = ChargingSession.builder()
                .sessionId(SESSION_ID)
                .chargeBoxId(CHARGE_BOX_ID)
                .connectorId(CONNECTOR_ID)
                .transactionId(TRANSACTION_ID)
                .status(ChargingSession.ChargingSessionStatus.CHARGING)
                .hasPreparing(true)
                .hasCharging(true)
                .hasValidMeterValues(true)
                .hasFinishing(false)
                .meterValueCount(5)
                .validMeterValueCount(3)
                .build();
        
        when(chargingSuccessRepository.getSessionByTransactionId(TRANSACTION_ID))
                .thenReturn(Optional.of(mockSession));
        
        // 测试处理Finishing状态
        chargingSuccessService.handleStatusNotification(
                CHARGE_BOX_ID, CONNECTOR_ID, ChargePointStatus.FINISHING, TRANSACTION_ID);
        
        // 验证更新了会话状态并完成会话
        verify(chargingSuccessRepository).updateSessionStatus(SESSION_ID, ChargingSession.ChargingSessionStatus.FINISHING);
        verify(chargingSuccessRepository).completeSession(SESSION_ID, true); // 应该是成功的
        verify(chargingSuccessRepository).recalculateSuccessStats(CHARGE_BOX_ID);
    }
    
    @Test
    void testHandleMeterValues() {
        // 模拟找到活跃会话
        ChargingSession mockSession = ChargingSession.builder()
                .sessionId(SESSION_ID)
                .chargeBoxId(CHARGE_BOX_ID)
                .connectorId(CONNECTOR_ID)
                .transactionId(TRANSACTION_ID)
                .status(ChargingSession.ChargingSessionStatus.CHARGING)
                .meterValueCount(0)
                .validMeterValueCount(0)
                .build();
        
        when(chargingSuccessRepository.getSessionByTransactionId(TRANSACTION_ID))
                .thenReturn(Optional.of(mockSession));
        
        // 创建MeterValue数据
        MeterValue meterValue = new MeterValue();
        SampledValue sampledValue = new SampledValue();
        sampledValue.setValue("1250.5"); // 功率值大于0
        sampledValue.setMeasurand(Measurand.POWER_ACTIVE_IMPORT);
        meterValue.getSampledValue().add(sampledValue);
        
        // 测试处理MeterValues
        chargingSuccessService.handleMeterValues(
                CHARGE_BOX_ID, CONNECTOR_ID, Arrays.asList(meterValue), TRANSACTION_ID);
        
        // 验证更新了MeterValues统计
        verify(chargingSuccessRepository).updateSessionMeterValues(
                eq(SESSION_ID), eq(1), eq(1), eq(true));
    }
    
    @Test
    void testGetSuccessStats() {
        // 模拟统计数据
        ChargingSuccessStats mockStats = ChargingSuccessStats.builder()
                .statsId(1)
                .chargeBoxId(CHARGE_BOX_ID)
                .totalSessions(10)
                .successfulSessions(8)
                .successRate(new BigDecimal("80.00"))
                .build();
        
        when(chargingSuccessRepository.getSuccessStats(CHARGE_BOX_ID))
                .thenReturn(Optional.of(mockStats));
        
        // 测试获取统计数据
        ChargingSuccessStats result = chargingSuccessService.getSuccessStats(CHARGE_BOX_ID);
        
        // 验证结果
        assert result != null;
        assert result.getChargeBoxId().equals(CHARGE_BOX_ID);
        assert result.getTotalSessions() == 10;
        assert result.getSuccessfulSessions() == 8;
        assert result.getSuccessRate().compareTo(new BigDecimal("80.00")) == 0;
    }
    
    @Test
    void testRecalculateAllSuccessStats() {
        // 测试重新计算所有统计数据
        chargingSuccessService.recalculateAllSuccessStats();
        
        // 验证调用了repository方法
        verify(chargingSuccessRepository).recalculateAllSuccessStats();
    }
}
