/*
 * SteV<PERSON> - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.repository.impl;

import de.rwth.idsg.steve.SteveException;
import de.rwth.idsg.steve.repository.GenericRepository;
import de.rwth.idsg.steve.repository.ReservationStatus;
import de.rwth.idsg.steve.repository.dto.DbVersion;
import de.rwth.idsg.steve.utils.DateTimeUtils;
import de.rwth.idsg.steve.web.dto.Statistics;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.jooq.DSLContext;
import org.jooq.DatePart;
import org.jooq.Field;
import org.jooq.Record2;
import org.jooq.Record8;
import org.jooq.impl.DSL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Repository;

import static de.rwth.idsg.steve.utils.CustomDSL.date;
import static de.rwth.idsg.steve.utils.CustomDSL.timestampDiff;
import static de.rwth.idsg.steve.utils.CustomDSL.utcTimestamp;
import static jooq.steve.db.Tables.RESERVATION;
import static jooq.steve.db.Tables.TRANSACTION;
import static jooq.steve.db.tables.ChargeBox.CHARGE_BOX;
import static jooq.steve.db.tables.Connector.CONNECTOR;
import static jooq.steve.db.tables.OcppTag.OCPP_TAG;
import static jooq.steve.db.tables.SchemaVersion.SCHEMA_VERSION;
import static jooq.steve.db.tables.User.USER;
import static jooq.steve.db.tables.UserChargeBox.USER_CHARGE_BOX;
import static org.jooq.impl.DSL.max;
import static org.jooq.impl.DSL.select;

/**
 * <AUTHOR> Goekay <<EMAIL>>
 * @since 14.08.2014
 */
@Slf4j
@Repository
public class GenericRepositoryImpl implements GenericRepository {

    @Autowired private DSLContext ctx;

    @EventListener
    public void afterStart(ContextRefreshedEvent event) {
        checkJavaAndMySQLOffsets();
    }

    @Override
    public void checkJavaAndMySQLOffsets() {
        long java = DateTimeUtils.getOffsetFromUtcInSeconds();

        long sql = ctx.select(timestampDiff(DatePart.SECOND, utcTimestamp(), DSL.currentTimestamp()))
                      .fetchOne()
                      .getValue(0, Long.class);

        if (sql != java) {
            throw new SteveException("MySQL and Java are not using the same time zone. " +
                "Java offset in seconds (%s) != MySQL offset in seconds (%s)", java, sql);
        }
    }

    @Override
    public Statistics getStats() {
        return getStatsByUser(null);
    }

    @Override
    public Statistics getStatsByUser(Integer webUserPk) {
        DateTime now = DateTime.now();
        DateTime yesterdaysNow = now.minusDays(1);

        Field<Integer> numChargeBoxes;
        Field<Integer> numReservations;
        Field<Integer> numTransactions;

        if (webUserPk != null) {
            // 对于特定用户，只统计他们拥有的充电桩相关数据
            numChargeBoxes = ctx.selectCount()
                    .from(CHARGE_BOX)
                    .join(USER_CHARGE_BOX).on(USER_CHARGE_BOX.CHARGE_BOX_PK.eq(CHARGE_BOX.CHARGE_BOX_PK))
                    .where(USER_CHARGE_BOX.WEB_USER_PK.eq(webUserPk))
                    .asField("num_charge_boxes");

            // 对于预约，需要通过 connector 表关联到 charge_box
            numReservations = ctx.selectCount()
                    .from(RESERVATION)
                    .join(CONNECTOR).on(CONNECTOR.CONNECTOR_PK.eq(RESERVATION.CONNECTOR_PK))
                    .join(CHARGE_BOX).on(CHARGE_BOX.CHARGE_BOX_ID.eq(CONNECTOR.CHARGE_BOX_ID))
                    .join(USER_CHARGE_BOX).on(USER_CHARGE_BOX.CHARGE_BOX_PK.eq(CHARGE_BOX.CHARGE_BOX_PK))
                    .where(RESERVATION.EXPIRY_DATETIME.greaterThan(now))
                    .and(RESERVATION.STATUS.eq(ReservationStatus.ACCEPTED.name()))
                    .and(USER_CHARGE_BOX.WEB_USER_PK.eq(webUserPk))
                    .asField("num_reservations");

            // 对于交易，也需要通过 connector 表关联到 charge_box
            numTransactions = ctx.selectCount()
                    .from(TRANSACTION)
                    .join(CONNECTOR).on(CONNECTOR.CONNECTOR_PK.eq(TRANSACTION.CONNECTOR_PK))
                    .join(CHARGE_BOX).on(CHARGE_BOX.CHARGE_BOX_ID.eq(CONNECTOR.CHARGE_BOX_ID))
                    .join(USER_CHARGE_BOX).on(USER_CHARGE_BOX.CHARGE_BOX_PK.eq(CHARGE_BOX.CHARGE_BOX_PK))
                    .where(TRANSACTION.STOP_TIMESTAMP.isNull())
                    .and(USER_CHARGE_BOX.WEB_USER_PK.eq(webUserPk))
                    .asField("num_transactions");
        } else {
            // 管理员或工厂运营商，统计所有数据
            numChargeBoxes = ctx.selectCount()
                    .from(CHARGE_BOX)
                    .asField("num_charge_boxes");

            numReservations = ctx.selectCount()
                    .from(RESERVATION)
                    .where(RESERVATION.EXPIRY_DATETIME.greaterThan(now))
                    .and(RESERVATION.STATUS.eq(ReservationStatus.ACCEPTED.name()))
                    .asField("num_reservations");

            numTransactions = ctx.selectCount()
                    .from(TRANSACTION)
                    .where(TRANSACTION.STOP_TIMESTAMP.isNull())
                    .asField("num_transactions");
        }

        // OCPP标签和用户数量对所有用户都是全局统计
        Field<Integer> numOcppTags =
                ctx.selectCount()
                   .from(OCPP_TAG)
                   .asField("num_ocpp_tags");

        Field<Integer> numUsers =
                ctx.selectCount()
                   .from(USER)
                   .asField("num_users");

        Field<Integer> heartbeatsToday;
        Field<Integer> heartbeatsYesterday;
        Field<Integer> heartbeatsEarlier;

        if (webUserPk != null) {
            // 对于特定用户，只统计他们拥有的充电桩的心跳
            heartbeatsToday = ctx.selectCount()
                    .from(CHARGE_BOX)
                    .join(USER_CHARGE_BOX).on(USER_CHARGE_BOX.CHARGE_BOX_PK.eq(CHARGE_BOX.CHARGE_BOX_PK))
                    .where(date(CHARGE_BOX.LAST_HEARTBEAT_TIMESTAMP).eq(date(now)))
                    .and(USER_CHARGE_BOX.WEB_USER_PK.eq(webUserPk))
                    .asField("heartbeats_today");

            heartbeatsYesterday = ctx.selectCount()
                    .from(CHARGE_BOX)
                    .join(USER_CHARGE_BOX).on(USER_CHARGE_BOX.CHARGE_BOX_PK.eq(CHARGE_BOX.CHARGE_BOX_PK))
                    .where(date(CHARGE_BOX.LAST_HEARTBEAT_TIMESTAMP).eq(date(yesterdaysNow)))
                    .and(USER_CHARGE_BOX.WEB_USER_PK.eq(webUserPk))
                    .asField("heartbeats_yesterday");

            heartbeatsEarlier = ctx.selectCount()
                    .from(CHARGE_BOX)
                    .join(USER_CHARGE_BOX).on(USER_CHARGE_BOX.CHARGE_BOX_PK.eq(CHARGE_BOX.CHARGE_BOX_PK))
                    .where(date(CHARGE_BOX.LAST_HEARTBEAT_TIMESTAMP).lessThan(date(yesterdaysNow)))
                    .and(USER_CHARGE_BOX.WEB_USER_PK.eq(webUserPk))
                    .asField("heartbeats_earlier");
        } else {
            // 管理员或工厂运营商，统计所有心跳
            heartbeatsToday = ctx.selectCount()
                    .from(CHARGE_BOX)
                    .where(date(CHARGE_BOX.LAST_HEARTBEAT_TIMESTAMP).eq(date(now)))
                    .asField("heartbeats_today");

            heartbeatsYesterday = ctx.selectCount()
                    .from(CHARGE_BOX)
                    .where(date(CHARGE_BOX.LAST_HEARTBEAT_TIMESTAMP).eq(date(yesterdaysNow)))
                    .asField("heartbeats_yesterday");

            heartbeatsEarlier = ctx.selectCount()
                    .from(CHARGE_BOX)
                    .where(date(CHARGE_BOX.LAST_HEARTBEAT_TIMESTAMP).lessThan(date(yesterdaysNow)))
                    .asField("heartbeats_earlier");
        }

        Record8<Integer, Integer, Integer, Integer, Integer, Integer, Integer, Integer> gs =
                ctx.select(
                        numChargeBoxes,
                        numOcppTags,
                        numUsers,
                        numReservations,
                        numTransactions,
                        heartbeatsToday,
                        heartbeatsYesterday,
                        heartbeatsEarlier
                ).fetchOne();

        return Statistics.builder()
                         .numChargeBoxes(gs.value1())
                         .numOcppTags(gs.value2())
                         .numUsers(gs.value3())
                         .numReservations(gs.value4())
                         .numTransactions(gs.value5())
                         .heartbeatToday(gs.value6())
                         .heartbeatYesterday(gs.value7())
                         .heartbeatEarlier(gs.value8())
                         .build();
    }

    @Override
    public DbVersion getDBVersion() {
        Record2<String, DateTime> record = ctx.select(SCHEMA_VERSION.VERSION, SCHEMA_VERSION.INSTALLED_ON)
                                              .from(SCHEMA_VERSION)
                                              .where(SCHEMA_VERSION.INSTALLED_RANK.eq(
                                                      select(max(SCHEMA_VERSION.INSTALLED_RANK)).from(SCHEMA_VERSION)))
                                              .fetchOne();

        String ts = DateTimeUtils.humanize(record.value2());
        return DbVersion.builder()
                        .version(record.value1())
                        .updateTimestamp(ts)
                        .build();
    }
}
