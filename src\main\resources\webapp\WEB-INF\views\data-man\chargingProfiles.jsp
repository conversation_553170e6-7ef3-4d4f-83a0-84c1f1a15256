<%--

    SteV<PERSON> - SteckdosenVerwaltung - https://github.com/steve-community/steve
    Copyright (C) 2013-2025 SteVe Community Team
    All Rights Reserved.

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.

--%>
<%@ include file="../00-header.jsp" %>
<script type="text/javascript">
    $(document).ready(function () {
        <%@ include file="../snippets/sortable.js" %>
        <%@ include file="../snippets/dateTimePicker.js" %>
    });
</script>
<div class="content-container">
    <div class="page-title">Charging Profile Management</div>
    <section><span>Charging Profile Overview</span></section>
    <div id="overview">
        <div class="search-panel mb-4">
            <form:form action="${ctxPath}/manager/chargingProfiles/query" method="get" modelAttribute="params">
                <div class="row mb-3">
                    <div class="col-md-4 mb-3">
                        <div class="form-group">
                            <label for="chargingProfilePk">Charging Profile ID (integer):</label>
                            <form:input path="chargingProfilePk" class="form-control"/>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="form-group">
                            <label for="stackLevel">Stack Level (integer):</label>
                            <form:input path="stackLevel" class="form-control"/>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="form-group">
                            <label for="description">Description:</label>
                            <form:input path="description" class="form-control"/>
                        </div>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 mb-3">
                        <div class="form-group">
                            <label for="profilePurpose">Profile Purpose:</label>
                            <form:select path="profilePurpose" class="form-control">
                                <option value="" selected>-- Empty --</option>
                                <form:options items="${profilePurpose}"/>
                            </form:select>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="form-group">
                            <label for="profileKind">Profile Kind:</label>
                            <form:select path="profileKind" class="form-control">
                                <option value="" selected>-- Empty --</option>
                                <form:options items="${profileKind}"/>
                            </form:select>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="form-group">
                            <label for="recurrencyKind">Recurrency Kind:</label>
                            <form:select path="recurrencyKind" class="form-control">
                                <option value="" selected>-- Empty --</option>
                                <form:options items="${recurrencyKind}"/>
                            </form:select>
                        </div>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6 mb-3">
                        <div class="form-group">
                            <label for="validFrom">Valid From:</label>
                            <form:input path="validFrom" class="dateTimePicker form-control"/>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="form-group">
                            <label for="validTo">Valid To:</label>
                            <form:input path="validTo" class="dateTimePicker form-control"/>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12 text-right">
                        <input type="submit" class="btn btn-primary" value="Search">
                    </div>
                </div>
            </form:form>
        </div>
        
        <div class="table-responsive">
            <table class="table table-striped table-hover table-bordered">
                <thead>
                <tr>
                    <th class="sorting" data-sort="string">Charging Profile ID</th>
                    <th class="sorting" data-sort="string">Stack Level</th>
                    <th class="sorting" data-sort="string">Description</th>
                    <th class="sorting" data-sort="string">Profile Purpose</th>
                    <th class="sorting" data-sort="string">Profile Kind</th>
                    <th class="sorting" data-sort="string">Recurrency Kind</th>
                    <th class="sorting" data-sort="date">Valid From</th>
                    <th class="sorting" data-sort="date">Valid To</th>
                    <th class="text-center">
                        <form:form action="${ctxPath}/manager/chargingProfiles/add" method="get">
                            <input type="submit" class="btn btn-blue" value="Add New">
                        </form:form>
                    </th>
                </tr>
                </thead>
                <tbody>
                <c:forEach items="${profileList}" var="cp">
                    <tr>
                        <td><a href="${ctxPath}/manager/chargingProfiles/details/${cp.chargingProfilePk}">${cp.chargingProfilePk}</a></td>
                        <td>${cp.stackLevel}</td>
                        <td>${cp.description}</td>
                        <td>${cp.profilePurpose}</td>
                        <td>${cp.profileKind}</td>
                        <td>${cp.recurrencyKind}</td>
                        <td data-sort-value="${cp.validFrom.millis}">${cp.validFrom}</td>
                        <td data-sort-value="${cp.validTo.millis}">${cp.validTo}</td>
                        <td class="text-center">
                            <form:form action="${ctxPath}/manager/chargingProfiles/delete/${cp.chargingProfilePk}">
                                <input type="submit" class="btn btn-standard" value="Delete">
                            </form:form>
                        </td>
                    </tr>
                </c:forEach>
                </tbody>
            </table>
        </div>
    </div>
</div>
<%@ include file="../00-footer.jsp" %>