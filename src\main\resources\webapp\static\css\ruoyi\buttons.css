/**
 * EVSE_OMS按钮样式
 * 基于RuoYi-Vue样式改造
 */

/* 默认按钮 */
.btn {
  display: inline-block;
  margin-bottom: 0;
  font-weight: 400;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  touch-action: manipulation;
  cursor: pointer;
  background-image: none;
  border: 1px solid transparent;
  padding: 6px 12px;
  font-size: 14px;
  line-height: 1.42857143;
  border-radius: 4px;
  user-select: none;
  transition: all .3s;
}

.btn:focus, .btn:active:focus {
  outline: thin dotted;
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}

.btn:hover, .btn:focus {
  text-decoration: none;
}

.btn:active {
  outline: 0;
  background-image: none;
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);
}

/* 按钮尺寸 */
.btn-xs {
  padding: 1px 5px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px;
}

.btn-sm {
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px;
}

.btn-lg {
  padding: 10px 16px;
  font-size: 18px;
  line-height: 1.3333333;
  border-radius: 6px;
}

/* 按钮颜色 */
.btn-default {
  color: #555;
  background-color: #fff;
  border-color: #dcdfe6;
}

.btn-default:hover, .btn-default:focus {
  color: #409EFF;
  border-color: #c6e2ff;
  background-color: #ecf5ff;
}

.btn-default:active {
  color: #409EFF;
  border-color: #409EFF;
  outline: none;
}

.btn-primary {
  color: #fff;
  background-color: #409EFF;
  border-color: #409EFF;
}

.btn-primary:hover, .btn-primary:focus {
  background: #66b1ff;
  border-color: #66b1ff;
  color: #fff;
}

.btn-primary:active {
  background: #3a8ee6;
  border-color: #3a8ee6;
  color: #fff;
}

.btn-success {
  color: #fff;
  background-color: #67C23A;
  border-color: #67C23A;
}

.btn-success:hover, .btn-success:focus {
  background: #85ce61;
  border-color: #85ce61;
  color: #fff;
}

.btn-success:active {
  background: #5daf34;
  border-color: #5daf34;
  color: #fff;
}

.btn-info {
  color: #fff;
  background-color: #909399;
  border-color: #909399;
}

.btn-info:hover, .btn-info:focus {
  background: #a6a9ad;
  border-color: #a6a9ad;
  color: #fff;
}

.btn-info:active {
  background: #82848a;
  border-color: #82848a;
  color: #fff;
}

.btn-warning {
  color: #fff;
  background-color: #E6A23C;
  border-color: #E6A23C;
}

.btn-warning:hover, .btn-warning:focus {
  background: #ebb563;
  border-color: #ebb563;
  color: #fff;
}

.btn-warning:active {
  background: #cf9236;
  border-color: #cf9236;
  color: #fff;
}

.btn-danger {
  color: #fff;
  background-color: #F56C6C;
  border-color: #F56C6C;
}

.btn-danger:hover, .btn-danger:focus {
  background: #f78989;
  border-color: #f78989;
  color: #fff;
}

.btn-danger:active {
  background: #dd6161;
  border-color: #dd6161;
  color: #fff;
}

/* 图标按钮 */
.btn-icon {
  padding: 7px;
  border-radius: 50%;
}

/* 按钮组 */
.btn-group {
  position: relative;
  display: inline-block;
  vertical-align: middle;
}

.btn-group > .btn {
  position: relative;
  float: left;
}

.btn-group > .btn:not(:first-child):not(:last-child) {
  border-radius: 0;
}

.btn-group > .btn:first-child {
  margin-left: 0;
}

.btn-group > .btn:first-child:not(:last-child) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.btn-group > .btn:last-child:not(:first-child) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.btn-group > .btn + .btn {
  margin-left: -1px;
}

.btn-group > .btn:hover,
.btn-group > .btn:focus,
.btn-group > .btn:active {
  z-index: 2;
}

/* 禁用按钮 */
.btn.disabled, .btn[disabled], fieldset[disabled] .btn {
  cursor: not-allowed;
  opacity: .65;
  filter: alpha(opacity=65);
  box-shadow: none;
}