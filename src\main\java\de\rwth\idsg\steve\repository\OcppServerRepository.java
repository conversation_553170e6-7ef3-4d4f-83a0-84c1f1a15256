/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.repository;

import de.rwth.idsg.steve.ocpp.OcppProtocol;
import de.rwth.idsg.steve.repository.dto.InsertConnectorStatusParams;
import de.rwth.idsg.steve.repository.dto.InsertTransactionParams;
import de.rwth.idsg.steve.repository.dto.UpdateChargeboxParams;
import de.rwth.idsg.steve.repository.dto.UpdateTransactionParams;
import ocpp.cs._2015._10.MeterValue;
import org.joda.time.DateTime;

import java.util.List;

/**
 * <AUTHOR> Goekay <<EMAIL>>
 * @since 19.08.2014
 */
public interface OcppServerRepository {

    void updateChargebox(UpdateChargeboxParams params);
    void updateOcppProtocol(String chargeBoxId, OcppProtocol protocol);
    void updateEndpointAddress(String chargeBoxIdentity, String endpointAddress);
    void updateChargeboxFirmwareStatus(String chargeBoxIdentity, String firmwareStatus);
    void updateChargeboxDiagnosticsStatus(String chargeBoxIdentity, String status);
    void updateChargeboxHeartbeat(String chargeBoxIdentity, DateTime ts);

    void insertConnectorStatus(InsertConnectorStatusParams params);

    void insertMeterValues(String chargeBoxIdentity, List<MeterValue> list, int connectorId, Integer transactionId);
    void insertMeterValues(String chargeBoxIdentity, List<MeterValue> list, int transactionId);

    int insertTransaction(InsertTransactionParams params);
    void updateTransaction(UpdateTransactionParams params);
    
    /**
     * 更新充电桩会话信息
     *
     * @param chargeBoxId 充电桩ID
     * @param sessionId 会话ID
     * @param connectionTime 连接时间，如果为null则不更新此字段
     * @param reconnectRequired 是否需要在服务器重启后重连
     */
    void updateChargeBoxSessionInfo(String chargeBoxId, String sessionId, DateTime connectionTime, boolean reconnectRequired);
    
    /**
     * 检查充电桩是否有活跃的充电事务
     *
     * @param chargeBoxId 充电桩ID
     * @return 如果有活跃事务返回true，否则返回false
     */
    boolean hasActiveTransaction(String chargeBoxId);
}
