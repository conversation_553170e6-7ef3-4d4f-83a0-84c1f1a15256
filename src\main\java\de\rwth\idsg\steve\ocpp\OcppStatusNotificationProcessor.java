/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.ocpp;

import de.rwth.idsg.steve.service.notification.OcppStationStatusFailure;
import lombok.extern.slf4j.Slf4j;
import ocpp.cs._2015._10.ChargePointStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * 处理OCPP状态通知消息的处理器
 * 会自动检测故障状态并发送事件
 */
@Slf4j
@Component
public class OcppStatusNotificationProcessor {

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    /**
     * 处理OCPP 1.6状态通知
     */
    public void process16(String chargeBoxId,
                         int connectorId,
                         ocpp.cs._2015._10.ChargePointStatus status,
                         ocpp.cs._2015._10.ChargePointErrorCode errorCode,
                         String errorInfo,
                         String timestamp,
                         String vendorErrorCode,
                         String vendorId) {

        // 新的故障判断逻辑：基于vendorErrorCode判断
        boolean hasFault = isFaultCondition(vendorErrorCode, status, errorCode);

        if (hasFault) {
            log.info("Received charging station fault status, ChargeBoxID={}, ConnectorID={}, Status={}, ErrorCode={}, VendorErrorCode={}, VendorId={}",
                    chargeBoxId, connectorId, status, errorCode, vendorErrorCode, vendorId);

            // 创建并发布故障事件
            OcppStationStatusFailure event = OcppStationStatusFailure.builder()
                    .chargeBoxId(chargeBoxId)
                    .connectorId(connectorId)
                    .errorCode(errorCode != null ? errorCode.value() : null)
                    .errorInfo(errorInfo)
                    .status(status.value())
                    .timestamp(timestamp)
                    .vendorErrorCode(vendorErrorCode)
                    .vendorId(vendorId)
                    .build();

            applicationEventPublisher.publishEvent(event);
        }
    }
    
    /**
     * 处理OCPP 1.5状态通知
     */
    public void process15(String chargeBoxId,
                         int connectorId,
                         ocpp.cs._2012._06.ChargePointStatus status,
                         ocpp.cs._2012._06.ChargePointErrorCode errorCode,
                         String errorInfo,
                         String timestamp,
                         String vendorErrorCode,
                         String vendorId) {

        // 新的故障判断逻辑：基于vendorErrorCode判断
        boolean hasFault = isFaultCondition(vendorErrorCode, status, errorCode);

        if (hasFault) {
            log.info("接收到充电桩故障状态，ChargeBoxID={}, ConnectorID={}, Status={}, ErrorCode={}, VendorErrorCode={}, VendorId={}",
                    chargeBoxId, connectorId, status, errorCode, vendorErrorCode, vendorId);

            // 创建并发布故障事件
            OcppStationStatusFailure event = OcppStationStatusFailure.builder()
                    .chargeBoxId(chargeBoxId)
                    .connectorId(connectorId)
                    .errorCode(errorCode != null ? errorCode.value() : null)
                    .errorInfo(errorInfo)
                    .status(status.value())
                    .timestamp(timestamp)
                    .vendorErrorCode(vendorErrorCode)
                    .vendorId(vendorId)
                    .build();

            applicationEventPublisher.publishEvent(event);
        }
    }
    
    /**
     * 判断是否存在故障条件
     * 新逻辑：vendorErrorCode为0、空或不存在时说明无故障，其他情况为有故障
     */
    private boolean isFaultCondition(String vendorErrorCode, Object status, Object errorCode) {
        // 如果vendorErrorCode为空、null或"0"，则认为无故障
        if (vendorErrorCode == null || vendorErrorCode.trim().isEmpty() || "0".equals(vendorErrorCode.trim())) {
            return false;
        }

        // 其他情况认为有故障
        return true;
    }

    /**
     * 判断OCPP 1.6状态是否是错误状态（保留用于兼容性）
     */
    private boolean isErrorStatus(ocpp.cs._2015._10.ChargePointStatus status) {
        return status == ChargePointStatus.FAULTED || status == ChargePointStatus.UNAVAILABLE;
    }

    /**
     * 判断OCPP 1.5状态是否是错误状态（保留用于兼容性）
     */
    private boolean isErrorStatus(ocpp.cs._2012._06.ChargePointStatus status) {
        return status == ocpp.cs._2012._06.ChargePointStatus.FAULTED ||
               status == ocpp.cs._2012._06.ChargePointStatus.UNAVAILABLE;
    }
} 