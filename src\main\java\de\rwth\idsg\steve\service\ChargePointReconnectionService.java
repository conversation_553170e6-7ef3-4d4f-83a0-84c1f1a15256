/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.service;

import de.rwth.idsg.steve.repository.ChargePointRepository;
import de.rwth.idsg.steve.repository.OcppServerRepository;
import de.rwth.idsg.steve.service.notification.OcppStationWebSocketConnected;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.util.List;

/**
 * 充电桩重连服务，用于处理服务器重启后的充电桩重连状态
 */
@Slf4j
@Service
public class ChargePointReconnectionService {

    @Autowired private ChargePointRepository chargePointRepository;
    @Autowired private OcppServerRepository ocppServerRepository;
    
    /**
     * 服务启动时调用，检查需要重连的充电桩
     */
    @PostConstruct
    public void initReconnectionCheck() {
        log.info("Checking for charging stations that require reconnection after server restart...");
        
        try {
            // 获取所有标记为需要重连的充电桩
            List<String> reconnectRequired = chargePointRepository.getChargeBoxIdsRequiringReconnect();
            
            if (!reconnectRequired.isEmpty()) {
                log.info("Found {} charging stations requiring reconnection: {}", 
                         reconnectRequired.size(), reconnectRequired);
                
                // 重置状态 - 充电桩会自行重连，服务器只需准备好接收
                chargePointRepository.resetReconnectFlags();
            } else {
                log.info("No charging stations requiring reconnection found");
            }
        } catch (Exception e) {
            log.error("Error during reconnection check: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 处理重连的充电桩
     */
    @EventListener
    public void handleStationConnected(OcppStationWebSocketConnected event) {
        String chargeBoxId = event.getChargeBoxId();
        
        try {
            // 检查充电桩是否标记为需要重连
            boolean reconnectRequired = chargePointRepository.isReconnectRequired(chargeBoxId);
            
            if (reconnectRequired) {
                log.info("Reconnected charging station with ongoing transaction: {}", chargeBoxId);
                
                // 获取充电桩状态并同步
                syncChargePointStatus(chargeBoxId);
                
                // 重置充电桩重连标志
                chargePointRepository.resetReconnectFlag(chargeBoxId);
            }
        } catch (Exception e) {
            log.error("Error handling reconnection for {}: {}", chargeBoxId, e.getMessage(), e);
        }
    }
    
    /**
     * 同步充电桩状态
     */
    private void syncChargePointStatus(String chargeBoxId) {
        // 检查是否有未完成的充电事务
        boolean hasActiveTransaction = ocppServerRepository.hasActiveTransaction(chargeBoxId);
        
        if (hasActiveTransaction) {
            log.info("Charging station {} has active transactions after reconnection", chargeBoxId);
            // 这里可以添加额外的同步逻辑，如请求充电桩状态更新等
        }
    }
} 