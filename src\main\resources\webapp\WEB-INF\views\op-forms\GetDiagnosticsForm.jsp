<%--

    SteV<PERSON> - SteckdosenVerwaltung - https://github.com/steve-community/steve
    Copyright (C) 2013-2025 SteVe Community Team
    All Rights Reserved.

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.

--%>
<form:form action="${ctxPath}/manager/operations/ajax/${opVersion}/GetDiagnostics"
         modelAttribute="params"
         cssClass="ocpp-operation-form" enctype="multipart/form-data">
    <section><span>Charge Points with OCPP ${opVersion}</span></section>
    <%@ include file="../00-cp-multiple.jsp" %>
    <section><span>Parameters</span></section>
    <div class="info-message">
        <p><strong>Two ways to get diagnostic logs:</strong></p>
        <p><strong>1. Upload diagnostic file directly:</strong> Upload a diagnostic file that will be saved to the FTP server logs directory</p>
        <p><strong>2. Request from charge point:</strong> Send a GetDiagnostics request to charge point to upload logs to FTP server</p>
        <p>FTP Server Address: ${ftpServerInfo}</p>
        <p><strong>Date Range (for charge point request):</strong> If not specified, will retrieve logs from yesterday.
           You can specify Start/Stop dates to retrieve logs from specific days (e.g., 2025-07-15 to 2025-07-17 will get logs from 15th, 16th, and 17th).</p>
    </div>
    <table class="userInput">
        <tr><td>Diagnostic File (Optional):</td><td><input type="file" name="diagnosticFile" accept=".zip,.log,.txt" /></td></tr>
        <tr><td colspan="2"><hr style="margin: 10px 0; border: 1px solid #ddd;"></td></tr>
        <tr><td colspan="2"><strong>OR request from charge point:</strong></td></tr>
        <tr><td>Retries (integer):</td><td><form:input path="retries" placeholder="optional" /></td></tr>
        <tr><td>Retry Interval (integer):</td><td><form:input path="retryInterval" placeholder="optional" /></td></tr>
        <tr><td>Start Date:</td>
            <td>
                <form:input path="startDate" placeholder="optional (e.g., 2025-07-15)" cssClass="datePicker"/>
            </td>
        </tr>
        <tr><td>Stop Date:</td>
            <td>
                <form:input path="stopDate" placeholder="optional (e.g., 2025-07-17)" cssClass="datePicker"/>
            </td>
        </tr>
        <tr><td></td><td><div class="submit-button"><input type="submit" value="Perform"></div></td></tr>
    </table>
</form:form>