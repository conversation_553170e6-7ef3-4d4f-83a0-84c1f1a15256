/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.service;

import de.rwth.idsg.steve.service.notification.OcppStationStatusFailure;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 用于监听充电桩OCPP故障事件并自动创建故障记录的监听器
 */
@Slf4j
@Component
public class FaultEventListener {

    @Autowired
    private FaultService faultService;
    
    @Value("${steve.ocpp.auto-fault.error-codes:ConnectorLockFailure,GroundFailure,HighTemperature,InternalError,LocalListConflict,OtherError,OverCurrentFailure,OverVoltage,PowerMeterFailure,PowerSwitchFailure,ReaderFailure,ResetFailure,UnderVoltage,WeakSignal}")
    private String autoFaultErrorCodes;
    
    private Set<String> autoFaultErrorCodeSet;
    
    /**
     * 初始化需要自动创建故障记录的错误代码集合
     */
    @jakarta.annotation.PostConstruct
    public void init() {
        String[] errorCodeArray = autoFaultErrorCodes.split(",");
        autoFaultErrorCodeSet = new HashSet<>(Arrays.asList(errorCodeArray));
        log.info("Configured auto fault error codes: {}", autoFaultErrorCodeSet);
    }
    
    /**
     * 监听充电桩状态失败事件
     */
    @EventListener
    public void handleStationStatusFailure(OcppStationStatusFailure event) {
        // 忽略无效事件
        if (event == null || event.getChargeBoxId() == null) {
            return;
        }

        // 新的故障判断逻辑：基于vendorErrorCode判断
        String vendorErrorCode = event.getVendorErrorCode();
        String errorCode = event.getErrorCode();

        // vendorErrorCode为0、空或不存在时说明无故障
        if (vendorErrorCode == null || vendorErrorCode.trim().isEmpty() || "0".equals(vendorErrorCode.trim())) {
            log.debug("No fault detected: ChargeBoxId={}, VendorErrorCode={}", event.getChargeBoxId(), vendorErrorCode);
            return;
        }

        // 有vendorErrorCode且不为0，创建故障记录
        log.info("Detected charging station error based on vendorErrorCode, auto creating fault record: ChargeBoxId={}, ErrorCode={}, VendorErrorCode={}, VendorId={}",
                 event.getChargeBoxId(), errorCode, vendorErrorCode, event.getVendorId());

        String description = "System auto-created fault record based on vendor error code: " + vendorErrorCode;
        if (event.getErrorInfo() != null && !event.getErrorInfo().isEmpty()) {
            description += ". Details: " + event.getErrorInfo();
        }

        if (event.getConnectorId() != null) {
            description += ". Connector ID: " + event.getConnectorId();
        }

        if (event.getVendorId() != null && !event.getVendorId().isEmpty()) {
            description += ". Vendor ID: " + event.getVendorId();
        }

        int issueId = faultService.createAutoIssue(
            event.getChargeBoxId(),
            errorCode,
            description,
            vendorErrorCode,
            event.getVendorId()
        );

        log.info("Auto fault record created, ID={}", issueId);
    }
} 