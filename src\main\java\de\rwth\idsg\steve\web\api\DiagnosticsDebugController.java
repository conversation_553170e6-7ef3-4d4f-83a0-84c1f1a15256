package de.rwth.idsg.steve.web.api;

import de.rwth.idsg.steve.ocpp.task.GetDiagnosticsTask;
import de.rwth.idsg.steve.service.DiagnosticsTaskManager;
import de.rwth.idsg.steve.service.DiagnosticsTaskInfo;
import de.rwth.idsg.steve.service.ChargePointIdentityMappingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 诊断调试API控制器
 * 用于调试诊断任务状态和映射问题
 */
@Slf4j
@RestController
@RequestMapping("/api/diagnostics/debug")
public class DiagnosticsDebugController {

    @Autowired
    private DiagnosticsTaskManager diagnosticsTaskManager;

    @Autowired
    private ChargePointIdentityMappingService identityMappingService;

    /**
     * 获取诊断调试信息
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getDebugStatus() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 记录所有等待的GetDiagnostics任务
            GetDiagnosticsTask.logAllPendingTasks();
            
            // 获取任务管理器中的任务
            Map<String, Object> taskManagerInfo = new HashMap<>();
            // 这里需要添加获取任务管理器状态的方法
            
            // 获取动态学习的映射信息
            Map<String, String> mappings = identityMappingService.getAllMappings();

            response.put("status", "success");
            response.put("dynamicMappings", mappings);
            response.put("mappingCount", mappings.size());
            response.put("learningMode", "dynamic");
            response.put("taskManagerInfo", taskManagerInfo);

            // 添加映射类型统计
            int cpMappings = 0;
            int macMappings = 0;
            int otherMappings = 0;

            for (String ocppId : mappings.keySet()) {
                if (ocppId.startsWith("CP")) {
                    cpMappings++;
                } else if (ocppId.matches("[A-F0-9]{12}")) {
                    macMappings++;
                } else {
                    otherMappings++;
                }
            }

            response.put("mappingStats", Map.of(
                "cpFormat", cpMappings,
                "macFormat", macMappings,
                "otherFormat", otherMappings
            ));

            log.info("🔧 Debug status requested - dynamic mappings: {} (CP: {}, MAC: {}, Other: {})",
                    mappings.size(), cpMappings, macMappings, otherMappings);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("status", "error");
            response.put("message", "Failed to get debug status: " + e.getMessage());
            
            log.error("❌ Failed to get debug status", e);
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 测试充电桩标识映射
     */
    @PostMapping("/test-mapping")
    public ResponseEntity<Map<String, Object>> testMapping(
            @RequestParam String fileName,
            @RequestParam String connectedChargeBoxId) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            String resolvedId = identityMappingService.resolveChargePointIdentity(fileName, connectedChargeBoxId);
            
            response.put("status", "success");
            response.put("fileName", fileName);
            response.put("connectedChargeBoxId", connectedChargeBoxId);
            response.put("resolvedId", resolvedId);
            response.put("isMapped", !resolvedId.equals(connectedChargeBoxId));
            
            // 检查是否有对应的等待任务
            GetDiagnosticsTask pendingTask = GetDiagnosticsTask.getPendingTask(resolvedId);
            response.put("hasPendingTask", pendingTask != null);
            
            if (pendingTask == null) {
                // 尝试其他可能的ID
                GetDiagnosticsTask originalTask = GetDiagnosticsTask.getPendingTask(connectedChargeBoxId);
                response.put("hasPendingTaskForOriginal", originalTask != null);
                
                String ocppId = identityMappingService.getOcppIdentity(resolvedId);
                GetDiagnosticsTask ocppTask = GetDiagnosticsTask.getPendingTask(ocppId);
                response.put("ocppId", ocppId);
                response.put("hasPendingTaskForOcpp", ocppTask != null);
            }
            
            log.info("🔧 Mapping test: {} + {} -> {} (pending task: {})", 
                    fileName, connectedChargeBoxId, resolvedId, pendingTask != null);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("status", "error");
            response.put("message", "Failed to test mapping: " + e.getMessage());
            
            log.error("❌ Failed to test mapping", e);
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 手动完成任务（用于调试）
     */
    @PostMapping("/complete-task")
    public ResponseEntity<Map<String, Object>> completeTask(
            @RequestParam String chargeBoxId,
            @RequestParam String fileName) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            GetDiagnosticsTask pendingTask = GetDiagnosticsTask.getPendingTask(chargeBoxId);
            
            if (pendingTask != null) {
                pendingTask.completeTaskForChargeBox(chargeBoxId, fileName);
                
                response.put("status", "success");
                response.put("message", "Task completed successfully");
                response.put("chargeBoxId", chargeBoxId);
                response.put("fileName", fileName);
                
                log.info("🎯 Manually completed task for {}: {}", chargeBoxId, fileName);
                
            } else {
                response.put("status", "error");
                response.put("message", "No pending task found for charge box: " + chargeBoxId);
                
                log.warn("⚠️ No pending task found for manual completion: {}", chargeBoxId);
            }
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("status", "error");
            response.put("message", "Failed to complete task: " + e.getMessage());
            
            log.error("❌ Failed to manually complete task", e);
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 获取任务管理器状态
     */
    @GetMapping("/task-manager")
    public ResponseEntity<Map<String, Object>> getTaskManagerStatus() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 这里需要添加获取任务管理器详细状态的方法
            response.put("status", "success");
            response.put("message", "Task manager status logged to console");
            
            log.info("🔧 Task manager status requested");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("status", "error");
            response.put("message", "Failed to get task manager status: " + e.getMessage());
            
            log.error("❌ Failed to get task manager status", e);
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 清理所有等待的任务（用于调试）
     */
    @PostMapping("/clear-pending-tasks")
    public ResponseEntity<Map<String, Object>> clearPendingTasks() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 这个方法需要在GetDiagnosticsTask中添加
            log.warn("🧹 Manual clear of pending tasks requested (not implemented yet)");
            
            response.put("status", "success");
            response.put("message", "Clear pending tasks logged (implementation needed)");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("status", "error");
            response.put("message", "Failed to clear pending tasks: " + e.getMessage());
            
            log.error("❌ Failed to clear pending tasks", e);
            return ResponseEntity.badRequest().body(response);
        }
    }
}
