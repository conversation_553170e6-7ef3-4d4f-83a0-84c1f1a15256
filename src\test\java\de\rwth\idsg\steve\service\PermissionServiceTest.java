package de.rwth.idsg.steve.service;

import de.rwth.idsg.steve.repository.ChargeStationRepository;
import de.rwth.idsg.steve.repository.UserChargeBoxRepository;
import de.rwth.idsg.steve.repository.WebUserRepository;
import jooq.steve.db.tables.records.WebUserRecord;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class PermissionServiceTest {

    @Mock
    private WebUserRepository webUserRepository;
    
    @Mock
    private UserChargeBoxRepository userChargeBoxRepository;
    
    @Mock
    private ChargeStationRepository chargeStationRepository;
    
    @Mock
    private Authentication authentication;
    
    @Mock
    private SecurityContext securityContext;
    
    @Mock
    private WebUserRecord webUserRecord;

    private PermissionService permissionService;

    @BeforeEach
    void setUp() {
        permissionService = new PermissionService(webUserRepository, userChargeBoxRepository, chargeStationRepository);
        SecurityContextHolder.setContext(securityContext);
    }

    @Test
    void testCanDeleteChargeBox_WithValidOwnership() {
        // Given
        int chargeBoxPk = 123;
        int userPk = 456;
        
        when(securityContext.getAuthentication()).thenReturn(authentication);
        when(authentication.getName()).thenReturn("testuser");
        when(webUserRepository.loadUserByUsername("testuser")).thenReturn(webUserRecord);
        when(webUserRecord.getWebUserPk()).thenReturn(userPk);
        when(userChargeBoxRepository.isChargeBoxAssignedToUserByPk(userPk, chargeBoxPk)).thenReturn(true);
        
        // Mock isOperatorOwner to return true
        when(webUserRecord.getUserRole()).thenReturn(jooq.steve.db.enums.WebUserUserRole.OPERATOR_OWNER);
        
        // When
        boolean result = permissionService.canDeleteChargeBox(chargeBoxPk);
        
        // Then
        assertTrue(result);
        verify(userChargeBoxRepository).isChargeBoxAssignedToUserByPk(userPk, chargeBoxPk);
    }

    @Test
    void testCanDeleteChargeBox_WithoutOwnership() {
        // Given
        int chargeBoxPk = 123;
        int userPk = 456;
        
        when(securityContext.getAuthentication()).thenReturn(authentication);
        when(authentication.getName()).thenReturn("testuser");
        when(webUserRepository.loadUserByUsername("testuser")).thenReturn(webUserRecord);
        when(webUserRecord.getWebUserPk()).thenReturn(userPk);
        when(userChargeBoxRepository.isChargeBoxAssignedToUserByPk(userPk, chargeBoxPk)).thenReturn(false);
        
        // Mock isOperatorOwner to return true
        when(webUserRecord.getUserRole()).thenReturn(jooq.steve.db.enums.WebUserUserRole.OPERATOR_OWNER);
        
        // When
        boolean result = permissionService.canDeleteChargeBox(chargeBoxPk);
        
        // Then
        assertFalse(result);
        verify(userChargeBoxRepository).isChargeBoxAssignedToUserByPk(userPk, chargeBoxPk);
    }

    @Test
    void testCanDeleteChargeBox_AsAdmin() {
        // Given
        int chargeBoxPk = 123;
        
        when(securityContext.getAuthentication()).thenReturn(authentication);
        when(authentication.getName()).thenReturn("admin");
        when(webUserRepository.loadUserByUsername("admin")).thenReturn(webUserRecord);
        
        // Mock isAdmin to return true
        when(webUserRecord.getUserRole()).thenReturn(jooq.steve.db.enums.WebUserUserRole.ADMIN);
        
        // When
        boolean result = permissionService.canDeleteChargeBox(chargeBoxPk);
        
        // Then
        assertTrue(result);
        // Should not check ownership for admin
        verify(userChargeBoxRepository, never()).isChargeBoxAssignedToUserByPk(anyInt(), anyInt());
    }
}
