<%--
    SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
    Copyright (C) 2013-2025 SteVe Community Team
    All Rights Reserved.

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.
--%>
<%@ include file="../00-header.jsp" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<script type="text/javascript">
    $(document).ready(function() {
        $('#maintenanceForm').submit(function(event) {
            var desc = $('#maintenanceDescription').val();
            if (!desc || desc.trim() === '') {
                alert('请输入维护描述');
                event.preventDefault();
            }
        });
        
        $('#resolveForm').submit(function(event) {
            var desc = $('#resolveDescription').val();
            if (!desc || desc.trim() === '') {
                alert('请输入解决方案描述');
                event.preventDefault();
            }
        });
        
        // 初始化图片查看器
        $('.issue-image').click(function() {
            var src = $(this).attr('src');
            $('#imageModal img').attr('src', src);
            $('#imageModal').show();
        });
        
        $('.close').click(function() {
            $('#imageModal').hide();
        });
    });
</script>
<style>
    .content {
        max-width: 1000px;
        margin: 0 auto;
    }
    .detail-container {
        background-color: #f9f9f9;
        padding: 25px;
        border-radius: 5px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        margin-top: 20px;
    }
    .detail-table {
        width: 100%;
        border-collapse: collapse;
    }
    .detail-table td {
        padding: 12px 8px;
        vertical-align: top;
    }
    .detail-table td:first-child {
        width: 180px;
        font-weight: bold;
        color: #555;
    }
    .maintenance-records {
        margin-top: 30px;
        border-top: 1px solid #ddd;
        padding-top: 20px;
    }
    .maintenance-record {
        background-color: white;
        padding: 15px;
        border-radius: 4px;
        box-shadow: 0 1px 2px rgba(0,0,0,0.05);
        margin-bottom: 15px;
    }
    .record-header {
        display: flex;
        justify-content: space-between;
        padding-bottom: 10px;
        border-bottom: 1px solid #eee;
        margin-bottom: 10px;
    }
    .record-title {
        font-weight: bold;
        color: #333;
    }
    .record-meta {
        color: #777;
        font-size: 0.9em;
    }
    .record-content {
        margin-top: 10px;
        white-space: pre-wrap;
    }
    .btn-group {
        margin-top: 25px;
    }
    .btn {
        padding: 8px 16px;
        border-radius: 4px;
        text-decoration: none;
        display: inline-block;
        margin-right: 10px;
        font-weight: bold;
    }
    .btn-primary {
        background-color: #5cb85c;
        color: white;
        border: none;
    }
    .btn-primary:hover {
        background-color: #4cae4c;
    }
    .btn-secondary {
        background-color: #f0f0f0;
        color: #333;
        border: 1px solid #ddd;
    }
    .btn-secondary:hover {
        background-color: #e0e0e0;
    }
    .status-badge {
        display: inline-block;
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 0.85em;
        font-weight: bold;
    }
    .status-new {
        background-color: #f8d7da;
        color: #721c24;
    }
    .status-in-progress {
        background-color: #fff3cd;
        color: #856404;
    }
    .status-resolved {
        background-color: #d4edda;
        color: #155724;
    }
    .image-gallery {
        margin-top: 20px;
    }
    .image-thumbnail {
        display: inline-block;
        margin: 5px;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 5px;
    }
    .image-thumbnail img {
        height: 100px;
        width: auto;
        display: block;
    }
</style>

<div class="content">
    <section><span>故障详情</span></section>
    
    <div class="detail-container">
        <table class="detail-table">
            <tr>
                <td>ID:</td>
                <td>${issue.issueId}</td>
            </tr>
            <tr>
                <td>充电桩:</td>
                <td>${issue.chargeBoxId}</td>
            </tr>
            <tr>
                <td>上报时间:</td>
                <td>${issue.reportTime}</td>
            </tr>
            <tr>
                <td>上报人:</td>
                <td>${issue.reporterUsername}</td>
            </tr>
            <tr>
                <td>故障描述:</td>
                <td style="white-space: pre-wrap;">${issue.faultDescription}</td>
            </tr>
            <tr>
                <td>OCPP错误码:</td>
                <td>${issue.ocppErrorCode}</td>
            </tr>
            <tr>
                <td>是否自动上报:</td>
                <td>${issue.autoReported ? '是' : '否'}</td>
            </tr>
            <tr>
                <td>状态:</td>
                <td>
                    <span class="status-badge 
                        ${issue.status eq 'NEW' ? 'status-new' : 
                          issue.status eq 'IN_PROGRESS' ? 'status-in-progress' : 
                          'status-resolved'}">
                        ${issue.status.value()}
                    </span>
                </td>
            </tr>
            <c:if test="${issue.status eq 'RESOLVED'}">
                <tr>
                    <td>解决时间:</td>
                    <td>${issue.resolveTime}</td>
                </tr>
                <tr>
                    <td>解决描述:</td>
                    <td style="white-space: pre-wrap;">${issue.resolveDescription}</td>
                </tr>
            </c:if>
        </table>
        
        <c:if test="${not empty images}">
            <div class="image-gallery">
                <h3>故障图片</h3>
                <c:forEach items="${images}" var="image">
                    <a class="image-thumbnail" href="${ctxPath}/manager/faults/images/${image.imageId}" target="_blank">
                        <img src="${ctxPath}/manager/faults/images/${image.imageId}" alt="故障图片">
                    </a>
                </c:forEach>
            </div>
        </c:if>
        
        <div class="maintenance-records">
            <h3>维护记录 (${fn:length(records)})</h3>
            <c:if test="${empty records}">
                <p>暂无维护记录</p>
            </c:if>
            <c:if test="${not empty records}">
                <c:forEach items="${records}" var="record">
                    <div class="maintenance-record">
                        <div class="record-header">
                            <div class="record-title">维护记录 #${record.recordId}</div>
                            <div class="record-meta">
                                <span>维护人员: ${record.maintainerUsername}</span> | 
                                <span>维护时间: <fmt:formatDate value="${record.maintenanceTime}" pattern="yyyy-MM-dd HH:mm:ss" /></span>
                            </div>
                        </div>
                        <div class="record-content">${record.maintenanceDescription}</div>
                    </div>
                </c:forEach>
            </c:if>
        </div>
        
        <div class="btn-group">
            <sec:authorize access="hasRole('ADMIN') or hasRole('OPERATOR')">
                <c:if test="${issue.status ne 'RESOLVED'}">
                    <a href="${ctxPath}/manager/faults/record/add?issueId=${issue.issueId}" class="btn btn-primary">添加维护记录</a>
                    <a href="${ctxPath}/manager/faults/resolve/${issue.issueId}" class="btn btn-primary">解决故障</a>
                </c:if>
                <a href="${ctxPath}/manager/faults/edit/${issue.issueId}" class="btn btn-secondary">编辑故障</a>
            </sec:authorize>
            <a href="${ctxPath}/manager/faults/list" class="btn btn-secondary">返回列表</a>
        </div>
    </div>
</div>

<%@ include file="../00-footer.jsp" %> 