<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>排序功能测试</title>
    <script src="https://code.jquery.com/jquery-2.0.3.min.js"></script>
    <script src="src/main/resources/webapp/static/js/stupidtable.min.js"></script>
    <link rel="stylesheet" href="src/main/resources/webapp/static/css/style.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        th {
            background-color: #f2f2f2;
            cursor: pointer;
        }
        .sorting {
            cursor: pointer;
        }
        .sorting-asc, .sorting-desc {
            background: #CCC;
            border-radius: 5px 5px 0 0;
        }
    </style>
</head>
<body>
    <h1>充电桩排序功能测试</h1>
    
    <h2>测试表格 1 - 使用 res 类</h2>
    <table class="res">
        <thead>
            <tr>
                <th data-sort="string">ChargeBox ID</th>
                <th data-sort="string">Status</th>
                <th data-sort="date">Last Heartbeat</th>
                <th data-sort="string">Firmware Version</th>
                <th data-sort="float">Success Rate</th>
                <th data-sort="int">Total Sessions</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>CP001</td>
                <td>Online</td>
                <td data-sort-value="1690780800000">2023-07-31 10:00:00</td>
                <td>v1.2.3</td>
                <td data-sort-value="95.5">95.5%</td>
                <td>150</td>
            </tr>
            <tr>
                <td>CP002</td>
                <td>Offline</td>
                <td data-sort-value="1690694400000">2023-07-30 10:00:00</td>
                <td>v1.1.0</td>
                <td data-sort-value="87.2">87.2%</td>
                <td>89</td>
            </tr>
            <tr>
                <td>CP003</td>
                <td>Online</td>
                <td data-sort-value="1690867200000">2023-08-01 10:00:00</td>
                <td>v1.3.0</td>
                <td data-sort-value="92.1">92.1%</td>
                <td>203</td>
            </tr>
        </tbody>
    </table>

    <h2>测试表格 2 - 使用 chargePointTable ID</h2>
    <table id="chargePointTable" class="table table-striped table-hover table-bordered">
        <thead>
            <tr>
                <th class="sorting" data-sort="string">ChargeBox ID</th>
                <th class="sorting" data-sort="string">Status</th>
                <th class="sorting" data-sort="date">Last Heartbeat</th>
                <th class="sorting" data-sort="string">Firmware Version</th>
                <th class="sorting" data-sort="float">Success Rate</th>
                <th class="sorting" data-sort="int">Total Sessions</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>CP004</td>
                <td>Online</td>
                <td data-sort-value="1690780800000">2023-07-31 10:00:00</td>
                <td>v2.0.1</td>
                <td data-sort-value="98.3">98.3%</td>
                <td>275</td>
            </tr>
            <tr>
                <td>CP005</td>
                <td>Offline</td>
                <td data-sort-value="1690694400000">2023-07-30 10:00:00</td>
                <td>v1.9.5</td>
                <td data-sort-value="85.7">85.7%</td>
                <td>156</td>
            </tr>
            <tr>
                <td>CP006</td>
                <td>Online</td>
                <td data-sort-value="1690867200000">2023-08-01 10:00:00</td>
                <td>v2.1.0</td>
                <td data-sort-value="94.8">94.8%</td>
                <td>312</td>
            </tr>
        </tbody>
    </table>

    <script>
        // 初始化排序功能 - 模拟 sortable.js 的内容
        var tables = $(".res, #chargePointTable").stupidtable({
            "date": function (a, b) {
                var aDate = (parseInt(a) || 0);
                var bDate = (parseInt(b) || 0);
                return aDate - bDate;
            }
        });

        tables.on("aftertablesort", function (event, data) {
            var th = $(this).find("th");
            th.find(".arrow").remove();
            var dir = $.fn.stupidtable.dir;

            // https://en.wikipedia.org/wiki/Geometric_Shapes
            var arrow = data.direction === dir.ASC ? "&#9650;" : "&#9660;";
            th.eq(data.column).append('<span class="arrow" style="float: right">' + arrow + '</span>');
        });

        // 为表格头部添加排序样式
        $('table.res th[data-sort], #chargePointTable th[data-sort]').addClass('sorting');
    </script>
</body>
</html>
