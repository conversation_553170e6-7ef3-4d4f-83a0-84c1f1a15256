<%@ include file="../00-header.jsp" %>
<%@ include file="../00-op-bind-errors.jsp" %>
<div class="content">
    <div>
        <section><span>Fault Notification Email Settings</span></section>
        
        <div class="action-row">
            <a class="btn btn-default" href="${ctxPath}/manager/settings">
                <span class="fa fa-cog"></span>Email Server Settings
            </a>
            <form:form action="${ctxPath}/manager/notification/test" method="post">
                <button class="btn btn-default">
                    <span class="fa fa-envelope"></span>Send Test Email
                </button>
            </form:form>
        </div>
        
        <c:if test="${not empty success}">
            <div class="success">${success}</div>
        </c:if>
        <c:if test="${not empty error}">
            <div class="error">${error}</div>
        </c:if>
        
        <!-- Add New Email -->
        <div class="form-group">
            <form:form action="${ctxPath}/manager/notification/add" modelAttribute="emailForm" method="post">
                <table class="userInput">
                    <tr>
                        <td>Notification Email:</td>
                        <td>
                            <form:input path="email" placeholder="Enter notification email address" cssClass="emailInput" />
                            <form:errors path="email" cssClass="error" />
                        </td>
                    </tr>
                    <tr>
                        <td>Description:</td>
                        <td>
                            <form:input path="description" placeholder="Optional, enter description" cssClass="descriptionInput" />
                            <form:errors path="description" cssClass="error" />
                        </td>
                    </tr>
                    <tr>
                        <td>Status:</td>
                        <td>
                            <form:checkbox path="enabled" cssClass="form-check-input" checked="checked" /> Enable Notification
                        </td>
                    </tr>
                    <tr>
                        <td></td>
                        <td>
                            <input type="submit" value="Add Notification Email">
                        </td>
                    </tr>
                </table>
            </form:form>
        </div>
            
        <!-- Email List -->
        <table class="res action">
            <thead>
                <tr>
                    <th>Email Address</th>
                    <th>Status</th>
                    <th>Create Time</th>
                    <th>Last Notified Time</th>
                    <th>Description</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <c:if test="${empty emailList}">
                    <tr>
                        <td colspan="6">No notification emails yet</td>
                    </tr>
                </c:if>
                <c:forEach items="${emailList}" var="email">
                    <tr>
                        <td>${email.email}</td>
                        <td>
                            <c:choose>
                                <c:when test="${email.enabled}">
                                    <span class="status-enabled">Enabled</span>
                                </c:when>
                                <c:otherwise>
                                    <span class="status-disabled">Disabled</span>
                                </c:otherwise>
                            </c:choose>
                        </td>
                        <td><fmt:formatDate value="${email.createTime}" pattern="yyyy-MM-dd HH:mm:ss" /></td>
                        <td>
                            <c:choose>
                                <c:when test="${empty email.lastNotifiedTime}">
                                    <span>Never Notified</span>
                                </c:when>
                                <c:otherwise>
                                    <fmt:formatDate value="${email.lastNotifiedTime}" pattern="yyyy-MM-dd HH:mm:ss" />
                                </c:otherwise>
                            </c:choose>
                        </td>
                        <td>${email.description}</td>
                        <td>
                            <!-- Edit Form -->
                            <form:form action="${ctxPath}/manager/notification/update/${email.id}" method="post">
                                <input type="hidden" name="id" value="${email.id}">
                                <input type="hidden" name="email" value="${email.email}">
                                <input type="hidden" name="description" value="${email.description}">
                                <input type="hidden" name="enabled" value="${!email.enabled}">
                                <button type="submit" class="btn btn-default">
                                    ${email.enabled ? 'Disable' : 'Enable'}
                                </button>
                            </form:form>
                            <form:form action="${ctxPath}/manager/notification/delete/${email.id}" method="post" onsubmit="return confirm('Are you sure you want to delete this email?');">
                                <button type="submit" class="btn btn-danger">
                                    Delete
                                </button>
                            </form:form>
                        </td>
                    </tr>
                </c:forEach>
            </tbody>
        </table>
    </div>
</div>

<style>
.content .action-row {
    margin-bottom: 15px !important;
    display: flex !important;
    gap: 10px !important;
    align-items: center !important;
    flex-wrap: wrap !important;
}

.content .action-row form {
    display: inline-block !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* 统一所有action-row中的按钮和链接样式 */
.content .action-row .btn,
.content .action-row a.btn,
.content .action-row button,
.content .action-row input[type="submit"] {
    height: 38px !important;
    padding: 8px 16px !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
    vertical-align: middle !important;
    white-space: nowrap !important;
    min-width: 120px !important;
    box-sizing: border-box !important;
    display: inline-block !important;
    text-decoration: none !important;
    border: 1px solid #ccc !important;
    border-radius: 4px !important;
    background-color: #f8f9fa !important;
    color: #495057 !important;
    cursor: pointer !important;
    margin: 0 !important;
}

.content .action-row .btn:hover,
.content .action-row a.btn:hover,
.content .action-row button:hover,
.content .action-row input[type="submit"]:hover {
    background-color: #e9ecef !important;
    border-color: #adb5bd !important;
    color: #495057 !important;
    text-decoration: none !important;
}
.content .emailInput {
    width: 300px;
}
.content .descriptionInput {
    width: 300px;
}
.status-enabled {
    color: green;
    font-weight: bold;
}
.status-disabled {
    color: red;
}

/* 修复Actions列按钮布局 - 使用更强的选择器 */
.content table.res.action td:last-child,
table.res.action td:last-child,
table.res td:last-child {
    display: flex !important;
    gap: 8px !important;
    align-items: center !important;
    justify-content: center !important;
    flex-wrap: wrap !important;
    padding: 8px !important;
    text-align: center !important;
}

.content table.res.action td:last-child form,
table.res.action td:last-child form,
table.res td:last-child form {
    display: inline-block !important;
    margin: 0 !important;
    padding: 0 !important;
}

.content table.res.action td:last-child .btn,
table.res.action td:last-child .btn,
table.res td:last-child .btn,
.content table.res.action td:last-child button,
table.res.action td:last-child button,
table.res td:last-child button {
    margin: 2px !important;
    min-width: 80px !important;
    height: 32px !important;
    padding: 6px 12px !important;
    font-size: 12px !important;
    line-height: 1.4 !important;
    white-space: nowrap !important;
    vertical-align: middle !important;
    box-sizing: border-box !important;
    display: inline-block !important;
    border-radius: 4px !important;
    cursor: pointer !important;
    text-decoration: none !important;
    border: 1px solid transparent !important;
}

/* 确保按钮样式统一 */
table.res .btn-default {
    background-color: #f8f9fa !important;
    border-color: #dee2e6 !important;
    color: #495057 !important;
}

table.res .btn-default:hover {
    background-color: #e9ecef !important;
    border-color: #adb5bd !important;
    color: #495057 !important;
}

table.res .btn-danger {
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
    color: #fff !important;
}

table.res .btn-danger:hover {
    background-color: #c82333 !important;
    border-color: #bd2130 !important;
    color: #fff !important;
}
</style>

<script type="text/javascript">
    $(document).ready(function() {
        // Client-side validation and interaction can be added here
    });
</script>

<%@ include file="../00-footer.jsp" %> 