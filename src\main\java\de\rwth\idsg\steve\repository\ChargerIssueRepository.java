/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.repository;

import de.rwth.idsg.steve.web.dto.ChargerIssueDTO;
import de.rwth.idsg.steve.web.dto.ChargerIssueForm;
import de.rwth.idsg.steve.web.dto.ChargerIssueStatus;
import de.rwth.idsg.steve.web.dto.MaintenanceRecordDTO;
import de.rwth.idsg.steve.web.dto.MaintenanceRecordForm;
import de.rwth.idsg.steve.web.dto.FaultForm;

import java.util.List;

/**
 * 充电桩故障仓库接口
 */
public interface ChargerIssueRepository {

    /**
     * 获取所有故障
     */
    List<ChargerIssueDTO> getIssues();
    
    /**
     * 按状态获取故障
     */
    List<ChargerIssueDTO> getIssuesByStatus(ChargerIssueStatus status);
    
    /**
     * 获取特定充电桩的故障
     */
    List<ChargerIssueDTO> getIssuesByChargeBoxId(String chargeBoxId);
    
    /**
     * 获取特定充电桩主键的故障
     */
    List<ChargerIssueDTO> getIssuesByChargeBoxPk(int chargeBoxPk);
    
    /**
     * 根据ID获取故障
     */
    ChargerIssueDTO getIssue(Integer issueId);
    
    /**
     * 创建故障
     */
    int createIssue(ChargerIssueForm form, Integer reporterUserPk);
    
    /**
     * 更新故障
     */
    void updateIssue(ChargerIssueForm form);
    
    /**
     * 改变故障状态
     */
    void updateIssueStatus(int issueId, ChargerIssueStatus status);
    
    /**
     * 解决故障
     */
    void resolveIssue(int issueId, String resolveDescription);
    
    /**
     * 添加故障图片
     */
    void addIssueImage(int issueId, String imagePath);
    
    /**
     * 获取故障图片
     */
    List<String> getIssueImages(int issueId);
    
    /**
     * 添加维护记录
     */
    int addMaintenanceRecord(MaintenanceRecordForm form, Integer maintainerUserPk);
    
    /**
     * 获取故障的维护记录
     */
    List<MaintenanceRecordDTO> getMaintenanceRecords(int issueId);
    
    /**
     * 根据OCPP错误代码自动创建故障
     */
    int createAutoIssue(int chargeBoxPk, String ocppErrorCode, String faultDescription);

    /**
     * 根据OCPP错误代码和厂商错误代码自动创建故障
     */
    int createAutoIssue(int chargeBoxPk, String ocppErrorCode, String faultDescription, String vendorErrorCode, String vendorId);
    
    /**
     * 根据条件获取过滤后的故障列表
     */
    List<ChargerIssueDTO> getFilteredIssues(FaultForm form);

    /**
     * 获取指定状态的故障数量
     */
    int getIssueCountByStatus(ChargerIssueStatus status);

    /**
     * 获取指定用户拥有的充电桩的指定状态故障数量
     *
     * @param status 故障状态
     * @param webUserPk 用户主键
     * @return 故障数量
     */
    int getIssueCountByStatusAndUser(ChargerIssueStatus status, Integer webUserPk);
}