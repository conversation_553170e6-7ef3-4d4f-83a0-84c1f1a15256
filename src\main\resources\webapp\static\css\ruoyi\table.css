/**
 * EVSE_OMS表格样式
 * 基于RuoYi-Vue样式改造
 */

/* 基础表格 */
.table {
  width: 100%;
  max-width: 100%;
  margin-bottom: 20px;
  border-collapse: collapse;
  border-spacing: 0;
  table-layout: fixed;
}

.table > thead > tr > th,
.table > tbody > tr > th,
.table > tfoot > tr > th,
.table > thead > tr > td,
.table > tbody > tr > td,
.table > tfoot > tr > td {
  padding: 8px;
  line-height: 1.42857143;
  vertical-align: middle;
  border-top: 1px solid #ebeef5;
}

.table > thead > tr > th {
  vertical-align: bottom;
  border-bottom: 2px solid #ebeef5;
  background-color: #f8f8f9;
  color: #606266;
  font-weight: 500;
  text-align: center;
  padding: 12px 8px;
}

.table > caption + thead > tr:first-child > th,
.table > colgroup + thead > tr:first-child > th,
.table > thead:first-child > tr:first-child > th,
.table > caption + thead > tr:first-child > td,
.table > colgroup + thead > tr:first-child > td,
.table > thead:first-child > tr:first-child > td {
  border-top: 0;
}

.table > tbody + tbody {
  border-top: 2px solid #ebeef5;
}

/* 紧凑表格 */
.table-condensed > thead > tr > th,
.table-condensed > tbody > tr > th,
.table-condensed > tfoot > tr > th,
.table-condensed > thead > tr > td,
.table-condensed > tbody > tr > td,
.table-condensed > tfoot > tr > td {
  padding: 5px;
}

/* 带边框表格 */
.table-bordered {
  border: 1px solid #ebeef5;
}

.table-bordered > thead > tr > th,
.table-bordered > tbody > tr > th,
.table-bordered > tfoot > tr > th,
.table-bordered > thead > tr > td,
.table-bordered > tbody > tr > td,
.table-bordered > tfoot > tr > td {
  border: 1px solid #ebeef5;
}

.table-bordered > thead > tr > th,
.table-bordered > thead > tr > td {
  border-bottom-width: 2px;
}

/* 条纹表格 */
.table-striped > tbody > tr:nth-of-type(odd) {
  background-color: #fafafa;
}

/* 悬停效果 */
.table-hover > tbody > tr:hover {
  background-color: #f5f7fa;
}

/* 响应式表格 */
@media screen and (max-width: 767px) {
  .table-responsive {
    width: 100%;
    margin-bottom: 15px;
    overflow-y: hidden;
    -ms-overflow-style: -ms-autohiding-scrollbar;
    border: 1px solid #ebeef5;
  }
  .table-responsive > .table {
    margin-bottom: 0;
  }
  .table-responsive > .table > thead > tr > th,
  .table-responsive > .table > tbody > tr > th,
  .table-responsive > .table > tfoot > tr > th,
  .table-responsive > .table > thead > tr > td,
  .table-responsive > .table > tbody > tr > td,
  .table-responsive > .table > tfoot > tr > td {
    white-space: nowrap;
  }
  .table-responsive > .table-bordered {
    border: 0;
  }
  .table-responsive > .table-bordered > thead > tr > th:first-child,
  .table-responsive > .table-bordered > tbody > tr > th:first-child,
  .table-responsive > .table-bordered > tfoot > tr > th:first-child,
  .table-responsive > .table-bordered > thead > tr > td:first-child,
  .table-responsive > .table-bordered > tbody > tr > td:first-child,
  .table-responsive > .table-bordered > tfoot > tr > td:first-child {
    border-left: 0;
  }
  .table-responsive > .table-bordered > thead > tr > th:last-child,
  .table-responsive > .table-bordered > tbody > tr > th:last-child,
  .table-responsive > .table-bordered > tfoot > tr > th:last-child,
  .table-responsive > .table-bordered > thead > tr > td:last-child,
  .table-responsive > .table-bordered > tbody > tr > td:last-child,
  .table-responsive > .table-bordered > tfoot > tr > td:last-child {
    border-right: 0;
  }
  .table-responsive > .table-bordered > tbody > tr:last-child > th,
  .table-responsive > .table-bordered > tfoot > tr:last-child > th,
  .table-responsive > .table-bordered > tbody > tr:last-child > td,
  .table-responsive > .table-bordered > tfoot > tr:last-child > td {
    border-bottom: 0;
  }
}

/* 表格上方工具栏 */
.table-toolbar {
  padding: 10px;
  margin-bottom: 10px;
  background-color: #fff;
  border-radius: 2px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}

.table-toolbar .left-toolbar {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.table-toolbar .right-toolbar {
  display: flex;
  align-items: center;
}

.table-toolbar .btn {
  margin-right: 5px;
  margin-bottom: 5px;
}

/* 表格分页栏 */
.table-pagination {
  display: flex;
  justify-content: flex-end;
  margin-top: 15px;
}

/* 表格排序图标 */
.table .sorting {
  cursor: pointer;
  position: relative;
}

.table .sorting:after {
  content: "↕";
  opacity: 0.5;
  margin-left: 5px;
}

.table .sorting_asc:after {
  content: "↑";
  opacity: 1;
}

.table .sorting_desc:after {
  content: "↓";
  opacity: 1;
}

/* 表格行选中状态 */
.table > tbody > tr.active > td,
.table > tbody > tr.active > th,
.table > tbody > tr > td.active,
.table > tbody > tr > th.active,
.table > tfoot > tr.active > td,
.table > tfoot > tr.active > th,
.table > tfoot > tr > td.active,
.table > tfoot > tr > th.active,
.table > thead > tr.active > td,
.table > thead > tr.active > th,
.table > thead > tr > td.active,
.table > thead > tr > th.active {
  background-color: #ecf5ff;
}

.table-hover > tbody > tr.active:hover > td,
.table-hover > tbody > tr.active:hover > th,
.table-hover > tbody > tr:hover > .active,
.table-hover > tbody > tr > td.active:hover,
.table-hover > tbody > tr > th.active:hover {
  background-color: #e8f1ff;
}

/* 表格内表单元素 */
.table input[type="checkbox"],
.table input[type="radio"] {
  margin: 0;
}