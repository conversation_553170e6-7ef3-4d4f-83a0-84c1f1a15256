<%--

    SteV<PERSON> - SteckdosenVerwaltung - https://github.com/steve-community/steve
    Copyright (C) 2013-2025 SteVe Community Team
    All Rights Reserved.

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.

--%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form" %>
<%-- 注释掉冲突的c标签前缀定义，因为它已经在00-header.jsp中定义 --%>
<%-- <%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %> --%>

<table class="userInput">
	<thead><tr><th>OCPP</th><th></th></thead>
	<tbody>
	<tr>
		<td>OCPP ID Tag:</td>
		<td><form:select path="ocppIdTag" items="${idTagList}" /></td>
	</tr>
	<tr>
		<td>Username:</td>
		<td>
			<c:choose>
				<c:when test="${not empty userForm.username}">
					<%-- 如果用户名已存在，则显示为只读字段 --%>
					<form:input path="username" readonly="true" disabled="true" />
					<form:hidden path="username" />
					<span style="color: #666; font-size: 0.8em; margin-left: 5px;">(Username cannot be modified)</span>
				</c:when>
				<c:otherwise>
					<%-- 如果是新用户，则正常显示输入框 --%>
					<form:input path="username" />
				</c:otherwise>
			</c:choose>
		</td>
	</tr>
	<tr>
		<td>Password:</td>
		<td>
			<c:choose>
				<c:when test="${not empty userForm.username}">
					<%-- 如果用户名已存在，则密码字段显示为可选 --%>
					<form:password path="password" showPassword="false" />
					<span style="color: #666; font-size: 0.8em; margin-left: 5px;">(Leave empty to keep current password)</span>
				</c:when>
				<c:otherwise>
					<%-- 如果是新用户，则密码为必填 --%>
					<form:password path="password" showPassword="false" />
				</c:otherwise>
			</c:choose>
		</td>
	</tr>
	<tr><td></td>
		<td id="add_space">
			<input type="submit" name="${submitButtonName}" value="${submitButtonValue}">
			<input type="submit" name="backToOverview" value="Back to Overview">
		</td></tr>
	</tbody>
</table>