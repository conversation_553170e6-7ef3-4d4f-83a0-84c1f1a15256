/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.service;

import de.rwth.idsg.steve.repository.dto.User;
import de.rwth.idsg.steve.web.dto.UserDTO;
import de.rwth.idsg.steve.web.dto.UserForm;
import de.rwth.idsg.steve.web.dto.UserQueryForm;

import java.util.List;

/**
 * 用户服务接口
 */
public interface UserService {
    
    /**
     * 获取用户列表概览
     */
    List<User.Overview> getOverview(UserQueryForm form);
    
    /**
     * 获取用户详情
     */
    User.Details getDetails(int userPk);
    
    /**
     * 添加用户
     */
    void add(UserForm form);
    
    /**
     * 更新用户信息
     */
    void update(UserForm form);
    
    /**
     * 删除用户
     */
    void delete(int userPk);
    
    /**
     * 获取所有用户
     */
    List<UserDTO> getUsers();
    
    /**
     * 根据主键获取用户
     */
    UserForm getUser(int userPk);
    
    /**
     * 添加企业用户
     */
    void addEnterpriseUser(UserForm form);
    
    /**
     * 添加车辆用户
     */
    void addCarUser(UserForm form);
    
    /**
     * 更新用户信息
     */
    void updateUser(UserForm form);
    
    /**
     * 删除用户
     */
    void deleteUser(int userPk);
    
    /**
     * 根据用户名获取用户ID列表
     */
    List<Integer> getUserIdsByName(String userName);
    
    /**
     * 根据用户名获取Web用户ID
     */
    Integer getWebUserId(String userName);
    
    /**
     * 根据用户PK获取用户名
     */
    String getUserName(int userPk);
} 