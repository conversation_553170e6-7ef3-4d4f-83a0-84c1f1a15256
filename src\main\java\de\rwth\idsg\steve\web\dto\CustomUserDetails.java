/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.web.dto;

import jooq.steve.db.tables.records.WebUserRecord;
import lombok.Getter;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.User;

import java.util.Collection;

/**
 * 扩展Spring Security的User类，添加更多用户信息
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Getter
public class CustomUserDetails extends User {

    private final WebUserRecord userRecord;

    public CustomUserDetails(WebUserRecord userRecord, 
                           Collection<? extends GrantedAuthority> authorities) {
        super(userRecord.getUsername(), 
              userRecord.getPassword(), 
              userRecord.getEnabled(), 
              true, // accountNonExpired
              true, // credentialsNonExpired
              true, // accountNonLocked
              authorities);
        this.userRecord = userRecord;
    }
} 