/*
 * SteV<PERSON> - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.web.controller;

import de.rwth.idsg.steve.ocpp.OcppProtocol;
import de.rwth.idsg.steve.repository.ChargePointRepository;
import de.rwth.idsg.steve.repository.WebUserRepository;
import de.rwth.idsg.steve.repository.dto.ChargePointSelect;
import de.rwth.idsg.steve.service.ChargePointHelperService;
import de.rwth.idsg.steve.service.FaultService;
import de.rwth.idsg.steve.web.dto.ChargerIssueDTO;
import de.rwth.idsg.steve.web.dto.ChargerIssueForm;
import de.rwth.idsg.steve.web.dto.ChargerIssueStatus;
import de.rwth.idsg.steve.web.dto.FaultUpdateForm;
import de.rwth.idsg.steve.web.dto.MaintenanceRecordDTO;
import de.rwth.idsg.steve.web.dto.MaintenanceRecordForm;
import lombok.extern.slf4j.Slf4j;
import ocpp.cs._2015._10.RegistrationStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.propertyeditors.CustomDateEditor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import jakarta.validation.Valid;
import java.security.Principal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Charge Point Fault Management Controller
 */
@Slf4j
@Controller
@RequestMapping(value = "/manager/operations")
@PreAuthorize("hasAnyAuthority('ADMIN', 'OPERATOR_FACTORY', 'OPERATOR_OWNER')")
public class OperationsFaultController {

    @Autowired
    private FaultService faultService;

    @Autowired
    private ChargePointRepository chargePointRepository;
    
    @Autowired
    private ChargePointHelperService chargePointHelperService;
    
    @Autowired
    private WebUserRepository webUserRepository;
    
    /**
     * Initialize date binder
     */
    @InitBinder
    public void initBinder(WebDataBinder binder) {
        // 支持多种日期格式
        SimpleDateFormat isoFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        SimpleDateFormat usFormat = new SimpleDateFormat("MM/dd/yyyy HH:mm");
        
        // 创建一个可以处理多种日期格式的编辑器
        binder.registerCustomEditor(Date.class, new CustomDateEditor(isoFormat, true) {
            @Override
            public void setAsText(String text) {
                if (text == null || text.trim().isEmpty()) {
                    setValue(null);
                    return;
                }
                
                try {
                    // 先尝试标准格式 (yyyy-MM-dd HH:mm)
                    setValue(isoFormat.parse(text));
                } catch (Exception e1) {
                    try {
                        // 尝试美式格式 (MM/dd/yyyy HH:mm)
                        setValue(usFormat.parse(text));
                    } catch (Exception e2) {
                        throw new IllegalArgumentException("Could not parse date: " + e2.getMessage(), e2);
                    }
                }
            }
        });
    }

    /**
     * View fault list page
     */
    @GetMapping("/faults")
    public String getList(Model model,
                        @RequestParam(value = "status", required = false) String statusParam,
                        @RequestParam(value = "chargeBoxId", required = false) String chargeBoxId,
                        @RequestParam(value = "errorCode", required = false) String errorCode,
                        @RequestParam(value = "reportType", required = false) String reportType,
                        @RequestParam(value = "startDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
                        @RequestParam(value = "endDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
                        Principal principal) {
        
        // Process status filter
        ChargerIssueStatus status = null;
        if (statusParam != null && !statusParam.isEmpty()) {
            try {
                status = ChargerIssueStatus.valueOf(statusParam);
            } catch (IllegalArgumentException e) {
                log.warn("Invalid status parameter: {}", statusParam);
            }
        }

        // Get fault list
        List<ChargerIssueDTO> issues;
        if (status != null) {
            issues = faultService.getIssuesByStatus(status);
            model.addAttribute("selectedStatus", status);
        } else {
            issues = faultService.getIssues();
        }
        
        // Apply other filters
        if (chargeBoxId != null && !chargeBoxId.isEmpty()) {
            issues = issues.stream()
                    .filter(i -> i.getChargeBoxId().equals(chargeBoxId))
                    .collect(Collectors.toList());
            model.addAttribute("selectedChargeBox", chargeBoxId);
        }
        
        if (errorCode != null && !errorCode.isEmpty()) {
            issues = issues.stream()
                    .filter(i -> i.getOcppErrorCode() != null && i.getOcppErrorCode().contains(errorCode))
                    .collect(Collectors.toList());
            model.addAttribute("selectedErrorCode", errorCode);
        }
        
        if (reportType != null && !reportType.isEmpty()) {
            boolean isAuto = "auto".equals(reportType);
            issues = issues.stream()
                    .filter(i -> i.getIsAutoReported() == isAuto)
                    .collect(Collectors.toList());
            model.addAttribute("selectedReportType", reportType);
        }
        
        if (startDate != null) {
            issues = issues.stream()
                    .filter(i -> i.getReportTime() != null && !i.getReportTime().before(startDate))
                    .collect(Collectors.toList());
            model.addAttribute("startDate", startDate);
        }
        
        if (endDate != null) {
            issues = issues.stream()
                    .filter(i -> i.getReportTime() != null && !i.getReportTime().after(endDate))
                    .collect(Collectors.toList());
            model.addAttribute("endDate", endDate);
        }

        // Add owner information for each fault
        for (ChargerIssueDTO issue : issues) {
            if (issue.getChargeBoxPk() != null) {
                String owner = chargePointHelperService.getChargePointOwner(issue.getChargeBoxPk());
                issue.setChargeBoxOwner(owner);
            }
        }

        model.addAttribute("issues", issues);
        model.addAttribute("statuses", ChargerIssueStatus.values());
        model.addAttribute("chargePoints", getChargePointList(principal));

        // Add empty form object for the search form
        model.addAttribute("faultForm", new Object());
        
        return "operations-faults";
    }
    
    /**
     * Fault details page
     */
    @GetMapping("/fault-details/{issueId}")
    public String getDetails(@PathVariable("issueId") int issueId, Model model) {
        ChargerIssueDTO issue = faultService.getIssue(issueId);
        if (issue == null) {
            return "redirect:/manager/operations/faults";
        }

        // Get charge point owner information
        if (issue.getChargeBoxPk() != null) {
            String owner = chargePointHelperService.getChargePointOwner(issue.getChargeBoxPk());
            issue.setChargeBoxOwner(owner);
        }

        List<MaintenanceRecordDTO> maintenanceRecords = faultService.getMaintenanceRecords(issueId);

        model.addAttribute("issue", issue);
        model.addAttribute("maintenanceRecords", maintenanceRecords);
        model.addAttribute("statuses", ChargerIssueStatus.values());

        // Add maintenance record form
        MaintenanceRecordForm recordForm = new MaintenanceRecordForm();
        recordForm.setIssueId(issueId);
        model.addAttribute("maintenanceForm", recordForm);

        return "operations-fault-details";
    }

    /**
     * Report fault page
     */
    @GetMapping("/report-fault")
    public String reportFaultForm(Model model, Principal principal) {
        ChargerIssueForm form = new ChargerIssueForm();
        // Default to current time
        form.setReportTime(new Date());
        model.addAttribute("faultForm", form);
        model.addAttribute("chargePoints", getChargePointList(principal));
        return "operations-report-fault";
    }

    /**
     * Process fault report
     */
    @PostMapping("/report-fault")
    public String reportFault(@Valid @ModelAttribute("faultForm") ChargerIssueForm form,
                             BindingResult result, Model model, Principal principal,
                             RedirectAttributes redirectAttributes) {
        if (result.hasErrors()) {
            model.addAttribute("chargePoints", getChargePointList(principal));
            return "operations-report-fault";
        }

        // Convert chargeBoxId to chargeBoxPk
        if (form.getChargeBoxId() != null && !form.getChargeBoxId().isEmpty()) {
            Integer chargeBoxPk = chargePointRepository.getChargeBoxPkFromChargeBoxId(form.getChargeBoxId());
            if (chargeBoxPk != null) {
                // Security check: verify user has permission to access this charge point
                List<ChargePointSelect> userChargePoints = getChargePointList(principal);
                boolean hasPermission = userChargePoints.stream()
                        .anyMatch(cp -> cp.getChargeBoxId().equals(form.getChargeBoxId()));

                if (!hasPermission) {
                    result.rejectValue("chargeBoxId", "error.unauthorizedChargePoint",
                            "You don't have permission to access this charge point");
                    model.addAttribute("chargePoints", getChargePointList(principal));
                    return "operations-report-fault";
                }

                form.setChargeBoxPk(chargeBoxPk);
            } else {
                result.rejectValue("chargeBoxId", "error.invalidChargePoint", "Invalid charge point ID");
                model.addAttribute("chargePoints", getChargePointList(principal));
                return "operations-report-fault";
            }
        } else {
            result.rejectValue("chargeBoxId", "error.emptyChargePoint", "Please select a charge point");
            model.addAttribute("chargePoints", getChargePointList(principal));
            return "operations-report-fault";
        }

        try {
            int issueId = faultService.createIssue(form, principal != null ? principal.getName() : null);
            
            // If there are images to upload, save them
            if (form.getImages() != null && !form.getImages().isEmpty()) {
                for (MultipartFile image : form.getImages()) {
                    if (image != null && !image.isEmpty()) {
                        try {
                            faultService.saveIssueImages(issueId, form.getImages());
                            break; // Only need to process the list once
                        } catch (Exception e) {
                            // Log error but continue processing
                            log.error("Failed to save image", e);
                        }
                    }
                }
            }
            
            redirectAttributes.addFlashAttribute("success", "The submission was successful");
            return "redirect:/manager/operations/faults";
        } catch (Exception e) {
            log.error("Error reporting fault", e);
            result.reject("error.general", "An error occurred while submitting the fault report. Please try again later.");
            model.addAttribute("chargePoints", getChargePointList(principal));
            return "operations-report-fault";
        }
    }

    /**
     * Update fault status page
     */
    @GetMapping("/update-fault/{issueId}")
    public String updateFaultForm(@PathVariable("issueId") int issueId, Model model) {
        ChargerIssueDTO issue = faultService.getIssue(issueId);
        if (issue == null) {
            return "redirect:/manager/operations/faults";
        }

        // Get charge point owner information
        if (issue.getChargeBoxPk() != null) {
            String owner = chargePointHelperService.getChargePointOwner(issue.getChargeBoxPk());
            issue.setChargeBoxOwner(owner);
        }

        // 获取维护记录并添加到模型中
        List<MaintenanceRecordDTO> maintenanceRecords = faultService.getMaintenanceRecords(issueId);
        
        // 添加日志来验证维护记录 - 增强日志信息
        log.info("====================== MAINTENANCE RECORDS DEBUG INFO ======================");
        log.info("Maintenance records found: {}, for fault ID: {}", maintenanceRecords.size(), issueId);
        
        if (maintenanceRecords.isEmpty()) {
            log.info("No maintenance records were found for this fault");
        } else {
            log.info("Listing all maintenance records for fault ID: {}", issueId);
            int count = 1;
            for (MaintenanceRecordDTO record : maintenanceRecords) {
                log.info("Record #{}: [ID={}] [Time={}] [User={}] [Content={}]",
                        count++,
                        record.getRecordId(), 
                        record.getMaintenanceTime(), 
                        record.getMaintainerUsername(),
                        record.getMaintenanceDescription());
            }
        }
        log.info("====================================================================");
        
        // 更改：确保模型中的maintenanceRecords不为null
        model.addAttribute("maintenanceRecords", maintenanceRecords != null ? maintenanceRecords : new ArrayList<>());

        FaultUpdateForm updateForm = new FaultUpdateForm();
        updateForm.setIssueId(issue.getIssueId());
        updateForm.setStatus(issue.getStatus());
        
        // 不自动填充最新的维护记录内容
        // 只有当解决方案描述存在时才使用它
        if (issue.getResolveDescription() != null && !issue.getResolveDescription().isEmpty()) {
            updateForm.setNote(issue.getResolveDescription());
        }

        model.addAttribute("issue", issue);
        model.addAttribute("updateForm", updateForm);
        model.addAttribute("statuses", ChargerIssueStatus.values());

        // 添加日志验证模型中的维护记录数据
        log.info("Maintenance records in model: {}", ((List<?>)model.getAttribute("maintenanceRecords")).size());

        return "operations-update-fault";
    }

    /**
     * Process update fault status
     */
    @PostMapping("/update-fault/{issueId}")
    public String updateFault(@PathVariable("issueId") int issueId,
                             @Valid @ModelAttribute("updateForm") FaultUpdateForm form,
                             BindingResult result, Principal principal,
                             RedirectAttributes redirectAttributes) {
        if (result.hasErrors() || issueId != form.getIssueId()) {
            return "redirect:/manager/operations/update-fault/" + issueId;
        }

        // 更新故障状态（将不再自动创建"Status updated to: xxx"的记录）
        faultService.updateIssueStatus(form.getIssueId(), form.getStatus(), principal != null ? principal.getName() : null);

        // 只有当用户实际输入了维护记录内容时，才创建维护记录
        if (form.getNote() != null && !form.getNote().trim().isEmpty()) {
            MaintenanceRecordForm recordForm = new MaintenanceRecordForm();
            recordForm.setIssueId(issueId);
            recordForm.setMaintenanceDescription(form.getNote());
            faultService.addMaintenanceRecord(recordForm, principal != null ? principal.getName() : null);
        }

        // 如果状态更改为已解决，记录解决方案信息（但不自动创建"Issue resolved: xxx"的记录）
        if (form.getStatus() == ChargerIssueStatus.RESOLVED) {
            String resolveDescription = (form.getNote() != null && !form.getNote().trim().isEmpty()) 
                                    ? form.getNote() 
                                    : "Resolved without specific details";
            faultService.resolveIssue(issueId, resolveDescription, principal != null ? principal.getName() : null);
        }
        
        // 添加成功消息
        redirectAttributes.addFlashAttribute("success", "Fault status has been successfully updated");

        // 重定向到故障列表页面
        return "redirect:/manager/operations/faults";
    }

    /**
     * Process resolve fault
     */
    @PostMapping("/resolve/{issueId}")
    public String resolveFault(@PathVariable("issueId") int issueId,
                              @RequestParam("resolveDescription") String resolveDescription,
                              Principal principal) {
        // 标记故障为已解决，但不自动创建维护记录
        faultService.resolveIssue(issueId, resolveDescription, principal != null ? principal.getName() : null);
        
        // 手动创建一个维护记录
        if (resolveDescription != null && !resolveDescription.trim().isEmpty()) {
            MaintenanceRecordForm recordForm = new MaintenanceRecordForm();
            recordForm.setIssueId(issueId);
            recordForm.setMaintenanceDescription(resolveDescription);
            faultService.addMaintenanceRecord(recordForm, principal != null ? principal.getName() : null);
        }
        
        return "redirect:/manager/operations/fault-details/" + issueId;
    }

    /**
     * Add maintenance record
     */
    @PostMapping("/add-maintenance/{issueId}")
    public String addMaintenanceRecord(@PathVariable("issueId") int issueId,
                                      @Valid @ModelAttribute("maintenanceForm") MaintenanceRecordForm form,
                                      BindingResult result, Principal principal) {
        if (result.hasErrors() || issueId != form.getIssueId()) {
            return "redirect:/manager/operations/fault-details/" + issueId;
        }

        faultService.addMaintenanceRecord(form, principal != null ? principal.getName() : null);
        return "redirect:/manager/operations/fault-details/" + issueId;
    }

    /**
     * Get charge point list
     * Returns different charge point lists based on user role
     * 
     * @param principal Current logged-in user
     * @return Charge point selection list
     */
    private List<ChargePointSelect> getChargePointList(Principal principal) {
        if (principal == null) {
            // If no logged-in user, return empty list
            return Collections.emptyList();
        }
        
        // Get username
        String username = principal.getName();
        
        // Get user authorities from SecurityContext
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        Collection<? extends GrantedAuthority> authorities = authentication.getAuthorities();
        
        // Check user roles
        boolean isAdmin = authorities.stream()
                .anyMatch(a -> a.getAuthority().equals("ADMIN"));
        boolean isOperatorFactory = authorities.stream()
                .anyMatch(a -> a.getAuthority().equals("OPERATOR_FACTORY"));
        
        if (isAdmin || isOperatorFactory) {
            // Admins or factory operators can see all charge points
            return chargePointRepository.getChargePointSelectList();
        } else {
            // Owners can only see their own charge points
            // First get user ID
            Integer userPk = webUserRepository.getUserPkByUsername(username);
            if (userPk == null) {
                return Collections.emptyList();
            }
            
            // Get charge points owned by the user
            return chargePointRepository.getChargePointSelectByOwner(userPk);
        }
    }
} 