/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.utils.mapper;

import de.rwth.idsg.steve.repository.dto.User;
import de.rwth.idsg.steve.service.WebUserService;
import de.rwth.idsg.steve.utils.ControllerHelper;
import de.rwth.idsg.steve.web.dto.UserForm;
import de.rwth.idsg.steve.web.dto.UserRole;
import de.rwth.idsg.steve.web.dto.UserSex;
import jooq.steve.db.tables.records.UserRecord;
import jooq.steve.db.tables.records.WebUserRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> Goekay <<EMAIL>>
 * @since 23.03.2021
 */
@Component
@Slf4j
public class UserFormMapper {

    private final WebUserService webUserService;

    public UserFormMapper(WebUserService webUserService) {
        this.webUserService = webUserService;
    }

    public UserForm toForm(User.Details details) {
        UserRecord userRecord = details.getUserRecord();

        UserForm form = new UserForm();
        form.setUserPk(userRecord.getUserPk());
        form.setFirstName(userRecord.getFirstName());
        form.setLastName(userRecord.getLastName());
        form.setBirthDay(userRecord.getBirthDay());
        form.setPhone(userRecord.getPhone());
        form.setSex(UserSex.fromDatabaseValue(userRecord.getSex()));
        form.setEMail(userRecord.getEMail());
        form.setNote(userRecord.getNote());
        form.setAddress(AddressMapper.recordToDto(details.getAddress()));
        form.setOcppIdTag(details.getOcppIdTag().orElse(ControllerHelper.EMPTY_OPTION));

        // 查找该用户对应的web_user信息
        try {
            // 使用新的直接关联方法查找对应的web_user记录
            WebUserRecord webUser = webUserService.findWebUserByUserPk(userRecord.getUserPk());
            
            if (webUser != null) {
                log.debug("Found web user for user_pk={}: {}", userRecord.getUserPk(), webUser.getUsername());
                form.setUsername(webUser.getUsername());
                // 设置用户角色
                try {
                    // 获取web_user记录中的用户角色（是jooq.steve.db.enums.WebUserUserRole类型）
                    jooq.steve.db.enums.WebUserUserRole dbRole = webUser.getUserRole();
                    if (dbRole != null) {
                        // 从数据库枚举类型转换为应用中的UserRole枚举
                        UserRole role = UserRole.valueOf(dbRole.name());
                        form.setUserRole(role);
                        log.debug("Set user role for user_pk={}: {}", userRecord.getUserPk(), role);
                    } else {
                        // 如果角色为空，设置默认角色ADMIN
                        form.setUserRole(UserRole.ADMIN);
                        log.debug("No role found, setting default ADMIN role for user_pk={}", userRecord.getUserPk());
                    }
                } catch (Exception e) {
                    log.error("Error setting user role for user_pk={}: {}", userRecord.getUserPk(), e.getMessage(), e);
                    // 出错时设置默认角色
                    form.setUserRole(UserRole.ADMIN);
                }
                // 不设置密码，密码将在修改时单独处理
            } else {
                log.debug("No web user found for user_pk={}", userRecord.getUserPk());
                // 如果没有找到关联的web_user，也设置默认角色
                form.setUserRole(UserRole.ADMIN);
            }
        } catch (Exception e) {
            log.error("Error finding web user for user_pk={}: {}", userRecord.getUserPk(), e.getMessage(), e);
            // 出错时设置默认角色
            form.setUserRole(UserRole.ADMIN);
        }

        return form;
    }
}
