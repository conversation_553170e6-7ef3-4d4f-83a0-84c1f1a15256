package de.rwth.idsg.steve.service;

import de.rwth.idsg.steve.web.dto.DashboardChartsDTO;

/**
 * 图表数据服务接口
 *
 * <AUTHOR> Assistant
 */
public interface ChartDataService {

    /**
     * 获取仪表板所有图表数据
     *
     * @return 仪表板图表数据
     */
    DashboardChartsDTO getDashboardChartsData();

    /**
     * 根据用户权限获取仪表板图表数据
     *
     * @param webUserPk 用户主键，如果为null则返回所有数据
     * @return 仪表板图表数据
     */
    DashboardChartsDTO getDashboardChartsDataByUser(Integer webUserPk);
}
