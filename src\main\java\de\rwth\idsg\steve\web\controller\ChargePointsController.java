/*
 * SteV<PERSON> - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.web.controller;

import de.rwth.idsg.steve.ocpp.OcppProtocol;
import de.rwth.idsg.steve.repository.ChargePointRepository;
import de.rwth.idsg.steve.repository.ChargeStationRepository;
import de.rwth.idsg.steve.repository.UserChargeBoxRepository;
import de.rwth.idsg.steve.repository.dto.ChargePoint;
import de.rwth.idsg.steve.service.ChargePointHelperService;
import de.rwth.idsg.steve.service.ChargingSuccessService;
import de.rwth.idsg.steve.repository.dto.FirmwareFile;
import de.rwth.idsg.steve.service.FirmwareFileService;
import de.rwth.idsg.steve.service.PermissionService;
import de.rwth.idsg.steve.service.WebUserService;
import de.rwth.idsg.steve.utils.ControllerHelper;
import de.rwth.idsg.steve.utils.mapper.ChargePointDetailsMapper;
import de.rwth.idsg.steve.web.dto.ChargePointBatchInsertForm;
import de.rwth.idsg.steve.web.dto.ChargePointForm;
import de.rwth.idsg.steve.web.dto.ChargePointQueryForm;
import de.rwth.idsg.steve.web.dto.UserRole;
import de.rwth.idsg.steve.ocpp.OcppVersion;
import de.rwth.idsg.steve.SteveException;
import de.rwth.idsg.steve.SteveConfiguration;
import de.rwth.idsg.steve.ocpp.OcppCallback;
import de.rwth.idsg.steve.ocpp.ws.data.OcppJsonError;
import de.rwth.idsg.steve.repository.UpdateFirmwareLogRepository;
import de.rwth.idsg.steve.service.ChargePointServiceClient;
import de.rwth.idsg.steve.utils.DateTimeUtils;
import de.rwth.idsg.steve.web.dto.ocpp.UpdateFirmwareParams;
import de.rwth.idsg.steve.repository.dto.ChargePointSelect;
import de.rwth.idsg.steve.repository.dto.ChargeStationSelect;
import jooq.steve.db.tables.records.ChargeBoxRecord;
import jooq.steve.db.tables.records.WebUserRecord;
import org.jooq.DSLContext;

import static jooq.steve.db.tables.UserChargeBox.USER_CHARGE_BOX;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.joda.time.LocalDateTime;
import org.jooq.DSLContext;
import org.jooq.Record;
import org.jooq.exception.IntegrityConstraintViolationException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;
import org.springframework.http.ResponseEntity;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.DeleteMapping;

import java.util.List;

import jakarta.validation.Valid;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static org.jooq.impl.DSL.field;
import static org.jooq.impl.DSL.table;

import de.rwth.idsg.steve.service.DataTransferService;
import de.rwth.idsg.steve.web.dto.DataTransferForm;

/**
 *
 * <AUTHOR> Goekay <<EMAIL>>
 *
 */
@Slf4j
@Controller
@RequestMapping(value = "/manager/chargepoints")
public class ChargePointsController {

    @Autowired protected ChargePointRepository chargePointRepository;
    @Autowired protected ChargeStationRepository chargeStationRepository;
    @Autowired protected DataTransferService dataTransferService;
    @Autowired protected ChargePointHelperService chargePointHelperService;
    @Autowired protected PermissionService permissionService;
    @Autowired protected UserChargeBoxRepository userChargeBoxRepository;
    @Autowired private ChargePointServiceClient chargePointServiceClient;
    @Autowired private ChargingSuccessService chargingSuccessService;
    @Autowired private UpdateFirmwareLogRepository updateFirmwareLogRepository;
    @Autowired private DSLContext ctx;
    @Autowired private OcppAjaxController ocppAjaxController;
    @Autowired private WebUserService webUserService;
    @Autowired private FirmwareFileService firmwareFileService;

    private final Map<String, ImportProgress> importTasks = new ConcurrentHashMap<>();

    protected static final String PARAMS = "params";
    private static final int DEFAULT_PAGE_SIZE = 10;
    private static final List<Integer> AVAILABLE_PAGE_SIZES = Arrays.asList(3, 10, 50, 100);

    private static final List<String> upToOcpp15RegistrationStatusList = Arrays.stream(ocpp.cs._2012._06.RegistrationStatus.values())
                                                                               .map(ocpp.cs._2012._06.RegistrationStatus::value)
                                                                               .collect(Collectors.toList());

    private static final List<String> ocpp16RegistrationStatusList = Arrays.stream(ocpp.cs._2015._10.RegistrationStatus.values())
                                                                           .map(ocpp.cs._2015._10.RegistrationStatus::value)
                                                                           .collect(Collectors.toList());

    // -------------------------------------------------------------------------
    // Paths
    // -------------------------------------------------------------------------

    protected static final String QUERY_PATH = "/query";

    protected static final String DETAILS_PATH = "/details/{chargeBoxId}";
    protected static final String DELETE_PATH = "/delete/{chargeBoxPk}";
    protected static final String UPDATE_PATH = "/update";
    protected static final String BATCH_UPDATE_PATH = "/batch-update";
    protected static final String ADD_PATH = "/add";

    protected static final String ADD_SINGLE_PATH = "/add/single";
    protected static final String ADD_BATCH_PATH = "/add/batch";
    protected static final String IMPORT_PATH = "/import";
    protected static final String IMPORT_PROGRESS_PATH = "/import/progress/{taskId}";

    // We need the slash at the end to support chargeBoxIds with dots etc. in them
    // Issue: https://github.com/steve-community/steve/issues/270
    // Solution: https://stackoverflow.com/a/18378817
    protected static final String UNKNOWN_REMOVE_PATH = "/unknown/remove/{chargeBoxId}/";
    protected static final String UNKNOWN_ADD_PATH = "/unknown/add/{chargeBoxId}/";

    // -------------------------------------------------------------------------
    // HTTP methods
    // -------------------------------------------------------------------------

    @RequestMapping(method = RequestMethod.GET)
    public String getOverview(Model model, Pageable pageable, @RequestParam(value = "size", required = false) Integer size) {
        log.info("Accessing charge points overview page with pageable: {}", pageable);
        int pageSize = (size != null && AVAILABLE_PAGE_SIZES.contains(size)) ? size : DEFAULT_PAGE_SIZE;
        Pageable newPageable = PageRequest.of(pageable.getPageNumber(), pageSize, pageable.getSort());
        initList(model, new ChargePointQueryForm(), newPageable);
        return "data-man/chargepoints";
    }

    @RequestMapping(value = QUERY_PATH, method = RequestMethod.GET)
    public String getQuery(@ModelAttribute(PARAMS) ChargePointQueryForm params, Model model, Pageable pageable, @RequestParam(value = "size", required = false) Integer size) {
        log.info("Querying charge points with parameters: {} and pageable: {}", params, pageable);
        int pageSize = (size != null && AVAILABLE_PAGE_SIZES.contains(size)) ? size : DEFAULT_PAGE_SIZE;
        Pageable newPageable = PageRequest.of(pageable.getPageNumber(), pageSize, pageable.getSort());
        initList(model, params, newPageable);
        return "data-man/chargepoints";
    }

    private void initList(Model model, ChargePointQueryForm params, Pageable pageable) {
        Page<ChargePoint.Overview> chargePointPage;

        if (params == null) { // Ensure params is not null for query
            params = new ChargePointQueryForm();
        }

        if (permissionService.isOperatorOwner()) {
            WebUserRecord currentUser = permissionService.getCurrentUser();
            List<ChargePoint.Overview> ownerList = chargePointRepository.getOwnerChargeBoxes(currentUser.getWebUserPk(), params);
            
            int start = (int) pageable.getOffset();
            int end = Math.min((start + pageable.getPageSize()), ownerList.size());
            
            List<ChargePoint.Overview> pageContent;
            if (start >= ownerList.size()) { // if start is beyond the list size, content is empty
                pageContent = Collections.emptyList();
            } else {
                pageContent = ownerList.subList(start, end);
            }
            
            chargePointPage = new PageImpl<>(pageContent, pageable, ownerList.size());
        } else {
            chargePointPage = chargePointRepository.getOverview(params, pageable);
        }

        model.addAttribute("chargePointPage", chargePointPage);
        model.addAttribute("cpList", chargePointPage.getContent()); 
        model.addAttribute(PARAMS, params); // Add query form to model for search fields repopulation
        model.addAttribute("availablePageSizes", AVAILABLE_PAGE_SIZES);
        
        // Add enum values for dropdowns in the search form
        model.addAttribute("heartbeatPeriod", ChargePointQueryForm.QueryPeriodType.values());
        model.addAttribute("ocppVersion", OcppVersion.values()); // Corrected attribute name for OCPP versions
        model.addAttribute("firmwareFiles", getAllFirmwareFiles());

        // 添加owner列表用于筛选（仅对非OPERATOR_OWNER角色显示）
        if (!permissionService.isOperatorOwner()) {
            try {
                List<WebUserRecord> ownerUsers = webUserService.getUsersByRole(UserRole.OPERATOR_OWNER);
                List<Map<String, String>> ownerList = ownerUsers.stream()
                    .map(user -> {
                        Map<String, String> ownerMap = new HashMap<>();
                        ownerMap.put("username", user.getUsername());
                        ownerMap.put("displayName", user.getUsername()); // 可以根据需要修改显示名称
                        return ownerMap;
                    })
                    .collect(Collectors.toList());
                model.addAttribute("ownerList", ownerList);
            } catch (Exception e) {
                log.warn("Failed to get owner list for filtering", e);
                model.addAttribute("ownerList", Collections.emptyList());
            }
        }

        // 添加充电站选择列表用于筛选，根据用户权限过滤
        Integer userPkForFilter = null;
        if (permissionService.isOperatorOwner()) {
            try {
                WebUserRecord currentUser = permissionService.getCurrentUser();
                userPkForFilter = currentUser.getWebUserPk();
            } catch (Exception e) {
                log.warn("Failed to get current user for charge station filtering", e);
            }
        }
        model.addAttribute("chargeStationList", chargeStationRepository.getChargeStationSelectListByUser(userPkForFilter));

        // The userRole attribute was added in a previous step, keeping it if it serves a purpose
        // If permissionService.getCurrentUser() can be null or throw an exception, add null checks
        try {
            WebUserRecord currentUser = permissionService.getCurrentUser();
            if (currentUser != null && currentUser.getUserRole() != null) {
                 model.addAttribute("userRole", currentUser.getUserRole().toString());
            } else {
                 model.addAttribute("userRole", ""); // Default or empty if no user/role
            }
        } catch (Exception e) {
            log.warn("Could not determine user role for model attribute", e);
            model.addAttribute("userRole", ""); // Default on error
        }
    }

    @RequestMapping(value = DETAILS_PATH, method = RequestMethod.GET)
    public String getDetails(@PathVariable("chargeBoxId") String chargeBoxId, Model model) {
        ChargePoint.Details cp = chargePointRepository.getDetailsByChargeBoxId(chargeBoxId);
        
        if (cp == null) {
            log.warn("ChargeBox with ID '{}' not found.", chargeBoxId);
            return "redirect:/manager/chargepoints";
        }
        
        ChargePointForm form = ChargePointDetailsMapper.mapToForm(cp);

        // 获取当前充电桩的拥有者信息
        try {
            Integer currentOwnerPk = ctx.select(USER_CHARGE_BOX.WEB_USER_PK)
                .from(USER_CHARGE_BOX)
                .where(USER_CHARGE_BOX.CHARGE_BOX_PK.eq(cp.getChargeBox().getChargeBoxPk()))
                .fetchOneInto(Integer.class);
            form.setOwnerUserPk(currentOwnerPk);
        } catch (Exception e) {
            log.warn("Failed to get current owner for charge box {}", chargeBoxId, e);
        }

        model.addAttribute("chargePointForm", form);
        model.addAttribute("cp", cp);
        model.addAttribute("registrationStatusList", getRegistrationStatusList(cp.getChargeBox()));
        // 添加充电站选择列表，根据用户权限过滤
        Integer userPkForFilter = null;
        if (permissionService.isOperatorOwner()) {
            try {
                WebUserRecord currentUser = permissionService.getCurrentUser();
                userPkForFilter = currentUser.getWebUserPk();
            } catch (Exception e) {
                log.warn("Failed to get current user for charge station filtering", e);
            }
        }
        model.addAttribute("chargeStationList", chargeStationRepository.getChargeStationSelectListByUser(userPkForFilter));

        // 添加拥有者列表（仅对非OPERATOR_OWNER角色显示）
        boolean isOperatorOwner = permissionService.isOperatorOwner();
        log.info("User role check - isOperatorOwner: {}", isOperatorOwner);

        if (!isOperatorOwner) {
            try {
                List<WebUserRecord> ownerUsers = webUserService.getUsersByRole(UserRole.OPERATOR_OWNER);
                List<Map<String, Object>> ownerList = ownerUsers.stream()
                    .map(user -> {
                        Map<String, Object> ownerMap = new HashMap<>();
                        ownerMap.put("userPk", user.getWebUserPk());
                        ownerMap.put("username", user.getUsername());
                        return ownerMap;
                    })
                    .collect(Collectors.toList());
                model.addAttribute("ownerList", ownerList);
                log.info("Added {} owners to model for charge point details", ownerList.size());
            } catch (Exception e) {
                log.warn("Failed to get owner list for charge point details", e);
                model.addAttribute("ownerList", Collections.emptyList());
            }
        } else {
            log.info("User is OPERATOR_OWNER, not showing owner selection");
        }

        addCountryCodes(model);

        return "data-man/chargepointDetails";
    }

    @PostMapping(IMPORT_PATH)
    @ResponseBody
    public ResponseEntity<Map<String, String>> importChargePoints(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return ResponseEntity.badRequest().body(Collections.singletonMap("error", "File is empty"));
        }

        try {
            String taskId = UUID.randomUUID().toString();
            importTasks.put(taskId, new ImportProgress());

            // 在异步处理之前先读取文件内容到内存中，避免临时文件被清理的问题
            byte[] fileContent = file.getBytes();
            chargePointHelperService.importChargePointsFromIni(fileContent, taskId, importTasks);

            return ResponseEntity.ok(Collections.singletonMap("taskId", taskId));
        } catch (IOException e) {
            log.error("Failed to process imported file", e);
            return ResponseEntity.status(500).body(Collections.singletonMap("error", "Failed to process file"));
        }
    }

    @GetMapping(IMPORT_PROGRESS_PATH)
    @ResponseBody
    public ResponseEntity<ImportProgress> getImportProgress(@PathVariable("taskId") String taskId) {
        ImportProgress progress = importTasks.get(taskId);
        if (progress == null) {
            return ResponseEntity.notFound().build();
        }

        if (progress.isFinished()) {
            importTasks.remove(taskId);
        }

        return ResponseEntity.ok(progress);
    }

    @RequestMapping(value = "/datatransfer/{chargeBoxId}", method = RequestMethod.GET)
    public String getDataTransfer(@PathVariable("chargeBoxId") String chargeBoxId, Model model) {
        DataTransferForm form = null;
        boolean isOnline = chargePointRepository.isOnline(chargeBoxId);

        if (isOnline) {
            try {
                log.info("Charge point '{}' is online. Attempting to read live configuration.", chargeBoxId);
                form = dataTransferService.readConfiguration(chargeBoxId)
                        .toCompletableFuture()
                        .get(60, TimeUnit.SECONDS); 
            } catch (Exception e) {
                log.warn("Failed to read live configuration from '{}'. Will proceed with an empty form.", chargeBoxId, e);
                model.addAttribute("errorMessage", "Failed to retrieve configuration: " + e.getMessage());
            }
        } else {
            log.info("Charge point '{}' is offline. Proceeding with an empty form.", chargeBoxId);
            model.addAttribute("errorMessage", "Charge point is offline. Cannot retrieve live configuration.");
        }

        if (form == null) {
            form = new DataTransferForm();
        }
        
        form.setChargeBoxId(chargeBoxId);
        form.setOnline(isOnline);

        model.addAttribute("dataTransferForm", form);
        return "data-man/datatransfer";
    }

    @RequestMapping(value = "/datatransfer/{chargeBoxId}", method = RequestMethod.POST)
    public String postDataTransfer(@PathVariable("chargeBoxId") String chargeBoxId,
                                   @ModelAttribute("dataTransferForm") DataTransferForm form,
                                   RedirectAttributes redirectAttributes) {

        boolean isOnline = chargePointRepository.isOnline(chargeBoxId);
        if (!isOnline) {
            redirectAttributes.addFlashAttribute("errorMessage", "Charge point is offline. Cannot send configuration.");
            return "redirect:/manager/chargepoints/datatransfer/" + chargeBoxId;
        }

        try {
            // Ensure the chargeBoxId from the path is set in the form
            form.setChargeBoxId(chargeBoxId);

            boolean success = dataTransferService.writeConfiguration(form)
                    .toCompletableFuture()
                    .get(60, TimeUnit.SECONDS);

            if (success) {
                redirectAttributes.addFlashAttribute("successMessage", "Successfully sent configuration to charge point.");
            } else {
                redirectAttributes.addFlashAttribute("errorMessage", "Charge point rejected the configuration.");
            }
        } catch (Exception e) {
            log.error("Failed to send configuration to {}", chargeBoxId, e);
            redirectAttributes.addFlashAttribute("errorMessage", "Failed to send configuration: " + e.getMessage());
        }

        return "redirect:/manager/chargepoints/datatransfer/" + chargeBoxId;
    }

    private List<String> getRegistrationStatusList(ChargeBoxRecord chargeBoxRecord) {
        // Per user feedback, only OCPP 1.6 is currently in use.
        // This simplification avoids potential NullPointerExceptions for new charge points
        // and aligns with the project's current operational reality.
        return ocpp16RegistrationStatusList;
    }

    @RequestMapping(value = ADD_PATH, method = RequestMethod.GET)
    public String addGet(Model model) {
        model.addAttribute("chargePointForm", new ChargePointForm());
        setCommonAttributesForSingleAdd(model);
        return "data-man/chargepointAdd";
    }

    @RequestMapping(params = "add", value = ADD_SINGLE_PATH, method = RequestMethod.POST)
    public String addSinglePost(@Valid @ModelAttribute("chargePointForm") ChargePointForm chargePointForm,
                                BindingResult result, Model model) {
        if (result.hasErrors()) {
            setCommonAttributesForSingleAdd(model);
            return "data-man/chargepointAdd";
        }

        try {
            add(chargePointForm);
        } catch (SteveException e) {
            if (e.getCause() instanceof IntegrityConstraintViolationException) {
                model.addAttribute("errorMessage", "A charge point with this ID already exists.");
                setCommonAttributesForSingleAdd(model);
                return "data-man/chargepointAdd";
            } else {
                // Or some other generic error handling
                throw e;
            }
        }
        return toOverview();
    }

    @RequestMapping(value = ADD_BATCH_PATH, method = RequestMethod.POST)
    public String addBatchPost(@Valid @ModelAttribute("batchChargePointForm") ChargePointBatchInsertForm form,
                               BindingResult result, Model model) {
        if (result.hasErrors()) {
            addCountryCodes(model);
            model.addAttribute("chargePointForm", new ChargePointForm());
            return "data-man/chargepointAdd";
        }

        add(form.getIdList());
        return toOverview();
    }

    @RequestMapping(params = "update", value = UPDATE_PATH, method = RequestMethod.POST)
    public String update(@Valid @ModelAttribute("chargePointForm") ChargePointForm chargePointForm,
                         BindingResult result, Model model) {
        if (result.hasErrors()) {
            // 添加充电站列表
            Integer userPkForFilter = null;
            if (permissionService.isOperatorOwner()) {
                try {
                    WebUserRecord currentUser = permissionService.getCurrentUser();
                    userPkForFilter = currentUser.getWebUserPk();
                } catch (Exception e) {
                    log.warn("Failed to get current user for charge station filtering", e);
                }
            }
            model.addAttribute("chargeStationList", chargeStationRepository.getChargeStationSelectListByUser(userPkForFilter));

            // 添加拥有者列表（仅对非OPERATOR_OWNER角色显示）
            if (!permissionService.isOperatorOwner()) {
                try {
                    List<WebUserRecord> ownerUsers = webUserService.getUsersByRole(UserRole.OPERATOR_OWNER);
                    List<Map<String, Object>> ownerList = ownerUsers.stream()
                        .map(user -> {
                            Map<String, Object> ownerMap = new HashMap<>();
                            ownerMap.put("userPk", user.getWebUserPk());
                            ownerMap.put("username", user.getUsername());
                            return ownerMap;
                        })
                        .collect(Collectors.toList());
                    model.addAttribute("ownerList", ownerList);
                } catch (Exception e) {
                    log.warn("Failed to get owner list for charge point update", e);
                    model.addAttribute("ownerList", Collections.emptyList());
                }
            }

            addCountryCodes(model);
            return "data-man/chargepointDetails";
        }

        chargePointRepository.updateChargePoint(chargePointForm);
        return toOverview();
    }

    // 临时调试端点 - 检查用户充电桩关联
    @GetMapping(value = "/debug-assignments")
    @ResponseBody
    public ResponseEntity<?> debugAssignments() {
        try {
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            String currentUser = auth != null ? auth.getName() : "unknown";

            WebUserRecord userRecord = null;
            try {
                userRecord = permissionService.getCurrentUser();
            } catch (Exception e) {
                return ResponseEntity.ok("Error getting current user: " + e.getMessage());
            }

            if (userRecord == null) {
                return ResponseEntity.ok("Current user record is null");
            }

            // 获取用户的所有充电桩分配
            List<Integer> assignments = userChargeBoxRepository.getChargeBoxPksByUser(userRecord.getWebUserPk());

            String debugInfo = String.format(
                "User Assignment Debug:\n" +
                "Current User: %s\n" +
                "User Record: pk=%d, username=%s, role=%s\n" +
                "Assigned Charge Box PKs: %s\n" +
                "Total Assignments: %d",
                currentUser,
                userRecord.getWebUserPk(), userRecord.getUsername(), userRecord.getUserRole(),
                assignments.toString(),
                assignments.size()
            );

            return ResponseEntity.ok(debugInfo);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                 .body("Debug error: " + e.getMessage());
        }
    }

    // 临时调试端点
    @GetMapping(value = "/debug/{chargeBoxPk}")
    @ResponseBody
    public ResponseEntity<?> debugChargeBox(@PathVariable("chargeBoxPk") int chargeBoxPk) {
        try {
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            String currentUser = auth != null ? auth.getName() : "unknown";

            // 获取用户信息
            WebUserRecord userRecord = null;
            try {
                userRecord = permissionService.getCurrentUser();
            } catch (Exception e) {
                // 忽略异常，继续调试
            }

            boolean isAdmin = permissionService.isAdmin();
            boolean isOperatorFactory = permissionService.isOperatorFactory();
            boolean isOperatorOwner = permissionService.isOperatorOwner();
            boolean canView = permissionService.canViewChargeBoxByPk(chargeBoxPk);
            boolean canDelete = permissionService.canDeleteChargeBox(chargeBoxPk);

            // 检查用户充电桩关联
            String assignments = "N/A";
            if (userRecord != null) {
                try {
                    var userAssignments = userChargeBoxRepository.getChargeBoxPksByUser(userRecord.getWebUserPk());
                    assignments = userAssignments.toString();
                } catch (Exception e) {
                    assignments = "Error: " + e.getMessage();
                }
            }

            String debugInfo = String.format(
                "Debug Info for Charge Box PK %d:\n" +
                "Current User: %s\n" +
                "User Record: %s\n" +
                "Is Admin: %s\n" +
                "Is Operator Factory: %s\n" +
                "Is Operator Owner: %s\n" +
                "Can View: %s\n" +
                "Can Delete: %s\n" +
                "User Assignments: %s\n" +
                "Auth: %s",
                chargeBoxPk, currentUser,
                userRecord != null ? String.format("pk=%d, username=%s, role=%s",
                    userRecord.getWebUserPk(), userRecord.getUsername(), userRecord.getUserRole()) : "null",
                isAdmin, isOperatorFactory, isOperatorOwner, canView, canDelete, assignments,
                auth != null ? auth.toString() : "null"
            );

            return ResponseEntity.ok(debugInfo);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                 .body("Debug error: " + e.getMessage() + "\nStack: " + java.util.Arrays.toString(e.getStackTrace()));
        }
    }

    @DeleteMapping(value = DELETE_PATH)
    @ResponseBody
    public ResponseEntity<?> delete(@PathVariable("chargeBoxPk") int chargeBoxPk) {
        log.info("=== Starting delete operation for charge point with pk: {} ===", chargeBoxPk);

        try {
            // 获取当前用户信息用于调试
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            String currentUser = auth != null ? auth.getName() : "unknown";
            log.info("Current user attempting deletion: {}", currentUser);

            // 检查删除权限
            boolean canDelete;
            try {
                log.info("Checking delete permission for charge box pk: {}", chargeBoxPk);
                canDelete = permissionService.canDeleteChargeBox(chargeBoxPk);
                log.info("Permission check result for charge box pk {}: {}", chargeBoxPk, canDelete);
            } catch (Exception e) {
                log.error("Error checking delete permission for charge box with pk {}", chargeBoxPk, e);
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                     .body("Error checking permissions: " + e.getMessage());
            }

            if (!canDelete) {
                log.warn("User {} does not have permission to delete charge box with pk: {}", currentUser, chargeBoxPk);
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                                     .body("You do not have permission to delete this charge point.");
            }

            // 执行删除操作
            try {
                log.info("Starting deletion process for charge box pk: {}", chargeBoxPk);
                chargePointRepository.deleteChargePoint(chargeBoxPk);
                log.info("Successfully deleted charge point with pk: {}", chargeBoxPk);
                return ResponseEntity.ok().build();
            } catch (SteveException e) {
                log.error("SteveException while deleting charge box with pk {}: {}", chargeBoxPk, e.getMessage(), e);
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                     .body("Deletion failed: " + e.getMessage());
            } catch (org.jooq.exception.DataAccessException e) {
                log.error("Database error while deleting charge box with pk {}", chargeBoxPk, e);
                if (e.getCause() instanceof java.sql.SQLIntegrityConstraintViolationException) {
                    return ResponseEntity.status(HttpStatus.CONFLICT)
                                         .body("Cannot delete charge point. It has associated records that must be removed first.");
                } else {
                    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                         .body("Database error: " + e.getMessage());
                }
            }

        } catch (IntegrityConstraintViolationException e) {
            log.warn("Could not delete charge box with pk {} due to integrity constraint", chargeBoxPk, e);
            return ResponseEntity.status(HttpStatus.CONFLICT)
                                 .body("Cannot delete charge point. It might have associated transaction records or other dependencies.");
        } catch (Exception e) {
            log.error("Unexpected error while deleting charge box with pk {}", chargeBoxPk, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                 .body("An unexpected error occurred while deleting the charge point: " + e.getMessage());
        }
    }

    @RequestMapping(value = UNKNOWN_ADD_PATH, method = RequestMethod.POST)
    public String addUnknownChargeBoxId(@PathVariable("chargeBoxId") String chargeBoxId) {
        add(Collections.singletonList(chargeBoxId));
        return toOverview();
    }

    @RequestMapping(value = UNKNOWN_REMOVE_PATH, method = RequestMethod.POST)
    public String removeUnknownChargeBoxId(@PathVariable("chargeBoxId") String chargeBoxId) {
        chargePointHelperService.removeUnknown(Collections.singletonList(chargeBoxId));
        return toOverview();
    }

    protected void addCountryCodes(Model model) {
        model.addAttribute("countryCodes", ControllerHelper.COUNTRY_DROPDOWN);
    }

    // -------------------------------------------------------------------------
    // Back to Overview
    // -------------------------------------------------------------------------

    @RequestMapping(params = "backToOverview", value = ADD_SINGLE_PATH, method = RequestMethod.POST)
    public String addBackToOverview() {
        return toOverview();
    }

    @RequestMapping(params = "backToOverview", value = UPDATE_PATH, method = RequestMethod.POST)
    public String updateBackToOverview() {
        return toOverview();
    }

    protected String toOverview() {
        return "redirect:/manager/chargepoints";
    }

    // -------------------------------------------------------------------------
    // Helpers
    // -------------------------------------------------------------------------

    private void setCommonAttributesForSingleAdd(Model model) {
        addCountryCodes(model);
        model.addAttribute("batchChargePointForm", new ChargePointBatchInsertForm());
        // we don't know the protocol yet. but, a list with only "accepted" and "rejected" is a good starting point.
        model.addAttribute("registrationStatusList", upToOcpp15RegistrationStatusList);

        // 优化：一次性获取用户信息和充电站列表，避免重复查询
        Integer userPkForFilter = null;
        if (permissionService.isOperatorOwner()) {
            try {
                WebUserRecord currentUser = permissionService.getCurrentUser();
                userPkForFilter = currentUser.getWebUserPk();
                log.debug("Setting charge station filter for user: {} (pk: {})",
                         currentUser.getUsername(), userPkForFilter);
            } catch (Exception e) {
                log.warn("Failed to get current user for charge station filtering", e);
            }
        }

        // 获取过滤后的充电站列表
        List<ChargeStationSelect> chargeStationList = chargeStationRepository.getChargeStationSelectListByUser(userPkForFilter);
        model.addAttribute("chargeStationList", chargeStationList);
        log.debug("Added {} charge stations to model for user filter: {}",
                 chargeStationList.size(), userPkForFilter);

        // 添加拥有者列表（仅对非OPERATOR_OWNER角色显示）
        if (!permissionService.isOperatorOwner()) {
            try {
                List<WebUserRecord> ownerUsers = webUserService.getUsersByRole(UserRole.OPERATOR_OWNER);
                List<Map<String, Object>> ownerList = ownerUsers.stream()
                    .map(user -> {
                        Map<String, Object> ownerMap = new HashMap<>();
                        ownerMap.put("userPk", user.getWebUserPk());
                        ownerMap.put("username", user.getUsername());
                        return ownerMap;
                    })
                    .collect(Collectors.toList());
                model.addAttribute("ownerList", ownerList);
                log.debug("Added {} owners to model for charge point creation", ownerList.size());
            } catch (Exception e) {
                log.warn("Failed to get owner list for charge point creation", e);
                model.addAttribute("ownerList", Collections.emptyList());
            }
        }
    }

    private void add(ChargePointForm form) {
        int chargeBoxPk = chargePointHelperService.addChargePoint(form);
        chargePointHelperService.removeUnknown(Collections.singletonList(form.getChargeBoxId()));

        // 处理拥有者分配
        if (permissionService.isOperatorOwner()) {
            // 如果当前用户是Charge Box Owner，自动分配充电桩给创建者
            try {
                WebUserRecord currentUser = permissionService.getCurrentUser();
                if (currentUser != null) {
                    userChargeBoxRepository.assignChargeBoxToUser(currentUser.getWebUserPk(), chargeBoxPk);
                    log.info("Auto-assigned charge box {} to user {} ({})",
                            form.getChargeBoxId(), currentUser.getUsername(), currentUser.getWebUserPk());
                }
            } catch (Exception e) {
                log.error("Failed to auto-assign charge box {} to current user: {}",
                         form.getChargeBoxId(), e.getMessage(), e);
                // 不抛出异常，因为充电桩已经创建成功，只是自动分配失败
            }
        } else if (form.getOwnerUserPk() != null) {
            // 如果是管理员或工厂运营商，且选择了拥有者，则分配给指定的拥有者
            try {
                userChargeBoxRepository.assignChargeBoxToUser(form.getOwnerUserPk(), chargeBoxPk);
                log.info("Assigned charge box {} to selected owner (userPk: {})",
                        form.getChargeBoxId(), form.getOwnerUserPk());
            } catch (Exception e) {
                log.error("Failed to assign charge box {} to selected owner (userPk: {}): {}",
                         form.getChargeBoxId(), form.getOwnerUserPk(), e.getMessage(), e);
                // 不抛出异常，因为充电桩已经创建成功，只是分配失败
            }
        }
    }

    private void add(List<String> idList) {
        chargePointHelperService.addChargePoints(idList);
        chargePointHelperService.removeUnknown(idList);
    }

    @PostMapping(value = BATCH_UPDATE_PATH)
    public String batchUpdateFirmware(@RequestParam(value = "chargePointIds", required = false) List<String> chargePointIds,
                                      @RequestParam(value = "firmwareFile", required = false) String firmwareFile,
                                      RedirectAttributes redirectAttributes) {

        if (chargePointIds == null || chargePointIds.isEmpty() || !StringUtils.hasText(firmwareFile)) {
            redirectAttributes.addFlashAttribute("errorMessage", "No charge points or firmware file selected.");
            return "redirect:/manager/chargepoints";
        }

        log.info("Attempting to start batch firmware update. Received Charge Points: {}, Firmware: {}", chargePointIds, firmwareFile);

        try {
            String firmwareBasePath = SteveConfiguration.CONFIG.getPhysicalFirmwareUploadPath();
            Path filePath = Paths.get(firmwareBasePath, firmwareFile);
            File file = filePath.toFile();

            if (!file.exists()) {
                log.error("Firmware file not found at path: {}", filePath);
                redirectAttributes.addFlashAttribute("errorMessage", "Selected firmware file does not exist on the server.");
                return "redirect:/manager/chargepoints";
            }
            
            // For batch updates, we must ensure all selected charge points are OCPP 1.6
            // The getChargePointSelect method can filter by protocol and a list of IDs.
            List<ChargePointSelect> ocpp16ChargePoints = chargePointRepository.getChargePointSelect(OcppProtocol.V_16_JSON, null, chargePointIds);

            if (ocpp16ChargePoints.isEmpty()) {
                redirectAttributes.addFlashAttribute("errorMessage", "None of the selected charge points support OCPP 1.6 firmware updates.");
                return "redirect:/manager/chargepoints";
            }
            
            String warningInfo = null;
            if (ocpp16ChargePoints.size() < chargePointIds.size()) {
                int excludedCount = chargePointIds.size() - ocpp16ChargePoints.size();
                warningInfo = String.format("%d station(s) were excluded because they are not compatible with this update method (requires OCPP 1.6 JSON).", excludedCount);
            }

            UpdateFirmwareParams params = new UpdateFirmwareParams();
            params.setChargePointSelectList(ocpp16ChargePoints);
            params.setRetrieve(new LocalDateTime()); // Set retrieve time to now

            ResponseEntity<Map<String, Object>> response = ocppAjaxController.processFirmwareUpdate(params, file);

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, Object> body = response.getBody();
                Boolean success = (Boolean) body.getOrDefault("success", false);
                if (success) {
                    String successMessage = (String) body.get("message");
                    // Combine success message with warning info if it exists
                    if (warningInfo != null) {
                        successMessage = successMessage + " " + warningInfo;
                    }
                    redirectAttributes.addFlashAttribute("successMessage", successMessage);
                } else {
                    redirectAttributes.addFlashAttribute("errorMessage", (String) body.get("errorMessage"));
                }
            } else {
                redirectAttributes.addFlashAttribute("errorMessage", "Failed to initiate firmware update. Server returned status: " + response.getStatusCode());
            }

        } catch (Exception e) {
            log.error("An unexpected error occurred during batch firmware update.", e);
            redirectAttributes.addFlashAttribute("errorMessage", "An unexpected error occurred: " + e.getMessage());
        }

        return "redirect:/manager/chargepoints";
    }

    @PreAuthorize("hasAnyAuthority('ADMIN', 'OPERATOR_FACTORY', 'OPERATOR_OWNER')")
    @RequestMapping(value = "/online-ids", method = RequestMethod.GET)
    @ResponseBody
    public List<String> getOnlineChargePointIds() {
        return chargePointRepository.getOnlineChargeBoxIds();
    }

    /**
     * 删除固件文件 - 仅限管理员和工厂运营商
     */
    @PreAuthorize("hasAnyAuthority('ADMIN', 'OPERATOR_FACTORY')")
    @RequestMapping(value = "/firmware/delete", method = RequestMethod.POST)
    @ResponseBody
    public ResponseEntity<Map<String, Object>> deleteFirmwareFile(@RequestParam String filename) {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("Attempting to delete firmware file: {} by user: {}", filename,
                    permissionService.getCurrentUser().getUsername());

            boolean deleted = firmwareFileService.deleteFirmwareFile(filename);

            if (deleted) {
                result.put("success", true);
                result.put("message", "Firmware file deleted successfully");
                log.info("Successfully deleted firmware file: {}", filename);
            } else {
                result.put("success", false);
                result.put("errorMessage", "Firmware file not found or deletion failed");
                log.warn("Failed to delete firmware file (not found): {}", filename);
            }

        } catch (Exception e) {
            log.error("Error deleting firmware file: " + filename, e);
            result.put("success", false);
            result.put("errorMessage", "Error occurred while deleting firmware file: " + e.getMessage());
        }

        return ResponseEntity.ok(result);
    }

    /**
     * Get firmware files with their status for deletion modal
     */
    @GetMapping("/firmware-files")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getFirmwareFilesForDeletion() {
        try {
            List<FirmwareFile> firmwareFiles = firmwareFileService.getAllFirmwareFiles();

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("files", firmwareFiles);

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Failed to get firmware files for deletion", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("errorMessage", "Failed to get firmware files: " + e.getMessage());
            return ResponseEntity.status(500).body(result);
        }
    }

    /**
     * Batch delete firmware files
     */
    @PostMapping("/firmware-files/delete")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> batchDeleteFirmwareFiles(@RequestParam("filenames") List<String> filenames) {
        Map<String, Object> result = new HashMap<>();

        if (filenames == null || filenames.isEmpty()) {
            result.put("success", false);
            result.put("errorMessage", "No files selected for deletion");
            return ResponseEntity.badRequest().body(result);
        }

        try {
            int deletedCount = 0;
            List<String> failedFiles = new ArrayList<>();

            for (String filename : filenames) {
                try {
                    boolean deleted = firmwareFileService.deleteFirmwareFile(filename);
                    if (deleted) {
                        deletedCount++;
                        log.info("Successfully deleted firmware file: {}", filename);
                    } else {
                        failedFiles.add(filename);
                        log.warn("Failed to delete firmware file (not found): {}", filename);
                    }
                } catch (Exception e) {
                    failedFiles.add(filename);
                    log.error("Error deleting firmware file: " + filename, e);
                }
            }

            result.put("success", true);
            result.put("deletedCount", deletedCount);
            result.put("totalCount", filenames.size());

            if (!failedFiles.isEmpty()) {
                result.put("failedFiles", failedFiles);
                result.put("message", String.format("Deleted %d of %d files. Failed: %s",
                        deletedCount, filenames.size(), String.join(", ", failedFiles)));
            } else {
                result.put("message", String.format("Successfully deleted %d files", deletedCount));
            }

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Error during batch firmware file deletion", e);
            result.put("success", false);
            result.put("errorMessage", "Batch deletion failed: " + e.getMessage());
            return ResponseEntity.status(500).body(result);
        }
    }

    private List<String> getAllFirmwareFiles() {
        try {
            // Get only successfully installed firmware files for dropdown
            return firmwareFileService.getSuccessfulFirmwareFilenames();
        } catch (Exception e) {
            log.error("Failed to get successful firmware files from database", e);
            return Collections.emptyList();
        }
    }

    private String getInternalIpAddress() throws SocketException {
        Enumeration<NetworkInterface> nics = NetworkInterface.getNetworkInterfaces();
        while (nics.hasMoreElements()) {
            NetworkInterface nic = nics.nextElement();
            if (nic.isUp() && !nic.isLoopback() && !nic.isVirtual()) {
                Enumeration<InetAddress> addrs = nic.getInetAddresses();
                while (addrs.hasMoreElements()) {
                    InetAddress addr = addrs.nextElement();
                    if (addr instanceof java.net.Inet4Address && addr.isSiteLocalAddress()) {
                        return addr.getHostAddress();
                    }
                }
            }
        }
        return null;
    }

    private Integer createUpdateFirmwareBatch(int totalCount) {
        try {
            Record record = ctx.insertInto(table("update_firmware_batch"))
                    .set(field("total_count"), totalCount)
                    .set(field("pending_count"), totalCount)
                    .set(field("success_count"), 0)
                    .set(field("error_count"), 0)
                    .set(field("status"), "INPROGRESS")
                    .set(field("created_by"), "system")
                    .returning(field("batch_id", Integer.class))
                    .fetchOne();

            if (record == null) {
                return null;
            }

            return record.getValue(field("batch_id", Integer.class));
        } catch (Exception e) {
            log.warn("Could not create batch record due to: {}. Will continue without batch tracking", e.getMessage());
            return null;
        }
    }

    private void recordFirmwareUpdateLog(Integer batchId, UpdateFirmwareParams params,
                                        String chargeBoxId, String status, String response) {
        try {
            DateTime retrieveDateTime = DateTimeUtils.toDateTime(params.getRetrieve());

            if (batchId == null) {
                updateFirmwareLogRepository.insert(params.getLocation(), params.getRetries(), params.getRetryInterval(),
                        retrieveDateTime, chargeBoxId, status, response);
                return;
            }

            ctx.insertInto(table("update_firmware_log"))
               .set(field("location"), params.getLocation())
               .set(field("retries"), params.getRetries())
               .set(field("retry_interval"), params.getRetryInterval())
               .set(field("retrieve_datetime"), retrieveDateTime)
               .set(field("charge_box_id"), chargeBoxId)
               .set(field("status"), status)
               .set(field("response"), response)
               .set(field("batch_id"), batchId)
               .execute();
        } catch (Exception e) {
            log.error("Error recording firmware update log for chargeBoxId {}: {}", chargeBoxId, e.getMessage(), e);
        }
    }

    private void updateBatchStatus(Integer batchId, int successCount, int errorCount, int pendingCount) {
        if (batchId == null) {
            return;
        }

        try {
            String status = (pendingCount <= 0) ? (errorCount == 0 ? "COMPLETED" : "COMPLETED_WITH_ERRORS") : "INPROGRESS";

            ctx.update(table("update_firmware_batch"))
               .set(field("success_count"), successCount)
               .set(field("error_count"), errorCount)
               .set(field("pending_count"), Math.max(0, pendingCount))
               .set(field("status"), status)
               .where(field("batch_id").eq(batchId))
               .execute();
        } catch (Exception e) {
            log.error("Error updating batch status for batch ID {}: {}", batchId, e.getMessage(), e);
        }
    }

    // Inner class for progress tracking
    public static class ImportProgress {
        private int total;
        private final AtomicInteger processed = new AtomicInteger(0);
        private final AtomicInteger added = new AtomicInteger(0);
        private final AtomicInteger existed = new AtomicInteger(0);
        private boolean finished = false;

        // Getters and setters for JSON serialization
        public int getTotal() { return total; }
        public void setTotal(int total) { this.total = total; }
        public int getProcessed() { return processed.get(); }
        public void incrementProcessed() { this.processed.incrementAndGet(); }
        public int getAdded() { return added.get(); }
        public void incrementAdded() { this.added.incrementAndGet(); }
        public int getExisted() { return existed.get(); }
        public void incrementExisted() { this.existed.incrementAndGet(); }
        public boolean isFinished() { return finished; }
        public void setFinished(boolean finished) { this.finished = finished; }
        
        public void addAndGetAdded(int delta) {
            this.added.addAndGet(delta);
        }
    }

    // A mock implementation of MultipartFile to wrap the file from the server path
    static class MockMultipartFile implements MultipartFile {
        private final String name;
        private final String originalFilename;
        private final String contentType;
        private final byte[] content;

        public MockMultipartFile(String name, String originalFilename, String contentType, byte[] content) {
            this.name = name;
            this.originalFilename = originalFilename;
            this.contentType = contentType;
            this.content = content;
        }

        @Override
        public String getName() {
            return name;
        }

        @Override
        public String getOriginalFilename() {
            return originalFilename;
        }

        @Override
        public String getContentType() {
            return contentType;
        }

        @Override
        public boolean isEmpty() {
            return content.length == 0;
        }

        @Override
        public long getSize() {
            return content.length;
        }

        @Override
        public byte[] getBytes() throws IOException {
            return content;
        }

        @Override
        public InputStream getInputStream() throws IOException {
            return new ByteArrayInputStream(content);
        }

        @Override
        public void transferTo(File dest) throws IOException, IllegalStateException {
            // Not needed for this use case
        }
    }

    /**
     * Create test firmware file data - for testing purposes only
     */
    @PostMapping("/firmware-files/create-test-data")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> createTestFirmwareData() {
        Map<String, Object> result = new HashMap<>();

        try {
            firmwareFileService.createTestData();
            result.put("success", true);
            result.put("message", "Test firmware file data created successfully");
            log.info("Test firmware file data created via web endpoint");
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Failed to create test firmware file data", e);
            result.put("success", false);
            result.put("errorMessage", "Failed to create test data: " + e.getMessage());
            return ResponseEntity.status(500).body(result);
        }
    }

}
