/*
 * SteV<PERSON> - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import de.rwth.idsg.steve.SteveConfiguration;
import de.rwth.idsg.steve.repository.UserChargeBoxRepository;
import de.rwth.idsg.steve.repository.WebUserRepository;
import de.rwth.idsg.steve.web.dto.CustomUserDetails;
import de.rwth.idsg.steve.web.dto.UserForm;
import de.rwth.idsg.steve.web.dto.UserRole;
import jooq.steve.db.tables.records.WebUserRecord;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jooq.JSON;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolderStrategy;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.provisioning.JdbcUserDetailsManager;
import org.springframework.security.provisioning.UserDetailsManager;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.Collection;
import java.util.Collections;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static org.springframework.security.authentication.UsernamePasswordAuthenticationToken.authenticated;
import static org.springframework.security.core.context.SecurityContextHolder.getContextHolderStrategy;

/**
 * Inspired by {@link org.springframework.security.provisioning.JdbcUserDetailsManager}
 *
 * <AUTHOR> Goekay <<EMAIL>>
 * @since 15.08.2024
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class WebUserService implements UserDetailsService {

    // Because Guava's cache does not accept a null value
    private static final UserDetails DUMMY_USER = new User("#", "#", Collections.emptyList());

    private final ObjectMapper jacksonObjectMapper;
    private final WebUserRepository webUserRepository;
    private final UserChargeBoxRepository userChargeBoxRepository;
    private final SecurityContextHolderStrategy securityContextHolderStrategy = getContextHolderStrategy();
    private final PasswordEncoder passwordEncoder;

    private final Cache<String, UserDetails> userCache = CacheBuilder.newBuilder()
        .expireAfterWrite(10, TimeUnit.MINUTES) // TTL
        .maximumSize(100)
        .build();

    @EventListener
    public void afterStart(ContextRefreshedEvent event) {
        if (this.hasUserWithRole(UserRole.ADMIN)) {
            // 运行密码修复程序
            fixNonEncodedPasswords();
            return;
        }

        var headerVal = SteveConfiguration.CONFIG.getWebApi().getHeaderValue();

        var encodedApiPassword = StringUtils.isEmpty(headerVal)
            ? null
            : SteveConfiguration.CONFIG.getAuth().getPasswordEncoder().encode(headerVal);

        var user = new WebUserRecord()
            .setUsername(SteveConfiguration.CONFIG.getAuth().getUserName())
            .setPassword(SteveConfiguration.CONFIG.getAuth().getEncodedPassword())
            .setApiPassword(encodedApiPassword)
            .setEnabled(true)
            .setUserRole(convertUserRole(UserRole.ADMIN));

        webUserRepository.createUser(user);
    }

    /**
     * 检查并修复数据库中的明文密码
     * 明文密码会被识别并重新使用BCrypt加密
     */
    private void fixNonEncodedPasswords() {
        log.info("Checking and fixing non-encrypted passwords...");
        List<WebUserRecord> allUsers = webUserRepository.getAllUsers();
        int fixed = 0;
        
        for (WebUserRecord user : allUsers) {
            String password = user.getPassword();
            // 如果密码不是以"$2a$"开头，则可能是明文密码
            if (password != null && !password.startsWith("$2a$")) {
                log.warn("Found that the password of user {} is not encrypted, and is fixing it...", user.getUsername());
                // 使用当前明文密码创建加密密码
                String encodedPassword = passwordEncoder.encode(password);
                // 更新用户密码
                webUserRepository.updateUserPassword(user.getUsername(), encodedPassword);
                fixed++;
            }
        }
        
        if (fixed > 0) {
            log.info("Successfully fixed the passwords of {} users.", fixed);
        } else {
            log.info("No passwords that require fixing were found.");
        }
    }

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        WebUserRecord record = webUserRepository.loadUserByUsername(username);

        if (record == null) {
            throw new UsernameNotFoundException(username);
        }

        // 从用户角色创建权限
        Collection<GrantedAuthority> authorities = createAuthoritiesFromRole(record.getUserRole());

        return new CustomUserDetails(record, authorities);
    }

    /**
     * 从用户角色创建对应的Spring Security权限集合
     * 
     * @param userRole 用户角色枚举值
     * @return 对应的权限集合
     */
    private Collection<GrantedAuthority> createAuthoritiesFromRole(jooq.steve.db.enums.WebUserUserRole userRole) {
        if (userRole == null) {
            return Collections.emptyList();
        }
        
        // 将枚举值转换回我们的UserRole
        UserRole role = UserRole.valueOf(userRole.name());
        
        // 根据角色创建相应的权限
        switch (role) {
            case ADMIN:
                return AuthorityUtils.createAuthorityList("ADMIN");
            case OPERATOR_FACTORY:
                return AuthorityUtils.createAuthorityList("OPERATOR_FACTORY");
            case OPERATOR_OWNER:
                return AuthorityUtils.createAuthorityList("OPERATOR_OWNER");
            default:
                return Collections.emptyList();
        }
    }

    public UserDetails loadUserByUsernameForApi(String username) {
        try {
            UserDetails userExt = userCache.get(username, () -> {
                UserDetails user = this.loadUserByUsernameForApiInternal(username);
                // map null to dummy
                return (user == null) ? DUMMY_USER : user;
            });
            // map dummy back to null
            return (userExt == DUMMY_USER) ? null : userExt;
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }
    }

    public void changeStatusOfUser(String username, boolean enabled) {
        webUserRepository.changeStatusOfUser(username, enabled);
    }

    public boolean hasUserWithRole(UserRole role) {
        Integer count = webUserRepository.getUserCountWithRole(role);
        return count != null && count > 0;
    }

    /**
     * Get the role of a user
     *
     * @param username Username
     * @return User role
     */
    public UserRole getUserRole(String username) {
        return webUserRepository.getUserRole(username);
    }

    public void updateUser(UserDetails user, UserRole role) {
        validateUserDetails(user);
        var record = toWebUserRecord(user);
        record.setUserRole(convertUserRole(role));
        webUserRepository.updateUser(record);
    }

    public void deleteUser(String username) {
        WebUserRecord user = webUserRepository.loadUserByUsername(username);
        if (user != null) {
            // Delete all charge box assignments for this user
            userChargeBoxRepository.removeAllChargeBoxesFromUser(user.getWebUserPk());
        }
        webUserRepository.deleteUser(username);
    }

    public void deleteUser(int webUserPk) {
        // Delete all charge box assignments for this user
        userChargeBoxRepository.removeAllChargeBoxesFromUser(webUserPk);
        webUserRepository.deleteUser(webUserPk);
    }

    /**
     * Not only just an SQL Update.
     * The flow is inspired by {@link JdbcUserDetailsManager#changePassword(String, String)}
     */
    public void changePassword(String oldPassword, String newPassword) {
        Authentication currentUser = this.securityContextHolderStrategy.getContext().getAuthentication();
        if (currentUser == null) {
            // This would indicate bad coding somewhere
            throw new AccessDeniedException(
                "Can't change password as no Authentication object found in context for current user."
            );
        }

        String username = currentUser.getName();
        webUserRepository.changePassword(username, newPassword);

        Authentication authentication = createNewAuthentication(currentUser, newPassword);
        SecurityContext context = this.securityContextHolderStrategy.createEmptyContext();
        context.setAuthentication(authentication);
        this.securityContextHolderStrategy.setContext(context);
    }

    public boolean userExists(String username) {
        return webUserRepository.userExists(username);
    }

    public void add(UserForm form) {
        String userName = form.getUsername();
        String encodedPassword = passwordEncoder.encode(form.getPassword());
        webUserRepository.addUser(userName, encodedPassword, form.getUserRole());
    }

    public void update(UserForm form) {
        String userName = form.getUsername();
        UserRole role = form.getUserRole();
        webUserRepository.updateUserRole(userName, role);
    }

    public void updatePassword(UserForm form) {
        String userName = form.getUsername();
        String encodedPassword = passwordEncoder.encode(form.getPassword());
        webUserRepository.updateUserPassword(userName, encodedPassword);
    }

    /**
     * 检查给定用户名是否有管理员权限
     *
     * @param username 用户名
     * @return 如果是管理员返回true，否则返回false
     */
    public boolean isAdmin(String username) {
        UserRole role = webUserRepository.getUserRole(username);
        return role == UserRole.ADMIN;
    }

    /**
     * 检查给定用户名是否有工厂运营商权限
     *
     * @param username 用户名
     * @return 如果是工厂运营商返回true，否则返回false
     */
    public boolean isOperatorFactory(String username) {
        UserRole role = webUserRepository.getUserRole(username);
        return role == UserRole.OPERATOR_FACTORY;
    }

    /**
     * 检查给定用户名是否有充电桩所有者权限
     *
     * @param username 用户名
     * @return 如果是充电桩所有者返回true，否则返回false
     */
    public boolean isOperatorOwner(String username) {
        UserRole role = webUserRepository.getUserRole(username);
        return role == UserRole.OPERATOR_OWNER;
    }

    /**
     * Update the role of a user by id
     *
     * @param webUserPk User primary key
     * @param role New role
     */
    public void updateUserRole(int webUserPk, UserRole role) {
        webUserRepository.updateUserRole(webUserPk, role);
    }

    /**
     * Get all users with a specific role
     *
     * @param role Role to filter by
     * @return List of users with the specified role
     */
    public List<WebUserRecord> getUsersByRole(UserRole role) {
        return webUserRepository.getUsersByRole(role);
    }

    /**
     * Assign a charge box to a user
     *
     * @param webUserPk User primary key
     * @param chargeBoxPk Charge box primary key
     */
    public void assignChargeBoxToUser(int webUserPk, int chargeBoxPk) {
        userChargeBoxRepository.assignChargeBoxToUser(webUserPk, chargeBoxPk);
    }

    /**
     * Remove a charge box assignment from a user
     *
     * @param webUserPk User primary key
     * @param chargeBoxPk Charge box primary key
     */
    public void removeChargeBoxFromUser(int webUserPk, int chargeBoxPk) {
        userChargeBoxRepository.removeChargeBoxFromUser(webUserPk, chargeBoxPk);
    }

    /**
     * Get all charge box IDs assigned to a user
     *
     * @param webUserPk User primary key
     * @return List of charge box IDs
     */
    public List<String> getChargeBoxIdsByUser(int webUserPk) {
        return userChargeBoxRepository.getChargeBoxIdsByUser(webUserPk);
    }

    /**
     * Check if a charge box is assigned to a user
     *
     * @param webUserPk User primary key
     * @param chargeBoxId Charge box ID
     * @return true if assigned, false otherwise
     */
    public boolean isChargeBoxAssignedToUser(int webUserPk, String chargeBoxId) {
        return userChargeBoxRepository.isChargeBoxAssignedToUser(webUserPk, chargeBoxId);
    }

    private UserDetails loadUserByUsernameForApiInternal(String username) {
        WebUserRecord record = webUserRepository.loadUserByUsername(username);
        if (record == null) {
            return null;
        }

        // the builder User.password(..) does not allow null values
        String apiPassword = record.getApiPassword();
        if (apiPassword == null) {
            apiPassword = "";
        }

        // 从用户角色创建权限
        Collection<GrantedAuthority> authorities = createAuthoritiesFromRole(record.getUserRole());

        return User
            .withUsername(record.getUsername())
            .password(apiPassword)
            .disabled(!record.getEnabled())
            .authorities(authorities)
            .build();
    }

    private WebUserRecord toWebUserRecord(UserDetails user) {
        WebUserRecord record = new WebUserRecord()
            .setUsername(user.getUsername())
            .setPassword(user.getPassword())
            .setEnabled(user.isEnabled());
        
        // Default to ADMIN role for now, can be overridden by caller
        record.setUserRole(convertUserRole(UserRole.ADMIN));
        
        return record;
    }

    private static void validateUserDetails(UserDetails user) {
        Assert.hasText(user.getUsername(), "Username may not be empty or null");
        validateAuthorities(user.getAuthorities());
    }

    private static void validateAuthorities(Collection<? extends GrantedAuthority> authorities) {
        Assert.notNull(authorities, "Authorities list must not be null");
        for (GrantedAuthority authority : authorities) {
            Assert.notNull(authority, "Authorities list contains a null entry");
            Assert.hasText(authority.getAuthority(), "getAuthority() method must return a non-empty string");
        }
    }

    private Authentication createNewAuthentication(Authentication currentAuth, String newPassword) {
        var user = this.loadUserByUsername(currentAuth.getName());
        var newAuthentication = authenticated(user, null, user.getAuthorities());
        newAuthentication.setDetails(currentAuth.getDetails());
        return newAuthentication;
    }

    /**
     * 将我们的UserRole枚举转换为JOOQ生成的WebUserUserRole枚举
     */
    private jooq.steve.db.enums.WebUserUserRole convertUserRole(UserRole role) {
        return jooq.steve.db.enums.WebUserUserRole.valueOf(role.name());
    }

    /**
     * Load a user by primary key
     *
     * @param webUserPk The primary key of the user
     * @return The user record or null if not found
     */
    public WebUserRecord loadUserByPk(int webUserPk) {
        return webUserRepository.loadUserByPk(webUserPk);
    }
    
    /**
     * 根据user表的user_pk查找对应的web_user记录
     * 现在使用直接的数据库关联，不再需要复杂的匹配逻辑
     *
     * @param userPk user表中的主键
     * @return 关联的WebUserRecord，如果找不到则返回null
     */
    public WebUserRecord findWebUserByUserPk(int userPk) {
        log.debug("Finding web_user for user_pk={}", userPk);
        return webUserRepository.findByUserPk(userPk);
    }

    /**
     * 根据user表的user_pk查找对应的web_user记录（兼容旧接口）
     * @deprecated 使用 {@link #findWebUserByUserPk(int)} 替代
     *
     * @param userPk user表中的主键
     * @param namePattern 用户名模式（已不再使用，保留为兼容性）
     * @return 关联的WebUserRecord，如果找不到则返回null
     */
    @Deprecated
    public WebUserRecord findWebUserByUserPk(int userPk, String namePattern) {
        log.debug("Finding web_user for user_pk={} (namePattern ignored)", userPk);
        return findWebUserByUserPk(userPk);
    }

    // ========== 新增：user_pk关联管理方法 ==========

    /**
     * 建立web_user和user的关联
     *
     * @param webUserPk web_user表的主键
     * @param userPk user表的主键
     */
    public void associateWithUser(int webUserPk, int userPk) {
        log.debug("Associating web_user_pk {} with user_pk {}", webUserPk, userPk);
        webUserRepository.associateWithUser(webUserPk, userPk);
    }

    /**
     * 解除web_user和user的关联
     *
     * @param webUserPk web_user表的主键
     */
    public void disassociateFromUser(int webUserPk) {
        log.debug("Disassociating web_user_pk {} from user", webUserPk);
        webUserRepository.disassociateFromUser(webUserPk);
    }

    /**
     * 根据user_pk解除关联（当删除user记录时使用）
     *
     * @param userPk user表的主键
     */
    public void disassociateByUserPk(int userPk) {
        log.debug("Disassociating all web_users from user_pk: {}", userPk);
        webUserRepository.disassociateByUserPk(userPk);
    }

    /**
     * 获取web_user关联的user记录的主键
     *
     * @param webUserPk web_user表的主键
     * @return 关联的user_pk，如果未关联则返回null
     */
    public Integer getAssociatedUserPk(int webUserPk) {
        return webUserRepository.getAssociatedUserPk(webUserPk);
    }

    /**
     * 获取所有已关联user的web_user记录
     *
     * @return 已关联user的WebUserRecord列表
     */
    public List<WebUserRecord> getAllUsersWithAssociation() {
        return webUserRepository.getAllUsersWithAssociation();
    }

    /**
     * 获取所有未关联user的web_user记录
     *
     * @return 未关联user的WebUserRecord列表
     */
    public List<WebUserRecord> getAllUsersWithoutAssociation() {
        return webUserRepository.getAllUsersWithoutAssociation();
    }

    /**
     * 根据用户ID、姓名模式和用户名查找可能关联的web_user记录
     * @deprecated 此方法已废弃，现在使用直接的数据库关联
     *
     * @param userPk 用户ID
     * @param namePattern 姓名模式，通常是firstName + lastName
     * @param username 可能的用户名格式
     * @return 可能关联的web_user记录列表
     */
    @Deprecated
    public List<WebUserRecord> findWebUsersByPattern(int userPk, String namePattern, String username) {
        log.warn("findWebUsersByPattern is deprecated. Use direct database association instead.");
        // 返回基于user_pk的直接查询结果
        WebUserRecord webUser = findWebUserByUserPk(userPk);
        return webUser != null ? List.of(webUser) : List.of();
    }
}