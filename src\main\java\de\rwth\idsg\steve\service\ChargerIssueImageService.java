package de.rwth.idsg.steve.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

import de.rwth.idsg.steve.repository.ChargerIssueRepository;
import lombok.extern.slf4j.Slf4j;

/**
 * Handles the upload and storage of charge point fault images
 */
@Slf4j
@Service
public class ChargerIssueImageService {
    
    @Value("${steve.issue.image.upload-path:uploads/issue-images}")
    private String uploadPath;
    
    @Autowired
    private ChargerIssueRepository chargerIssueRepository;
    
    /**
     * Initialization method, ensures that the upload directory exists
     */
    @PostConstruct
    public void init() {
        try {
            File uploadDir = new File(uploadPath);
            if (!uploadDir.exists()) {
                if (uploadDir.mkdirs()) {
                    log.info("Created image upload directory: {}", uploadPath);
                } else {
                    log.error("Unable to create image upload directory: {}", uploadPath);
                }
            }
        } catch (Exception e) {
            log.error("Error initializing image upload directory", e);
        }
    }
    
    /**
     * Saves the uploaded image file
     * 
     * @param issueId Fault ID
     * @param file Uploaded file
     * @return Path of the saved file
     * @throws IOException if saving fails
     */
    public String saveImage(int issueId, MultipartFile file) throws IOException {
        if (file == null || file.isEmpty()) {
            return null;
        }
        
        // Create upload directory (if it doesn't exist)
        File uploadDir = new File(uploadPath);
        if (!uploadDir.exists()) {
            uploadDir.mkdirs();
        }
        
        // Generate unique filename
        String originalFilename = file.getOriginalFilename();
        String fileExtension = "";
        if (originalFilename != null && originalFilename.contains(".")) {
            fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
        }
        
        String newFilename = "issue_" + issueId + "_" + 
                            LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + 
                            "_" + UUID.randomUUID().toString().substring(0, 8) + 
                            fileExtension;
        
        String filePath = uploadPath + File.separator + newFilename;
        Path path = Paths.get(filePath);
        
        // Save file
        Files.write(path, file.getBytes());
        
        // 使用统一的路径格式存储到数据库
        // 将Windows路径分隔符(\)替换为Web友好的正斜杠(/)
        String dbPath = uploadPath + "/" + newFilename;
        dbPath = dbPath.replace('\\', '/');
        
        // 保存到数据库
        chargerIssueRepository.addIssueImage(issueId, dbPath);
        
        log.info("Saved fault image: {}", filePath);
        return filePath;
    }
    
    /**
     * Batch save multiple images
     * 
     * @param issueId Fault ID
     * @param files Multiple image files
     * @return Number of successfully saved images
     */
    public int saveImages(int issueId, MultipartFile[] files) {
        if (files == null || files.length == 0) {
            return 0;
        }
        
        int successCount = 0;
        for (MultipartFile file : files) {
            try {
                if (!file.isEmpty()) {
                    saveImage(issueId, file);
                    successCount++;
                }
            } catch (IOException e) {
                log.error("Failed to save fault image", e);
            }
        }
        
        return successCount;
    }
} 