package de.rwth.idsg.steve.service;

import de.rwth.idsg.steve.repository.UpdateFirmwareLogRepository;
import de.rwth.idsg.steve.web.dto.UpdateFirmwareLog;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

@Slf4j
@Component
public class FirmwareUpdateTimeoutService {

    private static final int TIMEOUT_MINUTES = 5;
    // Define installation states that are subject to timeout
    private static final List<String> ACTIVE_INSTALLATION_STATUSES = Arrays.asList("Downloading", "Installing");


    @Autowired
    private UpdateFirmwareLogRepository updateFirmwareLogRepository;

    /**
     * Periodically checks for firmware updates that have timed out.
     * This task runs every minute.
     */
    @Scheduled(cron = "0 * * * * ?") // Run every minute
    public void checkFirmwareUpdateTimeout() {
        log.debug("Running firmware update timeout check...");
        try {
            List<UpdateFirmwareLog> activeLogs = updateFirmwareLogRepository.getLogsWithStatus(ACTIVE_INSTALLATION_STATUSES);

            if (activeLogs.isEmpty()) {
                log.debug("No active firmware updates to check for timeout.");
                return;
            }

            DateTime now = DateTime.now();
            for (UpdateFirmwareLog logEntry : activeLogs) {
                if (logEntry.getLastUpdated() != null) {
                    DateTime timeoutThreshold = logEntry.getLastUpdated().plusMinutes(TIMEOUT_MINUTES);
                    if (now.isAfter(timeoutThreshold)) {
                        log.info("Firmware update for ChargeBox ID: {} (Log ID: {}) has timed out. Current status: {}, Last updated: {}. Setting status to 'Installation timeout'.",
                                logEntry.getChargeBoxId(), logEntry.getLogId(), logEntry.getStatus(), logEntry.getLastUpdated());
                        int updated = updateFirmwareLogRepository.updateStatus(
                                logEntry.getLogId(),
                                "Installation timeout",
                                "Installation timed out after " + TIMEOUT_MINUTES + " minutes."
                        );
                        if (updated > 0) {
                            log.info("Successfully updated status to 'Installation timeout' for Log ID: {}", logEntry.getLogId());
                        } else {
                            log.warn("Failed to update status to 'Installation timeout' for Log ID: {}. It might have been updated by another process.", logEntry.getLogId());
                        }
                    }
                } else {
                    // This case should ideally not happen if lastUpdated is always set.
                    // Consider updating lastUpdated when the log is first created or enters an active state.
                    log.warn("Firmware log for ChargeBox ID: {} (Log ID: {}) has an active status '{}' but 'lastUpdated' is null. Cannot determine timeout.",
                            logEntry.getChargeBoxId(), logEntry.getLogId(), logEntry.getStatus());
                }
            }
            log.debug("Finished firmware update timeout check. Processed {} active logs.", activeLogs.size());
        } catch (Exception e) {
            log.error("Error during firmware update timeout check: {}", e.getMessage(), e);
        }
    }
} 