<%--

    Ste<PERSON><PERSON> - SteckdosenVerwaltung - https://github.com/steve-community/steve
    Copyright (C) 2013-2025 SteVe Community Team
    All Rights Reserved.

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.

--%>
<%@ page contentType="text/html;charset=UTF-8" pageEncoding="UTF-8" language="java" trimDirectiveWhitespaces="true" %>
<%@ taglib uri="jakarta.tags.core" prefix="c" %>
<%@ taglib uri="http://www.springframework.org/tags" prefix="spring" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ include file="../00-context.jsp" %>
<%@ include file="../00-header.jsp" %>

<script type="text/javascript">
    $(document).ready(function() {
        // Content from ../snippets/dateTimePicker-future.js
        $('.dateTimePicker').datetimepicker({
            dateFormat: 'yy-mm-dd'
            //, minDateTime: new Date()
        });
    });
</script>

<div class="breadcrumb-container">
    <a href="${ctxPath}/manager/home">Home</a> &gt;
    <span>Data Management</span> &gt; 
    <span>Firmware Update</span>
</div>

<div class="content">
    <div class="data-management-content">
        <form:form action="${ctxPath}/manager/operations/ajax/v1.6/UpdateFirmware" modelAttribute="params" cssClass="ocpp-operation-form" enctype="multipart/form-data">
            <section><span>Charge Points with OCPP v1.6</span></section>
            <c:set var="cpList" value="${ocppCpList}" scope="page"/>
            <%@ include file="../00-cp-multiple.jsp" %>
            <section><span>Parameters</span></section>
            <div class="form-section parameters-section">
                <div class="form-group">
                    <label for="firmwareFile">Firmware File:</label>
                    <input type="file" id="firmwareFile" name="firmwareFile" required="required" class="form-control-file" />
                </div>
                <div class="form-group">
                    <label for="retries">Retries (integer):</label>
                    <form:input path="retries" id="retries" cssClass="form-control" placeholder="optional" />
                </div>
                <div class="form-group">
                    <label for="retryInterval">Retry Interval (integer):</label>
                    <form:input path="retryInterval" id="retryInterval" cssClass="form-control" placeholder="optional" />
                </div>
                <div class="form-group">
                    <label for="retrieve">Retrieve Date/Time:</label>
                    <form:input path="retrieve" id="retrieve" cssClass="form-control dateTimePicker" required="required"/>
                </div>
                <div class="form-group submit-group">
                    <div class="submit-button">
                        <input type="submit" value="Perform" class="btn btn-primary"/>
                    </div>
                </div>
            </div>
        </form:form>

        <%-- Content from firmwarelogs.jsp starts here --%>
        <section><span>Firmware Update Logs</span></section>
        <form:form action="${ctxPath}/manager/firmwareUpdate" method="get" modelAttribute="logQueryParams">
            <div class="form-section parameters-section">
                <div class="form-group">
                    <label for="logLocation">Location:</label>
                    <form:input path="location" id="logLocation" cssClass="form-control"/>
                </div>
                <div class="form-group">
                    <label for="logRetries">Retries:</label>
                    <form:input path="retries" id="logRetries" cssClass="form-control" placeholder="e.g. 1, 3, 5"/>
                </div>
                <div class="form-group">
                    <label for="logRetryInterval">Retry Interval:</label>
                    <form:input path="retryInterval" id="logRetryInterval" cssClass="form-control" placeholder="e.g. 60, 120, 300"/>
                </div>
                <div class="form-group">
                    <label for="logChargeBoxId">ChargeBox ID:</label>
                    <form:select path="chargeBoxId" id="logChargeBoxId" cssClass="form-control">
                            <option value="" selected>All</option>
                            <form:options items="${chargeBoxIdListForLogs}"/>
                    </form:select>
                </div>
                <div class="form-group">
                    <label for="logStatus">Status:</label>
                    <form:select path="status" id="logStatus" cssClass="form-control">
                            <option value="" selected>All</option>
                            <option value="Downloading">Downloading</option>
                            <option value="Installing">Installing</option>
                            <option value="Installed">Installed</option> 
                            <option value="Completed">Completed (Legacy)</option>
                            <option value="Success">Success (Legacy)</option>
                            <option value="InstallationFailed">Installation Failed</option>
                            <option value="DownloadFailed">Download Failed</option>
                            <option value="Failed">Failed (Legacy)</option>
                            <option value="Installation timeout">Installation Timeout</option>
                            <option value="Timeout">Timeout (Generic)</option>
                    </form:select>
                </div>
                <div class="form-group">
                    <label for="logRetrieveDateFrom">Retrieve Date From:</label>
                    <form:input path="retrieveDateFrom" id="logRetrieveDateFrom" cssClass="form-control dateTimePicker"/>
                </div>
                <div class="form-group">
                    <label for="logRetrieveDateTo">Retrieve Date To:</label>
                    <form:input path="retrieveDateTo" id="logRetrieveDateTo" cssClass="form-control dateTimePicker"/>
                </div>
                <div class="form-group">
                    <label for="logSendingTimeFrom">Sending Time From:</label>
                    <form:input path="sendingTimeFrom" id="logSendingTimeFrom" cssClass="form-control dateTimePicker"/>
                </div>
                <div class="form-group">
                    <label for="logSendingTimeTo">Sending Time To:</label>
                    <form:input path="sendingTimeTo" id="logSendingTimeTo" cssClass="form-control dateTimePicker"/>
                </div>
                <div class="form-group submit-group">
                    <div class="submit-button">
                        <input type="submit" value="Get Logs" class="btn btn-primary"/>
                    </div>
                </div>
            </div>
        </form:form>
        <br>

        <table class="res action">
            <thead>
                <tr>
                    <th data-sort="string">ChargeBox ID</th>
                    <th data-sort="string">Location</th>
                    <th data-sort="int">Retries</th>
                    <th data-sort="int">Retry Interval</th>
                    <th data-sort="date">Retrieve Date/Time</th>
                    <th data-sort="date">Sending Time</th>
                    <th data-sort="date">Last Updated</th>
                    <th data-sort="string">Status</th>
                </tr>
            </thead>
            <tbody>
            <c:forEach items="${logList}" var="log">
                <tr>
                    <td>
                        <c:set var="found" value="false" />
                        <c:forEach items="${chargeBoxIdListForLogs}" var="cpId">
                            <c:if test="${cpId eq log.chargeBoxId}">
                                <c:set var="found" value="true" />
                            </c:if>
                        </c:forEach>
                        
                        <c:choose>
                            <c:when test="${found}">
                                <a href="${ctxPath}${chargePointDetailsPath}${log.chargeBoxId}" title="Charge point details">${log.chargeBoxId}</a>
                            </c:when>
                            <c:otherwise>
                                ${log.chargeBoxId}
                            </c:otherwise>
                        </c:choose>
                    </td>
                    <td>${log.location}</td>
                    <td>${log.retries}</td>
                    <td>${log.retryInterval}</td>
                    <td data-sort-value="${log.retrieveDatetime.millis}">${log.retrieveDatetime}</td>
                    <td data-sort-value="${log.sendingTime.millis}">${log.sendingTime}</td>
                    <td data-sort-value="${log.lastUpdated.millis}">${log.lastUpdated}</td>
                    <td>${log.status}</td>
                </tr>
            </c:forEach>
            </tbody>
        </table>
        
        <c:if test="${empty logList}">
            <p>No records found.</p>
        </c:if>
        
        <%-- Pagination Controls --%>
        <c:if test="${firmwareLogPage != null && firmwareLogPage.totalPages > 0}">
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div>
                    Showing ${firmwareLogPage.number * firmwareLogPage.size + 1} 
                    to ${firmwareLogPage.number * firmwareLogPage.size + firmwareLogPage.numberOfElements} 
                    of ${firmwareLogPage.totalElements} entries
                </div>
                <nav aria-label="Page navigation">
                    <ul class="pagination mb-0">
                        <%-- Previous Page Link --%>
                        <li class="page-item <c:if test='${firmwareLogPage.number == 0}'>disabled</c:if>">
                            <c:url value="/manager/firmwareUpdate" var="prevUrl">
                                <c:if test="${not empty logQueryParams.location}"><c:param name="location" value="${logQueryParams.location}"/></c:if>
                                <c:if test="${not empty logQueryParams.retries}"><c:param name="retries" value="${logQueryParams.retries}"/></c:if>
                                <c:if test="${not empty logQueryParams.retryInterval}"><c:param name="retryInterval" value="${logQueryParams.retryInterval}"/></c:if>
                                <c:if test="${not empty logQueryParams.chargeBoxId}"><c:param name="chargeBoxId" value="${logQueryParams.chargeBoxId}"/></c:if>
                                <c:if test="${not empty logQueryParams.status}"><c:param name="status" value="${logQueryParams.status}"/></c:if>
                                <c:if test="${not empty logQueryParams.retrieveDateFrom}"><c:param name="retrieveDateFrom" value="${logQueryParams.retrieveDateFrom}"/></c:if>
                                <c:if test="${not empty logQueryParams.retrieveDateTo}"><c:param name="retrieveDateTo" value="${logQueryParams.retrieveDateTo}"/></c:if>
                                <c:if test="${not empty logQueryParams.sendingTimeFrom}"><c:param name="sendingTimeFrom" value="${logQueryParams.sendingTimeFrom}"/></c:if>
                                <c:if test="${not empty logQueryParams.sendingTimeTo}"><c:param name="sendingTimeTo" value="${logQueryParams.sendingTimeTo}"/></c:if>
                                <c:param name="page" value="${firmwareLogPage.number - 1}"/>
                                <c:param name="size" value="${firmwareLogPage.size}"/>
                                <%-- Include sort parameters --%>
                                <c:if test="${not empty firmwareLogPage.sort && firmwareLogPage.sort.isSorted()}">
                                    <c:forEach items="${firmwareLogPage.sort.toList()}" var="order">
                                        <c:param name="sort" value="${order.property},${order.direction}"/>
                                    </c:forEach>
                                </c:if>
                            </c:url>
                            <a class="page-link" href="${prevUrl}">Previous</a>
                        </li>

                        <%-- Page Number Links --%>
                        <c:forEach begin="0" end="${firmwareLogPage.totalPages - 1}" var="i">
                            <li class="page-item <c:if test='${firmwareLogPage.number == i}'>active</c:if>">
                                <c:url value="/manager/firmwareUpdate" var="pageUrl">
                                     <c:if test="${not empty logQueryParams.location}"><c:param name="location" value="${logQueryParams.location}"/></c:if>
                                    <c:if test="${not empty logQueryParams.retries}"><c:param name="retries" value="${logQueryParams.retries}"/></c:if>
                                    <c:if test="${not empty logQueryParams.retryInterval}"><c:param name="retryInterval" value="${logQueryParams.retryInterval}"/></c:if>
                                    <c:if test="${not empty logQueryParams.chargeBoxId}"><c:param name="chargeBoxId" value="${logQueryParams.chargeBoxId}"/></c:if>
                                    <c:if test="${not empty logQueryParams.status}"><c:param name="status" value="${logQueryParams.status}"/></c:if>
                                    <c:if test="${not empty logQueryParams.retrieveDateFrom}"><c:param name="retrieveDateFrom" value="${logQueryParams.retrieveDateFrom}"/></c:if>
                                    <c:if test="${not empty logQueryParams.retrieveDateTo}"><c:param name="retrieveDateTo" value="${logQueryParams.retrieveDateTo}"/></c:if>
                                    <c:if test="${not empty logQueryParams.sendingTimeFrom}"><c:param name="sendingTimeFrom" value="${logQueryParams.sendingTimeFrom}"/></c:if>
                                    <c:if test="${not empty logQueryParams.sendingTimeTo}"><c:param name="sendingTimeTo" value="${logQueryParams.sendingTimeTo}"/></c:if>
                                    <c:param name="page" value="${i}"/>
                                    <c:param name="size" value="${firmwareLogPage.size}"/>
                                    <%-- Include sort parameters --%>
                                    <c:if test="${not empty firmwareLogPage.sort && firmwareLogPage.sort.isSorted()}">
                                        <c:forEach items="${firmwareLogPage.sort.toList()}" var="order">
                                            <c:param name="sort" value="${order.property},${order.direction}"/>
                                        </c:forEach>
                                    </c:if>
                                </c:url>
                                <a class="page-link" href="${pageUrl}">${i + 1}</a>
                            </li>
                        </c:forEach>

                        <%-- Next Page Link --%>
                        <li class="page-item <c:if test='${firmwareLogPage.number == firmwareLogPage.totalPages - 1 || firmwareLogPage.totalPages == 0}'>disabled</c:if>">
                            <c:url value="/manager/firmwareUpdate" var="nextUrl">
                                <c:if test="${not empty logQueryParams.location}"><c:param name="location" value="${logQueryParams.location}"/></c:if>
                                <c:if test="${not empty logQueryParams.retries}"><c:param name="retries" value="${logQueryParams.retries}"/></c:if>
                                <c:if test="${not empty logQueryParams.retryInterval}"><c:param name="retryInterval" value="${logQueryParams.retryInterval}"/></c:if>
                                <c:if test="${not empty logQueryParams.chargeBoxId}"><c:param name="chargeBoxId" value="${logQueryParams.chargeBoxId}"/></c:if>
                                <c:if test="${not empty logQueryParams.status}"><c:param name="status" value="${logQueryParams.status}"/></c:if>
                                <c:if test="${not empty logQueryParams.retrieveDateFrom}"><c:param name="retrieveDateFrom" value="${logQueryParams.retrieveDateFrom}"/></c:if>
                                <c:if test="${not empty logQueryParams.retrieveDateTo}"><c:param name="retrieveDateTo" value="${logQueryParams.retrieveDateTo}"/></c:if>
                                <c:if test="${not empty logQueryParams.sendingTimeFrom}"><c:param name="sendingTimeFrom" value="${logQueryParams.sendingTimeFrom}"/></c:if>
                                <c:if test="${not empty logQueryParams.sendingTimeTo}"><c:param name="sendingTimeTo" value="${logQueryParams.sendingTimeTo}"/></c:if>
                                <c:param name="page" value="${firmwareLogPage.number + 1}"/>
                                <c:param name="size" value="${firmwareLogPage.size}"/>
                                <%-- Include sort parameters --%>
                                <c:if test="${not empty firmwareLogPage.sort && firmwareLogPage.sort.isSorted()}">
                                    <c:forEach items="${firmwareLogPage.sort.toList()}" var="order">
                                        <c:param name="sort" value="${order.property},${order.direction}"/>
                                    </c:forEach>
                                </c:if>
                            </c:url>
                            <a class="page-link" href="${nextUrl}">Next</a>
                        </li>
                    </ul>
                </nav>
            </div>
        </c:if>
        <%-- Content from firmwarelogs.jsp ends here --%>
    </div>
</div>

<style>
/* Ensure pagination styles from chargepoints.jsp are available or copied here */
/* It's better to have a common CSS file for pagination if not already present */
.pagination {
    display: flex; /* Use flexbox for horizontal layout */
    justify-content: flex-end; /* Align items to the right */
    padding-left: 0;
    list-style: none;
    margin-top: 20px; /* Keep some margin */
}
.pagination .page-item .page-link {
    color: #495057; /* Darker text for page links */
    border-radius: 0.25rem; /* Slightly rounded corners */
    margin: 0 2px; /* Add small margin between page items */
    padding: .375rem .75rem; /* Standard padding */
    line-height: 1.5; /* Standard line height */
    background-color: #fff;
    border: 1px solid #dee2e6;
}
.pagination .page-item.active .page-link {
    z-index: 3;
    color: #fff;
    background-color: #007bff; /* Standard primary color for active page */
    border-color: #007bff;
}
.pagination .page-item:not(.disabled) .page-link:hover {
    background-color: #e9ecef; /* Light grey background on hover */
    border-color: #dee2e6;
}
.pagination .page-item.disabled .page-link {
    color: #6c757d;
    pointer-events: none;
    background-color: #fff;
    border-color: #dee2e6;
}

/* Remove old pagination styles if they conflict */
/* .pagination-info, .pagination-controls, .pagination-button can be removed if not used by the new structure */

.details-link {
    margin-left: 5px;
    font-size: 0.9em;
}

.details-link i {
    color: #337ab7;
}
</style>

<%@ include file="../00-footer.jsp" %> 