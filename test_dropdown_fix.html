<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>下拉菜单修复测试</title>
    <link rel="stylesheet" type="text/css" href="src/main/resources/webapp/static/css/dropdown-fix.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            background-color: white;
            padding: 20px;
            border-radius: 4px;
            box-shadow: 0 0 20px 0 rgba(76,87,125,.02);
            margin-bottom: 20px;
        }
        
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        
        /* 模拟原始的下拉菜单样式 */
        .multi-select-container {
            position: relative;
            width: 100%;
            margin-bottom: 10px;
        }
        
        .selected-options {
            padding: 10px;
            cursor: pointer;
            min-height: 20px;
            line-height: 1.5;
            border: 1px solid #ccc;
            border-radius: 4px;
            background-color: white;
        }
        
        .options-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            border: 1px solid #ccc;
            border-top: none;
            background-color: white;
            z-index: 1000; /* 原始的低z-index */
            max-height: 200px;
            overflow-y: auto;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            display: none;
        }
        
        .search-input {
            width: calc(100% - 20px);
            padding: 8px 10px;
            border: 1px solid #eee;
            border-bottom: 1px solid #ccc;
            box-sizing: border-box;
        }
        
        .options-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .options-list li {
            padding: 8px 10px;
            cursor: pointer;
        }
        
        .options-list li:hover {
            background-color: #f0f0f0;
        }
        
        /* 模拟可能遮挡下拉菜单的元素 */
        .blocking-element {
            position: relative;
            z-index: 500;
            background-color: #007bff;
            color: white;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        
        .high-z-element {
            position: relative;
            z-index: 2000;
            background-color: #dc3545;
            color: white;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>下拉菜单修复测试页面</h1>
    
    <div class="test-container">
        <div class="test-title">测试1: 基本下拉菜单（应用修复后）</div>
        <div class="multi-select-container">
            <div class="selected-options" onclick="toggleDropdown(this)">点击选择充电桩...</div>
            <div class="options-dropdown">
                <input type="text" class="search-input" placeholder="搜索充电桩...">
                <ul class="options-list">
                    <li>CP001 - 充电桩001</li>
                    <li>CP002 - 充电桩002</li>
                    <li>CBI-146b9c8b1e52 - 充电桩003</li>
                    <li>510004 - 充电桩004</li>
                    <li>CBI-0c8c24238798 - 充电桩005</li>
                    <li>CBI-1597302927231 - 充电桩006</li>
                    <li>CBI-1597302917231 - 充电桩007</li>
                    <li>CBI-203233a00a8f - 充电桩008</li>
                </ul>
            </div>
        </div>
        
        <div class="blocking-element">
            这是一个可能遮挡下拉菜单的元素 (z-index: 500)
        </div>
        
        <div class="high-z-element">
            这是一个高z-index的元素 (z-index: 2000) - 下拉菜单应该显示在它之上
        </div>
    </div>
    
    <div class="test-container">
        <div class="test-title">测试2: 多个下拉菜单</div>
        <div class="multi-select-container">
            <div class="selected-options" onclick="toggleDropdown(this)">已分配充电桩 (6)</div>
            <div class="options-dropdown">
                <input type="text" class="search-input" placeholder="搜索充电桩...">
                <ul class="options-list">
                    <li>CP001 - 已分配充电桩1</li>
                    <li>CP002 - 已分配充电桩2</li>
                    <li>CP003 - 已分配充电桩3</li>
                </ul>
            </div>
        </div>
        
        <div class="multi-select-container">
            <div class="selected-options" onclick="toggleDropdown(this)">未分配充电桩 (13)</div>
            <div class="options-dropdown">
                <input type="text" class="search-input" placeholder="搜索充电桩...">
                <ul class="options-list">
                    <li>CP004 - 未分配充电桩1</li>
                    <li>CP005 - 未分配充电桩2</li>
                    <li>CP006 - 未分配充电桩3</li>
                    <li>CP007 - 未分配充电桩4</li>
                    <li>CP008 - 未分配充电桩5</li>
                </ul>
            </div>
        </div>
    </div>
    
    <script>
        function toggleDropdown(element) {
            // 关闭所有其他下拉菜单
            document.querySelectorAll('.options-dropdown').forEach(function(dropdown) {
                if (dropdown !== element.nextElementSibling) {
                    dropdown.style.display = 'none';
                }
            });
            
            // 切换当前下拉菜单
            var dropdown = element.nextElementSibling;
            dropdown.style.display = dropdown.style.display === 'none' ? 'block' : 'none';
        }
        
        // 点击外部关闭下拉菜单
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.multi-select-container')) {
                document.querySelectorAll('.options-dropdown').forEach(function(dropdown) {
                    dropdown.style.display = 'none';
                });
            }
        });
        
        // 阻止下拉菜单内部点击关闭菜单
        document.querySelectorAll('.options-dropdown').forEach(function(dropdown) {
            dropdown.addEventListener('click', function(event) {
                event.stopPropagation();
            });
        });
    </script>
</body>
</html>
