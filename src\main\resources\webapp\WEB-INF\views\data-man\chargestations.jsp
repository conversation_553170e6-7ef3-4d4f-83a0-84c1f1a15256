<%--

    SteV<PERSON> - SteckdosenVerwaltung - https://github.com/steve-community/steve
    Copyright (C) 2013-2025 SteVe Community Team
    All Rights Reserved.

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.

--%>
<%@ include file="../00-header.jsp" %>
<script type="text/javascript">
    $(document).ready(function() {
        <%@ include file="../snippets/sortable.js" %>
    });
</script>
<div class="content-container">
    <div class="page-title">Charge Station Management</div>
    
    <section><span>
    Charge Station Overview
    <a class="tooltip" href="#"><img src="${ctxPath}/static/images/info.png" style="vertical-align:middle">
        <span>
        <sec:authorize access="hasAnyAuthority('ADMIN', 'OPERATOR_FACTORY')">
        Manage your charge stations.
        </sec:authorize>
        <sec:authorize access="hasAuthority('OPERATOR_OWNER')">
        You can only see charge stations assigned to you.
        </sec:authorize>
        </span>
    </a>
    </span></section>
    <div id="overview">
        <div class="search-panel mb-4">
            <form:form action="${ctxPath}/manager/chargestations/query" method="get" modelAttribute="params">
                <div class="row mb-3">
                    <div class="col-md-4 mb-3">
                        <div class="form-group">
                            <label for="stationName">Station Name:</label>
                            <form:input path="stationName" class="form-control"/>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="form-group">
                            <label for="operatorName">Operator Name:</label>
                            <form:input path="operatorName" class="form-control"/>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="form-group">
                            <label for="assignedUser">Assigned User:</label>
                            <form:input path="assignedUser" class="form-control"/>
                        </div>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 mb-3">
                        <div class="form-group">
                            <label for="constructionDate">Construction Date:</label>
                            <form:input path="constructionDate" class="form-control" type="date"/>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="form-group">
                            <label for="operationDate">Operation Date:</label>
                            <form:input path="operationDate" class="form-control" type="date"/>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3 d-flex align-items-end">
                        <div class="form-group">
                            <input type="submit" class="btn btn-primary" value="Search">
                        </div>
                    </div>
                </div>
            </form:form>
        </div>
        
        <div class="table-responsive">
            <table class="table table-striped table-hover table-bordered">
                <thead>
                <tr>
                    <th class="sorting" data-sort="string">Station Name</th>
                    <th class="sorting" data-sort="string">Operator Name</th>
                    <th class="sorting" data-sort="string">Location</th>
                    <th class="sorting" data-sort="date">Construction Date</th>
                    <th class="sorting" data-sort="date">Operation Date</th>
                    <th class="sorting" data-sort="string">Assigned User</th>
                    <th class="text-center">
                        <sec:authorize access="hasAnyAuthority('ADMIN', 'OPERATOR_FACTORY', 'OPERATOR_OWNER')">
                        <form:form action="${ctxPath}/manager/chargestations/add" method="get">
                            <input type="submit" class="btn btn-blue" value="Add New">
                        </form:form>
                        </sec:authorize>
                    </th>
                </tr>
                </thead>
                <tbody>
                <c:forEach items="${stationList}" var="station">
                    <tr>
                        <td><a href="${ctxPath}/manager/chargestations/details/${station.chargingStationPk}">${station.stationName}</a></td>
                        <td>${station.operatorName}</td>
                        <td>${station.location}</td>
                        <td data-sort-value="${station.constructionDateDT.millis}">${station.constructionDateFormatted}</td>
                        <td data-sort-value="${station.operationDateDT.millis}">${station.operationDateFormatted}</td>
                        <td>${station.userName}</td>
                        <td class="text-center">
                            <sec:authorize access="hasAnyAuthority('ADMIN', 'OPERATOR_FACTORY', 'OPERATOR_OWNER')">
                            <form:form action="${ctxPath}/manager/chargestations/delete/${station.chargingStationPk}" method="post">
                                <input type="submit" class="btn btn-standard" value="Delete">
                            </form:form>
                            </sec:authorize>
                        </td>
                    </tr>
                </c:forEach>
                </tbody>
            </table>
        </div>
    </div>
</div>
<%@ include file="../00-footer.jsp" %> 