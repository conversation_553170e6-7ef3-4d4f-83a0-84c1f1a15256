package de.rwth.idsg.steve.repository.impl;

import de.rwth.idsg.steve.repository.ChargingSuccessRepository;
import de.rwth.idsg.steve.repository.dto.ChargingSession;
import de.rwth.idsg.steve.repository.dto.ChargingSuccessStats;
import de.rwth.idsg.steve.utils.DateTimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.jooq.DSLContext;
import org.jooq.Record;
import org.jooq.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 充电成功率统计数据访问实现类
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Repository
public class ChargingSuccessRepositoryImpl implements ChargingSuccessRepository {
    
    @Autowired
    private DSLContext ctx;
    
    // -------------------------------------------------------------------------
    // 充电会话管理
    // -------------------------------------------------------------------------
    
    @Override
    public int createChargingSession(String chargeBoxId, int connectorId, Integer transactionId) {
        String sql = """
            INSERT INTO charging_session (charge_box_id, connector_id, transaction_id, session_start_time, status)
            VALUES (?, ?, ?, NOW(), 'PREPARING')
            """;

        ctx.execute(sql, chargeBoxId, connectorId, transactionId);

        // 获取刚插入的记录ID
        return ctx.fetchOne("SELECT LAST_INSERT_ID()").getValue(0, Integer.class);
    }
    
    @Override
    public Optional<ChargingSession> getActiveSession(String chargeBoxId, int connectorId) {
        String sql = """
            SELECT session_id, charge_box_id, connector_id, transaction_id, session_start_time, 
                   session_end_time, status, is_successful, has_preparing, has_charging, 
                   has_valid_meter_values, has_finishing, meter_value_count, valid_meter_value_count,
                   created_at, updated_at
            FROM charging_session 
            WHERE charge_box_id = ? AND connector_id = ? 
            AND status IN ('PREPARING', 'CHARGING', 'FINISHING')
            ORDER BY session_start_time DESC 
            LIMIT 1
            """;
        
        Result<Record> result = ctx.fetch(sql, chargeBoxId, connectorId);
        if (result.isEmpty()) {
            return Optional.empty();
        }
        
        return Optional.of(mapToChargingSession(result.get(0)));
    }
    
    @Override
    public Optional<ChargingSession> getSessionByTransactionId(Integer transactionId) {
        if (transactionId == null) {
            return Optional.empty();
        }
        
        String sql = """
            SELECT session_id, charge_box_id, connector_id, transaction_id, session_start_time, 
                   session_end_time, status, is_successful, has_preparing, has_charging, 
                   has_valid_meter_values, has_finishing, meter_value_count, valid_meter_value_count,
                   created_at, updated_at
            FROM charging_session 
            WHERE transaction_id = ?
            ORDER BY session_start_time DESC 
            LIMIT 1
            """;
        
        Result<Record> result = ctx.fetch(sql, transactionId);
        if (result.isEmpty()) {
            return Optional.empty();
        }
        
        return Optional.of(mapToChargingSession(result.get(0)));
    }
    
    @Override
    public void updateSessionStatus(int sessionId, ChargingSession.ChargingSessionStatus status) {
        String sql = """
            UPDATE charging_session 
            SET status = ?, 
                has_preparing = CASE WHEN ? = 'PREPARING' THEN TRUE ELSE has_preparing END,
                has_charging = CASE WHEN ? = 'CHARGING' THEN TRUE ELSE has_charging END,
                has_finishing = CASE WHEN ? = 'FINISHING' THEN TRUE ELSE has_finishing END,
                session_end_time = CASE WHEN ? IN ('COMPLETED', 'FAILED') THEN NOW() ELSE session_end_time END,
                updated_at = NOW()
            WHERE session_id = ?
            """;
        
        String statusStr = status.name();
        ctx.execute(sql, statusStr, statusStr, statusStr, statusStr, statusStr, sessionId);
    }
    
    @Override
    public void updateSessionMeterValues(int sessionId, int meterValueCount, int validMeterValueCount, boolean hasValidMeterValues) {
        String sql = """
            UPDATE charging_session 
            SET meter_value_count = ?, 
                valid_meter_value_count = ?,
                has_valid_meter_values = ?,
                updated_at = NOW()
            WHERE session_id = ?
            """;
        
        ctx.execute(sql, meterValueCount, validMeterValueCount, hasValidMeterValues, sessionId);
    }
    
    @Override
    public void completeSession(int sessionId, boolean isSuccessful) {
        String sql = """
            UPDATE charging_session 
            SET status = ?, 
                is_successful = ?,
                session_end_time = NOW(),
                updated_at = NOW()
            WHERE session_id = ?
            """;
        
        String status = isSuccessful ? "COMPLETED" : "FAILED";
        ctx.execute(sql, status, isSuccessful, sessionId);
    }
    
    // -------------------------------------------------------------------------
    // 充电成功率统计
    // -------------------------------------------------------------------------
    
    @Override
    public Map<String, ChargingSuccessStats> getAllSuccessStats() {
        String sql = """
            SELECT stats_id, charge_box_id, total_sessions, successful_sessions, success_rate, last_updated
            FROM charging_success_stats
            """;

        Result<Record> result = ctx.fetch(sql);
        Map<String, ChargingSuccessStats> statsMap = new HashMap<>();

        for (Record record : result) {
            ChargingSuccessStats stats = mapToChargingSuccessStats(record);
            statsMap.put(stats.getChargeBoxId(), stats);
        }

        return statsMap;
    }

    @Override
    public Map<String, ChargingSuccessStats> getSuccessStatsByUser(Integer webUserPk) {
        String sql = """
            SELECT css.stats_id, css.charge_box_id, css.total_sessions, css.successful_sessions, css.success_rate, css.last_updated
            FROM charging_success_stats css
            JOIN charge_box cb ON cb.charge_box_id = css.charge_box_id
            JOIN user_charge_box ucb ON ucb.charge_box_pk = cb.charge_box_pk
            WHERE ucb.web_user_pk = ?
            """;

        Result<Record> result = ctx.fetch(sql, webUserPk);
        Map<String, ChargingSuccessStats> statsMap = new HashMap<>();

        for (Record record : result) {
            ChargingSuccessStats stats = mapToChargingSuccessStats(record);
            statsMap.put(stats.getChargeBoxId(), stats);
        }

        return statsMap;
    }
    
    @Override
    public Optional<ChargingSuccessStats> getSuccessStats(String chargeBoxId) {
        String sql = """
            SELECT stats_id, charge_box_id, total_sessions, successful_sessions, success_rate, last_updated
            FROM charging_success_stats
            WHERE charge_box_id = ?
            """;
        
        Result<Record> result = ctx.fetch(sql, chargeBoxId);
        if (result.isEmpty()) {
            return Optional.empty();
        }
        
        return Optional.of(mapToChargingSuccessStats(result.get(0)));
    }
    
    @Override
    public void updateSuccessStats(String chargeBoxId, int totalSessions, int successfulSessions) {
        BigDecimal successRate = ChargingSuccessStats.calculateSuccessRate(successfulSessions, totalSessions);

        String sql = """
            INSERT INTO charging_success_stats (charge_box_id, total_sessions, successful_sessions, success_rate)
            VALUES (?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE
                total_sessions = VALUES(total_sessions),
                successful_sessions = VALUES(successful_sessions),
                success_rate = VALUES(success_rate),
                last_updated = NOW()
            """;

        ctx.execute(sql, chargeBoxId, totalSessions, successfulSessions, successRate);

        log.debug("Updated success stats for {}: total={}, successful={}, rate={}%",
                chargeBoxId, totalSessions, successfulSessions, successRate);
    }
    
    @Override
    public void initializeSuccessStats(String chargeBoxId) {
        String sql = """
            INSERT IGNORE INTO charging_success_stats (charge_box_id, total_sessions, successful_sessions, success_rate)
            VALUES (?, 0, 0, 0.00)
            """;
        
        ctx.execute(sql, chargeBoxId);
    }
    
    @Override
    public void recalculateAllSuccessStats() {
        // 基于现有的charging_success_stats表重新计算成功率
        String sql = """
            UPDATE charging_success_stats
            SET success_rate = CASE
                WHEN total_sessions = 0 THEN 0.00
                ELSE ROUND((successful_sessions * 100.0 / total_sessions), 2)
            END,
            last_updated = NOW()
            """;

        ctx.execute(sql);

        log.info("Recalculated success rates for all charge points");
    }
    
    @Override
    public void recalculateSuccessStats(String chargeBoxId) {
        // 基于现有的charging_success_stats表重新计算指定充电桩的成功率
        String sql = """
            UPDATE charging_success_stats
            SET success_rate = CASE
                WHEN total_sessions = 0 THEN 0.00
                ELSE ROUND((successful_sessions * 100.0 / total_sessions), 2)
            END,
            last_updated = NOW()
            WHERE charge_box_id = ?
            """;

        ctx.execute(sql, chargeBoxId);

        log.info("Recalculated success rate for charge point: {}", chargeBoxId);
    }
    
    // -------------------------------------------------------------------------
    // 私有辅助方法
    // -------------------------------------------------------------------------
    
    private ChargingSession mapToChargingSession(Record record) {
        return ChargingSession.builder()
                .sessionId(record.get("session_id", Integer.class))
                .chargeBoxId(record.get("charge_box_id", String.class))
                .connectorId(record.get("connector_id", Integer.class))
                .transactionId(record.get("transaction_id", Integer.class))
                .sessionStartTime(DateTimeUtils.convertLocalDateTimeValue(record.get("session_start_time")))
                .sessionEndTime(DateTimeUtils.convertLocalDateTimeValue(record.get("session_end_time")))
                .status(ChargingSession.ChargingSessionStatus.valueOf(record.get("status", String.class)))
                .successful(record.get("is_successful", Boolean.class))
                .hasPreparing(record.get("has_preparing", Boolean.class))
                .hasCharging(record.get("has_charging", Boolean.class))
                .hasValidMeterValues(record.get("has_valid_meter_values", Boolean.class))
                .hasFinishing(record.get("has_finishing", Boolean.class))
                .meterValueCount(record.get("meter_value_count", Integer.class))
                .validMeterValueCount(record.get("valid_meter_value_count", Integer.class))
                .createdAt(DateTimeUtils.convertLocalDateTimeValue(record.get("created_at")))
                .updatedAt(DateTimeUtils.convertLocalDateTimeValue(record.get("updated_at")))
                .build();
    }
    
    private ChargingSuccessStats mapToChargingSuccessStats(Record record) {
        return ChargingSuccessStats.builder()
                .statsId(record.get("stats_id", Integer.class))
                .chargeBoxId(record.get("charge_box_id", String.class))
                .totalSessions(record.get("total_sessions", Integer.class))
                .successfulSessions(record.get("successful_sessions", Integer.class))
                .successRate(record.get("success_rate", BigDecimal.class))
                .lastUpdated(DateTimeUtils.convertLocalDateTimeValue(record.get("last_updated")))
                .build();
    }
}
