/**
 * EVSE_OMS UI增强脚本
 * 基于RuoYi-Vue样式改造
 */

$(function() {
    // 为表格添加排序和响应式支持
    $('.table').addClass('table-striped table-hover');
    
    // 强化按钮样式
    $('input[type="submit"]').addClass('btn btn-primary');
    $('input[type="button"]').addClass('btn btn-default');
    
    // 为链接添加样式
    $('.tileWrapper a').addClass('transition-all');
    
    // 为表单添加样式
    $('input[type="text"], input[type="number"], input[type="password"]').addClass('form-control');
    $('select').addClass('form-control');
    $('textarea').addClass('form-control');
    
    // 表格排序增强
    $('table.res th[data-sort], #chargePointTable th[data-sort]').addClass('sorting');
    
    // 卡片样式增强
    $('.content').addClass('shadow');
    
    // 为已有的表格添加响应式支持
    $('table.res').wrap('<div class="table-responsive"></div>');
    
    // 为分页添加样式
    $('.pagination-container').addClass('d-flex justify-content-end');
    
    // 表单提交验证
    $('form').on('submit', function() {
        let isValid = true;
        $(this).find('input[required], select[required], textarea[required]').each(function() {
            if (!$(this).val()) {
                $(this).addClass('has-error');
                isValid = false;
            } else {
                $(this).removeClass('has-error');
            }
        });
        return isValid;
    });
    
    // 表单字段验证
    $('input[required], select[required], textarea[required]').on('blur', function() {
        if (!$(this).val()) {
            $(this).addClass('has-error');
        } else {
            $(this).removeClass('has-error');
        }
    });
    
    // 实现表格行选择功能
    $('.table tbody tr').click(function() {
        $(this).toggleClass('active').siblings().removeClass('active');
    });
    
    // 实现工具提示
    $('[data-toggle="tooltip"]').each(function() {
        const $this = $(this);
        $this.hover(
            function() {
                const tooltip = $('<div class="tooltip-inner">' + $this.attr('title') + '</div>');
                $('body').append(tooltip);
                const pos = $this.offset();
                tooltip.css({
                    top: pos.top - tooltip.height() - 10,
                    left: pos.left + $this.width() / 2 - tooltip.width() / 2
                }).fadeIn('fast');
            },
            function() {
                $('.tooltip-inner').remove();
            }
        );
    });
    
    // 数字格式化
    $('.formatNumber').each(function() {
        const num = $(this).text();
        if (!isNaN(num) && num.trim() !== '') {
            $(this).text(parseFloat(num).toLocaleString());
        }
    });
});