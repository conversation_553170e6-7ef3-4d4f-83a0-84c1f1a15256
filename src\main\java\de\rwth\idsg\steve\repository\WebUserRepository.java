/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.repository;

import de.rwth.idsg.steve.web.dto.UserRole;
import jooq.steve.db.tables.records.WebUserRecord;

import java.util.List;

public interface WebUserRepository  {

    void createUser(WebUserRecord user);

    void updateUser(WebUserRecord user);

    void deleteUser(String username);

    void deleteUser(int webUserPk);

    void changeStatusOfUser(String username, boolean enabled);

    /**
     * 获取具有指定角色的用户数量
     * 
     * @param role 角色
     * @return 具有该角色的用户数量
     */
    Integer getUserCountWithRole(UserRole role);

    void changePassword(String username, String newPassword);

    boolean userExists(String username);

    WebUserRecord loadUserByUsername(String username);

    /**
     * Get a list of all users with a specific role
     *
     * @param role The role to filter by
     * @return List of users with the specified role
     */
    List<WebUserRecord> getUsersByRole(UserRole role);

    /**
     * Update the role of a user
     *
     * @param username The username of the user
     * @param role The new role
     */
    void updateUserRole(String username, UserRole role);

    /**
     * Update the role of a user by primary key
     *
     * @param webUserPk The primary key of the user
     * @param role The new role
     */
    void updateUserRole(int webUserPk, UserRole role);

    /**
     * Get the role of a user
     *
     * @param username The username of the user
     * @return The user's role
     */
    UserRole getUserRole(String username);

    /**
     * Get the role of a user by primary key
     *
     * @param webUserPk The primary key of the user
     * @return The user's role
     */
    UserRole getUserRole(int webUserPk);

    /**
     * Load a user by primary key
     *
     * @param webUserPk The primary key of the user
     * @return The user record or null if not found
     */
    WebUserRecord loadUserByPk(int webUserPk);

    /**
     * 根据用户名模式查找可能匹配的web_user记录
     * 通常用于将user表和web_user表关联起来
     *
     * @param namePattern 用户名模式，通常是firstName + lastName组合
     * @return 所有可能匹配的WebUserRecord列表
     */
    List<WebUserRecord> findWebUsersByNamePattern(String namePattern);

    /**
     * 获取所有用户记录
     *
     * @return 所有用户记录的列表
     */
    List<WebUserRecord> getAllUsers();

    /**
     * 添加新用户
     *
     * @param username 用户名
     * @param encodedPassword 加密后的密码
     * @param userRole 用户角色
     */
    void addUser(String username, String encodedPassword, UserRole userRole);

    /**
     * 更新用户密码
     *
     * @param username 用户名
     * @param encodedPassword 加密后的密码
     */
    void updateUserPassword(String username, String encodedPassword);

    /**
     * 根据用户名获取用户主键
     *
     * @param username 用户名
     * @return 用户主键，如果未找到则返回null
     */
    Integer getUserPkByUsername(String username);

    // ========== 新增：user_pk关联相关方法 ==========

    /**
     * 根据user_pk查找关联的web_user记录
     *
     * @param userPk user表的主键
     * @return 关联的WebUserRecord，如果未找到则返回null
     */
    WebUserRecord findByUserPk(int userPk);

    /**
     * 获取web_user关联的user记录的主键
     *
     * @param webUserPk web_user表的主键
     * @return 关联的user_pk，如果未关联则返回null
     */
    Integer getAssociatedUserPk(int webUserPk);

    /**
     * 建立web_user和user的关联
     *
     * @param webUserPk web_user表的主键
     * @param userPk user表的主键
     */
    void associateWithUser(int webUserPk, int userPk);

    /**
     * 解除web_user和user的关联
     *
     * @param webUserPk web_user表的主键
     */
    void disassociateFromUser(int webUserPk);

    /**
     * 根据user_pk解除关联（当删除user记录时使用）
     *
     * @param userPk user表的主键
     */
    void disassociateByUserPk(int userPk);

    /**
     * 获取所有已关联user的web_user记录
     *
     * @return 已关联user的WebUserRecord列表
     */
    List<WebUserRecord> getAllUsersWithAssociation();

    /**
     * 获取所有未关联user的web_user记录
     *
     * @return 未关联user的WebUserRecord列表
     */
    List<WebUserRecord> getAllUsersWithoutAssociation();
}