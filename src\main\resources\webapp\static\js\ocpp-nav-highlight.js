/**
 * 自动为当前OCPP v1.6页面高亮相应的导航项
 */
document.addEventListener('DOMContentLoaded', function() {
    // 获取当前路径
    const path = window.location.pathname;
    
    // 查找所有顶部导航链接
    const navLinks = document.querySelectorAll('.ocpp-v16-subnav a');
    
    // 先移除所有的active类和预设样式
    navLinks.forEach(link => {
        link.classList.remove('active');
        link.style.backgroundColor = '';
        link.style.color = '';
        link.style.borderColor = '';
    });
    
    // 为当前页面的链接添加active类（使用精确匹配而不是includes）
    let isTopNavHighlighted = false;
    navLinks.forEach(link => {
        const linkPath = link.getAttribute('href');
        // 使用精确匹配当前路径而不是部分匹配
        if (path === linkPath) {
            link.classList.add('active');
            link.style.backgroundColor = '#337ab7';
            link.style.color = '#fff';
            link.style.borderColor = '#337ab7';
            isTopNavHighlighted = true;
            
            // 额外检查，确保只有当前页面的导航链接被高亮
            // 由于之前可能存在的状态问题，我们强制移除其他链接的高亮
            navLinks.forEach(otherLink => {
                if (otherLink !== link) {
                    otherLink.classList.remove('active');
                    otherLink.style.backgroundColor = '';
                    otherLink.style.color = '';
                    otherLink.style.borderColor = '';
                }
            });
        }
    });
    
    // 如果顶部导航有高亮项，则移除左侧菜单中OCPP v1.6的高亮
    if (isTopNavHighlighted) {
        // 查找左侧菜单中的OCPP v1.6导航项
        const leftMenuItems = document.querySelectorAll('.left-sidebar ul li a');
        leftMenuItems.forEach(item => {
            if (item.textContent.includes('OCPP v1.6')) {
                // 找到其父级li元素
                const parentLi = item.closest('li');
                if (parentLi) {
                    parentLi.classList.remove('active');
                }
                
                // 查找包含Operations的父菜单项，并移除active类
                const operationsMenu = document.querySelector('.left-sidebar ul li.submenu.active');
                if (operationsMenu && operationsMenu.querySelector('a').textContent.includes('Operations')) {
                    operationsMenu.classList.remove('active');
                }
            }
        });
    }
    
    // 解决同时高亮多个导航项的问题
    // 确保页面加载时清除可能残留的状态
    sessionStorage.setItem('currentActivePage', path);
});