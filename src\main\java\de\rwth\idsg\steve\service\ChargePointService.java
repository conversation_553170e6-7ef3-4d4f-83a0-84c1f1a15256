/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.service;

import de.rwth.idsg.steve.repository.dto.ChargePoint;
import de.rwth.idsg.steve.repository.dto.ChargePointSelect;
import de.rwth.idsg.steve.repository.dto.ConnectorStatus;
import de.rwth.idsg.steve.web.dto.ChargePointForm;
import de.rwth.idsg.steve.web.dto.ChargePointQueryForm;
import de.rwth.idsg.steve.web.dto.ConnectorStatusForm;

import java.util.List;
import java.util.Map;

/**
 * 充电点服务接口
 */
public interface ChargePointService {

    /**
     * 获取充电点概览
     */
    List<ChargePoint.Overview> getOverview(ChargePointQueryForm form);

    /**
     * 获取充电点详情
     */
    ChargePoint.Details getDetails(int chargeBoxPk);

    /**
     * 获取充电点连接器状态
     */
    List<ConnectorStatus> getChargePointConnectorStatus(ConnectorStatusForm form);

    /**
     * 添加充电点列表
     */
    void addChargePointList(List<String> chargeBoxIdList);

    /**
     * 添加充电点
     */
    int addChargePoint(ChargePointForm form);

    /**
     * 更新充电点
     */
    void updateChargePoint(ChargePointForm form);

    /**
     * 删除充电点
     */
    void deleteChargePoint(int chargeBoxPk);
    
    /**
     * 获取按拥有者筛选的充电点选择列表
     */
    List<ChargePointSelect> getChargePointSelectByOwner(Integer userPk);
    
    /**
     * 获取拥有者的充电点
     */
    List<ChargePoint.Overview> getOwnerChargeBoxes(int userPk, ChargePointQueryForm form);
} 