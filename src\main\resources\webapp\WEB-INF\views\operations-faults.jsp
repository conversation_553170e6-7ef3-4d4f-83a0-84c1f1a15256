<%--
    St<PERSON><PERSON><PERSON> - SteckdosenVerwaltung - https://github.com/steve-community/steve
    Copyright (C) 2013-2025 SteVe Community Team
    All Rights Reserved.

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.
--%>
<%@ include file="00-header.jsp" %>
<%@ include file="00-op-bind-errors.jsp" %>

<!-- Add DataTables library references -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.7/css/jquery.dataTables.min.css">
<script type="text/javascript" src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>

<!-- Add unified form styles -->
<link rel="stylesheet" type="text/css" href="${ctxPath}/static/css/unified-form-styles.css">
<link rel="stylesheet" type="text/css" href="${ctxPath}/static/css/form-layout-fix.css">
<link rel="stylesheet" type="text/css" href="${ctxPath}/static/css/layout-alignment-fix.css">

<script type="text/javascript">
    $(document).ready(function() {
        // Auto-hide success message
        setTimeout(function() {
            $('.success-message').slideUp();
        }, 5000);
        
        $('#faultTable').DataTable({
            "bPaginate": true,
            "bFilter": true,
            "bInfo": true,
            "bLengthChange": true,
            "bSort": true,
            "pageLength": 10, // Set 10 records per page
            "lengthMenu": [[5, 10, 25, 50, -1], [5, 10, 25, 50, "All"]], // Set optional page lengths
            "order": [[ 2, "desc" ]],  // Default sort by report time descending
            "language": {
                "search": "Search:",
                "lengthMenu": "Show _MENU_ entries",
                "info": "Showing _START_ to _END_ of _TOTAL_ entries",
                "infoEmpty": "No data available",
                "emptyTable": "No data available in table",
                "paginate": {
                    "first": "First",
                    "last": "Last",
                    "next": "Next",
                    "previous": "Previous"
                }
            }
        });
    });
</script>

<style>
    /* 故障状态样式 */
    .status-new {
        background-color: #f0ad4e;
        color: white;
        padding: 4px 8px;
        border-radius: 3px;
        font-size: 12px;
        font-weight: bold;
        display: inline-block;
    }

    .status-in-progress {
        background-color: #5bc0de;
        color: white;
        padding: 4px 8px;
        border-radius: 3px;
        font-size: 12px;
        font-weight: bold;
        display: inline-block;
    }

    .status-resolved {
        background-color: #5cb85c;
        color: white;
        padding: 4px 8px;
        border-radius: 3px;
        font-size: 12px;
        font-weight: bold;
        display: inline-block;
    }

    /* 操作按钮样式 */
    .button {
        display: inline-block;
        padding: 6px 12px;
        margin-bottom: 0;
        font-size: 14px;
        font-weight: 400;
        line-height: 1.42857143;
        text-align: center;
        white-space: nowrap;
        vertical-align: middle;
        cursor: pointer;
        user-select: none;
        background-image: none;
        border: 1px solid transparent;
        border-radius: 4px;
        color: #fff;
        background-color: #337ab7;
        border-color: #2e6da4;
        text-decoration: none;
        transition: all 0.15s ease-in-out;
    }

    .button:hover {
        color: #fff;
        background-color: #286090;
        border-color: #204d74;
        text-decoration: none;
    }

    /* 添加新故障按钮样式 */
    .add-button {
        display: inline-block;
        padding: 8px 16px;
        background-color: #5cb85c;
        color: white;
        text-decoration: none;
        border-radius: 4px;
        font-weight: 500;
        transition: all 0.15s ease-in-out;
        border: 1px solid #4cae4c;
    }

    .add-button:hover {
        background-color: #4cae4c;
        border-color: #398439;
        color: white;
        text-decoration: none;
    }

    /* 操作区域样式 */
    .action-box {
        margin: 20px 0;
        padding: 15px 0;
        border-bottom: 1px solid #e9ecef;
    }

    /* DataTables样式优化 */
    .dataTables_wrapper {
        margin-top: 20px;
    }

    .dataTables_length,
    .dataTables_filter {
        margin-bottom: 15px;
    }

    .dataTables_length select,
    .dataTables_filter input {
        padding: 6px 12px;
        border-radius: 4px;
        border: 1px solid #ced4da;
        font-size: 14px;
    }

    .dataTables_filter input {
        margin-left: 8px;
    }

    /* 分页样式 */
    .dataTables_paginate {
        margin-top: 15px;
        text-align: right;
    }

    .dataTables_paginate .paginate_button {
        padding: 6px 12px;
        margin: 0 2px;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        cursor: pointer;
        background-color: #fff;
        color: #495057;
        text-decoration: none;
        transition: all 0.15s ease-in-out;
    }

    .dataTables_paginate .paginate_button.current {
        background-color: #007bff;
        color: white;
        border-color: #007bff;
    }

    .dataTables_paginate .paginate_button:hover:not(.current) {
        background-color: #e9ecef;
        border-color: #adb5bd;
    }

    /* 表格样式增强 */
    table.res {
        border-collapse: collapse;
        width: 100%;
        background-color: #fff;
        box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
        border-radius: 4px;
        overflow: hidden;
    }

    table.res th {
        background-color: #f8f9fa;
        color: #495057;
        font-weight: 600;
        text-align: center;
        padding: 12px 8px;
        border-bottom: 2px solid #dee2e6;
    }

    table.res td {
        padding: 10px 8px;
        text-align: center;
        border-bottom: 1px solid #dee2e6;
        vertical-align: middle;
    }

    table.res tr:hover {
        background-color: #f8f9fa;
    }

    /* 响应式表格 */
    .table-responsive {
        display: block;
        width: 100%;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
</style>

<div class="content">
    <div>
        <c:if test="${not empty success}">
            <div class="success-message" style="background-color: #dff0d8; color: #3c763d; padding: 15px; border-radius: 4px; margin-bottom: 20px;">
                <strong>Success!</strong> ${success}
            </div>
        </c:if>
        
        <section><span>Charge Point Fault Management</span></section>
        
        <div class="search-panel">
            <form:form action="${ctxPath}/manager/operations/faults" method="get" modelAttribute="faultForm">
                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="statusFilter">Status Filter:</label>
                            <select id="statusFilter" name="status" class="form-control">
                                <option value="">All</option>
                                <c:forEach items="${statuses}" var="status">
                                    <option value="${status}" ${status eq selectedStatus ? 'selected' : ''}>
                                        <c:choose>
                                            <c:when test="${status eq 'NEW'}">New</c:when>
                                            <c:when test="${status eq 'IN_PROGRESS'}">In Progress</c:when>
                                            <c:when test="${status eq 'RESOLVED'}">Resolved</c:when>
                                            <c:otherwise>${status.value()}</c:otherwise>
                                        </c:choose>
                                    </option>
                                </c:forEach>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="chargeBoxFilter">Charge Point:</label>
                            <select id="chargeBoxFilter" name="chargeBoxId" class="form-control">
                                <option value="">All</option>
                                <c:forEach items="${chargePoints}" var="cp">
                                    <option value="${cp.chargeBoxId}" ${cp.chargeBoxId eq selectedChargeBox ? 'selected' : ''}>
                                        ${cp.chargeBoxId}
                                    </option>
                                </c:forEach>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="errorCodeFilter">Error Code:</label>
                            <input type="text" id="errorCodeFilter" name="errorCode" value="${selectedErrorCode}" class="form-control" placeholder="Enter error code" />
                        </div>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="reportTypeFilter">Report Type:</label>
                            <select id="reportTypeFilter" name="reportType" class="form-control">
                                <option value="">All</option>
                                <option value="auto" ${selectedReportType eq 'auto' ? 'selected' : ''}>Automatic</option>
                                <option value="manual" ${selectedReportType eq 'manual' ? 'selected' : ''}>Manual</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="startDate">Start Date:</label>
                            <input type="date" id="startDate" name="startDate" value="${startDate}" class="form-control" />
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="endDate">End Date:</label>
                            <input type="date" id="endDate" name="endDate" value="${endDate}" class="form-control" />
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            <div class="btn-group">
                                <button type="submit" class="btn btn-primary">Search</button>
                                <a href="${ctxPath}/manager/operations/faults" class="btn btn-secondary">Reset</a>
                            </div>
                        </div>
                    </div>
                </div>
            </form:form>
        </div>
        
        <div class="action-box">
            <a href="${ctxPath}/manager/operations/report-fault" class="add-button">Report New Fault</a>
        </div>
    </div>

    <div class="table-responsive">
        <table class="res" id="faultTable">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Charge Point</th>
                    <th>Report Time</th>
                    <th>Current Status</th>
                    <th>Error Code</th>
                    <th>Report Type</th>
                    <th>Reporter</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
            <c:forEach items="${issues}" var="issue">
                <tr>
                    <td>${issue.issueId}</td>
                    <td>
                        <a href="${ctxPath}/manager/chargepoints/details/${issue.chargeBoxPk}">${issue.chargeBoxId}</a>
                    </td>
                    <td><fmt:formatDate value="${issue.reportTime}" pattern="yyyy-MM-dd HH:mm:ss" /></td>
                    <td>
                        <c:choose>
                            <c:when test="${issue.status eq 'NEW'}"><span class="status-new">New</span></c:when>
                            <c:when test="${issue.status eq 'IN_PROGRESS'}"><span class="status-in-progress">In Progress</span></c:when>
                            <c:when test="${issue.status eq 'RESOLVED'}"><span class="status-resolved">Resolved</span></c:when>
                            <c:otherwise>${issue.status}</c:otherwise>
                        </c:choose>
                    </td>
                    <td>${issue.ocppErrorCode}</td>
                    <td>${issue.isAutoReported ? 'Automatic' : 'Manual'}</td>
                    <td>${issue.reporterUsername}</td>
                    <td>
                        <a href="${ctxPath}/manager/operations/update-fault/${issue.issueId}" class="button">Update</a>
                    </td>
                </tr>
            </c:forEach>
            </tbody>
        </table>
    </div>
</div>

<%@ include file="00-footer.jsp" %> 