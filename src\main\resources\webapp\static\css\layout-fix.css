/* 新的布局样式，实现左侧垂直导航+顶部水平导航+内容区的三区布局 */

/* 基础布局样式 */
body {
    margin: 0;
    padding: 0;
    background-color: #f0f2f5;
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

.main {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    height: auto;
}

/* 顶部导航栏样式 */
.top-header {
    height: 50px;
    background-color: #1e1e2d;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 15px;
    box-shadow: 0 1px 4px rgba(0,0,0,.08);
    z-index: 10;
}

.top-header .logo {
    font-size: 18px;
    font-weight: bold;
    color: #fff;
    text-decoration: none;
}

.top-header .user-info {
    display: flex;
    align-items: center;
}

.top-header .user-info span {
    margin-right: 20px;
}

/* 主要内容区布局 */
.content-wrapper {
    display: flex;
    flex: 1;
    min-height: calc(100vh - 50px);
    height: auto;
}

/* 左侧垂直导航样式 */
.left-sidebar {
    width: 220px;
    background-color: #1e1e2d;
    color: #a2a3b7;
    min-height: 100%;
    flex-shrink: 0;
    box-shadow: 1px 0 4px rgba(0,0,0,.08);
    z-index: 9;
}

.left-sidebar .menu-title {
    padding: 15px 20px;
    font-size: 14px;
    text-transform: uppercase;
    color: #5c5e81;
    font-weight: bold;
    border-bottom: 1px solid rgba(255,255,255,.05);
}

.left-sidebar ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.left-sidebar ul li {
    position: relative;
}

.left-sidebar ul li a {
    display: block;
    padding: 12px 20px;
    color: #a2a3b7;
    text-decoration: none;
    transition: all .3s;
    border-left: 3px solid transparent;
}

.left-sidebar ul li a:hover, 
.left-sidebar ul li a.active {
    color: #fff;
    background-color: rgba(255,255,255,.05);
    border-left-color: #3699ff;
}

.left-sidebar ul li a i {
    margin-right: 8px;
    width: 18px;
    text-align: center;
}

.left-sidebar ul li.submenu > a:after {
    content: "▼";
    font-size: 10px;
    float: right;
    margin-top: 3px;
}

.left-sidebar ul li.submenu > ul {
    display: none;
    background-color: rgba(0,0,0,.2);
}

.left-sidebar ul li.submenu > ul > li > a {
    padding-left: 40px;
}

.left-sidebar ul li.submenu.open > a {
    color: #fff;
    background-color: rgba(255,255,255,.05);
}

.left-sidebar ul li.submenu.open > ul {
    display: block;
}

/* 主内容区样式 */
.main-content {
    flex: 1;
    padding: 20px;
    background-color: #f0f2f5;
    min-height: 100%;
    height: auto;
    width: calc(100vw - 260px);
    overflow-x: hidden;
}

.breadcrumb {
    background-color: transparent;
    padding: 0 0 15px 0;
    margin-bottom: 20px;
    border-bottom: 1px solid #ebedf2;
    color: #6c7293;
}

.breadcrumb a {
    color: #6c7293;
    text-decoration: none;
}

.breadcrumb a:hover {
    color: #3699ff;
}

.breadcrumb .separator {
    margin: 0 8px;
}

.content-container {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 0 20px 0 rgba(76,87,125,.02);
    padding: 25px;
    min-height: calc(100vh - 150px);
    height: auto;
    word-wrap: break-word;
    overflow: hidden;
}

.page-title {
    font-size: 18px;
    font-weight: 500;
    color: #181c32;
    margin-bottom: 20px;
}

/* 表格自适应样式 */
.table-responsive {
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

table {
    width: 100%;
    table-layout: auto;
}

/* 响应式设计 */
@media (max-width: 991px) {
    .left-sidebar {
        width: 80px;
    }
    
    .left-sidebar ul li a {
        text-align: center;
        padding: 15px 10px;
    }
    
    .left-sidebar ul li a i {
        margin-right: 0;
        font-size: 18px;
    }
    
    .left-sidebar ul li a span, 
    .left-sidebar ul li a:after,
    .left-sidebar .menu-title {
        display: none;
    }
    
    .left-sidebar ul li.submenu > ul > li > a {
        padding-left: 10px;
    }
    
    .main-content {
        width: calc(100vw - 120px);
    }
}

/* 
   布局修复CSS文件
   用于修改OCPP v1.6下页面的布局为居左对齐
*/

/* 修改表格居左对齐 */
.op16-content table.userInput {
    width: auto;
    margin-left: 0;
    margin-bottom: 30px; /* 增加表格底部间距 */
}

/* 修改Select All/None按钮区域和表单元素对齐 */
.op16-content table.userInput td:first-child {
    text-align: left;
    width: auto;
    padding-right: 20px; /* 增加右侧间距 */
    padding-top: 10px;   /* 增加顶部间距 */
    padding-bottom: 10px; /* 增加底部间距 */
    vertical-align: middle;
}

/* 调整表单元素宽度和间距 */
.op16-content select, 
.op16-content input[type="text"], 
.op16-content input[type="number"], 
.op16-content input[type="password"] {
    width: 350px; /* 增加宽度 */
    padding: 8px; /* 增加内边距 */
    margin: 8px 0; /* 增加上下外边距 */
}

/* 提交按钮左对齐并增加间距 */
.op16-content .submit-button {
    text-align: left;
    margin-top: 30px; /* 增加顶部间距 */
    margin-bottom: 20px; /* 增加底部间距 */
}

/* 提交按钮样式优化 */
.op16-content input[type="submit"] {
    padding: 8px 20px; /* 增加按钮内边距 */
    font-size: 14px; /* 增加字体大小 */
    height: auto; /* 自动高度 */
}

/* 表格行高增加 */
.op16-content table.userInput tr {
    height: 50px; /* 增加行高 */
}

/* 增加表单组之间的间距 */
.op16-content section {
    margin-top: 25px; /* 增加段落之间的距离 */
    margin-bottom: 25px;
}

/* 增加选择框高度 */
.op16-content select {
    height: 36px; /* 增加选择框高度 */
}

/* 增加多选框的高度 */
.op16-content select[multiple] {
    height: auto;
    min-height: 150px; /* 增加多选框高度 */
}

/* 增加表单元素的描述文字间距 */
.op16-content form small,
.op16-content form .hint {
    display: block;
    margin-top: 5px;
    margin-bottom: 15px;
}

/* 增加表单元素间的垂直间距 */
.op16-content table.userInput td {
    padding-top: 10px;
    padding-bottom: 10px;
}

/* 
   Fault Management页面样式修复
   解决分页导航区域被挤压的问题
*/

/* 调整DataTables底部区域的布局 */
.dataTables_wrapper .dataTables_info {
    clear: both;
    float: left;
    padding-top: 20px;
}

/* 分页导航区域样式调整 */
.dataTables_wrapper .dataTables_paginate {
    float: right;
    text-align: right;
    padding-top: 20px;
    margin-bottom: 15px;
    clear: right;
}

/* 确保分页按钮有足够的间距 */
.dataTables_wrapper .dataTables_paginate .paginate_button {
    margin: 0 5px;
    padding: 8px 12px;
}

/* 为底部信息提供更多空间 */
#faultTable_wrapper {
    margin-bottom: 30px;
}

/* 在表格和分页之间添加额外的空间 */
#faultTable {
    margin-bottom: 15px;
}

/* 创建足够的底部空间 */
.content {
    padding-bottom: 50px;
}

/* 优化搜索和长度选择器的位置 */
.dataTables_wrapper .dataTables_length {
    float: left;
    margin-bottom: 15px;
}

.dataTables_wrapper .dataTables_filter {
    float: right;
    text-align: right;
    margin-bottom: 15px;
}

/* 确保页面底部有足够空间 */
#faultTable_wrapper::after {
    content: "";
    display: block;
    clear: both;
    height: 30px;
} 