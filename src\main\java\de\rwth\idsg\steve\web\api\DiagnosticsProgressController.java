/*
 * SteV<PERSON> - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.web.api;

import de.rwth.idsg.steve.service.DiagnosticFileDownloadService;
import de.rwth.idsg.steve.service.DiagnosticsProgressTracker;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * REST API for GetDiagnostics progress tracking
 */
@Slf4j
@RestController
@RequestMapping("/steve/api/diagnostics")
public class DiagnosticsProgressController {

    @Autowired
    private DiagnosticsProgressTracker progressTracker;

    @Autowired
    private DiagnosticFileDownloadService downloadService;
    
    /**
     * Get progress for a specific charge point
     */
    @GetMapping("/progress/{chargeBoxId}")
    public ResponseEntity<Map<String, Object>> getProgress(@PathVariable String chargeBoxId) {
        DiagnosticsProgressTracker.DiagnosticsProgress progress = progressTracker.getProgress(chargeBoxId);
        
        if (progress == null) {
            return ResponseEntity.notFound().build();
        }
        
        Map<String, Object> response = new HashMap<>();
        response.put("chargeBoxId", progress.getChargeBoxId());
        response.put("percentage", progress.getPercentage());
        response.put("status", progress.getStatus());
        response.put("message", progress.getMessage());
        response.put("elapsedTime", progress.getElapsedTime());
        response.put("startTime", progress.getStartTime());
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * Get progress for all charge points
     */
    @GetMapping("/progress")
    public ResponseEntity<Map<String, Object>> getAllProgress() {
        Map<String, DiagnosticsProgressTracker.DiagnosticsProgress> allProgress = progressTracker.getAllProgress();
        
        Map<String, Object> response = new HashMap<>();
        for (Map.Entry<String, DiagnosticsProgressTracker.DiagnosticsProgress> entry : allProgress.entrySet()) {
            DiagnosticsProgressTracker.DiagnosticsProgress progress = entry.getValue();
            Map<String, Object> progressData = new HashMap<>();
            progressData.put("chargeBoxId", progress.getChargeBoxId());
            progressData.put("percentage", progress.getPercentage());
            progressData.put("status", progress.getStatus());
            progressData.put("message", progress.getMessage());
            progressData.put("elapsedTime", progress.getElapsedTime());
            progressData.put("startTime", progress.getStartTime());
            
            response.put(entry.getKey(), progressData);
        }
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * Check if tracking exists for a charge point
     */
    @GetMapping("/tracking/{chargeBoxId}")
    public ResponseEntity<Map<String, Object>> isTracking(@PathVariable String chargeBoxId) {
        boolean tracking = progressTracker.isTracking(chargeBoxId);
        
        Map<String, Object> response = new HashMap<>();
        response.put("chargeBoxId", chargeBoxId);
        response.put("tracking", tracking);
        
        return ResponseEntity.ok(response);
    }

    /**
     * Clear progress tracking for a specific charge point (allows repeat requests)
     */
    @DeleteMapping("/progress/{chargeBoxId}")
    public ResponseEntity<Map<String, Object>> clearProgress(@PathVariable String chargeBoxId) {
        progressTracker.clearTracking(chargeBoxId);

        Map<String, Object> response = new HashMap<>();
        response.put("chargeBoxId", chargeBoxId);
        response.put("message", "Progress tracking cleared");

        return ResponseEntity.ok(response);
    }

    /**
     * Clear all progress tracking
     */
    @DeleteMapping("/progress")
    public ResponseEntity<Map<String, Object>> clearAllProgress() {
        progressTracker.clearAllTracking();

        Map<String, Object> response = new HashMap<>();
        response.put("message", "All progress tracking cleared");

        return ResponseEntity.ok(response);
    }

    /**
     * Download diagnostic file for a specific charge point
     */
    @GetMapping("/download/{chargeBoxId}")
    public ResponseEntity<Resource> downloadDiagnosticFile(@PathVariable String chargeBoxId) {
        log.info("Download request received for charge point: {}", chargeBoxId);

        if (!downloadService.hasDownloadableFile(chargeBoxId)) {
            log.warn("No downloadable file found for charge point: {}", chargeBoxId);
            return ResponseEntity.notFound().build();
        }

        ResponseEntity<Resource> response = downloadService.downloadFile(chargeBoxId);

        // 如果下载成功，启动清理任务
        if (response.getStatusCode().is2xxSuccessful()) {
            // 异步清理文件
            new Thread(() -> {
                try {
                    // 等待一小段时间确保下载完成
                    Thread.sleep(2000);
                    downloadService.cleanupAfterDownload(chargeBoxId);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.warn("Cleanup task interrupted for charge point: {}", chargeBoxId);
                }
            }).start();
        }

        return response;
    }

    /**
     * Check if a diagnostic file is available for download
     */
    @GetMapping("/download-status/{chargeBoxId}")
    public ResponseEntity<Map<String, Object>> getDownloadStatus(@PathVariable String chargeBoxId) {
        Map<String, Object> response = new HashMap<>();
        response.put("chargeBoxId", chargeBoxId);
        response.put("hasDownloadableFile", downloadService.hasDownloadableFile(chargeBoxId));
        response.put("filePath", downloadService.getDownloadableFilePath(chargeBoxId));

        return ResponseEntity.ok(response);
    }

    /**
     * Get all downloadable files
     */
    @GetMapping("/downloadable-files")
    public ResponseEntity<Map<String, Object>> getAllDownloadableFiles() {
        Map<String, Object> response = new HashMap<>();
        response.put("files", downloadService.getAllDownloadableFiles());

        return ResponseEntity.ok(response);
    }

    /**
     * Test endpoint to manually trigger file ready status (for debugging)
     */
    @PostMapping("/test/mark-file-ready/{chargeBoxId}")
    public ResponseEntity<Map<String, Object>> testMarkFileReady(@PathVariable String chargeBoxId) {
        log.info("🧪 Test: Marking file ready for charge point: {}", chargeBoxId);

        // Simulate file ready status
        downloadService.registerDownloadableFile(chargeBoxId, "/test/path/test_file.zip");

        Map<String, Object> response = new HashMap<>();
        response.put("chargeBoxId", chargeBoxId);
        response.put("message", "File marked as ready for testing");

        return ResponseEntity.ok(response);
    }
}
