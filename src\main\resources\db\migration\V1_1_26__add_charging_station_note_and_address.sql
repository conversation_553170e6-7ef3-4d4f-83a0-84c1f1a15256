-- 为charging_station表添加note字段和address_pk字段
ALTER TABLE `charging_station` 
ADD COLUMN `note` TEXT NULL COMMENT '充电站备注信息' AFTER `location`,
ADD COLUMN `address_pk` INT NULL COMMENT '地址外键' AFTER `note`,
ADD INDEX `idx_charging_station_address` (`address_pk`),
ADD CONSTRAINT `FK_charging_station_address` 
    FOREIGN KEY (`address_pk`) 
    REFERENCES `address` (`address_pk`) 
    ON DELETE SET NULL 
    ON UPDATE NO ACTION;
