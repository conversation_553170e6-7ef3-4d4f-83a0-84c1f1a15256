/**
 * Navigation Layout Styles
 * Improved navigation bar layout
 */

/* Top menu styling */
.top-menu {
  background-color: #2c3e50 !important;
  height: 50px !important;
  display: flex !important;
  align-items: center !important;
  padding: 0 !important;
  width: 100% !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  z-index: 1000 !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

/* Main content adjustment for fixed navigation */
.main-wrapper {
  padding-top: 50px !important;
}

/* Navigation menu */
ul.navigation {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  width: 100% !important;
  max-width: 1200px !important;
  margin: 0 auto !important;
  padding: 0 !important;
  list-style: none !important;
}

ul.navigation li {
  margin: 0 !important;
  padding: 0 !important;
  position: relative !important;
}

ul.navigation li a {
  padding: 0 20px !important;
  line-height: 50px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  letter-spacing: 0.5px !important;
  color: #f8f9fa !important;
  text-decoration: none !important;
  display: block !important;
  transition: background-color 0.3s !important;
}

ul.navigation li a:hover,
ul.navigation li a:focus {
  background-color: rgba(255, 255, 255, 0.1) !important;
  color: #fff !important;
}

/* Dropdown styling */
ul.navigation ul {
  position: absolute !important;
  top: 50px !important;
  left: 0 !important;
  width: 200px !important;
  background-color: #2c3e50 !important;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175) !important;
  visibility: hidden !important;
  opacity: 0 !important;
  transition: opacity 0.3s, visibility 0.3s !important;
  z-index: 1001 !important;
  border-radius: 0 0 4px 4px !important;
}

ul.navigation li:hover > ul {
  visibility: visible !important;
  opacity: 1 !important;
}

ul.navigation ul li {
  width: 100% !important;
}

ul.navigation ul li a {
  padding: 10px 20px !important;
  line-height: 1.5 !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05) !important;
}

/* Content area adjustment */
.content {
  margin-top: 30px !important;
}