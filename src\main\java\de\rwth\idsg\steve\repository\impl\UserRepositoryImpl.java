/*
 * SteV<PERSON> - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.repository.impl;

import de.rwth.idsg.steve.SteveException;
import de.rwth.idsg.steve.repository.AddressRepository;
import de.rwth.idsg.steve.repository.UserRepository;
import de.rwth.idsg.steve.repository.WebUserRepository;
import de.rwth.idsg.steve.repository.dto.User;
import de.rwth.idsg.steve.service.WebUserService;
import de.rwth.idsg.steve.web.dto.Address;
import de.rwth.idsg.steve.web.dto.UserDTO;
import de.rwth.idsg.steve.web.dto.UserForm;
import de.rwth.idsg.steve.web.dto.UserQueryForm;
import de.rwth.idsg.steve.web.dto.UserRole;
import jooq.steve.db.tables.records.AddressRecord;
import jooq.steve.db.tables.records.UserRecord;
import jooq.steve.db.tables.records.WebUserRecord;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.jooq.Field;
import org.jooq.JoinType;
import org.jooq.Record1;
import org.jooq.Record7;
import org.jooq.Result;
import org.jooq.SelectConditionStep;
import org.jooq.SelectQuery;
import org.jooq.exception.DataAccessException;
import org.jooq.impl.DSL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import com.neovisionaries.i18n.CountryCode;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static de.rwth.idsg.steve.utils.CustomDSL.includes;
import static jooq.steve.db.tables.OcppTag.OCPP_TAG;
import static jooq.steve.db.tables.User.USER;
import static jooq.steve.db.tables.WebUser.WEB_USER;
import static jooq.steve.db.tables.Address.ADDRESS;
import static org.jooq.impl.DSL.trueCondition;

/**
 * <AUTHOR> Goekay <<EMAIL>>
 * @since 25.11.2015
 */
@Slf4j
@Repository
public class UserRepositoryImpl implements UserRepository {

    @Autowired private DSLContext ctx;
    @Autowired private AddressRepository addressRepository;
    @Autowired private WebUserService webUserService;
    @Autowired private WebUserRepository webUserRepository;

    @Override
    public List<User.Overview> getOverview(UserQueryForm form) {
        return getOverviewInternal(form)
                .map(r -> User.Overview.builder()
                                       .userPk(r.value1())
                                       .ocppTagPk(r.value2())
                                       .ocppIdTag(r.value3())
                                       .name(r.value4() + " " + r.value5())
                                       .role(getUserRole(r.value1()))
                                       .phone(r.value6())
                                       .email(r.value7())
                                       .build()
                );
    }

    @Override
    public User.Details getDetails(int userPk) {

        // -------------------------------------------------------------------------
        // 1. user table
        // -------------------------------------------------------------------------

        UserRecord ur = ctx.selectFrom(USER)
                           .where(USER.USER_PK.equal(userPk))
                           .fetchOne();

        if (ur == null) {
            throw new SteveException("There is no user with id '%s'", userPk);
        }

        // -------------------------------------------------------------------------
        // 2. address table
        // -------------------------------------------------------------------------

        AddressRecord ar = addressRepository.get(ctx, ur.getAddressPk());

        // -------------------------------------------------------------------------
        // 3. ocpp_tag table
        // -------------------------------------------------------------------------

        String ocppIdTag = null;
        if (ur.getOcppTagPk() != null) {
            Record1<String> record = ctx.select(OCPP_TAG.ID_TAG)
                                        .from(OCPP_TAG)
                                        .where(OCPP_TAG.OCPP_TAG_PK.eq(ur.getOcppTagPk()))
                                        .fetchOne();

            if (record != null) {
                ocppIdTag = record.value1();
            }
        }

        return User.Details.builder()
                           .userRecord(ur)
                           .address(ar)
                           .ocppIdTag(Optional.ofNullable(ocppIdTag))
                           .build();
    }

    @Override
    public void add(UserForm form) {
        ctx.transaction(configuration -> {
            DSLContext ctx = DSL.using(configuration);
            try {
                Integer addressId = addressRepository.updateOrInsert(ctx, form.getAddress());
                addInternal(ctx, form, addressId);

            } catch (DataAccessException e) {
                throw new SteveException("Failed to add the user", e);
            }
        });
    }

    @Override
    public void update(UserForm form) {
        ctx.transaction(configuration -> {
            DSLContext ctx = DSL.using(configuration);
            try {
                Integer addressId = addressRepository.updateOrInsert(ctx, form.getAddress());
                updateInternal(ctx, form, addressId);

            } catch (DataAccessException e) {
                throw new SteveException("Failed to update the user", e);
            }
        });
    }

    @Override
    public void delete(int userPk) {
        ctx.transaction(configuration -> {
            DSLContext ctx = DSL.using(configuration);
            try {
                log.debug("Deleting user PK={}, including associated web_user and charge box assignments", userPk);

                // 1. Find and delete associated web_user record
                WebUserRecord associatedWebUser = webUserService.findWebUserByUserPk(userPk);
                if (associatedWebUser != null) {
                    log.debug("Found associated web_user: {}, preparing to delete", associatedWebUser.getUsername());

                    // Delete web_user record (foreign key constraints will automatically handle charge box assignments)
                    webUserService.deleteUser(associatedWebUser.getWebUserPk());
                    log.info("Deleted associated web_user: {} (charge box assignments automatically cleaned up by FK constraints)", associatedWebUser.getUsername());
                } else {
                    log.debug("User PK={} has no associated web_user record", userPk);
                }

                // 2. Delete address record
                addressRepository.delete(ctx, selectAddressId(userPk));

                // 3. Delete user record
                deleteInternal(ctx, userPk);

                log.info("Successfully deleted user PK={} and all associated data", userPk);

            } catch (DataAccessException e) {
                log.error("Failed to delete user PK={}: {}", userPk, e.getMessage(), e);
                throw new SteveException("Failed to delete user: " + e.getMessage(), e);
            }
        });
    }

    @Override
    public List<UserDTO> getUsers() {
        return ctx.selectFrom(USER)
                .fetch()
                .map(r -> {
                    // 获取用户对应的地址信息
                    jooq.steve.db.tables.records.AddressRecord addressRecord = null;
                    if (r.getAddressPk() != null) {
                        addressRecord = ctx.selectFrom(ADDRESS)
                                        .where(ADDRESS.ADDRESS_PK.eq(r.getAddressPk()))
                                        .fetchOne();
                    }
                    
                    return UserDTO.builder()
                        .userPk(r.getUserPk())
                        .firstName(r.getFirstName())
                        .lastName(r.getLastName())
                        .phone(r.getPhone())
                        .email(r.getEMail())
                        .note(r.getNote())
                        .address(addressRecord != null ? addressRecord.getStreet() : null)
                        .city(addressRecord != null ? addressRecord.getCity() : null)
                        .zipCode(addressRecord != null ? addressRecord.getZipCode() : null)
                        .country(addressRecord != null ? addressRecord.getCountry() : null)
                        .ocppTagPk(r.getOcppTagPk())
                        .build();
                });
    }

    @Override
    public UserForm getUser(int userPk) {
        return ctx.selectFrom(USER)
                .where(USER.USER_PK.eq(userPk))
                .fetchOne(r -> {
                    UserForm form = new UserForm();
                    form.setUserPk(r.getUserPk());
                    form.setOcppIdTag(String.valueOf(r.getOcppTagPk()));
                    form.setFirstName(r.getFirstName());
                    form.setLastName(r.getLastName());
                    form.setBirthDay(r.getBirthDay());
                    form.setPhone(r.getPhone());
                    form.setEMail(r.getEMail());
                    form.setNote(r.getNote());
                    
                    // 获取用户对应的地址信息
                    jooq.steve.db.tables.records.AddressRecord addressRecord = null;
                    if (r.getAddressPk() != null) {
                        addressRecord = ctx.selectFrom(ADDRESS)
                                        .where(ADDRESS.ADDRESS_PK.eq(r.getAddressPk()))
                                        .fetchOne();
                    }
                    
                    Address address = new Address();
                    if (addressRecord != null) {
                        address.setStreet(addressRecord.getStreet());
                        address.setCity(addressRecord.getCity());
                        address.setZipCode(addressRecord.getZipCode());
                        address.setCountry(addressRecord.getCountry() != null ? CountryCode.getByCode(addressRecord.getCountry()) : null);
                    }
                    form.setAddress(address);
                    
                    return form;
                });
    }

    @Override
    public void addEnterpriseUser(UserForm form) {
        this.add(form);
    }

    @Override
    public void addCarUser(UserForm form) {
        this.add(form);
    }

    @Override
    public void updateUser(UserForm form) {
        this.update(form);
    }

    @Override
    public void deleteUser(int userPk) {
        this.delete(userPk);
    }

    @Override
    public List<Integer> getUserIdsByName(String userName) {
        return ctx.select(USER.USER_PK)
                .from(USER)
                .where(DSL.concat(USER.FIRST_NAME, DSL.val(" "), USER.LAST_NAME).contains(userName))
                .fetch(USER.USER_PK);
    }

    @Override
    public Integer getWebUserId(String userName) {
        Record1<Integer> record = ctx.select(WEB_USER.WEB_USER_PK)
                .from(WEB_USER)
                .where(WEB_USER.USERNAME.eq(userName))
                .fetchOne();
        return record != null ? record.value1() : null;
    }

    @Override
    public String getUserName(int userPk) {
        Record1<String> record = ctx.select(DSL.concat(USER.FIRST_NAME, DSL.val(" "), USER.LAST_NAME))
                .from(USER)
                .where(USER.USER_PK.eq(userPk))
                .fetchOne();
        return record != null ? record.value1() : null;
    }

    // -------------------------------------------------------------------------
    // Private helpers
    // -------------------------------------------------------------------------

    @SuppressWarnings("unchecked")
    private Result<Record7<Integer, Integer, String, String, String, String, String>> getOverviewInternal(UserQueryForm form) {
        SelectConditionStep<Record7<Integer, Integer, String, String, String, String, String>> query = 
            ctx.select(
                USER.USER_PK,
                USER.OCPP_TAG_PK,
                OCPP_TAG.ID_TAG,
                USER.FIRST_NAME,
                USER.LAST_NAME,
                USER.PHONE,
                USER.E_MAIL
            )
            .from(USER)
            .leftOuterJoin(OCPP_TAG)
            .on(USER.OCPP_TAG_PK.eq(OCPP_TAG.OCPP_TAG_PK))
            .where(trueCondition());

        if (form.isSetUserPk()) {
            query = query.and(USER.USER_PK.eq(form.getUserPk()));
        }

        if (form.isSetOcppIdTag()) {
            String value = form.getOcppIdTag().replaceAll("\\s+", "%");
            query = query.and(OCPP_TAG.ID_TAG.like("%" + value + "%"));
        }

        if (form.isSetEmail()) {
            String value = form.getEmail().replaceAll("\\s+", "%");
            query = query.and(USER.E_MAIL.like("%" + value + "%"));
        }

        if (form.isSetName()) {
            // Concatenate the two columns and search within the resulting representation
            // for flexibility, since the user can search by first or last name, or both.
            Field<String> joinedField = DSL.concat(USER.FIRST_NAME, USER.LAST_NAME);
            String value = form.getName().replaceAll("\\s+", "%");
            
            // Find a matching sequence anywhere within the concatenated representation
            query = query.and(joinedField.like("%" + value + "%"));
        }

        return query.fetch();
    }

    private SelectConditionStep<Record1<Integer>> selectAddressId(int userPk) {
        return ctx.select(USER.ADDRESS_PK)
                  .from(USER)
                  .where(USER.USER_PK.eq(userPk));
    }

    private SelectConditionStep<Record1<Integer>> selectOcppTagPk(String ocppIdTag) {
        return ctx.select(OCPP_TAG.OCPP_TAG_PK)
                  .from(OCPP_TAG)
                  .where(OCPP_TAG.ID_TAG.eq(ocppIdTag));
    }

    private void addInternal(DSLContext ctx, UserForm form, Integer addressPk) {
        // 1. 创建普通用户
        int userPk = ctx.insertInto(USER)
                       .set(USER.FIRST_NAME, form.getFirstName())
                       .set(USER.LAST_NAME, form.getLastName())
                       .set(USER.BIRTH_DAY, form.getBirthDay())
                       .set(USER.SEX, form.getSex().getDatabaseValue())
                       .set(USER.PHONE, form.getPhone())
                       .set(USER.E_MAIL, form.getEMail())
                       .set(USER.NOTE, form.getNote())
                       .set(USER.ADDRESS_PK, addressPk)
                       .set(USER.OCPP_TAG_PK, selectOcppTagPk(form.getOcppIdTag()))
                       .returning(USER.USER_PK)
                       .fetchOne()
                       .getUserPk();

        if (userPk <= 0) {
            throw new SteveException("Failed to insert the user");
        }

        log.debug("Created user with PK={}", userPk);

        // 2. 只有当提供了username和password时，才创建web用户并建立关联
        if (form.getUsername() != null && !form.getUsername().isEmpty() &&
            form.getPassword() != null && !form.getPassword().isEmpty()) {

            try {
                // 直接在同一个事务中创建web用户并建立关联
                createWebUserWithAssociation(ctx, form, userPk);
            } catch (Exception e) {
                log.error("Error creating web user and association for {}: {}",
                         form.getUsername(), e.getMessage(), e);
                // 不要因为web用户创建失败而影响普通用户的创建
            }
        }
    }

    private void updateInternal(DSLContext ctx, UserForm form, Integer addressPk) {
        // 保存旧的用户信息，以检查username是否有变化
        UserRecord oldUser = null;
        if (form.getUserPk() != null) {
            oldUser = ctx.selectFrom(USER)
                         .where(USER.USER_PK.eq(form.getUserPk()))
                         .fetchOne();
        }

        log.debug("Updating user with PK={}: firstName={}, lastName={}", 
                 form.getUserPk(), form.getFirstName(), form.getLastName());

        // 1. 更新普通用户
        ctx.update(USER)
           .set(USER.FIRST_NAME, form.getFirstName())
           .set(USER.LAST_NAME, form.getLastName())
           .set(USER.BIRTH_DAY, form.getBirthDay())
           .set(USER.SEX, form.getSex().getDatabaseValue())
           .set(USER.PHONE, form.getPhone())
           .set(USER.E_MAIL, form.getEMail())
           .set(USER.NOTE, form.getNote())
           .set(USER.ADDRESS_PK, addressPk)
           .set(USER.OCPP_TAG_PK, selectOcppTagPk(form.getOcppIdTag()))
           .where(USER.USER_PK.eq(form.getUserPk()))
           .execute();

        // 2. 如果有username，则尝试更新web用户
        if (form.getUsername() != null && !form.getUsername().isEmpty()) {
            log.debug("Updating web user with username={}", form.getUsername());

            // 检查web_user是否存在
            boolean webUserExists = webUserService.userExists(form.getUsername());

            if (webUserExists) {
                // 更新现有的web_user
                try {
                    if (form.getUserRole() != null) {
                        log.debug("Updating user role to {} for user {}", form.getUserRole(), form.getUsername());
                        webUserService.update(form);
                        log.info("User role updated successfully to {} for user {}", form.getUserRole(), form.getUsername());
                    }

                    // 如果提供了新密码，则更新密码
                    if (form.getPassword() != null && !form.getPassword().isEmpty()) {
                        log.debug("New password provided, updating password for user {}", form.getUsername());
                        webUserService.updatePassword(form);
                        log.info("Password updated successfully for user {}", form.getUsername());
                    } else {
                        log.debug("No new password provided, skipping password update for user {}", form.getUsername());
                    }

                    // 确保关联关系正确
                    Integer webUserPk = webUserRepository.getUserPkByUsername(form.getUsername());
                    if (webUserPk != null) {
                        Integer currentAssociatedUserPk = webUserRepository.getAssociatedUserPk(webUserPk);
                        if (currentAssociatedUserPk == null || !currentAssociatedUserPk.equals(form.getUserPk())) {
                            log.debug("Updating association: web_user_pk {} -> user_pk {}", webUserPk, form.getUserPk());
                            webUserService.associateWithUser(webUserPk, form.getUserPk());
                            log.info("Updated association for web_user {} to user_pk {}", form.getUsername(), form.getUserPk());
                        }
                    }

                } catch (Exception e) {
                    log.error("Error updating web user {}: {}", form.getUsername(), e.getMessage(), e);
                }
            } else if (form.getPassword() != null && !form.getPassword().isEmpty()) {
                // 如果web_user不存在但提供了密码，创建新的web_user
                log.debug("Web user {} does not exist, creating new one", form.getUsername());
                try {
                    createOrUpdateWebUser(form);

                    // 建立关联
                    Integer webUserPk = webUserRepository.getUserPkByUsername(form.getUsername());
                    if (webUserPk != null) {
                        webUserService.associateWithUser(webUserPk, form.getUserPk());
                        log.info("Created and associated new web_user {} with user_pk {}", form.getUsername(), form.getUserPk());
                    }
                } catch (Exception e) {
                    log.error("Error creating web user {}: {}", form.getUsername(), e.getMessage(), e);
                }
            }
        }
    }
    
    /**
     * 在同一事务中创建Web用户并建立关联
     */
    private void createWebUserWithAssociation(DSLContext ctx, UserForm form, int userPk) {
        if (form.getUsername() == null || form.getUsername().isEmpty()) {
            return;
        }

        log.debug("Creating web user {} and associating with user_pk {}", form.getUsername(), userPk);

        // 检查用户是否已存在
        boolean userExists = webUserService.userExists(form.getUsername());

        if (userExists) {
            log.warn("Web user {} already exists, updating instead", form.getUsername());
            webUserService.update(form);
        } else {
            // 创建新的web用户
            webUserService.add(form);
            log.debug("Created web user: {}", form.getUsername());
        }

        // 直接在数据库中建立关联，避免额外的查询
        int updatedRows = ctx.update(WEB_USER)
            .set(WEB_USER.USER_PK, userPk)
            .where(WEB_USER.USERNAME.eq(form.getUsername()))
            .execute();

        if (updatedRows > 0) {
            log.info("Successfully associated web_user {} with user_pk {}", form.getUsername(), userPk);
        } else {
            log.warn("Failed to associate web_user {} with user_pk {}", form.getUsername(), userPk);
        }
    }

    /**
     * 创建或更新Web用户（保留用于更新操作）
     */
    private void createOrUpdateWebUser(UserForm form) {
        if (form.getUsername() == null || form.getUsername().isEmpty()) {
            return;
        }

        try {
            // 检查用户是否存在
            boolean userExists = webUserService.userExists(form.getUsername());

            if (userExists) {
                // 更新用户
                webUserService.update(form);
            } else {
                // 创建新用户
                webUserService.add(form);
            }
        } catch (Exception e) {
            log.error("Failed to create/update web user: {}", e.getMessage(), e);
            // 不要因为web用户创建失败而影响普通用户的创建/更新
        }
    }

    private void deleteInternal(DSLContext ctx, int userPk) {
        ctx.delete(USER)
           .where(USER.USER_PK.equal(userPk))
           .execute();
    }

    // 获取用户角色的辅助方法
    private String getUserRole(int userPk) {
        try {
            // 使用新的直接关联方法查找关联的web_user
            WebUserRecord webUser = webUserService.findWebUserByUserPk(userPk);
            if (webUser != null) {
                return webUser.getUserRole().name();
            }
            return ""; // 如果没有关联的web_user，返回空字符串
        } catch (Exception e) {
            log.warn("Error getting user role for user_pk {}: {}", userPk, e.getMessage());
            return "";
        }
    }


}
