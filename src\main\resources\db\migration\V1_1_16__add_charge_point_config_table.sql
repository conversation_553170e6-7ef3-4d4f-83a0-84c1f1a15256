CREATE TABLE `charge_point_config` (
  `config_pk` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `charge_box_id` VARCHAR(255) NOT NULL,
  `max_voltage` INT(11) DEFAULT NULL,
  `min_voltage` INT(11) DEFAULT NULL,
  `max_current` INT(11) DEFAULT NULL,
  `module_quantity` INT(11) DEFAULT NULL,
  `module_type` VARCHAR(50) DEFAULT NULL,
  `app_version` VARCHAR(50) DEFAULT NULL,
  `cb1_voltage_k` INT(11) DEFAULT NULL,
  `cb1_voltage_b` INT(11) DEFAULT NULL,
  `cb1_current_k` INT(11) DEFAULT NULL,
  `cb1_current_b` INT(11) DEFAULT NULL,
  `cb1_version` VARCHAR(50) DEFAULT NULL,
  `cb1_gun1_mapping` INT(11) DEFAULT NULL,
  `cb1_gun2_mapping` INT(11) DEFAULT NULL,
  `cb2_voltage_k` INT(11) DEFAULT NULL,
  `cb2_voltage_b` INT(11) DEFAULT NULL,
  `cb2_current_k` INT(11) DEFAULT NULL,
  `cb2_current_b` INT(11) DEFAULT NULL,
  `cb2_version` VARCHAR(50) DEFAULT NULL,
  `cb2_gun1_mapping` INT(11) DEFAULT NULL,
  `cb2_gun2_mapping` INT(11) DEFAULT NULL,
  `last_updated` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`config_pk`),
  UNIQUE KEY `charge_box_id_UNIQUE` (`charge_box_id`),
  CONSTRAINT `FK_charge_box_id_config` FOREIGN KEY (`charge_box_id`) REFERENCES `charge_box` (`charge_box_id`) ON DELETE CASCADE ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci; 