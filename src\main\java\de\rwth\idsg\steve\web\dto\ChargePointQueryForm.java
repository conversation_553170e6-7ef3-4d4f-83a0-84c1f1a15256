/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.web.dto;

import com.google.common.base.Strings;
import de.rwth.idsg.steve.ocpp.OcppVersion;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR> Goekay <<EMAIL>>
 * @since 26.11.2015
 */
@Getter
@Setter
@ToString
public class ChargePointQueryForm {

    private String chargeBoxId;
    private String description;
    private String note;
    private OcppVersion ocppVersion;
    private QueryPeriodType heartbeatPeriod;
    private String firmwareVersion;
    private String firmwareUpdateTimestamp;
    private String lastUpgradeStatus;
    private String owner;
    private Integer chargingStationPk;

    /**
     * Init with sensible default values
     */
    public ChargePointQueryForm() {
        heartbeatPeriod = QueryPeriodType.ALL;
    }

    public boolean isSetOcppVersion() {
        return ocppVersion != null;
    }

    public boolean isSetDescription() {
        return description != null;
    }

    public boolean isSetChargeBoxId() {
        return chargeBoxId != null;
    }

    public boolean isSetNote() {
        return !Strings.isNullOrEmpty(note);
    }

    public boolean isSetFirmwareVersion() {
        return !Strings.isNullOrEmpty(firmwareVersion);
    }

    public boolean isSetFirmwareUpdateTimestamp() {
        return !Strings.isNullOrEmpty(firmwareUpdateTimestamp);
    }

    public boolean isSetLastUpgradeStatus() {
        return !Strings.isNullOrEmpty(lastUpgradeStatus);
    }

    public boolean isSetOwner() {
        return !Strings.isNullOrEmpty(owner);
    }

    public boolean isSetChargingStationPk() {
        return chargingStationPk != null;
    }

    @RequiredArgsConstructor
    public enum QueryPeriodType {
        ALL("All"),
        TODAY("Today"),
        YESTERDAY("Yesterday"),
        EARLIER("Earlier");

        @Getter private final String value;

        public static QueryPeriodType fromValue(String v) {
            for (QueryPeriodType c: QueryPeriodType.values()) {
                if (c.value.equals(v)) {
                    return c;
                }
            }
            throw new IllegalArgumentException(v);
        }
    }

}
