/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.utils;

import org.joda.time.LocalDateTime;
import org.jooq.Converter;

import java.sql.Timestamp;
import java.time.LocalDate;

/**
 * <AUTHOR> Goekay <<EMAIL>>
 * @since 22.03.2016
 */
public class LocalDateTimeConverter implements Converter<Timestamp, LocalDateTime> {

    @Override
    public LocalDateTime from(Timestamp timestamp) {
        if (timestamp == null) {
            return null;
        } else {
            return new LocalDateTime(timestamp.getTime());
        }
    }

    @Override
    public Timestamp to(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        } else {
            return new Timestamp(localDateTime.toDateTime().getMillis());
        }
    }

    @Override
    public Class<Timestamp> fromType() {
        return Timestamp.class;
    }

    @Override
    public Class<LocalDateTime> toType() {
        return LocalDateTime.class;
    }
} 