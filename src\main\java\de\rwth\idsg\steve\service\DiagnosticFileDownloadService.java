package de.rwth.idsg.steve.service;

import de.rwth.idsg.steve.ocpp.task.GetDiagnosticsTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.net.MalformedURLException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 诊断文件下载服务
 * 处理诊断文件的下载和清理
 */
@Slf4j
@Service
public class DiagnosticFileDownloadService {

    @Autowired
    private DiagnosticsProgressTracker progressTracker;

    @Autowired
    private DiagnosticsTaskManager diagnosticsTaskManager;

    @Autowired
    private ChargePointIdentityMappingService identityMappingService;

    // 存储可下载文件的映射：chargeBoxId -> 文件路径
    private final ConcurrentMap<String, String> downloadableFiles = new ConcurrentHashMap<>();

    /**
     * 注册一个可下载的诊断文件
     * 支持充电桩标识映射，处理OCPP标识和维护标识不匹配的情况
     */
    public void registerDownloadableFile(String chargeBoxId, String filePath) {
        // 使用映射服务解析正确的充电桩标识
        String resolvedChargeBoxId = resolveChargeBoxIdFromFile(chargeBoxId, filePath);

        downloadableFiles.put(resolvedChargeBoxId, filePath);
        log.info("📥 Registered downloadable diagnostic file for {} (resolved from {}): {}",
                resolvedChargeBoxId, chargeBoxId, filePath);

        // 验证文件是否存在
        File file = new File(filePath);
        if (file.exists()) {
            log.info("✅ File exists and is ready: {} (size: {} bytes)", file.getName(), file.length());
        } else {
            log.warn("⚠️ File does not exist: {}", filePath);
        }

        // 更新进度状态为文件可下载
        if (progressTracker != null) {
            progressTracker.markFileReadyForDownload(chargeBoxId, file.getName());
            log.info("📊 Updated progress status to 'File Ready' for charge point: {}", chargeBoxId);
        } else {
            log.warn("⚠️ Progress tracker is null, cannot update status for: {}", chargeBoxId);
        }

        // 通知DiagnosticsTaskManager文件已准备好
        log.info("🔧 DiagnosticFileDownloadService: diagnosticsTaskManager={}", diagnosticsTaskManager);
        if (diagnosticsTaskManager != null) {
            diagnosticsTaskManager.markFileReady(resolvedChargeBoxId, file.getName());
        } else {
            log.warn("⚠️ DiagnosticsTaskManager is null, cannot mark file ready");
        }

        // 通知对应的GetDiagnosticsTask任务完成
        // 记录所有等待的任务（用于调试）
        GetDiagnosticsTask.logAllPendingTasks();

        // 首先尝试使用解析后的充电桩ID
        GetDiagnosticsTask pendingTask = GetDiagnosticsTask.getPendingTask(resolvedChargeBoxId);
        if (pendingTask != null) {
            log.info("🎯 Found pending GetDiagnostics task for resolved ID {}, completing it now", resolvedChargeBoxId);
            pendingTask.completeTaskForChargeBox(resolvedChargeBoxId, file.getName());
        } else {
            log.warn("⚠️ No pending GetDiagnostics task found for resolved ID {}", resolvedChargeBoxId);

            // 如果解析后的ID没有找到任务，尝试使用原始ID
            if (!resolvedChargeBoxId.equals(chargeBoxId)) {
                log.info("🔍 Trying with original charge box ID: {}", chargeBoxId);
                GetDiagnosticsTask originalTask = GetDiagnosticsTask.getPendingTask(chargeBoxId);
                if (originalTask != null) {
                    log.info("🎯 Found pending GetDiagnostics task for original ID {}, completing it now", chargeBoxId);
                    originalTask.completeTaskForChargeBox(chargeBoxId, file.getName());
                } else {
                    log.warn("⚠️ No pending GetDiagnostics task found for original ID {} either", chargeBoxId);

                    // 如果还是找不到，尝试使用映射服务的反向查找
                    if (identityMappingService != null) {
                        String ocppId = identityMappingService.getOcppIdentity(resolvedChargeBoxId);
                        if (!ocppId.equals(resolvedChargeBoxId)) {
                            log.info("🔍 Trying with OCPP identity: {}", ocppId);
                            GetDiagnosticsTask ocppTask = GetDiagnosticsTask.getPendingTask(ocppId);
                            if (ocppTask != null) {
                                log.info("🎯 Found pending GetDiagnostics task for OCPP ID {}, completing it now", ocppId);
                                ocppTask.completeTaskForChargeBox(ocppId, file.getName());
                            } else {
                                log.warn("⚠️ No pending GetDiagnostics task found for OCPP ID {} either", ocppId);
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 获取指定充电桩的可下载文件路径
     */
    public String getDownloadableFilePath(String chargeBoxId) {
        return downloadableFiles.get(chargeBoxId);
    }

    /**
     * 检查是否有可下载的文件
     */
    public boolean hasDownloadableFile(String chargeBoxId) {
        String filePath = downloadableFiles.get(chargeBoxId);
        if (filePath == null) {
            return false;
        }
        
        File file = new File(filePath);
        return file.exists() && file.isFile();
    }

    /**
     * 下载文件并返回ResponseEntity
     */
    public ResponseEntity<Resource> downloadFile(String chargeBoxId) {
        String filePath = downloadableFiles.get(chargeBoxId);
        if (filePath == null) {
            log.warn("No downloadable file found for charge point: {}", chargeBoxId);
            return ResponseEntity.notFound().build();
        }

        try {
            Path file = Paths.get(filePath);
            Resource resource = new UrlResource(file.toUri());

            if (!resource.exists() || !resource.isReadable()) {
                log.warn("File not found or not readable: {}", filePath);
                downloadableFiles.remove(chargeBoxId);
                return ResponseEntity.notFound().build();
            }

            // 更新进度状态为下载中
            if (progressTracker != null) {
                progressTracker.markFileDownloading(chargeBoxId);
            }

            // 标记任务管理器中的文件已下载
            if (diagnosticsTaskManager != null) {
                diagnosticsTaskManager.markFileDownloaded(chargeBoxId);
            }

            String fileName = file.getFileName().toString();
            log.info("📤 Starting download of diagnostic file: {} for charge point: {}", fileName, chargeBoxId);

            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + fileName + "\"")
                    .body(resource);

        } catch (MalformedURLException e) {
            log.error("Invalid file path: {}", filePath, e);
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 下载完成后的清理工作
     */
    public void cleanupAfterDownload(String chargeBoxId) {
        String filePath = downloadableFiles.remove(chargeBoxId);
        if (filePath != null) {
            try {
                File file = new File(filePath);
                if (file.exists()) {
                    if (file.delete()) {
                        log.info("🗑️ Successfully deleted diagnostic file after download: {}", filePath);
                        
                        // 更新进度状态为下载完成
                        if (progressTracker != null) {
                            progressTracker.markDownloadCompleted(chargeBoxId, file.getName());
                        }
                    } else {
                        log.warn("Failed to delete diagnostic file: {}", filePath);
                    }
                } else {
                    log.warn("File to delete does not exist: {}", filePath);
                }
            } catch (Exception e) {
                log.error("Error deleting diagnostic file: {}", filePath, e);
            }
        }
    }

    /**
     * 获取所有可下载文件的信息
     */
    public ConcurrentMap<String, String> getAllDownloadableFiles() {
        return downloadableFiles;
    }

    /**
     * 清理指定充电桩的下载记录（不删除文件）
     */
    public void clearDownloadRecord(String chargeBoxId) {
        downloadableFiles.remove(chargeBoxId);
        log.info("🧹 Cleared download record for charge point: {}", chargeBoxId);
    }

    /**
     * 清理所有下载记录（不删除文件）
     */
    public void clearAllDownloadRecords() {
        downloadableFiles.clear();
        log.info("🧹 Cleared all download records");
    }

    /**
     * 强制删除文件（用于清理操作）
     */
    public boolean forceDeleteFile(String chargeBoxId) {
        String filePath = downloadableFiles.remove(chargeBoxId);
        if (filePath != null) {
            try {
                File file = new File(filePath);
                if (file.exists() && file.delete()) {
                    log.info("🗑️ Force deleted diagnostic file: {}", filePath);
                    return true;
                } else {
                    log.warn("Failed to force delete file or file does not exist: {}", filePath);
                    return false;
                }
            } catch (Exception e) {
                log.error("Error force deleting file: {}", filePath, e);
                return false;
            }
        }
        return false;
    }

    /**
     * 从文件路径解析正确的充电桩ID
     * 使用动态学习来处理标识不匹配的情况
     *
     * @param originalChargeBoxId 原始充电桩ID（可能来自文件名提取）
     * @param filePath 文件路径
     * @return 解析后的正确充电桩ID
     */
    private String resolveChargeBoxIdFromFile(String originalChargeBoxId, String filePath) {
        if (identityMappingService == null) {
            log.warn("⚠️ Identity mapping service not available, using original ID: {}", originalChargeBoxId);
            return originalChargeBoxId;
        }

        // 从文件路径中提取文件名
        String fileName = new File(filePath).getName();
        log.info("🔍 Resolving charge box ID from file: {} (original ID: {})", fileName, originalChargeBoxId);

        // 检查是否有活跃的诊断任务，找到对应的连接充电桩ID
        String connectedChargeBoxId = findConnectedChargeBoxId(fileName);

        if (connectedChargeBoxId != null) {
            // 使用动态学习的映射服务解析标识
            String resolvedId = identityMappingService.resolveChargePointIdentity(fileName, connectedChargeBoxId);
            log.info("🎓 Dynamic learning resolved: {} -> {} (file: {}, connected: {})",
                    originalChargeBoxId, resolvedId, fileName, connectedChargeBoxId);
            return resolvedId;
        } else {
            log.warn("⚠️ No connected charge box found for file: {}", fileName);

            // 如果找不到连接的充电桩，检查是否有已学习的映射
            String mappedId = identityMappingService.getDevOpsIdentity(originalChargeBoxId);
            if (!mappedId.equals(originalChargeBoxId)) {
                log.info("🔄 Using learned mapping: {} -> {} (file: {})",
                        originalChargeBoxId, mappedId, fileName);
                return mappedId;
            }
        }

        log.info("🔍 No mapping available, using original ID: {} (file: {})", originalChargeBoxId, fileName);
        return originalChargeBoxId;
    }

    /**
     * 查找与文件名匹配的连接充电桩ID
     * 通过检查活跃的诊断任务来确定哪个充电桩正在等待文件
     *
     * @param fileName 文件名
     * @return 连接的充电桩ID，如果找不到则返回null
     */
    private String findConnectedChargeBoxId(String fileName) {
        if (diagnosticsTaskManager == null) {
            return null;
        }

        // 从进度跟踪器获取所有正在跟踪的充电桩
        if (progressTracker != null) {
            for (String chargeBoxId : progressTracker.getAllTrackedChargePoints()) {
                // 检查该充电桩是否有活跃的诊断任务
                if (diagnosticsTaskManager.getValidTask(chargeBoxId) != null) {
                    log.debug("🔍 Found active diagnostics task for charge box: {}", chargeBoxId);
                    return chargeBoxId;
                }
            }
        }

        log.debug("🔍 No active diagnostics task found for file: {}", fileName);
        return null;
    }
}
