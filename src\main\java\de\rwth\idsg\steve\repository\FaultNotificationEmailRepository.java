/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.repository;

import de.rwth.idsg.steve.web.dto.FaultNotificationEmailDTO;
import de.rwth.idsg.steve.web.dto.FaultNotificationEmailForm;

import java.util.List;

/**
 * 故障通知邮箱仓库接口
 */
public interface FaultNotificationEmailRepository {

    /**
     * 获取所有通知邮箱
     */
    List<FaultNotificationEmailDTO> getAll();

    /**
     * 获取已启用的通知邮箱
     */
    List<FaultNotificationEmailDTO> getEnabled();

    /**
     * 通过ID获取通知邮箱
     */
    FaultNotificationEmailDTO getById(Integer id);

    /**
     * 添加通知邮箱
     */
    int add(FaultNotificationEmailForm form);

    /**
     * 更新通知邮箱
     */
    void update(FaultNotificationEmailForm form);

    /**
     * 删除通知邮箱
     */
    void delete(Integer id);

    /**
     * 更新最后通知时间
     */
    void updateLastNotifiedTime(Integer id);
} 