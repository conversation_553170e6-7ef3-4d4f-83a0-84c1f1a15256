<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GetDiagnostics 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .form-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .form-section h3 {
            margin-top: 0;
            color: #555;
        }
        
        /* 复制00-cp-multiple.jsp的样式 */
        .multi-select-container {
            position: relative;
            width: 100%;
            border: 1px solid #ccc;
            border-radius: 4px;
        }

        .selected-options {
            padding: 10px;
            cursor: pointer;
            min-height: 20px;
            line-height: 1.5;
            background-color: #fff;
        }

        .options-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            border: 1px solid #ccc;
            border-top: none;
            background-color: white;
            z-index: 1000;
            max-height: 200px;
            overflow-y: auto;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .search-input {
            width: calc(100% - 20px);
            padding: 8px 10px;
            border: 1px solid #eee;
            border-bottom: 1px solid #ccc;
            box-sizing: border-box;
        }

        .options-dropdown div {
            padding: 8px 10px;
            border-bottom: 1px solid #eee;
        }

        .options-dropdown div label {
            margin-left: 5px;
        }

        .options-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .options-list li {
            padding: 8px 10px;
            cursor: pointer;
        }

        .options-list li:hover {
            background-color: #f0f0f0;
        }

        .options-list li input[type="checkbox"] {
            margin-right: 8px;
        }

        .options-list li label {
            cursor: pointer;
        }
        
        .userInput {
            width: 100%;
            border-collapse: collapse;
        }
        
        .userInput td {
            padding: 8px;
            border: 1px solid #ddd;
        }
        
        .userInput input[type="text"] {
            width: 100%;
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 3px;
        }
        
        .submit-button {
            text-align: center;
            margin-top: 15px;
        }
        
        .submit-button input[type="submit"] {
            background-color: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        
        .submit-button input[type="submit"]:hover {
            background-color: #005a87;
        }
        
        .test-info {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>GetDiagnostics 功能测试</h1>
        
        <div class="test-info">
            <h3>测试说明</h3>
            <p>这个页面用于测试GetDiagnostics页面的"Click to select Charge Points"功能是否正常工作。</p>
            <p>最新修复内容：</p>
            <ul>
                <li>✅ 在Ocpp16Controller中添加了GetDiagnostics的GET方法</li>
                <li>✅ 修复了00-cp-multiple.jsp中的OCPP版本过滤条件</li>
                <li>✅ 添加了JavaScript中的空值检查</li>
                <li>✅ <strong>移除了手动输入Location字段，改为自动使用项目内置FTP服务器</strong></li>
                <li>✅ <strong>自动生成FTP路径：********************************/logs/</strong></li>
                <li>✅ 添加了详细的日志记录功能</li>
            </ul>
            <p><strong>重要改进：</strong>现在GetDiagnostics功能与UpdateFirmware功能一致，都使用项目内置的FTP服务器，无需用户手动填写FTP路径！</p>
        </div>

        <form class="ocpp-operation-form">
            <div class="form-section">
                <h3>Charge Points with OCPP v1.6</h3>
                
                <div class="multi-select-container">
                    <div class="selected-options" tabindex="0">Click to select Charge Points</div>
                    <div class="options-dropdown" style="display: none;">
                        <input type="text" class="search-input" placeholder="Search Charge Points...">
                        <div>
                            <input type="checkbox" id="select-all-options" class="select-all-checkbox">
                            <label for="select-all-options">Select All / None</label>
                        </div>
                        <ul class="options-list">
                            <!-- 模拟充电桩数据 -->
                            <li>
                                <input type="checkbox" id="cp-CP001" name="selectedChargePoints" value="V_16_SOAP;CP001;http://example.com/cp001">
                                <label for="cp-CP001">CP001</label>
                            </li>
                            <li>
                                <input type="checkbox" id="cp-CP002" name="selectedChargePoints" value="V_16_SOAP;CP002;http://example.com/cp002">
                                <label for="cp-CP002">CP002</label>
                            </li>
                            <li>
                                <input type="checkbox" id="cp-CP003" name="selectedChargePoints" value="V_16_SOAP;CP003;http://example.com/cp003">
                                <label for="cp-CP003">CP003</label>
                            </li>
                            <li>
                                <input type="checkbox" id="cp-CP004" name="selectedChargePoints" value="V_15_SOAP;CP004;http://example.com/cp004">
                                <label for="cp-CP004">CP004 (OCPP 1.5)</label>
                            </li>
                            <li>
                                <input type="checkbox" id="cp-CP005" name="selectedChargePoints" value="V_12_SOAP;CP005;http://example.com/cp005">
                                <label for="cp-CP005">CP005 (OCPP 1.2)</label>
                            </li>
                        </ul>
                    </div>
                    <!-- 隐藏的select元素 -->
                    <select name="chargePointSelectList" multiple="true" style="display:none;">
                        <option value="V_16_SOAP;CP001;http://example.com/cp001">CP001</option>
                        <option value="V_16_SOAP;CP002;http://example.com/cp002">CP002</option>
                        <option value="V_16_SOAP;CP003;http://example.com/cp003">CP003</option>
                        <option value="V_15_SOAP;CP004;http://example.com/cp004">CP004</option>
                        <option value="V_12_SOAP;CP005;http://example.com/cp005">CP005</option>
                    </select>
                </div>
            </div>
            
            <div class="form-section">
                <h3>Parameters</h3>
                <div class="info-message" style="background-color: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px; margin-bottom: 15px;">
                    <p><strong>诊断日志将自动保存到项目内置FTP服务器的logs目录</strong></p>
                    <p>FTP服务器地址: ftp://user@************:9988/logs/</p>
                </div>
                <table class="userInput">
                    <tr>
                        <td>Retries (integer):</td>
                        <td><input type="text" name="retries" placeholder="optional" /></td>
                    </tr>
                    <tr>
                        <td>Retry Interval (integer):</td>
                        <td><input type="text" name="retryInterval" placeholder="optional" /></td>
                    </tr>
                    <tr>
                        <td>Start Date/Time:</td>
                        <td><input type="text" name="start" placeholder="optional" /></td>
                    </tr>
                    <tr>
                        <td>Stop Date/Time:</td>
                        <td><input type="text" name="stop" placeholder="optional" /></td>
                    </tr>
                </table>
                
                <div class="submit-button">
                    <input type="submit" value="Perform" onclick="testSubmit(event)">
                </div>
            </div>
        </form>
    </div>

    <script>
        // 复制00-cp-multiple.jsp的JavaScript代码
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.querySelector('.multi-select-container');
            if (!container) {
                console.error('FATAL: Could not find the multi-select-container element.');
                return;
            }
            
            const selectedOptionsDisplay = container.querySelector('.selected-options');
            const dropdown = container.querySelector('.options-dropdown');
            const searchInput = container.querySelector('.search-input');
            const optionsList = container.querySelector('.options-list');
            const selectAllCheckbox = container.querySelector('#select-all-options');
            const hiddenSelect = document.querySelector('select[name="chargePointSelectList"]');

            if (!hiddenSelect) {
                console.error('FATAL: Could not find the hidden select element for charge points.');
                return;
            }
            
            if (!selectedOptionsDisplay || !dropdown || !optionsList) {
                console.error('FATAL: Could not find required elements for charge point selection.');
                return;
            }
            
            // Toggle dropdown visibility
            selectedOptionsDisplay.addEventListener('click', function() {
                dropdown.style.display = dropdown.style.display === 'none' ? 'block' : 'none';
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(event) {
                if (!container.contains(event.target)) {
                    dropdown.style.display = 'none';
                }
            });

            // Prevent dropdown from closing when clicking inside it
            dropdown.addEventListener('click', function(event) {
                event.stopPropagation();
            });
            
            // Update selection function
            function updateSelection() {
                const selectedValues = [];
                const displayTexts = [];
                optionsList.querySelectorAll('input[type="checkbox"]:checked').forEach(function(checkbox) {
                    selectedValues.push(checkbox.value);
                    const labelElement = checkbox.parentElement.querySelector('label[for="' + checkbox.id + '"]');
                    if (labelElement) {
                         displayTexts.push(labelElement.textContent);
                    }
                });

                // Update hidden select
                Array.from(hiddenSelect.options).forEach(function(option) {
                    option.selected = false;
                });
                selectedValues.forEach(function(value) {
                    const optionToSelect = Array.from(hiddenSelect.options).find(opt => opt.value === value);
                    if (optionToSelect) {
                        optionToSelect.selected = true;
                    }
                });
                
                var event = new Event('change', { bubbles: true });
                hiddenSelect.dispatchEvent(event);

                if (displayTexts.length > 0) {
                    selectedOptionsDisplay.textContent = displayTexts.join(', ');
                } else {
                    selectedOptionsDisplay.textContent = 'Click to select Charge Points';
                }
            }

            // Event listeners for checkboxes
            optionsList.querySelectorAll('input[type="checkbox"]').forEach(function(checkbox) {
                checkbox.addEventListener('change', updateSelection);
            });

            // Select All checkbox
            selectAllCheckbox.addEventListener('change', function() {
                const isChecked = this.checked;
                optionsList.querySelectorAll('li').forEach(function(item) {
                    if (item.style.display !== 'none') {
                        item.querySelector('input[type="checkbox"]').checked = isChecked;
                    }
                });
                updateSelection();
            });

            // Search functionality
            searchInput.addEventListener('input', function() {
                const filter = this.value.toLowerCase();
                let allVisibleAndChecked = true;
                let anyVisible = false;

                optionsList.querySelectorAll('li').forEach(function(item) {
                    const label = item.querySelector('label').textContent.toLowerCase();
                    if (label.includes(filter)) {
                        item.style.display = '';
                        anyVisible = true;
                        if (!item.querySelector('input[type="checkbox"]').checked) {
                            allVisibleAndChecked = false;
                        }
                    } else {
                        item.style.display = 'none';
                    }
                });
                
                if (anyVisible) {
                    selectAllCheckbox.checked = allVisibleAndChecked;
                } else {
                    selectAllCheckbox.checked = false; 
                }
            });
        });
        
        function testSubmit(event) {
            event.preventDefault();
            const selectedChargePoints = Array.from(document.querySelectorAll('select[name="chargePointSelectList"] option:checked')).map(opt => opt.value);
            alert('测试成功！\n\n选中的充电桩：\n' + (selectedChargePoints.length > 0 ? selectedChargePoints.join('\n') : '无'));
        }
    </script>
</body>
</html>
