/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.repository;

import de.rwth.idsg.steve.ocpp.OcppProtocol;
import de.rwth.idsg.steve.repository.dto.ChargePoint;
import de.rwth.idsg.steve.repository.dto.ChargePointSelect;
import de.rwth.idsg.steve.repository.dto.ConnectorStatus;
import de.rwth.idsg.steve.web.dto.ChargePointForm;
import de.rwth.idsg.steve.web.dto.ChargePointQueryForm;
import de.rwth.idsg.steve.web.dto.ConnectorStatusForm;
import org.jetbrains.annotations.Nullable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR> Goekay <<EMAIL>>
 * @since 19.08.2014
 */
public interface ChargePointRepository {
    Optional<String> getRegistrationStatus(String chargeBoxId);

    List<ChargePointSelect> getChargePointSelect(OcppProtocol protocol, List<String> inStatusFilter, List<String> chargeBoxIdFilter);

    default List<ChargePointSelect> getChargePointSelect(OcppProtocol protocol, List<String> inStatusFilter) {
        return getChargePointSelect(protocol, inStatusFilter, Collections.emptyList());
    }

    /**
     * 获取所有充电桩的选择列表，不带任何过滤条件
     * 
     * @return 所有充电桩的选择列表
     */
    List<ChargePointSelect> getChargePointSelectList();

    List<String> getChargeBoxIds();
    Map<String, Integer> getChargeBoxIdPkPair(List<String> chargeBoxIdList);

    List<ChargePoint.Overview> getOverview(ChargePointQueryForm form);
    Page<ChargePoint.Overview> getOverview(ChargePointQueryForm form, Pageable pageable);
    ChargePoint.Details getDetails(int chargeBoxPk);

    /**
     * 根据充电桩ID获取详细信息
     *
     * @param chargeBoxId 充电桩ID
     * @return 充电桩详情信息，如果未找到则返回null
     */
    ChargePoint.Details getDetailsByChargeBoxId(String chargeBoxId);

    /**
     * 获取特定桩主下的充电桩列表
     * 
     * @param webUserPk 桩主的用户主键
     * @param form 查询表单参数
     * @return 该桩主名下的充电桩列表
     */
    List<ChargePoint.Overview> getOwnerChargeBoxes(int webUserPk, ChargePointQueryForm form);

    default List<ConnectorStatus> getChargePointConnectorStatus() {
        return getChargePointConnectorStatus(null);
    }

    List<ConnectorStatus> getChargePointConnectorStatus(@Nullable ConnectorStatusForm form);

    List<Integer> getNonZeroConnectorIds(String chargeBoxId);

    void addChargePointList(List<String> chargeBoxIdList);
    int addChargePoint(ChargePointForm form);
    void updateChargePoint(ChargePointForm form);
    void deleteChargePoint(int chargeBoxPk);

    /**
     * 根据充电桩ID获取主键
     * 
     * @param chargeBoxId 充电桩ID
     * @return 充电桩的主键，如果未找到则返回null
     */
    Integer getChargeBoxPkFromChargeBoxId(String chargeBoxId);

    /**
     * 获取指定用户拥有的充电桩选择列表
     * 
     * @param webUserPk 用户主键
     * @return 用户拥有的充电桩选择列表
     */
    List<ChargePointSelect> getChargePointSelectByOwner(Integer webUserPk);

    /**
     * 获取需要重连的充电桩ID列表
     *
     * @return 需要重连的充电桩ID列表
     */
    List<String> getChargeBoxIdsRequiringReconnect();

    /**
     * 获取指定用户拥有的未分配充电站的充电桩列表
     *
     * @param webUserPk 用户主键
     * @return 用户拥有的未分配充电站的充电桩列表
     */
    List<ChargePointSelect> getUnassignedChargePointsByOwner(Integer webUserPk);

    /**
     * 根据充电站主键获取关联的充电桩列表
     *
     * @param chargingStationPk 充电站主键
     * @return 关联的充电桩列表
     */
    List<ChargePoint.Overview> getChargePointsByStation(Integer chargingStationPk);

    /**
     * 批量更新充电桩的充电站关联
     *
     * @param chargeBoxPks 充电桩主键列表
     * @param chargingStationPk 充电站主键，null表示取消关联
     */
    void batchUpdateChargingStation(List<Integer> chargeBoxPks, Integer chargingStationPk);

    /**
     * 获取未关联充电站的充电桩列表
     *
     * @return 未关联充电站的充电桩选择列表
     */
    List<ChargePointSelect> getUnassignedChargePoints();
    
    /**
     * 重置所有充电桩的重连标志
     */
    void resetReconnectFlags();
    
    /**
     * 重置特定充电桩的重连标志
     * 
     * @param chargeBoxId 充电桩ID
     */
    void resetReconnectFlag(String chargeBoxId);
    
    /**
     * 检查充电桩是否需要重连
     * 
     * @param chargeBoxId 充电桩ID
     * @return 如果需要重连返回true，否则返回false
     */
    boolean isReconnectRequired(String chargeBoxId);

    /**
     * Gets all charge box IDs that are currently considered online.
     *
     * @return A list of online charge box IDs.
     */
    List<String> getOnlineChargeBoxIds();

    void updateLastUpgradeStatus(String chargeBoxId, String status);

    /**
     * Finds the OCPP protocol for a specific charge point.
     *
     * @param chargeBoxId The ID of the charge point.
     * @return An Optional containing the OcppProtocol if found, otherwise empty.
     */
    Optional<OcppProtocol> findOcppProtocolByChargeBoxId(String chargeBoxId);

    /**
     * Checks if a charge point is currently considered online.
     *
     * @param chargeBoxId The ID of the charge point.
     * @return true if the charge point is online, false otherwise.
     */
    boolean isOnline(String chargeBoxId);
}
