<%@ include file="../00-header.jsp" %>

<div class="row">
    <div class="col-xs-12">
        <h3>
            DataTransfer for: ${dataTransferForm.chargeBoxId}
            <c:if test="${dataTransferForm.online}">
                <span class="label label-success">Online</span>
            </c:if>
            <c:if test="${!dataTransferForm.online}">
                <span class="label label-danger">Offline</span>
            </c:if>
        </h3>
    </div>
</div>

<div class="well">
    <c:if test="${not empty successMessage}">
        <div class="alert alert-success">${successMessage}</div>
    </c:if>
    <c:if test="${not empty errorMessage}">
        <div class="alert alert-danger">${errorMessage}</div>
    </c:if>

    <form:form modelAttribute="dataTransferForm" id="dataTransferForm" method="post" action="${ctxPath}/manager/chargepoints/datatransfer/${dataTransferForm.chargeBoxId}" cssClass="form-horizontal">
        <div id="dataTransferContent">
            <div class="row">
                <%-- Left Column --%>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="dcMaxVoltage" class="col-sm-4 control-label">Max Voltage (V)</label>
                        <div class="col-sm-8"><form:input path="dcMaxVoltage" cssClass="form-control" /></div>
                    </div>
                    <div class="form-group">
                        <label for="dcMinVoltage" class="col-sm-4 control-label">Min Voltage (V)</label>
                        <div class="col-sm-8"><form:input path="dcMinVoltage" cssClass="form-control" /></div>
                    </div>
                    <div class="form-group">
                        <label for="dcMaxCurrent" class="col-sm-4 control-label">Max Current (A)</label>
                        <div class="col-sm-8"><form:input path="dcMaxCurrent" cssClass="form-control" /></div>
                    </div>
                </div>

                <%-- Right Column --%>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="moduleNumber" class="col-sm-4 control-label">Module Quantity</label>
                        <div class="col-sm-8"><form:input path="moduleNumber" cssClass="form-control" /></div>
                    </div>
                    <div class="form-group">
                        <label for="dcModuleType" class="col-sm-4 control-label">Module Type</label>
                        <div class="col-sm-8">
                            <form:select path="dcModuleType" cssClass="form-control">
                                <form:option value="">-- Please select --</form:option>
                                <form:option value="0">Multipled3K3W</form:option>
                                <form:option value="1">General10KW</form:option>
                                <form:option value="3">Series3K3W</form:option>
                                <form:option value="4">General30KW</form:option>
                                <form:option value="5">General40KW</form:option>
                            </form:select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="appVersion" class="col-sm-4 control-label">App Version</label>
                        <div class="col-sm-8"><form:input path="appVersion" cssClass="form-control" readonly="true"/></div>
                    </div>
                </div>
            </div>

            <hr/>

            <div class="row">
                <%-- Control Board 1 --%>
                <div class="col-md-6">
                    <div class="panel panel-default">
                        <div class="panel-heading"><h4 class="panel-title">Control Board 1</h4></div>
                        <div class="panel-body">
                            <div class="form-group">
                                <label for="cb1VoltageK" class="col-sm-4 control-label">Voltage K</label>
                                <div class="col-sm-8"><form:input path="cb1VoltageK" cssClass="form-control" /></div>
                            </div>
                            <div class="form-group">
                                <label for="cb1VoltageB" class="col-sm-4 control-label">Voltage B</label>
                                <div class="col-sm-8"><form:input path="cb1VoltageB" cssClass="form-control" /></div>
                            </div>
                            <div class="form-group">
                                <label for="cb1CurrentK" class="col-sm-4 control-label">Current K</label>
                                <div class="col-sm-8"><form:input path="cb1CurrentK" cssClass="form-control" /></div>
                            </div>
                            <div class="form-group">
                                <label for="cb1CurrentB" class="col-sm-4 control-label">Current B</label>
                                <div class="col-sm-8"><form:input path="cb1CurrentB" cssClass="form-control" /></div>
                            </div>
                            <div class="form-group">
                                <label for="cb1Version" class="col-sm-4 control-label">Version</label>
                                <div class="col-sm-8"><form:input path="cb1Version" cssClass="form-control" readonly="true"/></div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-offset-2 col-sm-10">
                                    <div class="checkbox-inline">
                                        <form:checkbox path="cb1Gun1Enabled" label="Gun 1"/>
                                    </div>
                                    <form:select path="cb1Gun1ChannelType" cssClass="form-control-inline">
                                        <form:option value="">None</form:option>
                                        <form:option value="UNKNOW">UNKNOW</form:option>
                                        <form:option value="DC_CCS">DC_CCS</form:option>
                                        <form:option value="DC_CHADEMO">DC_CHADEMO</form:option>
                                        <form:option value="AC">AC</form:option>
                                        <form:option value="GB_T">GB_T</form:option>
                                    </form:select>
                                </div>
                            </div>
                             <div class="form-group">
                                <div class="col-sm-offset-2 col-sm-10">
                                    <div class="checkbox-inline">
                                        <form:checkbox path="cb1Gun2Enabled" label="Gun 2"/>
                                    </div>
                                    <form:select path="cb1Gun2ChannelType" cssClass="form-control-inline">
                                        <form:option value="">None</form:option>
                                        <form:option value="UNKNOW">UNKNOW</form:option>
                                        <form:option value="DC_CCS">DC_CCS</form:option>
                                        <form:option value="DC_CHADEMO">DC_CHADEMO</form:option>
                                        <form:option value="AC">AC</form:option>
                                        <form:option value="GB_T">GB_T</form:option>
                                    </form:select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <%-- Control Board 2 --%>
                <div class="col-md-6">
                    <div class="panel panel-default">
                        <div class="panel-heading"><h4 class="panel-title">Control Board 2</h4></div>
                        <div class="panel-body">
                            <div class="form-group">
                                <label for="cb2VoltageK" class="col-sm-4 control-label">Voltage K</label>
                                <div class="col-sm-8"><form:input path="cb2VoltageK" cssClass="form-control" /></div>
                            </div>
                            <div class="form-group">
                                <label for="cb2VoltageB" class="col-sm-4 control-label">Voltage B</label>
                                <div class="col-sm-8"><form:input path="cb2VoltageB" cssClass="form-control" /></div>
                            </div>
                            <div class="form-group">
                                <label for="cb2CurrentK" class="col-sm-4 control-label">Current K</label>
                                <div class="col-sm-8"><form:input path="cb2CurrentK" cssClass="form-control" /></div>
                            </div>
                            <div class="form-group">
                                <label for="cb2CurrentB" class="col-sm-4 control-label">Current B</label>
                                <div class="col-sm-8"><form:input path="cb2CurrentB" cssClass="form-control" /></div>
                            </div>
                             <div class="form-group">
                                <label for="cb2Version" class="col-sm-4 control-label">Version</label>
                                <div class="col-sm-8"><form:input path="cb2Version" cssClass="form-control" readonly="true"/></div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-offset-2 col-sm-10">
                                    <div class="checkbox-inline">
                                        <form:checkbox path="cb2Gun1Enabled" label="Gun 1"/>
                                    </div>
                                    <form:select path="cb2Gun1ChannelType" cssClass="form-control-inline">
                                        <form:option value="">None</form:option>
                                        <form:option value="UNKNOW">UNKNOW</form:option>
                                        <form:option value="DC_CCS">DC_CCS</form:option>
                                        <form:option value="DC_CHADEMO">DC_CHADEMO</form:option>
                                        <form:option value="AC">AC</form:option>
                                        <form:option value="GB_T">GB_T</form:option>
                                    </form:select>
                                </div>
                            </div>
                             <div class="form-group">
                                <div class="col-sm-offset-2 col-sm-10">
                                    <div class="checkbox-inline">
                                        <form:checkbox path="cb2Gun2Enabled" label="Gun 2"/>
                                    </div>
                                    <form:select path="cb2Gun2ChannelType" cssClass="form-control-inline">
                                        <form:option value="">None</form:option>
                                        <form:option value="UNKNOW">UNKNOW</form:option>
                                        <form:option value="DC_CCS">DC_CCS</form:option>
                                        <form:option value="DC_CHADEMO">DC_CHADEMO</form:option>
                                        <form:option value="AC">AC</form:option>
                                        <form:option value="GB_T">GB_T</form:option>
                                    </form:select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <hr/>

        <div class="form-group">
            <div class="col-sm-offset-4 col-sm-8">
                <button id="update-btn" class="btn btn-primary" type="submit">Update Configuration</button>
                <a href="${ctxPath}/manager/chargepoints" class="btn btn-default">Cancel</a>
            </div>
        </div>
    </form:form>
</div>

<%-- Custom Confirmation Dialog --%>
<div id="modalOverlay" style="display:none;"></div>
<div id="confirmationDialog" title="Confirm Configuration Update" style="display:none;">
    <h4>Confirm Configuration Update</h4>
    <hr>
    <p>Please review the changes before applying the update:</p>
    <div id="changes-summary"></div>
    <hr>
    <div class="dialog-buttons">
        <button id="dialog-confirm-btn" class="btn btn-primary">Confirm Update</button>
        <button id="dialog-cancel-btn" class="btn btn-default">Cancel</button>
    </div>
</div>

<style>
    .form-control-inline {
        min-width: 120px;
        width: auto;
        display: inline-block;
    }

    /* 左对齐所有标签 - 使用最强的选择器 */
    .form-horizontal .control-label,
    .panel-body .form-horizontal .control-label,
    .form-group .control-label,
    label.control-label {
        text-align: left !important;
    }

    /* Gun复选框和选择框之间的间距 */
    .checkbox-inline {
        margin-right: 20px !important;
        margin-bottom: 10px !important;
    }

    /* Gun选择框的样式调整 */
    .form-control-inline {
        margin-left: 10px !important;
    }

    #changes-summary table {
        width: 100%;
        border-collapse: collapse;
    }
    #changes-summary th, #changes-summary td {
        border: 1px solid #ddd;
        padding: 8px;
        text-align: left;
    }
    #changes-summary th {
        background-color: #f2f2f2;
    }
    .value-changed {
        font-weight: bold;
        color: #d9534f;
    }

    /* Custom Modal Styles */
    #modalOverlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.5);
        z-index: 999;
    }
    #confirmationDialog {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 550px;
        max-width: 90%;
        background-color: #fff;
        border: 1px solid #ccc;
        border-radius: 6px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.5);
        z-index: 1000;
        padding: 20px;
    }
    .dialog-buttons {
        text-align: right;
        margin-top: 15px;
    }
    .dialog-buttons button {
        margin-left: 10px;
    }
</style>

<script>
$(document).ready(function() {
    // Stop further script execution if offline
    if ("${!dataTransferForm.online}" === "true") {
        $('#dataTransferContent input, #dataTransferContent select').prop('disabled', true);
        $('button[type="submit"]').prop('disabled', true);
        return;
    }

    let initialFormState = {};
    const form = $('#dataTransferForm');

    // Store initial state
    form.find('input, select').each(function() {
        const element = $(this);
        const id = element.attr('id');
        if (!id) return;

        let value;
        if (element.is(':checkbox')) {
            value = element.is(':checked');
        } else {
            value = element.val();
        }
        
        const label = $('label[for="' + id + '"]').text() || id;
        initialFormState[id] = { value: value, label: label.trim() };
    });

    function showDialog() {
        $('#modalOverlay, #confirmationDialog').fadeIn('fast');
    }

    function hideDialog() {
        $('#modalOverlay, #confirmationDialog').fadeOut('fast');
    }

    $('#update-btn').on('click', function(e) {
        e.preventDefault();

        let changes = [];
        form.find('input, select').each(function() {
            const element = $(this);
            const id = element.attr('id');
            if (!id || !initialFormState[id]) return;

            let currentValue;
            if (element.is(':checkbox')) {
                currentValue = element.is(':checked');
            } else {
                currentValue = element.val();
            }

            const initial = initialFormState[id];
            // Convert both to string for reliable comparison, except for booleans
            const initialValueStr = typeof initial.value === 'boolean' ? initial.value : String(initial.value);
            const currentValueStr = typeof currentValue === 'boolean' ? currentValue : String(currentValue);

            if (initialValueStr !== currentValueStr) {
                changes.push({
                    label: initial.label,
                    oldValue: initial.value,
                    newValue: currentValue
                });
            }
        });

        if (changes.length === 0) {
            alert('No changes detected.');
            return;
        }

        let changesHtml = '<table class="table table-bordered table-striped"><thead><tr><th>Field</th><th>Old Value</th><th>New Value</th></tr></thead><tbody>';
        changes.forEach(function(change) {
            changesHtml += '<tr><td>' + change.label + '</td><td>' + formatValue(change.oldValue) + '</td><td class="value-changed">' + formatValue(change.newValue) + '</td></tr>';
        });
        changesHtml += '</tbody></table>';

        $('#changes-summary').html(changesHtml);
        showDialog();
    });

    $('#dialog-confirm-btn').on('click', function() {
        form.submit();
    });

    $('#dialog-cancel-btn').on('click', function() {
        hideDialog();
    });
    
    function formatValue(value) {
        if (typeof value === 'boolean') {
            return value ? 'Enabled' : 'Disabled';
        }
        if (value === '' || value === null) {
            return '<em>(empty)</em>';
        }
        return value;
    }
});
</script>

<%@ include file="../00-footer.jsp" %> 