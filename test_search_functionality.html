<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Search Functionality Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div class="container mt-4">
        <h2>Search Functionality Test</h2>
        
        <!-- 搜索工具栏 -->
        <div class="row mb-3">
            <div class="col-md-6">
                <div class="input-group">
                    <input type="text" id="searchUnassigned" class="form-control" placeholder="Search by Charge Box ID or Description...">
                    <div class="input-group-append">
                        <button class="btn btn-outline-primary" type="button" onclick="filterUnassignedChargePoints()">
                            <i class="fas fa-search"></i> Search
                        </button>
                        <button class="btn btn-outline-secondary" type="button" onclick="clearUnassignedSearch()">
                            <i class="fas fa-times"></i> Clear
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <select id="protocolFilterUnassigned" class="form-control">
                    <option value="">All Protocols</option>
                    <option value="V_16_SOAP">OCPP 1.6 SOAP</option>
                    <option value="V_16_JSON">OCPP 1.6 JSON</option>
                </select>
            </div>
            <div class="col-md-3">
                <div class="text-muted small">
                    <div>Total: <span id="totalUnassignedCount">5</span></div>
                    <div>Showing: <span id="visibleUnassignedCount">5</span></div>
                    <div id="unassignedSearchStatus" class="text-info" style="display: none;">
                        <i class="fas fa-search"></i> Search active
                    </div>
                </div>
            </div>
        </div>

        <!-- 测试表格 -->
        <div class="table-container" style="max-height: 400px; overflow-y: auto; border: 1px solid #dee2e6; border-radius: 0.375rem;">
            <table class="table table-striped table-hover mb-0" id="unassignedChargePointsTable">
                <thead class="thead-light sticky-top">
                    <tr>
                        <th style="width: 50px;"><input type="checkbox" id="checkAllUnassigned"/></th>
                        <th style="width: 200px;">Charge Box ID</th>
                        <th>Description</th>
                        <th style="width: 150px;">OCPP Protocol</th>
                    </tr>
                </thead>
                <tbody>
                    <tr class="unassigned-row" data-chargebox-id="cp001" data-description="test charger 1" data-protocol="V_16_SOAP">
                        <td><input type="checkbox" name="selectedChargePoints" value="CP001" class="unassignedCheckbox"/></td>
                        <td><strong>CP001</strong></td>
                        <td class="text-muted">Test Charger 1</td>
                        <td><span class="badge badge-info">V_16_SOAP</span></td>
                    </tr>
                    <tr class="unassigned-row" data-chargebox-id="cp002" data-description="test charger 2" data-protocol="V_16_JSON">
                        <td><input type="checkbox" name="selectedChargePoints" value="CP002" class="unassignedCheckbox"/></td>
                        <td><strong>CP002</strong></td>
                        <td class="text-muted">Test Charger 2</td>
                        <td><span class="badge badge-info">V_16_JSON</span></td>
                    </tr>
                    <tr class="unassigned-row" data-chargebox-id="cp003" data-description="demo station" data-protocol="V_16_SOAP">
                        <td><input type="checkbox" name="selectedChargePoints" value="CP003" class="unassignedCheckbox"/></td>
                        <td><strong>CP003</strong></td>
                        <td class="text-muted">Demo Station</td>
                        <td><span class="badge badge-info">V_16_SOAP</span></td>
                    </tr>
                    <tr class="unassigned-row" data-chargebox-id="station001" data-description="main station" data-protocol="V_16_JSON">
                        <td><input type="checkbox" name="selectedChargePoints" value="STATION001" class="unassignedCheckbox"/></td>
                        <td><strong>STATION001</strong></td>
                        <td class="text-muted">Main Station</td>
                        <td><span class="badge badge-info">V_16_JSON</span></td>
                    </tr>
                    <tr class="unassigned-row" data-chargebox-id="test123" data-description="testing unit" data-protocol="V_16_SOAP">
                        <td><input type="checkbox" name="selectedChargePoints" value="TEST123" class="unassignedCheckbox"/></td>
                        <td><strong>TEST123</strong></td>
                        <td class="text-muted">Testing Unit</td>
                        <td><span class="badge badge-info">V_16_SOAP</span></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 没有找到结果的提示 -->
        <div id="noUnassignedResults" class="text-center text-muted py-4" style="display: none;">
            <i class="fas fa-search fa-2x mb-2"></i>
            <p>No charge points found matching your search criteria.</p>
        </div>
    </div>

    <script>
    $(document).ready(function() {
        console.log('Test page loaded');
        
        // 未分配充电桩的搜索和过滤功能
        window.filterUnassignedChargePoints = function() {
            const searchTerm = $('#searchUnassigned').val().toLowerCase();
            const protocolFilter = $('#protocolFilterUnassigned').val();
            let visibleCount = 0;
            
            console.log('Filtering unassigned charge points:', {
                searchTerm: searchTerm,
                protocolFilter: protocolFilter,
                totalRows: $('.unassigned-row').length
            });
            
            $('.unassigned-row').each(function() {
                const $row = $(this);
                const chargeBoxId = $row.data('chargebox-id');
                const description = $row.data('description');
                const protocol = $row.data('protocol');
                
                const matchesSearch = !searchTerm || 
                    chargeBoxId.includes(searchTerm) || 
                    description.includes(searchTerm);
                const matchesProtocol = !protocolFilter || protocol === protocolFilter;
                
                if (matchesSearch && matchesProtocol) {
                    $row.show();
                    $row.find('.unassignedCheckbox').prop('disabled', false);
                    visibleCount++;
                } else {
                    $row.hide();
                    const checkbox = $row.find('.unassignedCheckbox');
                    checkbox.prop('checked', false);
                    checkbox.prop('disabled', true);
                    $row.removeClass('selected');
                }
            });
            
            // 更新计数
            $('#visibleUnassignedCount').text(visibleCount);
            
            // 显示搜索状态
            const hasActiveSearch = searchTerm || protocolFilter;
            $('#unassignedSearchStatus').toggle(hasActiveSearch);
            
            // 显示/隐藏无结果提示
            if (visibleCount === 0) {
                $('#unassignedChargePointsTable').hide();
                $('#noUnassignedResults').show();
            } else {
                $('#unassignedChargePointsTable').show();
                $('#noUnassignedResults').hide();
            }
            
            console.log('Filter completed, visible count:', visibleCount);
        }

        // 清除搜索功能
        window.clearUnassignedSearch = function() {
            console.log('Clear button clicked');
            $('#searchUnassigned').val('');
            $('#protocolFilterUnassigned').val('');
            filterUnassignedChargePoints();
        }
        
        // 事件绑定
        $('#searchUnassigned').on('input', filterUnassignedChargePoints);
        $('#protocolFilterUnassigned').on('change', filterUnassignedChargePoints);
        
        // 移除了按钮事件绑定，现在使用onclick属性
        
        // 回车键搜索
        $('#searchUnassigned').on('keypress', function(e) {
            if (e.which === 13) {
                console.log('Enter key pressed');
                filterUnassignedChargePoints();
            }
        });
        
        console.log('Event handlers bound');
    });
    </script>
</body>
</html>
