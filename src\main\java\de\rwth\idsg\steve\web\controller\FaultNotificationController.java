/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.web.controller;

import de.rwth.idsg.steve.service.FaultNotificationService;
import de.rwth.idsg.steve.service.MailService;
import de.rwth.idsg.steve.web.dto.FaultNotificationEmailDTO;
import de.rwth.idsg.steve.web.dto.FaultNotificationEmailForm;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 故障通知设置控制器
 */
@Slf4j
@Controller
@RequestMapping(value = "/manager/notification")
@PreAuthorize("hasAnyAuthority('ADMIN', 'OPERATOR_FACTORY')")
public class FaultNotificationController {

    @Autowired
    private FaultNotificationService notificationService;
    
    @Autowired
    private MailService mailService;
    
    @GetMapping
    public String getNotificationSettings(Model model) {
        prepareModel(model);
        return "data-man/notification";
    }
    
    @GetMapping("/list")
    @ResponseBody
    public List<FaultNotificationEmailDTO> getEmailList() {
        return notificationService.getAllEmails();
    }

    @PostMapping("/add")
    public String addEmail(@Valid @ModelAttribute("emailForm") FaultNotificationEmailForm form, 
                         BindingResult result, Model model, RedirectAttributes redirectAttributes) {
        if (result.hasErrors()) {
            prepareModel(model);
            return "data-man/notification";
        }
        
        // 默认启用
        if (form.getEnabled() == null) {
            form.setEnabled(true);
        }
        
        try {
            notificationService.addEmail(form);
            redirectAttributes.addFlashAttribute("success", "Email address added successfully");
        } catch (Exception e) {
            log.error("Adding notification email failed", e);
            redirectAttributes.addFlashAttribute("error", "Failed to add email: " + e.getMessage());
        }
        
        return "redirect:/manager/notification";
    }
    
    @PostMapping("/update/{id}")
    public String updateEmail(@PathVariable("id") Integer id, 
                            @Valid @ModelAttribute("emailForm") FaultNotificationEmailForm form,
                            BindingResult result, RedirectAttributes redirectAttributes) {
        if (result.hasErrors()) {
            redirectAttributes.addFlashAttribute("error", "Failed to update email: Form validation error");
            return "redirect:/manager/notification";
        }
        
        // 确保ID正确
        form.setId(id);
        
        try {
            notificationService.updateEmail(form);
            redirectAttributes.addFlashAttribute("success", "Email settings updated");
        } catch (Exception e) {
            log.error("Updating notification email failed", e);
            redirectAttributes.addFlashAttribute("error", "Failed to update email: " + e.getMessage());
        }
        
        return "redirect:/manager/notification";
    }
    
    @PostMapping("/delete/{id}")
    public String deleteEmail(@PathVariable("id") Integer id, RedirectAttributes redirectAttributes) {
        try {
            notificationService.deleteEmail(id);
            redirectAttributes.addFlashAttribute("success", "Notification email deleted");
        } catch (Exception e) {
            log.error("Deleting notification email failed", e);
            redirectAttributes.addFlashAttribute("error", "Failed to delete email: " + e.getMessage());
        }
        
        return "redirect:/manager/notification";
    }
    
    @PostMapping("/test")
    public String testMailSettings(RedirectAttributes redirectAttributes) {
        try {
            // 发送测试邮件
            mailService.sendTestMail();
            redirectAttributes.addFlashAttribute("success", "Test email sent");
        } catch (Exception e) {
            log.error("Failed to send test email", e);
            redirectAttributes.addFlashAttribute("error", "Failed to send test email: " + e.getMessage());
        }
        
        return "redirect:/manager/notification";
    }
    
    private void prepareModel(Model model) {
        List<FaultNotificationEmailDTO> emails = notificationService.getAllEmails();
        model.addAttribute("emailList", emails);
        
        // 添加空表单
        if (!model.containsAttribute("emailForm")) {
            model.addAttribute("emailForm", new FaultNotificationEmailForm());
        }
    }
} 