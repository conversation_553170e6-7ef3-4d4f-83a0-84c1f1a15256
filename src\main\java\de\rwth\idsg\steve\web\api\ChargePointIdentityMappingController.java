package de.rwth.idsg.steve.web.api;

import de.rwth.idsg.steve.config.ChargePointIdentityMappingConfig;
import de.rwth.idsg.steve.service.ChargePointIdentityMappingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 充电桩标识映射管理API
 * 提供REST接口来管理OCPP标识和维护标识之间的映射关系
 */
@Slf4j
@RestController
@RequestMapping("/api/charge-point-identity-mapping")
public class ChargePointIdentityMappingController {

    @Autowired
    private ChargePointIdentityMappingService identityMappingService;

    @Autowired
    private ChargePointIdentityMappingConfig mappingConfig;

    /**
     * 获取所有映射关系
     */
    @GetMapping("/mappings")
    public ResponseEntity<Map<String, Object>> getAllMappings() {
        Map<String, String> mappings = identityMappingService.getAllMappings();
        
        Map<String, Object> response = new HashMap<>();
        response.put("mappings", mappings);
        response.put("count", mappings.size());
        response.put("status", "success");
        
        return ResponseEntity.ok(response);
    }

    /**
     * 添加新的映射关系
     */
    @PostMapping("/mappings")
    public ResponseEntity<Map<String, Object>> addMapping(
            @RequestParam String ocppIdentity,
            @RequestParam String devOpsIdentity) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            identityMappingService.addMapping(ocppIdentity, devOpsIdentity);
            
            response.put("status", "success");
            response.put("message", String.format("Added mapping: %s -> %s", ocppIdentity, devOpsIdentity));
            response.put("ocppIdentity", ocppIdentity);
            response.put("devOpsIdentity", devOpsIdentity);
            
            log.info("🔧 Added mapping via API: {} -> {}", ocppIdentity, devOpsIdentity);
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("status", "error");
            response.put("message", "Failed to add mapping: " + e.getMessage());
            
            log.error("❌ Failed to add mapping via API: {} -> {}", ocppIdentity, devOpsIdentity, e);
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 删除映射关系
     */
    @DeleteMapping("/mappings/{ocppIdentity}")
    public ResponseEntity<Map<String, Object>> removeMapping(@PathVariable String ocppIdentity) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            boolean existed = identityMappingService.hasOcppMapping(ocppIdentity);
            identityMappingService.removeMapping(ocppIdentity);
            
            response.put("status", "success");
            response.put("message", existed ? 
                String.format("Removed mapping for: %s", ocppIdentity) :
                String.format("No mapping found for: %s", ocppIdentity));
            response.put("ocppIdentity", ocppIdentity);
            response.put("existed", existed);
            
            log.info("🗑️ Removed mapping via API: {}", ocppIdentity);
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("status", "error");
            response.put("message", "Failed to remove mapping: " + e.getMessage());
            
            log.error("❌ Failed to remove mapping via API: {}", ocppIdentity, e);
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 根据OCPP标识获取对应的维护标识
     */
    @GetMapping("/resolve/devops/{ocppIdentity}")
    public ResponseEntity<Map<String, Object>> resolveToDevOps(@PathVariable String ocppIdentity) {
        Map<String, Object> response = new HashMap<>();
        
        String devOpsIdentity = identityMappingService.getDevOpsIdentity(ocppIdentity);
        boolean isMapped = !devOpsIdentity.equals(ocppIdentity);
        
        response.put("ocppIdentity", ocppIdentity);
        response.put("devOpsIdentity", devOpsIdentity);
        response.put("isMapped", isMapped);
        response.put("status", "success");
        
        return ResponseEntity.ok(response);
    }

    /**
     * 根据维护标识获取对应的OCPP标识
     */
    @GetMapping("/resolve/ocpp/{devOpsIdentity}")
    public ResponseEntity<Map<String, Object>> resolveToOcpp(@PathVariable String devOpsIdentity) {
        Map<String, Object> response = new HashMap<>();
        
        String ocppIdentity = identityMappingService.getOcppIdentity(devOpsIdentity);
        boolean isMapped = !ocppIdentity.equals(devOpsIdentity);
        
        response.put("devOpsIdentity", devOpsIdentity);
        response.put("ocppIdentity", ocppIdentity);
        response.put("isMapped", isMapped);
        response.put("status", "success");
        
        return ResponseEntity.ok(response);
    }

    /**
     * 智能解析充电桩标识
     */
    @PostMapping("/resolve")
    public ResponseEntity<Map<String, Object>> resolveIdentity(
            @RequestParam String fileName,
            @RequestParam String connectedChargeBoxId) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            String resolvedIdentity = identityMappingService.resolveChargePointIdentity(fileName, connectedChargeBoxId);
            
            response.put("fileName", fileName);
            response.put("connectedChargeBoxId", connectedChargeBoxId);
            response.put("resolvedIdentity", resolvedIdentity);
            response.put("status", "success");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("status", "error");
            response.put("message", "Failed to resolve identity: " + e.getMessage());
            
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 重置动态学习的映射
     */
    @PostMapping("/reset")
    public ResponseEntity<Map<String, Object>> resetDynamicMappings() {
        Map<String, Object> response = new HashMap<>();

        try {
            mappingConfig.resetDynamicMappings();

            response.put("status", "success");
            response.put("message", "Dynamic mappings reset successfully");
            response.put("count", identityMappingService.getMappingCount());

            log.info("🔄 Dynamic mappings reset via API");
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            response.put("status", "error");
            response.put("message", "Failed to reset mappings: " + e.getMessage());

            log.error("❌ Failed to reset mappings via API", e);
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 验证映射配置
     */
    @GetMapping("/validate")
    public ResponseEntity<Map<String, Object>> validateMappings() {
        Map<String, Object> response = new HashMap<>();
        
        boolean isValid = mappingConfig.validateMappings();
        
        response.put("isValid", isValid);
        response.put("count", identityMappingService.getMappingCount());
        response.put("status", "success");
        response.put("message", isValid ? "All mappings are valid" : "Some mappings have issues");
        
        return ResponseEntity.ok(response);
    }

    /**
     * 获取映射统计信息
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getMappingStats() {
        Map<String, Object> response = new HashMap<>();
        Map<String, String> mappings = identityMappingService.getAllMappings();
        
        response.put("totalMappings", mappings.size());
        response.put("status", "success");
        
        // 统计不同类型的映射
        int cpMappings = 0;
        int macMappings = 0;
        int otherMappings = 0;
        
        for (String ocppId : mappings.keySet()) {
            if (ocppId.startsWith("CP")) {
                cpMappings++;
            } else if (ocppId.matches("[A-F0-9]{12}")) {
                macMappings++;
            } else {
                otherMappings++;
            }
        }
        
        response.put("cpMappings", cpMappings);
        response.put("macMappings", macMappings);
        response.put("otherMappings", otherMappings);
        
        return ResponseEntity.ok(response);
    }

    /**
     * 清除所有映射关系
     */
    @DeleteMapping("/mappings")
    public ResponseEntity<Map<String, Object>> clearAllMappings() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            int count = identityMappingService.getMappingCount();
            identityMappingService.clearAllMappings();
            
            response.put("status", "success");
            response.put("message", String.format("Cleared %d mappings", count));
            response.put("clearedCount", count);
            
            log.info("🧹 Cleared all mappings via API: {} mappings removed", count);
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("status", "error");
            response.put("message", "Failed to clear mappings: " + e.getMessage());
            
            log.error("❌ Failed to clear mappings via API", e);
            return ResponseEntity.badRequest().body(response);
        }
    }
}
