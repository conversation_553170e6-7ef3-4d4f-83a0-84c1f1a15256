/* 修复按钮显示不完整的问题 */
.btn {
    display: inline-block;
    padding: 0.375rem 0.75rem;
    font-size: 12px;
    line-height: 1.5;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    border: 1px solid transparent;
    border-radius: 0.25rem;
    transition: all 0.15s ease-in-out;
    cursor: pointer;
    box-sizing: border-box;
    min-width: 100px;
    overflow: visible;
    margin: 2px;
}

.btn-primary {
    color: #fff;
    background-color: #397079;
    border-color: #296067;
}

.btn-primary:hover {
    background-color: #29575f;
    border-color: #1d464d;
}

.btn-blue {
    color: #fff;
    background-color: #397ac2;
    border-color: #2c65a8;
}

.btn-blue:hover {
    background-color: #1e63b0;
    border-color: #185293;
}

.btn-red {
    color: #fff;
    background-color: #c14848;
    border-color: #a33c3c;
}

.btn-red:hover {
    background-color: #af3232;
    border-color: #902929;
}

.btn-standard {
    color: #575757;
    background-color: #ededed;
    border-color: #b0b0b0;
}

.btn-standard:hover {
    background-color: #dfdfdf;
}

/* 修复表单输入不完整的问题 */
input[type="text"], 
input[type="number"], 
input[type="password"],
input[type="email"],
textarea,
select {
    box-sizing: border-box !important;
    max-width: 100%;
}

/* 改进表格元素布局 */
table {
    table-layout: fixed;
    word-wrap: break-word;
}

td {
    overflow: visible;
    max-width: 100%;
}

/* 确保按钮文本完整可见 */
input[type="button"],
input[type="submit"] {
    overflow: visible !important;
    text-overflow: clip !important;
    white-space: normal !important;
    height: auto !important;
    min-height: 25px;
    padding: 4px 8px !important;
} 