-- 在charge_box表中添加charging_station_pk字段，建立充电桩与充电站的关联关系
ALTER TABLE `charge_box` 
ADD COLUMN `charging_station_pk` INT NULL DEFAULT NULL COMMENT '关联的充电站主键' AFTER `address_pk`,
ADD INDEX `idx_charge_box_charging_station` (`charging_station_pk`),
ADD CONSTRAINT `FK_charge_box_charging_station` 
    FOREIGN KEY (`charging_station_pk`) 
    REFERENCES `charging_station` (`charging_station_pk`) 
    ON DELETE SET NULL 
    ON UPDATE NO ACTION;
