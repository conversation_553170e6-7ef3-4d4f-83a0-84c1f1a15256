package de.rwth.idsg.steve.config;

import de.rwth.idsg.steve.service.ChargePointIdentityMappingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 充电桩标识映射配置
 * 用于配置OCPP标识和维护标识之间的映射关系
 * 
 * 这个配置类在应用启动后自动加载映射关系，
 * 可以根据实际部署情况调整映射表。
 */
@Slf4j
@Component
public class ChargePointIdentityMappingConfig {

    @Autowired
    private ChargePointIdentityMappingService identityMappingService;

    /**
     * 应用启动完成后初始化映射关系
     */
    @EventListener(ApplicationReadyEvent.class)
    public void initializeMappings() {
        log.info("🔧 Initializing charge point identity mapping service with dynamic learning mode...");
        log.info("✅ Dynamic identity mapping service ready - will learn mappings automatically");
    }

    /**
     * 获取动态学习的映射统计信息
     *
     * @return 映射统计信息
     */
    public Map<String, Object> getMappingStatistics() {
        Map<String, String> allMappings = identityMappingService.getAllMappings();
        Map<String, Object> stats = new HashMap<>();

        stats.put("totalMappings", allMappings.size());
        stats.put("dynamicallyLearned", allMappings.size()); // 现在所有映射都是动态学习的

        // 按类型分类映射
        int cpMappings = 0;
        int macMappings = 0;
        int otherMappings = 0;

        for (String ocppId : allMappings.keySet()) {
            if (ocppId.startsWith("CP")) {
                cpMappings++;
            } else if (ocppId.matches("[A-F0-9]{12}")) {
                macMappings++;
            } else {
                otherMappings++;
            }
        }

        stats.put("cpFormatMappings", cpMappings);
        stats.put("macFormatMappings", macMappings);
        stats.put("otherFormatMappings", otherMappings);

        return stats;
    }

    /**
     * 动态添加映射关系的方法
     * 可以在运行时调用此方法添加新的映射
     * 
     * @param ocppIdentity OCPP标识
     * @param devOpsIdentity 维护标识
     */
    public void addMapping(String ocppIdentity, String devOpsIdentity) {
        identityMappingService.addMapping(ocppIdentity, devOpsIdentity);
        log.info("🔧 Dynamically added mapping: {} -> {}", ocppIdentity, devOpsIdentity);
    }

    /**
     * 动态移除映射关系的方法
     * 
     * @param ocppIdentity OCPP标识
     */
    public void removeMapping(String ocppIdentity) {
        identityMappingService.removeMapping(ocppIdentity);
        log.info("🗑️ Dynamically removed mapping for: {}", ocppIdentity);
    }

    /**
     * 获取当前所有映射关系
     * 
     * @return 映射关系表
     */
    public Map<String, String> getAllMappings() {
        return identityMappingService.getAllMappings();
    }

    /**
     * 重置动态学习的映射关系
     * 清除所有学习到的映射，重新开始学习
     */
    public void resetDynamicMappings() {
        log.info("🔄 Resetting dynamically learned mappings...");

        int clearedCount = identityMappingService.getMappingCount();
        identityMappingService.clearAllMappings();

        log.info("✅ Reset complete: {} mappings cleared, ready for new learning", clearedCount);
    }

    /**
     * 验证映射配置的有效性
     * 检查是否有循环映射或其他问题
     * 
     * @return 验证结果
     */
    public boolean validateMappings() {
        Map<String, String> mappings = identityMappingService.getAllMappings();
        
        // 检查是否有循环映射
        for (Map.Entry<String, String> entry : mappings.entrySet()) {
            String ocppId = entry.getKey();
            String devOpsId = entry.getValue();
            
            // 检查反向映射是否会造成循环
            if (mappings.containsKey(devOpsId) && mappings.get(devOpsId).equals(ocppId)) {
                log.warn("⚠️ Circular mapping detected: {} <-> {}", ocppId, devOpsId);
                return false;
            }
        }
        
        log.info("✅ Mapping validation passed: {} mappings are valid", mappings.size());
        return true;
    }

    /**
     * 打印当前映射状态（用于调试）
     */
    public void printMappingStatus() {
        Map<String, String> mappings = identityMappingService.getAllMappings();
        
        log.info("📊 Current Charge Point Identity Mappings:");
        log.info("   Total mappings: {}", mappings.size());
        
        for (Map.Entry<String, String> entry : mappings.entrySet()) {
            log.info("   {} (OCPP) -> {} (DevOps)", entry.getKey(), entry.getValue());
        }
        
        if (mappings.isEmpty()) {
            log.info("   No mappings configured");
        }
    }
}
