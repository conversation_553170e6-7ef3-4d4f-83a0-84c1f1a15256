/*
 * SteV<PERSON> - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.ocpp.ws;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.socket.PingMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.CloseStatus;

import java.io.IOException;
import java.nio.ByteBuffer;

import static java.nio.charset.StandardCharsets.UTF_8;

/**
 * <AUTHOR> Goekay <<EMAIL>>
 * @since 17.03.2015
 */
@Slf4j
@RequiredArgsConstructor
public class PingTask implements Runnable {
    private final String chargeBoxId;
    private final WebSocketSession session;

    private static final PingMessage PING_MESSAGE = new PingMessage(ByteBuffer.wrap("ping".getBytes(UTF_8)));
    private int failureCount = 0;
    private static final int MAX_FAILURES = 3;

    @Override
    public void run() {
        try {
            // 检查会话是否开启
            if (!session.isOpen()) {
                log.debug("[chargeBoxId={}, sessionId={}] Session is closed, skipping ping message", chargeBoxId, session.getId());
                return;
            }
            
            WebSocketLogger.sendingPing(chargeBoxId, session);
            try {
                session.sendMessage(PING_MESSAGE);
                // Reset failure count after successful ping
                failureCount = 0;
            } catch (IOException e) {
                // For closed channels, just log at debug level, not as an error
                if (isClosedChannelException(e)) {
                    log.debug("[chargeBoxId={}, sessionId={}] Channel is closed, skipping ping operation", chargeBoxId, session.getId());
                } else {
                    WebSocketLogger.pingError(chargeBoxId, session, e);
                    log.error("[chargeBoxId={}, sessionId={}] IO exception while sending ping message: {}", 
                              chargeBoxId, session.getId(), e.getMessage());
                    
                    // Increment failure count and close session when max failures reached
                    failureCount++;
                    if (failureCount >= MAX_FAILURES) {
                        log.warn("[chargeBoxId={}, sessionId={}] Ping failures reached {} times, attempting to reset connection", 
                                chargeBoxId, session.getId(), MAX_FAILURES);
                        try {
                            if (session.isOpen()) {
                                session.close(new CloseStatus(1000, 
                                             "Multiple ping failures, reconnection needed"));
                            }
                        } catch (Exception closeEx) {
                            log.debug("[chargeBoxId={}, sessionId={}] Exception while closing session: {}", 
                                     chargeBoxId, session.getId(), closeEx.getMessage());
                        }
                    }
                }
            }
        } catch (Exception e) {
            // Catch all possible exceptions to prevent the scheduled task from terminating
            log.error("[chargeBoxId={}, sessionId={}] Exception during ping task execution: {}", 
                      chargeBoxId, session.getId(), e.getMessage());
        }
    }
    
    /**
     * Check if exception is a ClosedChannelException
     */
    private boolean isClosedChannelException(Throwable t) {
        return t instanceof java.nio.channels.ClosedChannelException || 
              (t.getCause() != null && t.getCause() instanceof java.nio.channels.ClosedChannelException);
    }
}
