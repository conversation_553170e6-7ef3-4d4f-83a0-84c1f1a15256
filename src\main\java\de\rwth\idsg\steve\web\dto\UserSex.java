/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.web.dto;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR> Goekay <<EMAIL>>
 * @since 25.11.2015
 */
@RequiredArgsConstructor
public enum UserSex {
    FEMALE("f", "Female"),
    <PERSON><PERSON>("m", "Male"),
    OTHER("o", "Other");

    @Getter private final String databaseValue;
    @Getter private final String value;

    public static UserSex fromDatabaseValue(String v) {
        for (UserSex c: UserSex.values()) {
            if (c.databaseValue.equals(v)) {
                return c;
            }
        }
        throw new IllegalArgumentException(v);
    }

    public static UserSex fromValue(String v) {
        for (UserSex c: UserSex.values()) {
            if (c.value.equals(v)) {
                return c;
            }
        }
        throw new IllegalArgumentException(v);
    }
}
