/*
 * SteV<PERSON> - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.repository.dto;

import de.rwth.idsg.steve.web.dto.Address;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import org.joda.time.DateTime;

import javax.annotation.Nullable;

/**
 * <AUTHOR>
 */
public final class ChargeStation {

    @Getter
    @Builder
    public static final class Overview {
        private final int chargingStationPk;
        private final String stationName;
        private final String operatorName;
        private final String location;
        private final DateTime constructionDate;
        private final DateTime operationDate;
        private final Integer webUserPk;
        private final String userName;
        
        // Formatted strings for display
        private final String constructionDateFormatted;
        private final String operationDateFormatted;
        
        // For sorting
        private final DateTime constructionDateDT;
        private final DateTime operationDateDT;
    }

    @Getter
    @Builder
    public static final class Details {
        private final int chargingStationPk;
        private final String stationName;
        private final String operatorName;
        private final String location;
        private final DateTime constructionDate;
        private final DateTime operationDate;
        private final Integer webUserPk;
        private final String note;
        private final Address address;
        
        // User details if assigned
        private final String firstName;
        private final String lastName;
        private final String phone;
        private final String email;
    }
    
    @Getter
    @Setter
    public static final class Form {
        private Integer chargingStationPk;
        private String stationName;
        private String operatorName;
        private String location;
        private DateTime constructionDate;
        private DateTime operationDate;
        private Integer webUserPk;
        private String note;
        private Address address;
    }
} 