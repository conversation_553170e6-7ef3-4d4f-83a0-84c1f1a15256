/**
 * EVSE_OMS Theme Styles
 * Based on RuoYi-Vue style
 */

:root {
  --main-bg-color: #f3f3f3;
  --header-bg-color: #3c8dbc;
  --sidebar-bg-color: #2f4050;
  --sidebar-text-color: #a7b1c2;
  --sidebar-active-color: #f4f4f5;
  --sidebar-active-bg: #409EFF;
  --primary-color: #409EFF;
  --success-color: #67C23A;
  --info-color: #909399;
  --warning-color: #E6A23C;
  --danger-color: #F56C6C;
  --text-color: #333;
  --border-color: #E4E7ED;
}

body {
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 14px;
  line-height: 1.42857143;
  color: #333;
  background-color: var(--main-bg-color);
  margin: 0;
  padding: 0;
}

/* Top navigation */
.top-banner {
  background-color: var(--header-bg-color);
  color: #fff;
  height: 60px;
  border-bottom: 1px solid #e7e7e7;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.top-menu {
  background-color: #2c3e50;
  height: 50px;
  display: flex;
  align-items: center;
  padding: 0 15px;
}

/* Modified navigation menu to display in one line */
ul.navigation {
  float: none;
  display: flex;
  justify-content: center;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0;
}

ul.navigation li {
  margin: 0 5px;
  padding: 0;
}

ul.navigation li a {
  padding: 0 15px;
  line-height: 50px;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

ul.navigation li a:link, ul.navigation li a:visited {
  color: #f8f9fa;
}

ul.navigation li a:hover, ul.navigation li a:active {
  color: #fff;
  background-color: rgba(255, 255, 255, 0.1);
}

ul.navigation ul {
  background-color: #2c3e50;
  border-radius: 0 0 4px 4px;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  top: 50px;
}

ul.navigation ul li {
  background: #2c3e50;
  margin: 0;
}

ul.navigation ul li a {
  padding: 10px 20px;
  line-height: 1.5;
  text-align: left;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

ul.navigation ul li a:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Left menu */
.left-menu {
  background-color: var(--sidebar-bg-color);
  color: var(--sidebar-text-color);
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.1);
}

.left-menu li a:link, .left-menu li a:visited {
  color: var(--sidebar-text-color);
  display: block;
  padding: 12px 15px;
  text-decoration: none;
  border-left: 3px solid transparent;
  transition: all 0.3s;
}

.left-menu li a:hover, .left-menu li a:active {
  color: var(--sidebar-active-color);
  background-color: rgba(0, 0, 0, 0.1);
  border-left: 3px solid var(--primary-color);
}

.left-menu li a.highlight {
  color: var(--sidebar-active-color);
  background-color: var(--sidebar-active-bg);
  border-left: 3px solid var(--primary-color);
}

/* Content area */
.main {
  display: flex;
  min-height: 100vh;
  flex-direction: column;
}

.main-wrapper {
  flex: 1;
  width: 100%;
}

.container {
  padding: 15px;
  margin: 0 auto;
  width: 95%;
  max-width: 1400px;
}

/* Redesigned content layout to center and set reasonable width */
.content {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  margin: 20px auto;
  width: 95%;
  max-width: 1400px;
}

/* Improved tile styles for better appearance and even distribution */
.tileWrapper {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 20px;
  margin: 0 auto;
  padding: 20px 0;
  max-width: 1200px;
}

.tileRow1 {
  flex: 0 0 calc(25% - 20px);
  min-width: 220px;
  max-width: 300px;
  height: auto;
  min-height: 140px;
  margin: 0;
}

.tileWrapper a:link, .tileWrapper a:visited {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  text-decoration: none;
  color: #333;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px rgba(0, 21, 41, 0.08);
  transition: all .3s;
  padding: 20px 15px;
  text-align: center;
  border: 1px solid #ebeef5;
  margin: 0;
}

.tileWrapper a:hover, .tileWrapper a:active {
  transform: translateY(-4px);
  box-shadow: 0 4px 12px rgba(0, 21, 41, 0.12);
  color: var(--primary-color);
}

span.base {
  display: block;
  font-size: 24px;
  font-weight: bold;
  margin-top: 15px;
  color: var(--primary-color);
}

span.baseTable {
  display: flex;
  flex-direction: column;
  margin-top: 15px;
  width: 100%;
}

span.baseRow {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

span.baseCell {
  text-align: left;
}

span.baseCell:first-child {
  width: auto;
  text-align: left;
}

span.baseCell:nth-child(2) {
  text-align: right;
  font-weight: bold;
  color: var(--primary-color);
  padding-left: 15px;
}

span.formatNumber {
  font-size: 22px;
  color: var(--primary-color);
  font-weight: bold;
}

/* Table styles */
table.res {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 15px;
  table-layout: fixed;
}

table.res th {
  text-align: center;
  padding: 12px 10px;
  background: #f8f8f9;
  border-bottom: 1px solid #ebeef5;
  font-weight: bold;
  color: #333;
}

table.res th[data-sort] {
  cursor: pointer;
}

table.res tr {
  border-bottom: 1px solid #ebeef5;
}

table.res tr:nth-child(odd) {
  background-color: #fafafa;
}

table.res tr:hover {
  background-color: #f5f7fa;
}

table.res td {
  padding: 10px;
  text-align: center;
  word-wrap: break-word;
}

/* Form elements */
input[type="text"], input[type="number"], input[type="password"], textarea, select {
  display: inline-block;
  padding: 8px 12px;
  font-size: 14px;
  line-height: 1.42857143;
  color: #555;
  background-color: #fff;
  background-image: none;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
}

input[type="text"]:focus, input[type="number"]:focus, input[type="password"]:focus, textarea:focus, select:focus {
  border-color: var(--primary-color);
  outline: 0;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, .2);
}

input[type="submit"], input[type="button"] {
  display: inline-block;
  margin-bottom: 0;
  font-weight: 400;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  touch-action: manipulation;
  cursor: pointer;
  background-image: none;
  border: 1px solid transparent;
  padding: 6px 12px;
  font-size: 14px;
  line-height: 1.42857143;
  border-radius: 4px;
  user-select: none;
  transition: all .3s;
}

input[type="submit"] {
  color: #fff;
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

input[type="submit"]:hover {
  background-color: #66b1ff;
  border-color: #66b1ff;
}

input[type="button"] {
  color: #5a5e66;
  background: #fff;
  border-color: #dcdfe6;
}

input[type="button"]:hover {
  color: var(--primary-color);
  border-color: #c6e2ff;
  background-color: #ecf5ff;
}

/* Info alert boxes */
.info, .warning, .error {
  padding: 12px 20px;
  margin-bottom: 15px;
  border-radius: 4px;
  position: relative;
  overflow: hidden;
  opacity: 1;
  display: flex;
  align-items: center;
  transition: opacity .2s;
}

.info {
  color: #0c5460;
  background-color: #d1ecf1;
  border: 1px solid #bee5eb;
}

.warning {
  color: #856404;
  background-color: #fff3cd;
  border: 1px solid #ffeeba;
}

.error {
  color: #721c24;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  .tileRow1 {
    flex: 0 0 calc(33.333% - 30px);
  }
}

@media (max-width: 992px) {
  .tileRow1 {
    flex: 0 0 calc(50% - 30px);
  }
}

@media (max-width: 768px) {
  .tileRow1 {
    flex: 0 0 calc(100% - 30px);
  }
  
  .left-menu {
    width: 180px;
  }
  
  .right-content {
    margin-left: 180px;
  }
}