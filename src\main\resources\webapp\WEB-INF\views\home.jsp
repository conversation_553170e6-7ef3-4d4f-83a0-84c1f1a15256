<%--

    SteV<PERSON> - SteckdosenVerwaltung - https://github.com/steve-community/steve
    Copyright (C) 2013-2025 SteVe Community Team
    All Rights Reserved.

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.

--%>
<%@ include file="00-header.jsp" %>
<div class="full-content-container">
    <div class="content">
        <h2 class="text-center mb20">System Overview</h2>
        <!-- 原有瓦片区域已移除，只保留图表区域 -->

        <!-- Data Visualization Charts -->
        <div class="charts-section">
            <h3 class="text-center mb20">System Statistics Charts</h3>
            <div class="charts-container">
                <div class="chart-item">
                    <h4>Fault Status Distribution</h4>
                    <div class="chart-wrapper">
                        <canvas id="faultStatusChart" width="400" height="300"></canvas>
                    </div>
                </div>
                <div class="chart-item">
                    <h4>Charge Point Online Status</h4>
                    <div class="chart-wrapper">
                        <canvas id="onlineStatusChart" width="400" height="300"></canvas>
                    </div>
                </div>
                <div class="chart-item">
                    <h4>Success Rate - Most Used Charge Points</h4>
                    <div class="chart-wrapper">
                        <canvas id="chargingSuccessChart" width="400" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js库 - 使用本地版本 -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>

<script>
$(document).ready(function() {
    console.log('Page loaded, initializing charts...');

    // Check if Chart.js is loaded
    if (typeof Chart === 'undefined') {
        console.error('Chart.js not loaded');
        showErrorMessage('Chart.js library not loaded correctly');
        return;
    }

    // Load chart data
    loadChartData();
});

function loadChartData() {
    console.log('Loading chart data...');

    $.ajax({
        url: '${ctxPath}/manager/home/<USER>',
        type: 'GET',
        dataType: 'json',
        timeout: 10000,
        success: function(data) {
            console.log('Chart data loaded successfully:', data);
            try {
                renderFaultStatusChart(data.faultStatusChart);
                renderOnlineStatusChart(data.onlineStatusChart);
                renderChargingSuccessChart(data.chargingSuccessChart);
            } catch (e) {
                console.error('Error rendering charts:', e);
                showErrorMessage('Failed to render charts: ' + e.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('Failed to load chart data:', xhr.status, error);
            console.error('Response content:', xhr.responseText);
            showErrorMessage('Failed to load chart data: ' + error);
        }
    });
}

function renderFaultStatusChart(chartData) {
    console.log('Rendering fault status chart:', chartData);

    const canvas = document.getElementById('faultStatusChart');
    if (!canvas) {
        console.error('Cannot find fault status chart canvas element');
        return;
    }

    const ctx = canvas.getContext('2d');

    try {
        new Chart(ctx, {
            type: 'pie',
            data: {
                labels: chartData.labels || ['No Data'],
                datasets: [{
                    data: chartData.data || [1],
                    backgroundColor: chartData.backgroundColor || ['#cccccc'],
                    borderColor: chartData.borderColor || ['#999999'],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: chartData.title || 'Fault Status Distribution',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
        console.log('Fault status chart rendered successfully');
    } catch (e) {
        console.error('Failed to render fault status chart:', e);
    }
}

function renderOnlineStatusChart(chartData) {
    console.log('Rendering online status chart:', chartData);

    const canvas = document.getElementById('onlineStatusChart');
    if (!canvas) {
        console.error('Cannot find online status chart canvas element');
        return;
    }

    const ctx = canvas.getContext('2d');

    try {
        new Chart(ctx, {
            type: 'pie',
            data: {
                labels: chartData.labels || ['Online', 'Offline'],
                datasets: [{
                    data: chartData.data || [0, 1],
                    backgroundColor: chartData.backgroundColor || ['#36a2eb', '#ff9f40'],
                    borderColor: chartData.borderColor || ['#36a2eb', '#ff9f40'],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: chartData.title || 'Charge Point Online Status',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
        console.log('Online status chart rendered successfully');
    } catch (e) {
        console.error('Failed to render online status chart:', e);
    }
}

function renderChargingSuccessChart(chartData) {
    console.log('Rendering charging success chart:', chartData);

    const canvas = document.getElementById('chargingSuccessChart');
    if (!canvas) {
        console.error('Cannot find charging success chart canvas element');
        return;
    }

    const ctx = canvas.getContext('2d');

    try {
        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: chartData.labels || ['No Data'],
                datasets: [{
                    label: 'Success Rate (%)',
                    data: chartData.data || [0],
                    backgroundColor: chartData.backgroundColor || ['#4bc0c0'],
                    borderColor: chartData.borderColor || ['#4bc0c0'],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: chartData.title || 'Success Rate - Most Used Charge Points',
                        font: {
                            size: 14,
                            weight: 'bold'
                        }
                    },
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            afterLabel: function(context) {
                                // Display total sessions in tooltip
                                if (chartData.additionalData && chartData.additionalData[context.dataIndex]) {
                                    return 'Total Sessions: ' + chartData.additionalData[context.dataIndex];
                                }
                                return '';
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            maxRotation: 0,  // 水平显示标签
                            minRotation: 0,
                            font: {
                                size: 11
                            }
                        }
                    },
                    y: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        },
                        title: {
                            display: true,
                            text: 'Success Rate (%)',
                            font: {
                                size: 12,
                                weight: 'bold'
                            }
                        }
                    }
                }
            }
        });
        console.log('Charging success chart rendered successfully');
    } catch (e) {
        console.error('Failed to render charging success chart:', e);
    }
}

function showErrorMessage(message) {
    const errorMsg = message || 'Failed to load chart data, please refresh the page';
    $('.charts-container').html('<div class="error-message">' + errorMsg + '</div>');
    console.error('Showing error message:', errorMsg);
}
</script>

<%@ include file="00-footer.jsp" %>