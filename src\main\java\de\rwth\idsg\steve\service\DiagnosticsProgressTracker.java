/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * Service to track GetDiagnostics progress
 */
@Slf4j
@Service
public class DiagnosticsProgressTracker {

    private final ConcurrentMap<String, DiagnosticsProgress> progressMap = new ConcurrentHashMap<>();

    // 存储充电桩ID映射，用于文件命名
    private final Map<String, String> chargePointIdMap = new ConcurrentHashMap<>();

    @Autowired
    private ChargePointIdentityMappingService identityMappingService;
    
    public static class DiagnosticsProgress {
        private final String chargeBoxId;
        private int percentage = 0;
        private String status = "Initializing";
        private String message = "";
        private long startTime = System.currentTimeMillis();
        
        public DiagnosticsProgress(String chargeBoxId) {
            this.chargeBoxId = chargeBoxId;
        }
        
        public String getChargeBoxId() { return chargeBoxId; }
        public int getPercentage() { return percentage; }
        public String getStatus() { return status; }
        public String getMessage() { return message; }
        public long getStartTime() { return startTime; }
        public long getElapsedTime() { return System.currentTimeMillis() - startTime; }
        
        public void updateProgress(int percentage, String status, String message) {
            this.percentage = percentage;
            this.status = status;
            this.message = message;
            log.info("📊 GetDiagnostics Progress [{}]: {}% - {} - {}", 
                    chargeBoxId, percentage, status, message);
        }
    }
    
    /**
     * Start tracking progress for a charge point
     */
    public void startTracking(String chargeBoxId) {
        startTracking(chargeBoxId, null);
    }

    /**
     * Start tracking progress for a charge point with a display name
     */
    public void startTracking(String chargeBoxId, String displayName) {
        // 如果已经在跟踪，先清除旧的进度
        if (progressMap.containsKey(chargeBoxId)) {
            log.info("🔄 Clearing existing progress tracking for {} to start new request", chargeBoxId);
            progressMap.remove(chargeBoxId);
        }

        // 存储充电桩ID映射 - 使用显示名称或生成友好名称
        String friendlyName = displayName != null ? displayName : generateFriendlyName(chargeBoxId);
        chargePointIdMap.put(chargeBoxId, friendlyName);
        log.info("📝 Mapping charge point: {} -> {}", chargeBoxId, friendlyName);

        DiagnosticsProgress progress = new DiagnosticsProgress(chargeBoxId);
        progressMap.put(chargeBoxId, progress);
        progress.updateProgress(0, "Request Sent", "GetDiagnostics request sent to charge point");
        log.info("🚀 Started tracking GetDiagnostics progress for {}", chargeBoxId);
    }
    
    /**
     * Update progress for a charge point
     */
    public void updateProgress(String chargeBoxId, int percentage, String status, String message) {
        DiagnosticsProgress progress = progressMap.get(chargeBoxId);
        if (progress != null) {
            progress.updateProgress(percentage, status, message);
        }
    }
    
    /**
     * Mark as request acknowledged by charge point
     */
    public void markRequestAcknowledged(String chargeBoxId, String fileName) {
        updateProgress(chargeBoxId, 25, "Request Acknowledged", 
                "Charge point acknowledged request. File: " + (fileName != null ? fileName : "Unknown"));
    }
    
    /**
     * Mark as file generation in progress
     */
    public void markFileGenerating(String chargeBoxId) {
        updateProgress(chargeBoxId, 50, "Generating File", 
                "Charge point is generating diagnostic file...");
    }
    
    /**
     * Mark as file upload in progress
     */
    public void markFileUploading(String chargeBoxId) {
        updateProgress(chargeBoxId, 75, "Uploading File", 
                "Charge point is uploading diagnostic file to FTP server...");
    }
    
    /**
     * Mark as file ready for download
     */
    public void markFileReadyForDownload(String chargeBoxId, String fileName) {
        updateProgress(chargeBoxId, 85, "File Ready",
                "Diagnostic file ready for download: " + fileName);
    }

    /**
     * Mark as file downloading
     */
    public void markFileDownloading(String chargeBoxId) {
        updateProgress(chargeBoxId, 90, "Downloading",
                "Downloading diagnostic file to your computer...");
    }

    /**
     * Mark as download completed
     */
    public void markDownloadCompleted(String chargeBoxId, String fileName) {
        updateProgress(chargeBoxId, 100, "Download Completed",
                "Diagnostic file downloaded successfully: " + fileName);

        // Keep progress for 5 minutes then remove
        new Thread(() -> {
            try {
                Thread.sleep(300000); // 5 minutes
                progressMap.remove(chargeBoxId);
                log.info("🧹 Cleaned up progress tracking for {}", chargeBoxId);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }).start();
    }

    /**
     * Mark as completed (legacy method for backward compatibility)
     */
    public void markCompleted(String chargeBoxId, String fileName) {
        markDownloadCompleted(chargeBoxId, fileName);
    }
    
    /**
     * Mark as failed
     */
    public void markFailed(String chargeBoxId, String error) {
        updateProgress(chargeBoxId, -1, "Failed", "Error: " + error);
        
        // Keep progress for 10 minutes then remove
        new Thread(() -> {
            try {
                Thread.sleep(600000); // 10 minutes
                progressMap.remove(chargeBoxId);
                log.info("🧹 Cleaned up failed progress tracking for {}", chargeBoxId);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }).start();
    }
    
    /**
     * Get progress for a charge point
     */
    public DiagnosticsProgress getProgress(String chargeBoxId) {
        return progressMap.get(chargeBoxId);
    }
    
    /**
     * Get all current progress
     */
    public ConcurrentMap<String, DiagnosticsProgress> getAllProgress() {
        return progressMap;
    }
    
    /**
     * Check if tracking exists for a charge point
     */
    public boolean isTracking(String chargeBoxId) {
        return progressMap.containsKey(chargeBoxId);
    }

    /**
     * Force clear progress tracking for a charge point (allows repeat requests)
     */
    public void clearTracking(String chargeBoxId) {
        DiagnosticsProgress removed = progressMap.remove(chargeBoxId);
        if (removed != null) {
            log.info("🧹 Force cleared progress tracking for {}", chargeBoxId);
        }
    }

    /**
     * Clear all progress tracking
     */
    public void clearAllTracking() {
        int count = progressMap.size();
        progressMap.clear();
        chargePointIdMap.clear();
        log.info("🧹 Cleared all progress tracking ({} entries)", count);
    }

    /**
     * Get charge point ID for file naming
     * 使用动态学习的映射服务来处理标识转换
     */
    public String getChargePointIdForFile(String originalFileName) {
        log.info("🔍 Getting charge point ID for file: {}", originalFileName);

        // 首先尝试从本地映射中找到匹配的充电桩ID
        for (Map.Entry<String, String> entry : chargePointIdMap.entrySet()) {
            String chargeBoxId = entry.getKey();
            // 如果原文件名包含充电桩ID的一部分，返回充电桩ID
            if (originalFileName.contains(chargeBoxId) || chargeBoxId.contains(originalFileName.substring(0, Math.min(originalFileName.length(), 10)))) {
                log.info("✅ Found match in local mapping: {} -> {}", originalFileName, chargeBoxId);
                return chargeBoxId;
            }
        }

        // 使用智能映射服务进行动态学习和转换
        if (identityMappingService != null) {
            // 查找活跃的充电桩连接
            String activeChargeBoxId = findActiveChargeBoxId();
            if (activeChargeBoxId != null) {
                // 让映射服务动态学习和解析
                String resolvedId = identityMappingService.resolveChargePointIdentity(originalFileName, activeChargeBoxId);
                log.info("🎓 Dynamic resolution result: {} -> {} (active: {})",
                        originalFileName, resolvedId, activeChargeBoxId);
                return resolvedId;
            } else {
                log.warn("⚠️ No active charge box found for dynamic learning");
            }
        } else {
            log.warn("⚠️ Identity mapping service not available");
        }

        // 如果映射服务不可用，回退到传统方法
        String extractedId = extractChargePointIdFromFileName(originalFileName);
        log.info("🔙 Fallback to extracted ID: {}", extractedId);
        return extractedId;
    }

    /**
     * Extract charge point ID from file name
     */
    private String extractChargePointIdFromFileName(String fileName) {
        // 常见的充电桩ID模式
        // 1. 如果文件名以CP开头，提取CP后面的数字
        if (fileName.matches(".*CP\\d+.*")) {
            java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("(CP\\d+)");
            java.util.regex.Matcher matcher = pattern.matcher(fileName);
            if (matcher.find()) {
                return matcher.group(1);
            }
        }

        // 2. 如果文件名包含下划线分隔的时间戳，取第一部分作为ID
        if (fileName.contains("_")) {
            String[] parts = fileName.split("_");
            if (parts.length > 0 && parts[0].length() > 0) {
                // 如果第一部分看起来像充电桩ID（包含字母和数字），使用它
                if (parts[0].matches(".*[A-Za-z].*") && parts[0].length() <= 20) {
                    return parts[0];
                }
            }
        }

        // 3. 默认返回Unknown
        return "UnknownCP";
    }

    /**
     * Get all currently tracked charge points
     */
    public java.util.Set<String> getAllTrackedChargePoints() {
        return new java.util.HashSet<>(chargePointIdMap.keySet());
    }

    /**
     * Generate a friendly name from charge box ID
     */
    private String generateFriendlyName(String chargeBoxId) {
        // 静态映射表 - 将已知的内部ID映射到友好名称
        Map<String, String> knownMappings = Map.of(
            "A25A58A962D9", "CP005",
            // 可以在这里添加更多映射
            "B36B69C073E1", "CP001",
            "C47C7AD184F2", "CP002"
        );

        // 首先检查静态映射
        if (knownMappings.containsKey(chargeBoxId)) {
            String friendlyName = knownMappings.get(chargeBoxId);
            log.info("🎯 Found static mapping: {} -> {}", chargeBoxId, friendlyName);
            return friendlyName;
        }

        // 如果已经是友好格式（如CP005），直接返回
        if (chargeBoxId.matches("^CP\\d+$")) {
            return chargeBoxId;
        }

        // 尝试从复杂ID中提取有意义的部分
        // 方案1：如果ID很长，取前几位作为简化版本
        if (chargeBoxId.length() > 8) {
            String shortId = chargeBoxId.substring(0, 8);
            String result = "CP_" + shortId;
            log.info("⚠️ Generated friendly name: {} -> {}", chargeBoxId, result);
            return result;
        }

        // 方案2：直接使用原ID但加上CP前缀
        String result = "CP_" + chargeBoxId;
        log.info("⚠️ Generated friendly name: {} -> {}", chargeBoxId, result);
        return result;
    }

    /**
     * 查找当前活跃的充电桩ID
     * 通过检查进度状态来确定哪个充电桩正在进行诊断
     *
     * @return 活跃的充电桩ID，如果找不到则返回null
     */
    private String findActiveChargeBoxId() {
        // 查找状态不是"Completed"的充电桩
        for (Map.Entry<String, DiagnosticsProgress> entry : progressMap.entrySet()) {
            DiagnosticsProgress progress = entry.getValue();
            if (progress != null && !progress.getStatus().equals("Completed")) {
                log.debug("🔍 Found active charge box with ongoing diagnostics: {}", entry.getKey());
                return entry.getKey();
            }
        }

        log.debug("🔍 No active charge box found in progress tracker");
        return null;
    }

    /**
     * Simulate progress updates for demonstration
     * This would be called by actual OCPP message handlers
     * Note: This simulation should NOT complete automatically - real completion
     * happens when file is actually ready for download
     */
    public void simulateProgress(String chargeBoxId) {
        if (!isTracking(chargeBoxId)) {
            startTracking(chargeBoxId);
        }

        new Thread(() -> {
            try {
                Thread.sleep(2000);
                markRequestAcknowledged(chargeBoxId, "diagnostics_" + System.currentTimeMillis() + ".zip");

                Thread.sleep(5000);
                markFileGenerating(chargeBoxId);

                Thread.sleep(10000);
                markFileUploading(chargeBoxId);

                // 不再自动标记为完成 - 等待实际文件准备好
                log.info("📊 Simulation stopped at 'Uploading File' stage. Waiting for actual file to be ready...");

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                markFailed(chargeBoxId, "Progress simulation interrupted");
            }
        }).start();
    }
}
