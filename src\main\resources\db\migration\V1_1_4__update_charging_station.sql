-- 删除station_id列，因为其功能已经与charging_station_pk重合
ALTER TABLE charging_station DROP COLUMN station_id;

-- 重新创建视图，移除station_id列
DROP VIEW IF EXISTS VIEW_CHARGING_STATIONS;
CREATE VIEW VIEW_CHARGING_STATIONS AS 
SELECT 
    cs.charging_station_pk, 
    cs.station_name, 
    cs.operator_name, 
    cs.location, 
    cs.construction_date, 
    cs.operation_date, 
    cs.created_on, 
    cs.last_updated_on, 
    cs.web_user_pk, 
    u.first_name, 
    u.last_name, 
    u.birth_day, 
    u.sex, 
    u.e_mail, 
    u.phone 
FROM charging_station cs 
LEFT JOIN web_user wu ON cs.web_user_pk = wu.web_user_pk 
LEFT JOIN user u ON wu.web_user_pk = u.user_pk; 