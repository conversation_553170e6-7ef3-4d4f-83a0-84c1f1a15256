/*
 * SteV<PERSON> - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.web.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @since 29.04.2025
 */
@Getter
@Setter
@ToString
public class UpdateFirmwareLogQueryForm {
    private String location;
    private Integer retries;
    private Integer retryInterval;
    private String retrieveDateFrom;
    private String retrieveDateTo;
    private String sendingTimeFrom;
    private String sendingTimeTo;
    private String chargeBoxId;
    private String status;
    private String firmwareStatus;
    
    // Pagination
    private int offset = 0;
    private int limit = 10;

    private DateTime parseDateTimeString(String dateTimeString) {
        if (dateTimeString == null || dateTimeString.isEmpty()) {
            return null;
        }
        // Try parsing yyyy-MM-dd HH:mm first
        try {
            DateTimeFormatter fmtWithTime = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm");
            return fmtWithTime.parseDateTime(dateTimeString);
        } catch (IllegalArgumentException e1) {
            // If that fails, try parsing yyyy-MM-dd (time will be midnight)
            try {
                DateTimeFormatter fmtDateOnly = DateTimeFormat.forPattern("yyyy-MM-dd");
                return fmtDateOnly.parseDateTime(dateTimeString);
            } catch (IllegalArgumentException e2) {
                // If both fail, try ISO standard format as a last resort or rethrow/log
                try {
                    return DateTime.parse(dateTimeString); 
                } catch (IllegalArgumentException e3) {
                    // Log the parsing failure or handle it as appropriate
                    // For now, returning null if all parsing attempts fail
                    System.err.println("Failed to parse date-time string: " + dateTimeString + " after multiple attempts.");
                    return null;
                }
            }
        }
    }

    // Renamed getters to avoid conflict with Spring form binding to String fields
    public DateTime getRetrieveDateFromAsDateTime() {
        return parseDateTimeString(this.retrieveDateFrom);
    }

    public DateTime getRetrieveDateToAsDateTime() {
        return parseDateTimeString(this.retrieveDateTo);
    }

    public DateTime getSendingTimeFromAsDateTime() {
        return parseDateTimeString(this.sendingTimeFrom);
    }

    public DateTime getSendingTimeToAsDateTime() {
        return parseDateTimeString(this.sendingTimeTo);
    }
} 