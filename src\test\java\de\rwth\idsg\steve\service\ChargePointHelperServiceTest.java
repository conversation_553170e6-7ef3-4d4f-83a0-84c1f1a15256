/*
 * SteV<PERSON> - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.service;

import de.rwth.idsg.steve.repository.ChargePointRepository;
import de.rwth.idsg.steve.web.controller.ChargePointsController.ImportProgress;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import org.jooq.exception.IntegrityConstraintViolationException;

/**
 * 测试ChargePointHelperService的批量导入功能
 * 特别是验证修复临时文件问题后的功能是否正常
 */
@ExtendWith(MockitoExtension.class)
class ChargePointHelperServiceTest {

    @Mock
    private ChargePointRepository chargePointRepository;

    @InjectMocks
    private ChargePointHelperService chargePointHelperService;

    private Map<String, ImportProgress> importTasks;
    private String taskId;

    @BeforeEach
    void setUp() {
        importTasks = new HashMap<>();
        taskId = "test-task-id";
        importTasks.put(taskId, new ImportProgress());
    }

    @Test
    void testImportChargePointsFromIni_WithValidContent() throws InterruptedException {
        // 准备测试数据 - 模拟.ini文件内容
        String iniContent = """
                [ChargePoint001]
                description=Test Charge Point 1
                
                [ChargePoint002]
                description=Test Charge Point 2
                
                [ChargePoint003]
                description=Test Charge Point 3
                """;
        
        byte[] fileContent = iniContent.getBytes(StandardCharsets.UTF_8);
        
        // Mock repository行为 - 假设所有充电桩都是新的
        when(chargePointRepository.getChargeBoxPkFromChargeBoxId(anyString())).thenReturn(null);
        doNothing().when(chargePointRepository).addChargePointList(any());
        
        // 使用CountDownLatch等待异步操作完成
        CountDownLatch latch = new CountDownLatch(1);
        ImportProgress progress = importTasks.get(taskId);
        
        // 在另一个线程中监控进度
        Thread monitorThread = new Thread(() -> {
            while (!progress.isFinished()) {
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
            latch.countDown();
        });
        monitorThread.start();
        
        // 执行测试
        chargePointHelperService.importChargePointsFromIni(fileContent, taskId, importTasks);
        
        // 等待异步操作完成（最多等待5秒）
        assertTrue(latch.await(5, TimeUnit.SECONDS), "Import should complete within 5 seconds");
        
        // 验证结果
        assertTrue(progress.isFinished(), "Import should be finished");
        assertEquals(3, progress.getTotal(), "Should have found 3 charge points");
        assertEquals(3, progress.getProcessed(), "Should have processed 3 charge points");
        assertEquals(3, progress.getAdded(), "Should have added 3 new charge points");
        assertEquals(0, progress.getExisted(), "Should have 0 existing charge points");
        
        // 验证repository调用
        verify(chargePointRepository, times(3)).getChargeBoxPkFromChargeBoxId(anyString());
        verify(chargePointRepository, times(1)).addChargePointList(argThat(list -> 
            list.size() == 3 && 
            list.contains("ChargePoint001") && 
            list.contains("ChargePoint002") && 
            list.contains("ChargePoint003")
        ));
    }

    @Test
    void testImportChargePointsFromIni_WithExistingChargePoints() throws InterruptedException {
        // 准备测试数据
        String iniContent = """
                [ExistingPoint001]
                description=Existing Charge Point
                
                [NewPoint001]
                description=New Charge Point
                """;
        
        byte[] fileContent = iniContent.getBytes(StandardCharsets.UTF_8);
        
        // Mock repository行为 - ExistingPoint001已存在，NewPoint001是新的
        when(chargePointRepository.getChargeBoxPkFromChargeBoxId("ExistingPoint001")).thenReturn(1);
        when(chargePointRepository.getChargeBoxPkFromChargeBoxId("NewPoint001")).thenReturn(null);
        doNothing().when(chargePointRepository).addChargePointList(any());
        
        CountDownLatch latch = new CountDownLatch(1);
        ImportProgress progress = importTasks.get(taskId);
        
        Thread monitorThread = new Thread(() -> {
            while (!progress.isFinished()) {
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
            latch.countDown();
        });
        monitorThread.start();
        
        // 执行测试
        chargePointHelperService.importChargePointsFromIni(fileContent, taskId, importTasks);
        
        // 等待异步操作完成
        assertTrue(latch.await(5, TimeUnit.SECONDS), "Import should complete within 5 seconds");
        
        // 验证结果
        assertTrue(progress.isFinished(), "Import should be finished");
        assertEquals(2, progress.getTotal(), "Should have found 2 charge points");
        assertEquals(2, progress.getProcessed(), "Should have processed 2 charge points");
        assertEquals(1, progress.getAdded(), "Should have added 1 new charge point");
        assertEquals(1, progress.getExisted(), "Should have 1 existing charge point");
        
        // 验证repository调用
        verify(chargePointRepository, times(1)).addChargePointList(argThat(list -> 
            list.size() == 1 && list.contains("NewPoint001")
        ));
    }

    @Test
    void testImportChargePointsFromIni_WithEmptyContent() throws InterruptedException {
        // 准备空内容
        byte[] fileContent = "".getBytes(StandardCharsets.UTF_8);
        
        CountDownLatch latch = new CountDownLatch(1);
        ImportProgress progress = importTasks.get(taskId);
        
        Thread monitorThread = new Thread(() -> {
            while (!progress.isFinished()) {
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
            latch.countDown();
        });
        monitorThread.start();
        
        // 执行测试
        chargePointHelperService.importChargePointsFromIni(fileContent, taskId, importTasks);
        
        // 等待异步操作完成
        assertTrue(latch.await(5, TimeUnit.SECONDS), "Import should complete within 5 seconds");
        
        // 验证结果
        assertTrue(progress.isFinished(), "Import should be finished");
        assertEquals(0, progress.getTotal(), "Should have found 0 charge points");
        assertEquals(0, progress.getProcessed(), "Should have processed 0 charge points");
        assertEquals(0, progress.getAdded(), "Should have added 0 charge points");
        assertEquals(0, progress.getExisted(), "Should have 0 existing charge points");
        
        // 验证repository没有被调用添加充电桩
        verify(chargePointRepository, never()).addChargePointList(any());
    }

    @Test
    void testImportChargePointsFromIni_WithInvalidTaskId() {
        // 使用不存在的taskId
        String invalidTaskId = "invalid-task-id";
        byte[] fileContent = "[TestPoint]".getBytes(StandardCharsets.UTF_8);

        // 执行测试 - 应该不会抛出异常，但也不会处理任何内容
        assertDoesNotThrow(() -> {
            chargePointHelperService.importChargePointsFromIni(fileContent, invalidTaskId, importTasks);
        });

        // 验证repository没有被调用
        verify(chargePointRepository, never()).getChargeBoxPkFromChargeBoxId(anyString());
        verify(chargePointRepository, never()).addChargePointList(any());
    }

    @Test
    void testImportChargePointsFromIni_WithDuplicateIds() throws InterruptedException {
        // 准备包含重复ID的测试数据
        String iniContent = """
                [ChargePoint001]
                description=Test Charge Point 1

                [ChargePoint002]
                description=Test Charge Point 2

                [ChargePoint001]
                description=Duplicate Charge Point 1
                """;

        byte[] fileContent = iniContent.getBytes(StandardCharsets.UTF_8);

        // Mock repository行为 - 假设所有充电桩都是新的
        when(chargePointRepository.getChargeBoxPkFromChargeBoxId(anyString())).thenReturn(null);
        doNothing().when(chargePointRepository).addChargePointList(any());

        CountDownLatch latch = new CountDownLatch(1);
        ImportProgress progress = importTasks.get(taskId);

        Thread monitorThread = new Thread(() -> {
            while (!progress.isFinished()) {
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
            latch.countDown();
        });
        monitorThread.start();

        // 执行测试
        chargePointHelperService.importChargePointsFromIni(fileContent, taskId, importTasks);

        // 等待异步操作完成
        assertTrue(latch.await(5, TimeUnit.SECONDS), "Import should complete within 5 seconds");

        // 验证结果 - 应该只处理2个唯一的充电桩（去重后）
        assertTrue(progress.isFinished(), "Import should be finished");
        assertEquals(2, progress.getTotal(), "Should have found 2 unique charge points");
        assertEquals(2, progress.getProcessed(), "Should have processed 2 charge points");
        assertEquals(2, progress.getAdded(), "Should have added 2 new charge points");
        assertEquals(0, progress.getExisted(), "Should have 0 existing charge points");

        // 验证repository调用 - 只检查唯一的充电桩ID
        verify(chargePointRepository, times(2)).getChargeBoxPkFromChargeBoxId(anyString());
        verify(chargePointRepository, times(1)).addChargePointList(argThat(list ->
            list.size() == 2 &&
            list.contains("ChargePoint001") &&
            list.contains("ChargePoint002")
        ));
    }

    @Test
    void testImportChargePointsFromIni_WithConstraintViolation() throws InterruptedException {
        // 准备测试数据
        String iniContent = """
                [ChargePoint001]
                description=Test Charge Point 1

                [ChargePoint002]
                description=Test Charge Point 2
                """;

        byte[] fileContent = iniContent.getBytes(StandardCharsets.UTF_8);

        // Mock repository行为 - 初始检查时都不存在，但批量插入时抛出约束违反异常
        when(chargePointRepository.getChargeBoxPkFromChargeBoxId(anyString())).thenReturn(null);
        doThrow(new IntegrityConstraintViolationException("Duplicate entry"))
            .doNothing() // 第二次调用（逐个插入）成功
            .when(chargePointRepository).addChargePointList(any());

        CountDownLatch latch = new CountDownLatch(1);
        ImportProgress progress = importTasks.get(taskId);

        Thread monitorThread = new Thread(() -> {
            while (!progress.isFinished()) {
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
            latch.countDown();
        });
        monitorThread.start();

        // 执行测试
        chargePointHelperService.importChargePointsFromIni(fileContent, taskId, importTasks);

        // 等待异步操作完成
        assertTrue(latch.await(5, TimeUnit.SECONDS), "Import should complete within 5 seconds");

        // 验证结果
        assertTrue(progress.isFinished(), "Import should be finished");
        assertEquals(2, progress.getTotal(), "Should have found 2 charge points");
        assertEquals(2, progress.getProcessed(), "Should have processed 2 charge points");

        // 验证repository调用 - 应该先尝试批量插入，失败后逐个插入
        verify(chargePointRepository, atLeast(2)).addChargePointList(any());
    }
}
