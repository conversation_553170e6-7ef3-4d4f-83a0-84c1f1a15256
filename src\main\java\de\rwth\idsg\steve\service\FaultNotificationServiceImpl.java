/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.service;

import de.rwth.idsg.steve.repository.FaultNotificationEmailRepository;
import de.rwth.idsg.steve.web.dto.ChargerIssueDTO;
import de.rwth.idsg.steve.web.dto.FaultNotificationEmailDTO;
import de.rwth.idsg.steve.web.dto.FaultNotificationEmailForm;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.List;

/**
 * 故障通知服务实现
 */
@Slf4j
@Service
public class FaultNotificationServiceImpl implements FaultNotificationService {

    @Autowired
    private FaultNotificationEmailRepository emailRepository;
    
    @Autowired
    private MailService mailService;

    @Override
    public List<FaultNotificationEmailDTO> getAllEmails() {
        return emailRepository.getAll();
    }

    @Override
    public List<FaultNotificationEmailDTO> getEnabledEmails() {
        return emailRepository.getEnabled();
    }

    @Override
    @Transactional
    public int addEmail(FaultNotificationEmailForm form) {
        return emailRepository.add(form);
    }

    @Override
    @Transactional
    public void updateEmail(FaultNotificationEmailForm form) {
        emailRepository.update(form);
    }

    @Override
    @Transactional
    public void deleteEmail(Integer id) {
        emailRepository.delete(id);
    }

    @Override
    @Transactional
    public void sendNewIssueNotification(ChargerIssueDTO issue) {
        List<FaultNotificationEmailDTO> emails = getEnabledEmails();
        if (emails.isEmpty()) {
            log.info("No enabled notification emails configured, skipping fault notification");
            return;
        }
        
        log.debug("Preparing to send fault notification email, IssueID={}, recipient count={}", issue.getIssueId(), emails.size());
        
        String subject = String.format("Charging Station Fault Notification: %s", issue.getChargeBoxId());
        
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String reportTime = issue.getReportTime() != null ? sdf.format(issue.getReportTime()) : "Unknown";
        
        StringBuilder body = new StringBuilder();
        body.append("Charging Station Fault Report\n");
        body.append("------------\n\n");
        body.append("Fault ID: ").append(issue.getIssueId()).append("\n");
        body.append("Charging Station: ").append(issue.getChargeBoxId()).append("\n");
        body.append("Report Time: ").append(reportTime).append("\n");
        body.append("Fault Status: ").append(issue.getStatus().getDescription()).append("\n");
        
        if (issue.getOcppErrorCode() != null && !issue.getOcppErrorCode().isEmpty()) {
            body.append("OCPP Error Code: ").append(issue.getOcppErrorCode()).append("\n");
        }
        
        body.append("Fault Description: ").append(issue.getFaultDescription()).append("\n\n");
        body.append("Please login to the system for details: http://localhost:8080/steve/manager/operations/faults");
        
        try {
            // 发送一次邮件，所有收件人已由邮件服务的配置提供
            mailService.sendAsync(subject, body.toString());
            
            // 更新每个邮箱的最后通知时间
            for (FaultNotificationEmailDTO email : emails) {
                emailRepository.updateLastNotifiedTime(email.getId());
            }
            
            log.info("Successfully sent fault notification email, Fault ID={}", issue.getIssueId());
        } catch (Exception e) {
            log.error("Failed to send fault notification email: {}", e.getMessage(), e);
        }
    }
} 