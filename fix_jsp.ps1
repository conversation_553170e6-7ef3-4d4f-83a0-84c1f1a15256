$files = @("TriggerMessage.jsp", "DataTransfer.jsp", "GetCompositeSchedule.jsp", "GetCompositeScheduleResponse.jsp", "GetConfiguration.jsp", "GetDiagnostics.jsp", "GetLocalListVersion.jsp", "RemoteStartTransaction.jsp", "RemoteStopTransaction.jsp", "ReserveNow.jsp", "Reset.jsp", "SendLocalList.jsp", "SetChargingProfile.jsp", "UnlockConnector.jsp", "UpdateFirmware.jsp", "CancelReservation.jsp")

foreach ($file in $files) {
    Write-Output "正在处理: $file"
    
    $content = Get-Content "src/main/resources/webapp/WEB-INF/views/op16/$file" -Raw
    $scriptMatch = [regex]::Match($content, '<script[\s\S]*?</script>')
    $scriptContent = if ($scriptMatch.Success) { $scriptMatch.Value } else { "" }
    
    $baseName = $file.Replace(".jsp", "")
    $formName = $baseName + "Form.jsp"
    
    $headerContent = @'
<%--

    SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
    Copyright (C) 2013-2025 SteVe Community Team
    All Rights Reserved.

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.

--%>
<%@ include file="../00-header.jsp" %>
<%@ include file="../00-op-bind-errors.jsp" %>
'@

    $footerContent = '<%@ include file="../00-footer.jsp" %>'
    
    $newContent = $headerContent
    
    if ($scriptContent -ne "") {
        $newContent += "`n$scriptContent"
    }
    
    $newContent += @"
<div class="content">
    <%@ include file="../snippets/ocppv16-subnav.jsp" %>
    <div class="op16-content">
        <%@ include file="../op-forms/$formName" %>
    </div>
</div>
"@
    
    $newContent += $footerContent
    
    Set-Content "src/main/resources/webapp/WEB-INF/views/op16/$file" $newContent -Encoding UTF8
}