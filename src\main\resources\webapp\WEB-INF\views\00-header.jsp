<%--

    St<PERSON><PERSON><PERSON> - SteckdosenVerwaltung - https://github.com/steve-community/steve
    Copyright (C) 2013-2025 SteVe Community Team
    All Rights Reserved.

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.

--%>
<%@ page contentType="text/html;charset=UTF-8" pageEncoding="UTF-8" language="java" trimDirectiveWhitespaces="true" %>
<%@ taglib uri="jakarta.tags.core" prefix="c" %>
<%@ taglib uri="http://www.springframework.org/tags" prefix="spring" %>
<%@ taglib uri="http://www.springframework.org/tags/form" prefix="form" %>
<%@ taglib uri="owasp.encoder.jakarta" prefix="encode" %>
<%@ taglib uri="http://www.springframework.org/security/tags" prefix="sec" %>
<%@ taglib uri="jakarta.tags.fmt" prefix="fmt" %>

<%@ include file="00-context.jsp" %>

<!DOCTYPE html>
<html>
<head>
    <meta name="ctx" content="${ctxPath}">
    <meta name="_csrf" content="${_csrf.token}"/>
    <meta name="_csrf_header" content="${_csrf.headerName}"/>
    <link rel="icon" href="${ctxPath}/static/images/favicon.ico" type="image/x-icon" />
    <link rel="shortcut icon" href="${ctxPath}/static/images/favicon.ico" type="image/x-icon" />

    <!-- 基础样式文件 - 按优先级顺序加载 -->
    <link rel="stylesheet" type="text/css" href="${ctxPath}/static/css/jquery-ui.min.css">
    <link rel="stylesheet" type="text/css" href="${ctxPath}/static/css/jquery-ui-timepicker-addon.min.css">

    <!-- Bootstrap网格系统补充 - 提供完整的响应式支持 -->
    <link rel="stylesheet" type="text/css" href="${ctxPath}/static/css/bootstrap-grid-supplement.css">

    <!-- 统一表单样式系统 - 优先加载以建立基础 -->
    <link rel="stylesheet" type="text/css" href="${ctxPath}/static/css/unified-form-styles.css">

    <!-- RuoYi-Vue 样式系统 -->
    <link rel="stylesheet" type="text/css" href="${ctxPath}/static/css/ruoyi/index.css">

    <!-- 布局相关样式 -->
    <link rel="stylesheet" type="text/css" href="${ctxPath}/static/css/layout-fix.css">

    <!-- 布局对齐修复样式 - 解决具体的对齐问题 -->
    <link rel="stylesheet" type="text/css" href="${ctxPath}/static/css/layout-alignment-fix.css">

    <!-- 表单布局修复样式 - 专门解决表单对齐问题 -->
    <link rel="stylesheet" type="text/css" href="${ctxPath}/static/css/form-layout-fix.css">

    <!-- 页面布局修复样式 - 修复各个页面的布局和对齐问题 -->
    <link rel="stylesheet" type="text/css" href="${ctxPath}/static/css/page-layout-fix.css">

    <!-- 功能特定样式 -->
    <link rel="stylesheet" type="text/css" href="${ctxPath}/static/css/ocpp-operations.css">

    <!-- 原有样式文件 - 最后加载以保持兼容性 -->
    <link rel="stylesheet" type="text/css" href="${ctxPath}/static/css/style.css">

    <!-- 按钮修复样式 - 覆盖性修复 -->
    <link rel="stylesheet" type="text/css" href="${ctxPath}/static/css/button-fix.css">

    <!-- 下拉菜单修复样式 - 解决下拉菜单被遮挡问题 -->
    <link rel="stylesheet" type="text/css" href="${ctxPath}/static/css/dropdown-fix.css">

    <!-- 图表样式 -->
    <link rel="stylesheet" type="text/css" href="${ctxPath}/static/css/charts.css">

    <!-- 所有者分配页面样式 -->
    <c:if test="${requestScope['jakarta.servlet.forward.servlet_path'].contains('/manager/ownerAssignments')}">
    <link rel="stylesheet" type="text/css" href="${ctxPath}/static/css/owner-assignment.css">
    </c:if>
    <script type="text/javascript" src="${ctxPath}/static/js/jquery-2.0.3.min.js" ></script>
    <script type="text/javascript" src="${ctxPath}/static/js/jquery-ui.min.js" ></script>
    <script type="text/javascript" src="${ctxPath}/static/js/jquery-ui-timepicker-addon.min.js" ></script>
    <script type="text/javascript" src="${ctxPath}/static/js/script.js" ></script>
    <script type="text/javascript" src="${ctxPath}/static/js/stupidtable.min.js" ></script>
    <!-- RuoYi-Vue UI Enhancement Script -->
    <script type="text/javascript" src="${ctxPath}/static/js/ruoyi-ui.js" ></script>
    <!-- 按钮修复脚本 -->
    <script type="text/javascript" src="${ctxPath}/static/js/button-fix.js" ></script>
    <!-- 布局控制脚本 -->
    <script type="text/javascript" src="${ctxPath}/static/js/layout-fix.js" ></script>
    <!-- 面包屑导航脚本 -->
    <script type="text/javascript" src="${ctxPath}/static/js/breadcrumb.js" ></script>
    <!-- OCPP v1.6导航高亮脚本 -->
    <script type="text/javascript" src="${ctxPath}/static/js/ocpp-nav-highlight.js"></script>
    <!-- OCPP操作脚本 -->
    <script type="text/javascript" src="${ctxPath}/static/js/ocpp-operations.js"></script>
    <title>EVSE Management System</title>
</head>
<body>
<div class="main">
    <!-- 顶部导航 -->
    <div class="top-header">
        <div class="logo">EVSE Management System</div>
        <div class="user-info">
            <span><sec:authentication property="principal.username" /></span>
            <a href="${ctxPath}/manager/signout" style="color: #fff; text-decoration: none;">Sign Out</a>
        </div>
    </div>

    <!-- 主要内容区 -->
    <div class="content-wrapper">
        <!-- 左侧垂直导航 -->
        <div class="left-sidebar">
            <div class="menu-title">MENU</div>
            <ul>
                <li><a href="${ctxPath}/manager/home"><i>■</i><span>Home</span></a></li>
                <li class="submenu">
                    <a href="#"><i>■</i><span>Data Management</span></a>
                    <ul>
                        <li><a href="${ctxPath}/manager/chargepoints"><span>Charge Points</span></a></li>
                        <li><a href="${ctxPath}/manager/chargestations"><span>Charge Stations</span></a></li>
                        <sec:authorize access="hasAuthority('ADMIN')">
                        <li><a href="${ctxPath}/manager/ocppTags"><span>OCPP Tags</span></a></li>
                        </sec:authorize>
                        <sec:authorize access="hasAuthority('ADMIN')">
                        <li><a href="${ctxPath}/manager/users"><span>Users</span></a></li>
                        </sec:authorize>
                        <sec:authorize access="hasAnyAuthority('ADMIN', 'OPERATOR_FACTORY')">
                        <li><a href="${ctxPath}/manager/ownerAssignments"><span>Owner Assignments</span></a></li>
                        </sec:authorize>
                        <sec:authorize access="hasAuthority('ADMIN')">
                        <li><a href="${ctxPath}/manager/chargingProfiles"><span>Charging Profiles</span></a></li>
                        </sec:authorize>
                        <sec:authorize access="hasAuthority('ADMIN')">
                        <li><a href="${ctxPath}/manager/reservations"><span>Reservations</span></a></li>
                        </sec:authorize>
                        <sec:authorize access="hasAuthority('ADMIN')">
                        <li><a href="${ctxPath}/manager/transactions"><span>Transactions</span></a></li>
                        </sec:authorize>
                        <li><a href="${ctxPath}/manager/firmwareUpdate"><span>Firmware Update</span></a></li>
                        <sec:authorize access="hasAuthority('ADMIN')">
                        <li><a href="${ctxPath}/manager/data-man/connectorStatus"><span>Connector Status</span></a></li>
                        </sec:authorize>
                    </ul>
                </li>
                <li class="submenu">
                    <a href="#"><i>■</i><span>Operations</span></a>
                    <ul>
                        <sec:authorize access="hasAuthority('ADMIN')">
                        <%-- OCPP v1.2 and v1.5 are no longer needed in the project
                        <li><a href="${ctxPath}/manager/operations/v1.2"><span>OCPP v1.2</span></a></li>
                        <li><a href="${ctxPath}/manager/operations/v1.5"><span>OCPP v1.5</span></a></li>
                        --%>
                        <li><a href="${ctxPath}/manager/operations/v1.6"><span>OCPP v1.6</span></a></li>
                        </sec:authorize>
                        <sec:authorize access="hasAnyAuthority('ADMIN', 'OPERATOR_FACTORY')">
                        <li><a href="${ctxPath}/manager/operations/faults"><span>Fault Management</span></a></li>
                        </sec:authorize>
                        <sec:authorize access="hasAnyAuthority('ADMIN', 'OPERATOR_FACTORY', 'OPERATOR_OWNER')">
                        <li><a href="${ctxPath}/manager/operations/report-fault"><span>Report Fault</span></a></li>
                        </sec:authorize>
                        <sec:authorize access="hasAnyAuthority('ADMIN', 'OPERATOR_FACTORY')">
                        <li><a href="${ctxPath}/manager/notification"><span>Fault Notification</span></a></li>
                        </sec:authorize>
                    </ul>
                </li>
                <sec:authorize access="hasAnyAuthority('ADMIN', 'OPERATOR_FACTORY')">
                <li><a href="${ctxPath}/manager/settings"><i>■</i><span>Settings</span></a></li>
                </sec:authorize>
                <sec:authorize access="hasAnyAuthority('ADMIN', 'OPERATOR_FACTORY')">
                <li><a href="${ctxPath}/manager/log"><i>■</i><span>Log</span></a></li>
                </sec:authorize>
                <sec:authorize access="hasAnyAuthority('ADMIN', 'OPERATOR_FACTORY')">
                <li><a href="${ctxPath}/manager/about"><i>■</i><span>About</span></a></li>
                </sec:authorize>
            </ul>
        </div>
        
        <!-- 主内容区 -->
        <div class="main-content">
            <div class="content-container">
