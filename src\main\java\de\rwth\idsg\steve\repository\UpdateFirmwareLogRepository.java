/*
 * SteV<PERSON> - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2023 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.repository;

import de.rwth.idsg.steve.web.dto.UpdateFirmwareLog;
import de.rwth.idsg.steve.web.dto.UpdateFirmwareLogQueryForm;
import org.joda.time.DateTime;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Repository interface for UpdateFirmwareLog operations
 *
 * <AUTHOR>
 * @since 29.04.2023
 */
public interface UpdateFirmwareLogRepository {

    /**
     * Insert a new firmware update log record
     *
     * @param location 固件位置URI
     * @param retries 重试次数
     * @param retryInterval 重试间隔
     * @param retrieveDatetime 检索日期/时间
     * @param chargeBoxId 充电桩ID
     * @param status 状态信息
     * @param response 响应内容
     * @return 新记录的ID
     */
    int insert(String location, Integer retries, Integer retryInterval, 
              DateTime retrieveDatetime, String chargeBoxId, String status, String response);

    /**
     * Count total records based on query parameters
     *
     * @param form 查询参数表单
     * @return 记录总数
     */
    int getCount(UpdateFirmwareLogQueryForm form);

    /**
     * Get logs with pagination
     *
     * @param form 查询参数表单
     * @return 更新日志列表
     */
    List<UpdateFirmwareLog> getRecords(UpdateFirmwareLogQueryForm form);

    /**
     * Get logs with pagination using Spring Data Pageable
     *
     * @param form Query parameters form
     * @param pageable Pageable object for pagination
     * @return Page of update logs
     */
    Page<UpdateFirmwareLog> findAll(UpdateFirmwareLogQueryForm form, Pageable pageable);

    /**
     * Get a single log record by ID
     *
     * @param logId 日志ID
     * @return 更新日志
     */
    UpdateFirmwareLog getRecord(int logId);

    /**
     * 获取指定充电站的最近日志记录
     * 
     * @param chargeBoxId 充电站ID
     * @param seconds 过去的秒数
     * @return 最近的日志记录列表
     */
    List<UpdateFirmwareLog> getRecentLogs(String chargeBoxId, int seconds);
    
    /**
     * 更新固件日志的状态
     *
     * @param logId 日志ID
     * @param status 新状态
     * @param response 响应内容
     * @return 更新的记录数
     */
    @Transactional
    int updateStatus(int logId, String status, String response);

    /**
     * Get all logs with specific statuses.
     *
     * @param statuses List of statuses to filter by.
     * @return List of update logs matching the statuses.
     */
    List<UpdateFirmwareLog> getLogsWithStatus(List<String> statuses);
} 