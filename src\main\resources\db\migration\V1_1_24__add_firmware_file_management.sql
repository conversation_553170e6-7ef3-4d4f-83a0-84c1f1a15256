-- 创建固件文件管理表
CREATE TABLE IF NOT EXISTS `firmware_file` (
    `firmware_file_pk` INT(11) NOT NULL AUTO_INCREMENT,
    `filename` VARCHAR(255) NOT NULL COMMENT '文件名',
    `file_type` ENUM('FIRMWARE', 'APPLICATION') NOT NULL COMMENT '文件类型：FIRMWARE(.bin), APPLICATION(.apk)',
    `upload_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
    `upload_status` ENUM('SUCCESS', 'FAILED') NOT NULL COMMENT '上传状态',
    `uploaded_by_user_pk` INT(11) NULL COMMENT '上传用户ID',
    `file_size` BIGINT NULL COMMENT '文件大小（字节）',
    `description` VARCHAR(500) NULL COMMENT '文件描述',
    PRIMARY KEY (`firmware_file_pk`),
    UNIQUE KEY `unique_filename` (`filename`),
    INDEX `idx_upload_status` (`upload_status`),
    INDEX `idx_file_type` (`file_type`),
    INDEX `idx_upload_time` (`upload_time`),
    FOREIGN KEY (`uploaded_by_user_pk`) REFERENCES `web_user`(`web_user_pk`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='固件文件管理表';
