/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.repository.impl;

import de.rwth.idsg.steve.repository.FaultNotificationEmailRepository;
import de.rwth.idsg.steve.web.dto.FaultNotificationEmailDTO;
import de.rwth.idsg.steve.web.dto.FaultNotificationEmailForm;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.jooq.DSLContext;
import org.jooq.Record;
import org.jooq.RecordMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

import static jooq.steve.db.tables.FaultNotificationEmail.FAULT_NOTIFICATION_EMAIL;

/**
 * 故障通知邮箱仓库实现
 */
@Slf4j
@Repository
public class FaultNotificationEmailRepositoryImpl implements FaultNotificationEmailRepository {

    @Autowired
    private DSLContext ctx;

    @Override
    public List<FaultNotificationEmailDTO> getAll() {
        return ctx.selectFrom(FAULT_NOTIFICATION_EMAIL)
                .orderBy(FAULT_NOTIFICATION_EMAIL.CREATE_TIME.desc())
                .fetch(this.recordMapper());
    }

    @Override
    public List<FaultNotificationEmailDTO> getEnabled() {
        return ctx.selectFrom(FAULT_NOTIFICATION_EMAIL)
                .where(FAULT_NOTIFICATION_EMAIL.ENABLED.isTrue())
                .orderBy(FAULT_NOTIFICATION_EMAIL.CREATE_TIME.desc())
                .fetch(this.recordMapper());
    }

    @Override
    public FaultNotificationEmailDTO getById(Integer id) {
        return ctx.selectFrom(FAULT_NOTIFICATION_EMAIL)
                .where(FAULT_NOTIFICATION_EMAIL.ID.eq(id))
                .fetchOne(this.recordMapper());
    }

    @Override
    public int add(FaultNotificationEmailForm form) {
        return ctx.insertInto(FAULT_NOTIFICATION_EMAIL)
                .set(FAULT_NOTIFICATION_EMAIL.EMAIL, form.getEmail())
                .set(FAULT_NOTIFICATION_EMAIL.ENABLED, form.getEnabled() != null ? form.getEnabled() : true)
                .set(FAULT_NOTIFICATION_EMAIL.DESCRIPTION, form.getDescription())
                .returningResult(FAULT_NOTIFICATION_EMAIL.ID)
                .fetchOne()
                .getValue(FAULT_NOTIFICATION_EMAIL.ID);
    }

    @Override
    public void update(FaultNotificationEmailForm form) {
        ctx.update(FAULT_NOTIFICATION_EMAIL)
                .set(FAULT_NOTIFICATION_EMAIL.EMAIL, form.getEmail())
                .set(FAULT_NOTIFICATION_EMAIL.ENABLED, form.getEnabled())
                .set(FAULT_NOTIFICATION_EMAIL.DESCRIPTION, form.getDescription())
                .where(FAULT_NOTIFICATION_EMAIL.ID.eq(form.getId()))
                .execute();
    }

    @Override
    public void delete(Integer id) {
        ctx.deleteFrom(FAULT_NOTIFICATION_EMAIL)
                .where(FAULT_NOTIFICATION_EMAIL.ID.eq(id))
                .execute();
    }

    @Override
    public void updateLastNotifiedTime(Integer id) {
        ctx.update(FAULT_NOTIFICATION_EMAIL)
                .set(FAULT_NOTIFICATION_EMAIL.LAST_NOTIFIED_TIME, new DateTime())
                .where(FAULT_NOTIFICATION_EMAIL.ID.eq(id))
                .execute();
    }

    private RecordMapper<Record, FaultNotificationEmailDTO> recordMapper() {
        return record -> {
            DateTime createDateTime = record.getValue(FAULT_NOTIFICATION_EMAIL.CREATE_TIME);
            DateTime lastNotifiedDateTime = record.getValue(FAULT_NOTIFICATION_EMAIL.LAST_NOTIFIED_TIME);
            
            return FaultNotificationEmailDTO.builder()
                .id(record.getValue(FAULT_NOTIFICATION_EMAIL.ID))
                .email(record.getValue(FAULT_NOTIFICATION_EMAIL.EMAIL))
                .enabled(record.getValue(FAULT_NOTIFICATION_EMAIL.ENABLED))
                .createTime(createDateTime != null ? createDateTime.toDate() : null)
                .lastNotifiedTime(lastNotifiedDateTime != null ? lastNotifiedDateTime.toDate() : null)
                .description(record.getValue(FAULT_NOTIFICATION_EMAIL.DESCRIPTION))
                .build();
        };
    }
} 