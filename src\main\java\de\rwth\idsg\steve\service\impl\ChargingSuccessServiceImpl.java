package de.rwth.idsg.steve.service.impl;

import de.rwth.idsg.steve.repository.ChargingSuccessRepository;
import de.rwth.idsg.steve.repository.dto.ChargingSession;
import de.rwth.idsg.steve.repository.dto.ChargingSuccessStats;
import de.rwth.idsg.steve.service.ChargingSuccessService;
import lombok.extern.slf4j.Slf4j;
import ocpp.cs._2015._10.ChargePointStatus;
import ocpp.cs._2015._10.MeterValue;
import ocpp.cs._2015._10.SampledValue;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 充电成功率统计服务实现类
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
@Transactional
public class ChargingSuccessServiceImpl implements ChargingSuccessService {
    
    @Autowired
    private ChargingSuccessRepository chargingSuccessRepository;
    
    @Override
    public void handleStatusNotification(String chargeBoxId, int connectorId, ChargePointStatus status, Integer transactionId) {
        log.debug("Processing status notification: chargeBoxId={}, connectorId={}, status={}, transactionId={}",
                chargeBoxId, connectorId, status, transactionId);

        try {
            switch (status) {
                case PREPARING:
                    handlePreparingStatus(chargeBoxId, connectorId, transactionId);
                    break;
                case CHARGING:
                    handleChargingStatus(chargeBoxId, connectorId, transactionId);
                    break;
                case FINISHING:
                    handleFinishingStatus(chargeBoxId, connectorId, transactionId);
                    break;
                default:
                    // 其他状态暂不处理
                    break;
            }
        } catch (Exception e) {
            log.error("Error processing status notification: chargeBoxId={}, connectorId={}, status={}",
                    chargeBoxId, connectorId, status, e);
        }
    }
    
    @Override
    public void handleMeterValues(String chargeBoxId, int connectorId, List<MeterValue> meterValues, Integer transactionId) {
        log.debug("Processing meter values: chargeBoxId={}, connectorId={}, transactionId={}, meterValues.size={}",
                chargeBoxId, connectorId, transactionId, meterValues != null ? meterValues.size() : 0);

        if (meterValues == null || meterValues.isEmpty()) {
            return;
        }

        try {
            // 查找活跃的充电会话
            Optional<ChargingSession> sessionOpt = findActiveSession(chargeBoxId, connectorId, transactionId);
            if (sessionOpt.isEmpty()) {
                log.debug("No active charging session found: chargeBoxId={}, connectorId={}, transactionId={}",
                        chargeBoxId, connectorId, transactionId);
                return;
            }

            ChargingSession session = sessionOpt.get();

            // 分析MeterValues数据
            boolean hasValidEnergyValue = false;
            int totalMeterValues = 0;

            for (MeterValue meterValue : meterValues) {
                if (meterValue.getSampledValue() != null) {
                    for (SampledValue sampledValue : meterValue.getSampledValue()) {
                        totalMeterValues++;

                        // 检查是否为能量数据且数值大于0
                        if (isEnergyMeasurand(sampledValue) && isValidEnergyValue(sampledValue)) {
                            hasValidEnergyValue = true;
                            log.debug("Found valid energy value: {} for session {}",
                                    sampledValue.getValue(), session.getSessionId());
                        }
                    }
                }
            }

            // 更新会话的MeterValues统计
            int newMeterValueCount = session.getMeterValueCount() + totalMeterValues;
            int newValidMeterValueCount = session.getValidMeterValueCount() + (hasValidEnergyValue ? 1 : 0);
            boolean hasValidMeterValues = session.isHasValidMeterValues() || hasValidEnergyValue;

            chargingSuccessRepository.updateSessionMeterValues(
                    session.getSessionId(),
                    newMeterValueCount,
                    newValidMeterValueCount,
                    hasValidMeterValues
            );

        } catch (Exception e) {
            log.error("Error processing meter values: chargeBoxId={}, connectorId={}, transactionId={}",
                    chargeBoxId, connectorId, transactionId, e);
        }
    }
    
    @Override
    public Map<String, ChargingSuccessStats> getAllSuccessStats() {
        return chargingSuccessRepository.getAllSuccessStats();
    }
    
    @Override
    public ChargingSuccessStats getSuccessStats(String chargeBoxId) {
        return chargingSuccessRepository.getSuccessStats(chargeBoxId).orElse(null);
    }
    
    @Override
    public void recalculateAllSuccessStats() {
        log.info("重新计算所有充电桩的成功率统计");
        chargingSuccessRepository.recalculateAllSuccessStats();
    }
    
    @Override
    public void recalculateSuccessStats(String chargeBoxId) {
        log.info("重新计算充电桩成功率统计: chargeBoxId={}", chargeBoxId);
        chargingSuccessRepository.recalculateSuccessStats(chargeBoxId);
    }
    
    @Override
    public void initializeSuccessStats(String chargeBoxId) {
        chargingSuccessRepository.initializeSuccessStats(chargeBoxId);
    }
    
    // -------------------------------------------------------------------------
    // 私有辅助方法
    // -------------------------------------------------------------------------
    
    private void handlePreparingStatus(String chargeBoxId, int connectorId, Integer transactionId) {
        // 创建新的充电会话
        int sessionId = chargingSuccessRepository.createChargingSession(chargeBoxId, connectorId, transactionId);
        chargingSuccessRepository.updateSessionStatus(sessionId, ChargingSession.ChargingSessionStatus.PREPARING);

        log.debug("Created new charging session: sessionId={}, chargeBoxId={}, connectorId={}",
                sessionId, chargeBoxId, connectorId);
    }

    private void handleChargingStatus(String chargeBoxId, int connectorId, Integer transactionId) {
        Optional<ChargingSession> sessionOpt = findActiveSession(chargeBoxId, connectorId, transactionId);

        if (sessionOpt.isPresent()) {
            ChargingSession session = sessionOpt.get();
            chargingSuccessRepository.updateSessionStatus(session.getSessionId(), ChargingSession.ChargingSessionStatus.CHARGING);

            log.debug("Updated session status to CHARGING: sessionId={}", session.getSessionId());

            // 当收到Charging状态时，就算开始了一次充电，更新总次数统计
            updateTotalSessionCount(chargeBoxId);
        } else {
            // 如果没有找到会话，创建一个新的会话
            int sessionId = chargingSuccessRepository.createChargingSession(chargeBoxId, connectorId, transactionId);
            chargingSuccessRepository.updateSessionStatus(sessionId, ChargingSession.ChargingSessionStatus.CHARGING);

            log.debug("Created new charging session directly in CHARGING state: sessionId={}", sessionId);

            // 更新总次数统计
            updateTotalSessionCount(chargeBoxId);
        }
    }

    private void handleFinishingStatus(String chargeBoxId, int connectorId, Integer transactionId) {
        Optional<ChargingSession> sessionOpt = findActiveSession(chargeBoxId, connectorId, transactionId);
        if (sessionOpt.isPresent()) {
            ChargingSession session = sessionOpt.get();
            chargingSuccessRepository.updateSessionStatus(session.getSessionId(), ChargingSession.ChargingSessionStatus.FINISHING);

            // 检查是否满足成功条件并完成会话
            // 成功条件：必须有Charging状态和有效的能量值
            boolean isSuccessful = session.isHasCharging() && session.isHasValidMeterValues();

            chargingSuccessRepository.completeSession(session.getSessionId(), isSuccessful);

            // 更新成功率统计数据
            updateSuccessStatsAfterSession(chargeBoxId, isSuccessful);

            log.debug("Completed charging session: sessionId={}, isSuccessful={}", session.getSessionId(), isSuccessful);
        }
    }

    private void updateTotalSessionCount(String chargeBoxId) {
        try {
            // 获取当前统计数据
            Optional<ChargingSuccessStats> statsOpt = chargingSuccessRepository.getSuccessStats(chargeBoxId);

            if (statsOpt.isPresent()) {
                ChargingSuccessStats stats = statsOpt.get();
                int totalSessions = stats.getTotalSessions() + 1;
                int successfulSessions = stats.getSuccessfulSessions();

                // 更新总次数
                chargingSuccessRepository.updateSuccessStats(
                    chargeBoxId,
                    totalSessions,
                    successfulSessions
                );

                log.debug("Updated total session count for {}: {}", chargeBoxId, totalSessions);
            } else {
                // 如果不存在，初始化为1次
                chargingSuccessRepository.updateSuccessStats(chargeBoxId, 1, 0);
                log.debug("Initialized total session count for {}: 1", chargeBoxId);
            }
        } catch (Exception e) {
            log.error("Error updating total session count for {}", chargeBoxId, e);
        }
    }
    
    private Optional<ChargingSession> findActiveSession(String chargeBoxId, int connectorId, Integer transactionId) {
        // 优先通过事务ID查找
        if (transactionId != null) {
            Optional<ChargingSession> sessionOpt = chargingSuccessRepository.getSessionByTransactionId(transactionId);
            if (sessionOpt.isPresent()) {
                return sessionOpt;
            }
        }
        
        // 通过充电桩ID和连接器ID查找活跃会话
        return chargingSuccessRepository.getActiveSession(chargeBoxId, connectorId);
    }
    
    private boolean isPowerMeasurand(SampledValue sampledValue) {
        return sampledValue.isSetMeasurand() &&
               "Power.Active.Import".equals(sampledValue.getMeasurand().value());
    }

    private boolean isEnergyMeasurand(SampledValue sampledValue) {
        return sampledValue.isSetMeasurand() &&
               "Energy.Active.Import.Register".equals(sampledValue.getMeasurand().value());
    }

    private boolean isValidPowerValue(SampledValue sampledValue) {
        try {
            String value = sampledValue.getValue();
            if (value != null && !value.trim().isEmpty()) {
                double powerValue = Double.parseDouble(value);
                return powerValue > 0;
            }
        } catch (NumberFormatException e) {
            log.debug("Cannot parse power value: {}", sampledValue.getValue());
        }
        return false;
    }

    private boolean isValidEnergyValue(SampledValue sampledValue) {
        try {
            String value = sampledValue.getValue();
            if (value != null && !value.trim().isEmpty()) {
                double energyValue = Double.parseDouble(value);
                return energyValue > 0;
            }
        } catch (NumberFormatException e) {
            log.debug("Cannot parse energy value: {}", sampledValue.getValue());
        }
        return false;
    }
    
    private void updateSuccessStatsAfterSession(String chargeBoxId, boolean isSuccessful) {
        try {
            // 获取当前统计数据
            Optional<ChargingSuccessStats> statsOpt = chargingSuccessRepository.getSuccessStats(chargeBoxId);

            if (statsOpt.isPresent()) {
                ChargingSuccessStats stats = statsOpt.get();
                int totalSessions = stats.getTotalSessions();
                int successfulSessions = stats.getSuccessfulSessions();

                // 如果成功，增加成功次数
                if (isSuccessful) {
                    successfulSessions++;
                }

                // 更新统计数据
                chargingSuccessRepository.updateSuccessStats(
                    chargeBoxId,
                    totalSessions,
                    successfulSessions
                );

                log.debug("Updated success stats for {}: total={}, successful={}",
                        chargeBoxId, totalSessions, successfulSessions);
            }
        } catch (Exception e) {
            log.error("Error updating charging success rate statistics: chargeBoxId={}", chargeBoxId, e);
        }
    }
}
