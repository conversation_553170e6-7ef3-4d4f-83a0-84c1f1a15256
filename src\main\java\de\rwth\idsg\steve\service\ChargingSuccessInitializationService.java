package de.rwth.idsg.steve.service;

import de.rwth.idsg.steve.repository.ChargePointRepository;
import de.rwth.idsg.steve.repository.ChargingSuccessRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 充电成功率统计初始化服务
 * 在应用启动时初始化所有充电桩的成功率统计数据
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
public class ChargingSuccessInitializationService implements ApplicationRunner {
    
    @Autowired
    private ChargePointRepository chargePointRepository;
    
    @Autowired
    private ChargingSuccessRepository chargingSuccessRepository;
    
    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("开始初始化充电成功率统计数据...");
        
        try {
            // 获取所有充电桩ID
            List<String> chargeBoxIds = chargePointRepository.getChargeBoxIds();
            log.info("找到 {} 个充电桩需要初始化统计数据", chargeBoxIds.size());
            
            // 为每个充电桩初始化统计数据
            int initializedCount = 0;
            for (String chargeBoxId : chargeBoxIds) {
                try {
                    chargingSuccessRepository.initializeSuccessStats(chargeBoxId);
                    initializedCount++;
                } catch (Exception e) {
                    log.warn("初始化充电桩 {} 的统计数据失败: {}", chargeBoxId, e.getMessage());
                }
            }
            
            log.info("成功初始化了 {} 个充电桩的统计数据", initializedCount);
            
            // 重新计算所有统计数据（基于历史会话数据）
            log.info("开始重新计算所有充电桩的成功率统计...");
            chargingSuccessRepository.recalculateAllSuccessStats();
            log.info("充电成功率统计数据初始化完成");
            
        } catch (Exception e) {
            log.error("初始化充电成功率统计数据时发生错误", e);
        }
    }
}
