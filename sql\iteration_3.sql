CREATE DATABASE  IF NOT EXISTS `stevedb` /*!40100 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci */ /*!80016 DEFAULT ENCRYPTION='N' */;
USE `stevedb`;
-- MySQL dump 10.13  Distrib 8.0.41, for Win64 (x86_64)
--
-- Host: localhost    Database: stevedb
-- ------------------------------------------------------
-- Server version	8.0.41

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `address`
--

DROP TABLE IF EXISTS `address`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `address` (
  `address_pk` int NOT NULL AUTO_INCREMENT,
  `street` varchar(1000) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `house_number` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `zip_code` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `city` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `country` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`address_pk`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `address`
--

LOCK TABLES `address` WRITE;
/*!40000 ALTER TABLE `address` DISABLE KEYS */;
INSERT INTO `address` VALUES (3,NULL,NULL,NULL,NULL,'BB'),(4,NULL,NULL,NULL,NULL,'BB');
/*!40000 ALTER TABLE `address` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `charge_box`
--

DROP TABLE IF EXISTS `charge_box`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `charge_box` (
  `charge_box_pk` int NOT NULL AUTO_INCREMENT,
  `charge_box_id` varchar(255) COLLATE utf8mb3_unicode_ci NOT NULL,
  `endpoint_address` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `ocpp_protocol` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `registration_status` varchar(255) COLLATE utf8mb3_unicode_ci NOT NULL DEFAULT 'Accepted',
  `charge_point_vendor` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `charge_point_model` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `charge_point_serial_number` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `charge_box_serial_number` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `fw_version` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `fw_update_status` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `fw_update_timestamp` timestamp(6) NULL DEFAULT NULL,
  `iccid` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `imsi` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `meter_type` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `meter_serial_number` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `diagnostics_status` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `diagnostics_timestamp` timestamp(6) NULL DEFAULT NULL,
  `last_heartbeat_timestamp` timestamp(6) NULL DEFAULT NULL,
  `description` mediumtext COLLATE utf8mb3_unicode_ci,
  `note` mediumtext COLLATE utf8mb3_unicode_ci,
  `location_latitude` decimal(11,8) DEFAULT NULL,
  `location_longitude` decimal(11,8) DEFAULT NULL,
  `address_pk` int DEFAULT NULL,
  `admin_address` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `insert_connector_status_after_transaction_msg` tinyint(1) DEFAULT '1',
  PRIMARY KEY (`charge_box_pk`),
  UNIQUE KEY `chargeBoxId_UNIQUE` (`charge_box_id`),
  KEY `chargebox_op_ep_idx` (`ocpp_protocol`,`endpoint_address`),
  KEY `FK_charge_box_address_apk` (`address_pk`),
  CONSTRAINT `FK_charge_box_address_apk` FOREIGN KEY (`address_pk`) REFERENCES `address` (`address_pk`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `charge_box`
--

LOCK TABLES `charge_box` WRITE;
/*!40000 ALTER TABLE `charge_box` DISABLE KEYS */;
INSERT INTO `charge_box` VALUES (1,'CP002',NULL,'ocpp1.6J','Accepted','SETEC-POWER','DC','CP002','SN9803cfa76c7a','1.6.200.250408,v179 176 ',NULL,NULL,NULL,NULL,'Internal Meter',NULL,NULL,NULL,'2025-04-18 06:15:40.366000',NULL,NULL,NULL,NULL,NULL,NULL,0),(2,'CP003',NULL,NULL,'Accepted',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0),(3,'CP001',NULL,NULL,'Accepted',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0),(4,'CP004',NULL,NULL,'Accepted',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0),(5,'CP005',NULL,NULL,'Accepted',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0),(6,'CP006',NULL,NULL,'Accepted',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0),(7,'CP007',NULL,NULL,'Accepted',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0),(8,'CP008',NULL,NULL,'Accepted',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0),(9,'CP009',NULL,NULL,'Accepted',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0);
/*!40000 ALTER TABLE `charge_box` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `charger_issue`
--

DROP TABLE IF EXISTS `charger_issue`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `charger_issue` (
  `issue_id` int NOT NULL AUTO_INCREMENT,
  `charge_box_pk` int NOT NULL,
  `report_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `reporter_user_pk` int DEFAULT NULL,
  `fault_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` enum('NEW','IN_PROGRESS','RESOLVED') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'NEW',
  `ocpp_error_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_auto_reported` tinyint(1) NOT NULL DEFAULT '0',
  `resolve_time` timestamp NULL DEFAULT NULL,
  `resolve_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  PRIMARY KEY (`issue_id`),
  KEY `reporter_user_pk` (`reporter_user_pk`),
  KEY `idx_charge_box_pk` (`charge_box_pk`),
  KEY `idx_status` (`status`),
  KEY `idx_report_time` (`report_time`),
  CONSTRAINT `charger_issue_ibfk_1` FOREIGN KEY (`charge_box_pk`) REFERENCES `charge_box` (`charge_box_pk`) ON DELETE CASCADE,
  CONSTRAINT `charger_issue_ibfk_2` FOREIGN KEY (`reporter_user_pk`) REFERENCES `web_user` (`web_user_pk`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `charger_issue`
--

LOCK TABLES `charger_issue` WRITE;
/*!40000 ALTER TABLE `charger_issue` DISABLE KEYS */;
INSERT INTO `charger_issue` VALUES (1,1,'2025-04-21 07:12:42',10,'充电桩显示屏无法正常显示','RESOLVED',NULL,0,'2025-04-22 03:27:51','test'),(2,2,'2025-04-21 07:12:45',NULL,'充电接口损坏，无法正常充电','RESOLVED','ConnectorError',0,'2025-04-22 00:42:36','test'),(3,3,'2025-04-21 09:36:00',1,'test','NEW',NULL,0,NULL,NULL),(4,6,'2025-04-22 03:00:00',1,'test','NEW',NULL,0,NULL,NULL),(5,5,'2025-04-22 03:27:00',1,'test1','IN_PROGRESS',NULL,0,NULL,NULL),(6,4,'2025-04-22 03:48:00',1,'test','NEW',NULL,0,NULL,NULL),(7,7,'2025-04-22 05:35:00',1,'test','IN_PROGRESS',NULL,0,NULL,NULL),(8,8,'2025-04-22 05:53:00',1,'test','NEW','test',0,NULL,NULL),(9,1,'2025-04-18 00:00:00',1,'test','NEW',NULL,0,NULL,NULL),(10,1,'2025-04-22 06:34:00',1,'test333','IN_PROGRESS',NULL,0,NULL,NULL),(11,3,'2025-04-22 07:44:00',1,'test1','NEW',NULL,0,NULL,NULL);
/*!40000 ALTER TABLE `charger_issue` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `charging_profile`
--

DROP TABLE IF EXISTS `charging_profile`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `charging_profile` (
  `charging_profile_pk` int NOT NULL AUTO_INCREMENT,
  `stack_level` int NOT NULL,
  `charging_profile_purpose` varchar(255) COLLATE utf8mb3_unicode_ci NOT NULL,
  `charging_profile_kind` varchar(255) COLLATE utf8mb3_unicode_ci NOT NULL,
  `recurrency_kind` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `valid_from` timestamp(6) NULL DEFAULT NULL,
  `valid_to` timestamp(6) NULL DEFAULT NULL,
  `duration_in_seconds` int DEFAULT NULL,
  `start_schedule` timestamp(6) NULL DEFAULT NULL,
  `charging_rate_unit` varchar(255) COLLATE utf8mb3_unicode_ci NOT NULL,
  `min_charging_rate` decimal(15,1) DEFAULT NULL,
  `description` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `note` text COLLATE utf8mb3_unicode_ci,
  PRIMARY KEY (`charging_profile_pk`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `charging_profile`
--

LOCK TABLES `charging_profile` WRITE;
/*!40000 ALTER TABLE `charging_profile` DISABLE KEYS */;
/*!40000 ALTER TABLE `charging_profile` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `charging_schedule_period`
--

DROP TABLE IF EXISTS `charging_schedule_period`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `charging_schedule_period` (
  `charging_profile_pk` int NOT NULL,
  `start_period_in_seconds` int NOT NULL,
  `power_limit` decimal(15,1) NOT NULL,
  `number_phases` int DEFAULT NULL,
  UNIQUE KEY `UQ_charging_schedule_period` (`charging_profile_pk`,`start_period_in_seconds`),
  CONSTRAINT `FK_charging_schedule_period_charging_profile_pk` FOREIGN KEY (`charging_profile_pk`) REFERENCES `charging_profile` (`charging_profile_pk`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `charging_schedule_period`
--

LOCK TABLES `charging_schedule_period` WRITE;
/*!40000 ALTER TABLE `charging_schedule_period` DISABLE KEYS */;
/*!40000 ALTER TABLE `charging_schedule_period` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `connector`
--

DROP TABLE IF EXISTS `connector`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `connector` (
  `connector_pk` int unsigned NOT NULL AUTO_INCREMENT,
  `charge_box_id` varchar(255) COLLATE utf8mb3_unicode_ci NOT NULL,
  `connector_id` int NOT NULL,
  PRIMARY KEY (`connector_pk`),
  UNIQUE KEY `connector_pk_UNIQUE` (`connector_pk`),
  UNIQUE KEY `connector_cbid_cid_UNIQUE` (`charge_box_id`,`connector_id`),
  CONSTRAINT `FK_connector_charge_box_cbid` FOREIGN KEY (`charge_box_id`) REFERENCES `charge_box` (`charge_box_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `connector`
--

LOCK TABLES `connector` WRITE;
/*!40000 ALTER TABLE `connector` DISABLE KEYS */;
INSERT INTO `connector` VALUES (3,'CP002',0),(2,'CP002',1),(1,'CP002',2);
/*!40000 ALTER TABLE `connector` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `connector_charging_profile`
--

DROP TABLE IF EXISTS `connector_charging_profile`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `connector_charging_profile` (
  `connector_pk` int unsigned NOT NULL,
  `charging_profile_pk` int NOT NULL,
  UNIQUE KEY `UQ_connector_charging_profile` (`connector_pk`,`charging_profile_pk`),
  KEY `FK_connector_charging_profile_charging_profile_pk` (`charging_profile_pk`),
  CONSTRAINT `FK_connector_charging_profile_charging_profile_pk` FOREIGN KEY (`charging_profile_pk`) REFERENCES `charging_profile` (`charging_profile_pk`),
  CONSTRAINT `FK_connector_charging_profile_connector_pk` FOREIGN KEY (`connector_pk`) REFERENCES `connector` (`connector_pk`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `connector_charging_profile`
--

LOCK TABLES `connector_charging_profile` WRITE;
/*!40000 ALTER TABLE `connector_charging_profile` DISABLE KEYS */;
/*!40000 ALTER TABLE `connector_charging_profile` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `connector_meter_value`
--

DROP TABLE IF EXISTS `connector_meter_value`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `connector_meter_value` (
  `connector_pk` int unsigned NOT NULL,
  `transaction_pk` int unsigned DEFAULT NULL,
  `value_timestamp` timestamp(6) NULL DEFAULT NULL,
  `value` text COLLATE utf8mb3_unicode_ci,
  `reading_context` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `format` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `measurand` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `location` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `unit` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `phase` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  KEY `FK_cm_pk_idx` (`connector_pk`),
  KEY `FK_tid_cm_idx` (`transaction_pk`),
  KEY `cmv_value_timestamp_idx` (`value_timestamp`),
  CONSTRAINT `FK_pk_cm` FOREIGN KEY (`connector_pk`) REFERENCES `connector` (`connector_pk`) ON DELETE CASCADE,
  CONSTRAINT `FK_tid_cm` FOREIGN KEY (`transaction_pk`) REFERENCES `transaction_start` (`transaction_pk`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `connector_meter_value`
--

LOCK TABLES `connector_meter_value` WRITE;
/*!40000 ALTER TABLE `connector_meter_value` DISABLE KEYS */;
/*!40000 ALTER TABLE `connector_meter_value` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `connector_status`
--

DROP TABLE IF EXISTS `connector_status`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `connector_status` (
  `connector_pk` int unsigned NOT NULL,
  `status_timestamp` timestamp(6) NULL DEFAULT NULL,
  `status` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `error_code` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `error_info` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `vendor_id` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `vendor_error_code` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  KEY `FK_cs_pk_idx` (`connector_pk`),
  KEY `connector_status_cpk_st_idx` (`connector_pk`,`status_timestamp`),
  CONSTRAINT `FK_cs_pk` FOREIGN KEY (`connector_pk`) REFERENCES `connector` (`connector_pk`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `connector_status`
--

LOCK TABLES `connector_status` WRITE;
/*!40000 ALTER TABLE `connector_status` DISABLE KEYS */;
INSERT INTO `connector_status` VALUES (1,'2025-04-16 07:10:38.183000','Available',NULL,NULL,NULL,NULL),(2,'2025-04-16 07:10:38.209000','Available',NULL,NULL,NULL,NULL),(3,'2025-04-16 07:10:38.228000','Available',NULL,NULL,NULL,NULL),(1,'2025-04-18 02:24:38.266000','Available',NULL,NULL,NULL,NULL),(2,'2025-04-18 02:24:38.311000','Available',NULL,NULL,NULL,NULL),(3,'2025-04-18 02:24:38.329000','Available',NULL,NULL,NULL,NULL);
/*!40000 ALTER TABLE `connector_status` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `flyway_schema_history_custom`
--

DROP TABLE IF EXISTS `flyway_schema_history_custom`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `flyway_schema_history_custom` (
  `installed_rank` int NOT NULL,
  `version` varchar(50) DEFAULT NULL,
  `description` varchar(200) NOT NULL,
  `type` varchar(20) NOT NULL,
  `script` varchar(1000) NOT NULL,
  `checksum` int DEFAULT NULL,
  `installed_by` varchar(100) NOT NULL,
  `installed_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `execution_time` int NOT NULL,
  `success` tinyint(1) NOT NULL,
  PRIMARY KEY (`installed_rank`),
  KEY `success` (`success`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `flyway_schema_history_custom`
--

LOCK TABLES `flyway_schema_history_custom` WRITE;
/*!40000 ALTER TABLE `flyway_schema_history_custom` DISABLE KEYS */;
INSERT INTO `flyway_schema_history_custom` VALUES (43,'1.0.10','remove_authorities_column','SQL','V1_0_10__remove_authorities_column.sql',NULL,'EVSE_OMS','2025-04-17 09:21:25',0,1);
/*!40000 ALTER TABLE `flyway_schema_history_custom` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `issue_image`
--

DROP TABLE IF EXISTS `issue_image`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `issue_image` (
  `image_id` int NOT NULL AUTO_INCREMENT,
  `issue_id` int NOT NULL,
  `image_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `upload_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`image_id`),
  KEY `idx_issue_id` (`issue_id`),
  CONSTRAINT `issue_image_ibfk_1` FOREIGN KEY (`issue_id`) REFERENCES `charger_issue` (`issue_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `issue_image`
--

LOCK TABLES `issue_image` WRITE;
/*!40000 ALTER TABLE `issue_image` DISABLE KEYS */;
INSERT INTO `issue_image` VALUES (1,1,'/uploads/issues/1/display_error.jpg','2025-04-21 07:13:51'),(2,3,'uploads/issue-images\\issue_3_20250421_093650_af620144.png','2025-04-21 09:36:50'),(3,5,'uploads/issue-images\\issue_5_20250422_032739_1ba0e12c.png','2025-04-22 03:27:39');
/*!40000 ALTER TABLE `issue_image` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `issue_maintenance_record`
--

DROP TABLE IF EXISTS `issue_maintenance_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `issue_maintenance_record` (
  `record_id` int NOT NULL AUTO_INCREMENT,
  `issue_id` int NOT NULL,
  `maintenance_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `maintainer_user_pk` int DEFAULT NULL,
  `maintenance_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`record_id`),
  KEY `maintainer_user_pk` (`maintainer_user_pk`),
  KEY `idx_issue_id` (`issue_id`),
  KEY `idx_maintenance_time` (`maintenance_time`),
  CONSTRAINT `issue_maintenance_record_ibfk_1` FOREIGN KEY (`issue_id`) REFERENCES `charger_issue` (`issue_id`) ON DELETE CASCADE,
  CONSTRAINT `issue_maintenance_record_ibfk_2` FOREIGN KEY (`maintainer_user_pk`) REFERENCES `web_user` (`web_user_pk`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=30 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `issue_maintenance_record`
--

LOCK TABLES `issue_maintenance_record` WRITE;
/*!40000 ALTER TABLE `issue_maintenance_record` DISABLE KEYS */;
INSERT INTO `issue_maintenance_record` VALUES (13,7,'2025-04-22 05:35:48',1,'test'),(15,7,'2025-04-22 05:53:20',1,'test'),(17,9,'2025-04-22 06:05:51',1,'test1111'),(19,8,'2025-04-22 06:10:21',1,'test1'),(21,8,'2025-04-22 06:10:36',1,'test2'),(22,9,'2025-04-22 06:31:06',1,'test222'),(23,9,'2025-04-22 06:34:21',1,'test3333'),(24,10,'2025-04-22 06:50:04',1,'test1'),(25,10,'2025-04-22 06:50:09',1,'test2'),(26,10,'2025-04-22 07:17:09',1,'test3'),(27,10,'2025-04-22 07:17:15',1,'test4'),(28,10,'2025-04-22 07:17:25',1,'test5'),(29,10,'2025-04-22 07:17:41',1,'test6');
/*!40000 ALTER TABLE `issue_maintenance_record` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ocpp_tag`
--

DROP TABLE IF EXISTS `ocpp_tag`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ocpp_tag` (
  `ocpp_tag_pk` int NOT NULL AUTO_INCREMENT,
  `id_tag` varchar(255) COLLATE utf8mb3_unicode_ci NOT NULL,
  `parent_id_tag` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `expiry_date` timestamp(6) NULL DEFAULT NULL,
  `max_active_transaction_count` int NOT NULL DEFAULT '1',
  `note` mediumtext COLLATE utf8mb3_unicode_ci,
  PRIMARY KEY (`ocpp_tag_pk`),
  UNIQUE KEY `idTag_UNIQUE` (`id_tag`),
  KEY `user_expiryDate_idx` (`expiry_date`),
  KEY `FK_ocpp_tag_parent_id_tag` (`parent_id_tag`),
  CONSTRAINT `FK_ocpp_tag_parent_id_tag` FOREIGN KEY (`parent_id_tag`) REFERENCES `ocpp_tag` (`id_tag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ocpp_tag`
--

LOCK TABLES `ocpp_tag` WRITE;
/*!40000 ALTER TABLE `ocpp_tag` DISABLE KEYS */;
/*!40000 ALTER TABLE `ocpp_tag` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Temporary view structure for view `ocpp_tag_activity`
--

DROP TABLE IF EXISTS `ocpp_tag_activity`;
/*!50001 DROP VIEW IF EXISTS `ocpp_tag_activity`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `ocpp_tag_activity` AS SELECT 
 1 AS `ocpp_tag_pk`,
 1 AS `id_tag`,
 1 AS `parent_id_tag`,
 1 AS `expiry_date`,
 1 AS `max_active_transaction_count`,
 1 AS `note`,
 1 AS `active_transaction_count`,
 1 AS `in_transaction`,
 1 AS `blocked`*/;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `reservation`
--

DROP TABLE IF EXISTS `reservation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `reservation` (
  `reservation_pk` int unsigned NOT NULL AUTO_INCREMENT,
  `connector_pk` int unsigned NOT NULL,
  `transaction_pk` int unsigned DEFAULT NULL,
  `id_tag` varchar(255) COLLATE utf8mb3_unicode_ci NOT NULL,
  `start_datetime` datetime DEFAULT NULL,
  `expiry_datetime` datetime DEFAULT NULL,
  `status` varchar(255) COLLATE utf8mb3_unicode_ci NOT NULL,
  PRIMARY KEY (`reservation_pk`),
  UNIQUE KEY `reservation_pk_UNIQUE` (`reservation_pk`),
  UNIQUE KEY `transaction_pk_UNIQUE` (`transaction_pk`),
  KEY `FK_idTag_r_idx` (`id_tag`),
  KEY `reservation_start_idx` (`start_datetime`),
  KEY `reservation_expiry_idx` (`expiry_datetime`),
  KEY `reservation_status_idx` (`status`),
  KEY `FK_connector_pk_reserv_idx` (`connector_pk`),
  CONSTRAINT `FK_connector_pk_reserv` FOREIGN KEY (`connector_pk`) REFERENCES `connector` (`connector_pk`) ON DELETE CASCADE,
  CONSTRAINT `FK_reservation_ocpp_tag_id_tag` FOREIGN KEY (`id_tag`) REFERENCES `ocpp_tag` (`id_tag`) ON DELETE CASCADE,
  CONSTRAINT `FK_transaction_pk_r` FOREIGN KEY (`transaction_pk`) REFERENCES `transaction_start` (`transaction_pk`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `reservation`
--

LOCK TABLES `reservation` WRITE;
/*!40000 ALTER TABLE `reservation` DISABLE KEYS */;
/*!40000 ALTER TABLE `reservation` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `schema_version`
--

DROP TABLE IF EXISTS `schema_version`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `schema_version` (
  `installed_rank` int NOT NULL,
  `version` varchar(50) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `description` varchar(200) COLLATE utf8mb3_unicode_ci NOT NULL,
  `type` varchar(20) COLLATE utf8mb3_unicode_ci NOT NULL,
  `script` varchar(1000) COLLATE utf8mb3_unicode_ci NOT NULL,
  `checksum` int DEFAULT NULL,
  `installed_by` varchar(100) COLLATE utf8mb3_unicode_ci NOT NULL,
  `installed_on` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `execution_time` int NOT NULL,
  `success` tinyint(1) NOT NULL,
  PRIMARY KEY (`installed_rank`),
  KEY `schema_version_s_idx` (`success`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `schema_version`
--

LOCK TABLES `schema_version` WRITE;
/*!40000 ALTER TABLE `schema_version` DISABLE KEYS */;
INSERT INTO `schema_version` VALUES (1,'1.0.5','stevedb','SQL_BASELINE','B1_0_5__stevedb.sql',1755536181,'root','2025-04-15 08:13:52',712,1),(2,'1.0.6','update','SQL','V1_0_6__update.sql',-593788063,'root','2025-04-15 08:13:52',31,1),(3,'1.0.7','update','SQL','V1_0_7__update.sql',-1929107779,'root','2025-04-15 08:13:52',20,1),(4,'1.0.8','web user and user charge box','SQL','V1_0_8__web_user_and_user_charge_box.sql',238883439,'root','2025-04-15 09:18:19',213,1),(5,'1.0.9','remove authorities column','SQL','V1_0_10__remove_authorities_column.sql',2035225626,'root','2025-04-17 09:30:13',39,1),(6,'1.1.0','charger issue system','SQL','V1_1_0__charger_issue_system.sql',-609480177,'root','2025-04-21 01:56:54',40,1),(7,'1.1.1','fix charger issue system','SQL','V1_1_1__fix_charger_issue_system.sql',-18687803,'root','2025-04-21 01:56:54',297,1);
/*!40000 ALTER TABLE `schema_version` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `settings`
--

DROP TABLE IF EXISTS `settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `settings` (
  `app_id` varchar(40) COLLATE utf8mb3_unicode_ci NOT NULL,
  `heartbeat_interval_in_seconds` int DEFAULT NULL,
  `hours_to_expire` int DEFAULT NULL,
  `mail_enabled` tinyint(1) DEFAULT '0',
  `mail_host` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `mail_username` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `mail_password` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `mail_from` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `mail_protocol` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT 'smtp',
  `mail_port` int DEFAULT '25',
  `mail_recipients` text COLLATE utf8mb3_unicode_ci COMMENT 'comma separated list of email addresses',
  `notification_features` text COLLATE utf8mb3_unicode_ci COMMENT 'comma separated list',
  PRIMARY KEY (`app_id`),
  UNIQUE KEY `settings_id_UNIQUE` (`app_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `settings`
--

LOCK TABLES `settings` WRITE;
/*!40000 ALTER TABLE `settings` DISABLE KEYS */;
INSERT INTO `settings` VALUES ('U3RlY2tkb3NlblZlcndhbHR1bmc=',14400,1,0,NULL,NULL,NULL,NULL,'smtp',25,NULL,NULL);
/*!40000 ALTER TABLE `settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Temporary view structure for view `transaction`
--

DROP TABLE IF EXISTS `transaction`;
/*!50001 DROP VIEW IF EXISTS `transaction`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `transaction` AS SELECT 
 1 AS `transaction_pk`,
 1 AS `connector_pk`,
 1 AS `id_tag`,
 1 AS `start_event_timestamp`,
 1 AS `start_timestamp`,
 1 AS `start_value`,
 1 AS `stop_event_actor`,
 1 AS `stop_event_timestamp`,
 1 AS `stop_timestamp`,
 1 AS `stop_value`,
 1 AS `stop_reason`*/;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `transaction_start`
--

DROP TABLE IF EXISTS `transaction_start`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `transaction_start` (
  `transaction_pk` int unsigned NOT NULL AUTO_INCREMENT,
  `event_timestamp` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `connector_pk` int unsigned NOT NULL,
  `id_tag` varchar(255) COLLATE utf8mb3_unicode_ci NOT NULL,
  `start_timestamp` timestamp(6) NULL DEFAULT NULL,
  `start_value` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`transaction_pk`),
  UNIQUE KEY `transaction_pk_UNIQUE` (`transaction_pk`),
  KEY `idTag_idx` (`id_tag`),
  KEY `connector_pk_idx` (`connector_pk`),
  KEY `transaction_start_idx` (`start_timestamp`),
  CONSTRAINT `FK_connector_pk_t` FOREIGN KEY (`connector_pk`) REFERENCES `connector` (`connector_pk`) ON DELETE CASCADE,
  CONSTRAINT `FK_transaction_ocpp_tag_id_tag` FOREIGN KEY (`id_tag`) REFERENCES `ocpp_tag` (`id_tag`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `transaction_start`
--

LOCK TABLES `transaction_start` WRITE;
/*!40000 ALTER TABLE `transaction_start` DISABLE KEYS */;
/*!40000 ALTER TABLE `transaction_start` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `transaction_stop`
--

DROP TABLE IF EXISTS `transaction_stop`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `transaction_stop` (
  `transaction_pk` int unsigned NOT NULL,
  `event_timestamp` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `event_actor` enum('station','manual') COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `stop_timestamp` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `stop_value` varchar(255) COLLATE utf8mb3_unicode_ci NOT NULL,
  `stop_reason` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`transaction_pk`,`event_timestamp`),
  CONSTRAINT `FK_transaction_stop_transaction_pk` FOREIGN KEY (`transaction_pk`) REFERENCES `transaction_start` (`transaction_pk`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `transaction_stop`
--

LOCK TABLES `transaction_stop` WRITE;
/*!40000 ALTER TABLE `transaction_stop` DISABLE KEYS */;
/*!40000 ALTER TABLE `transaction_stop` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `transaction_stop_failed`
--

DROP TABLE IF EXISTS `transaction_stop_failed`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `transaction_stop_failed` (
  `transaction_pk` int DEFAULT NULL,
  `charge_box_id` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `event_timestamp` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `event_actor` enum('station','manual') COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `stop_timestamp` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `stop_value` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `stop_reason` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `fail_reason` text COLLATE utf8mb3_unicode_ci
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `transaction_stop_failed`
--

LOCK TABLES `transaction_stop_failed` WRITE;
/*!40000 ALTER TABLE `transaction_stop_failed` DISABLE KEYS */;
/*!40000 ALTER TABLE `transaction_stop_failed` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user`
--

DROP TABLE IF EXISTS `user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user` (
  `user_pk` int NOT NULL AUTO_INCREMENT,
  `ocpp_tag_pk` int DEFAULT NULL,
  `address_pk` int DEFAULT NULL,
  `first_name` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `last_name` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `birth_day` date DEFAULT NULL,
  `sex` char(1) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `phone` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `e_mail` varchar(255) COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `note` text COLLATE utf8mb3_unicode_ci,
  PRIMARY KEY (`user_pk`),
  KEY `FK_user_ocpp_tag_otpk` (`ocpp_tag_pk`),
  KEY `FK_user_address_apk` (`address_pk`),
  CONSTRAINT `FK_user_address_apk` FOREIGN KEY (`address_pk`) REFERENCES `address` (`address_pk`) ON DELETE SET NULL,
  CONSTRAINT `FK_user_ocpp_tag_otpk` FOREIGN KEY (`ocpp_tag_pk`) REFERENCES `ocpp_tag` (`ocpp_tag_pk`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user`
--

LOCK TABLES `user` WRITE;
/*!40000 ALTER TABLE `user` DISABLE KEYS */;
INSERT INTO `user` VALUES (7,NULL,3,'test1','test1',NULL,'m',NULL,NULL,NULL),(8,NULL,4,'test2','2',NULL,'o',NULL,NULL,NULL);
/*!40000 ALTER TABLE `user` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_charge_box`
--

DROP TABLE IF EXISTS `user_charge_box`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_charge_box` (
  `user_charge_box_pk` int NOT NULL AUTO_INCREMENT,
  `web_user_pk` int NOT NULL,
  `charge_box_pk` int NOT NULL,
  PRIMARY KEY (`user_charge_box_pk`),
  UNIQUE KEY `unique_user_charge_box` (`web_user_pk`,`charge_box_pk`),
  KEY `idx_user_charge_box_web_user_pk` (`web_user_pk`),
  KEY `idx_user_charge_box_charge_box_pk` (`charge_box_pk`),
  CONSTRAINT `user_charge_box_ibfk_1` FOREIGN KEY (`web_user_pk`) REFERENCES `web_user` (`web_user_pk`) ON DELETE CASCADE,
  CONSTRAINT `user_charge_box_ibfk_2` FOREIGN KEY (`charge_box_pk`) REFERENCES `charge_box` (`charge_box_pk`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_charge_box`
--

LOCK TABLES `user_charge_box` WRITE;
/*!40000 ALTER TABLE `user_charge_box` DISABLE KEYS */;
INSERT INTO `user_charge_box` VALUES (13,10,1),(14,10,2),(15,10,3),(18,11,6);
/*!40000 ALTER TABLE `user_charge_box` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Temporary view structure for view `view_charger_issues`
--

DROP TABLE IF EXISTS `view_charger_issues`;
/*!50001 DROP VIEW IF EXISTS `view_charger_issues`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `view_charger_issues` AS SELECT 
 1 AS `issue_id`,
 1 AS `charge_box_pk`,
 1 AS `charge_box_id`,
 1 AS `report_time`,
 1 AS `reporter_user_pk`,
 1 AS `reporter_username`,
 1 AS `fault_description`,
 1 AS `status`,
 1 AS `ocpp_error_code`,
 1 AS `is_auto_reported`,
 1 AS `resolve_time`,
 1 AS `resolve_description`,
 1 AS `charge_point_vendor`,
 1 AS `charge_point_model`,
 1 AS `owner_user_pks`*/;
SET character_set_client = @saved_cs_client;

--
-- Temporary view structure for view `view_issue_complete`
--

DROP TABLE IF EXISTS `view_issue_complete`;
/*!50001 DROP VIEW IF EXISTS `view_issue_complete`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `view_issue_complete` AS SELECT 
 1 AS `issue_id`,
 1 AS `charge_box_pk`,
 1 AS `charge_box_id`,
 1 AS `report_time`,
 1 AS `reporter_user_pk`,
 1 AS `reporter_username`,
 1 AS `fault_description`,
 1 AS `status`,
 1 AS `ocpp_error_code`,
 1 AS `is_auto_reported`,
 1 AS `resolve_time`,
 1 AS `resolve_description`,
 1 AS `charge_point_vendor`,
 1 AS `charge_point_model`,
 1 AS `maintenance_count`,
 1 AS `image_count`,
 1 AS `owner_user_pks`,
 1 AS `owner_usernames`*/;
SET character_set_client = @saved_cs_client;

--
-- Temporary view structure for view `view_issue_image_count`
--

DROP TABLE IF EXISTS `view_issue_image_count`;
/*!50001 DROP VIEW IF EXISTS `view_issue_image_count`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `view_issue_image_count` AS SELECT 
 1 AS `issue_id`,
 1 AS `charge_box_id`,
 1 AS `status`,
 1 AS `report_time`,
 1 AS `image_count`*/;
SET character_set_client = @saved_cs_client;

--
-- Temporary view structure for view `view_maintenance_records`
--

DROP TABLE IF EXISTS `view_maintenance_records`;
/*!50001 DROP VIEW IF EXISTS `view_maintenance_records`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `view_maintenance_records` AS SELECT 
 1 AS `record_id`,
 1 AS `issue_id`,
 1 AS `maintenance_time`,
 1 AS `maintainer_user_pk`,
 1 AS `maintainer_username`,
 1 AS `maintenance_description`,
 1 AS `charge_box_pk`,
 1 AS `charge_box_id`,
 1 AS `issue_status`,
 1 AS `fault_description`*/;
SET character_set_client = @saved_cs_client;

--
-- Temporary view structure for view `view_owner_charge_boxes`
--

DROP TABLE IF EXISTS `view_owner_charge_boxes`;
/*!50001 DROP VIEW IF EXISTS `view_owner_charge_boxes`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `view_owner_charge_boxes` AS SELECT 
 1 AS `charge_box_pk`,
 1 AS `charge_box_id`,
 1 AS `endpoint_address`,
 1 AS `ocpp_protocol`,
 1 AS `registration_status`,
 1 AS `charge_point_vendor`,
 1 AS `charge_point_model`,
 1 AS `charge_point_serial_number`,
 1 AS `charge_box_serial_number`,
 1 AS `fw_version`,
 1 AS `fw_update_status`,
 1 AS `fw_update_timestamp`,
 1 AS `iccid`,
 1 AS `imsi`,
 1 AS `meter_type`,
 1 AS `meter_serial_number`,
 1 AS `diagnostics_status`,
 1 AS `diagnostics_timestamp`,
 1 AS `last_heartbeat_timestamp`,
 1 AS `description`,
 1 AS `note`,
 1 AS `location_latitude`,
 1 AS `location_longitude`,
 1 AS `address_pk`,
 1 AS `admin_address`,
 1 AS `insert_connector_status_after_transaction_msg`,
 1 AS `web_user_pk`*/;
SET character_set_client = @saved_cs_client;

--
-- Temporary view structure for view `view_owner_transactions`
--

DROP TABLE IF EXISTS `view_owner_transactions`;
/*!50001 DROP VIEW IF EXISTS `view_owner_transactions`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `view_owner_transactions` AS SELECT 
 1 AS `transaction_pk`,
 1 AS `connector_pk`,
 1 AS `id_tag`,
 1 AS `start_event_timestamp`,
 1 AS `start_timestamp`,
 1 AS `start_value`,
 1 AS `stop_event_actor`,
 1 AS `stop_event_timestamp`,
 1 AS `stop_timestamp`,
 1 AS `stop_value`,
 1 AS `stop_reason`,
 1 AS `charge_box_id`,
 1 AS `web_user_pk`*/;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `web_user`
--

DROP TABLE IF EXISTS `web_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `web_user` (
  `web_user_pk` int NOT NULL AUTO_INCREMENT,
  `username` varchar(500) NOT NULL,
  `password` varchar(500) NOT NULL,
  `api_password` varchar(500) DEFAULT NULL,
  `enabled` tinyint(1) NOT NULL,
  `user_role` enum('ADMIN','OPERATOR_FACTORY','OPERATOR_OWNER') NOT NULL DEFAULT 'ADMIN',
  PRIMARY KEY (`web_user_pk`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `web_user`
--

LOCK TABLES `web_user` WRITE;
/*!40000 ALTER TABLE `web_user` DISABLE KEYS */;
INSERT INTO `web_user` VALUES (1,'admin','$2a$10$wt4EMzOcgXtlnvR46hfKV.9ICUlWYEYkOYw2Ar9HS2CeNDHW/gjKe',NULL,1,'ADMIN'),(10,'test1','$2a$10$fahASrgbdalNCUjWNQVDnOnwM/liE.JXDUF8gUNc0lLUFsjR828k.',NULL,1,'OPERATOR_OWNER'),(11,'test2','$2a$10$IiI40mEWth8eB7/H6jRSxOH57geBetZAAnofFyN1NBJQ.SDnFLPAq',NULL,1,'OPERATOR_OWNER');
/*!40000 ALTER TABLE `web_user` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping events for database 'stevedb'
--

--
-- Dumping routines for database 'stevedb'
--
/*!50003 DROP PROCEDURE IF EXISTS `get_user_charger_issues` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_unicode_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'IGNORE_SPACE,ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`root`@`localhost` PROCEDURE `get_user_charger_issues`(
    IN p_user_pk INT,
    IN p_user_role VARCHAR(50),
    IN p_status VARCHAR(20),
    IN p_start_date DATETIME,
    IN p_end_date DATETIME
)
BEGIN
    IF p_user_role = 'ADMIN' THEN
        -- 管理员可以查看所有故障
        SELECT * FROM view_issue_complete
        WHERE (p_status IS NULL OR status = p_status)
          AND (p_start_date IS NULL OR report_time >= p_start_date)
          AND (p_end_date IS NULL OR report_time <= p_end_date)
        ORDER BY report_time DESC;
    ELSEIF p_user_role = 'OPERATOR_FACTORY' THEN
        -- 工厂操作员可以查看所有故障
        SELECT * FROM view_issue_complete
        WHERE (p_status IS NULL OR status = p_status)
          AND (p_start_date IS NULL OR report_time >= p_start_date)
          AND (p_end_date IS NULL OR report_time <= p_end_date)
        ORDER BY report_time DESC;
    ELSEIF p_user_role = 'OPERATOR_OWNER' THEN
        -- 业主操作员只能查看自己拥有的充电桩的故障
        SELECT * FROM view_issue_complete
        WHERE FIND_IN_SET(p_user_pk, owner_user_pks)
          AND (p_status IS NULL OR status = p_status)
          AND (p_start_date IS NULL OR report_time >= p_start_date)
          AND (p_end_date IS NULL OR report_time <= p_end_date)
        ORDER BY report_time DESC;
    ELSE
        -- 默认情况，无权限
        SELECT 'No permission' as message;
    END IF;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;

--
-- Final view structure for view `ocpp_tag_activity`
--

/*!50001 DROP VIEW IF EXISTS `ocpp_tag_activity`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb3 */;
/*!50001 SET character_set_results     = utf8mb3 */;
/*!50001 SET collation_connection      = utf8mb4_0900_ai_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `ocpp_tag_activity` AS select `o`.`ocpp_tag_pk` AS `ocpp_tag_pk`,`o`.`id_tag` AS `id_tag`,`o`.`parent_id_tag` AS `parent_id_tag`,`o`.`expiry_date` AS `expiry_date`,`o`.`max_active_transaction_count` AS `max_active_transaction_count`,`o`.`note` AS `note`,count(`t`.`id_tag`) AS `active_transaction_count`,(case when (count(`t`.`id_tag`) > 0) then 1 else 0 end) AS `in_transaction`,(case when (`o`.`max_active_transaction_count` = 0) then 1 else 0 end) AS `blocked` from (`ocpp_tag` `o` left join `transaction` `t` on(((`o`.`id_tag` = `t`.`id_tag`) and (`t`.`stop_timestamp` is null) and (`t`.`stop_value` is null)))) group by `o`.`ocpp_tag_pk`,`o`.`parent_id_tag`,`o`.`expiry_date`,`o`.`max_active_transaction_count`,`o`.`note` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `transaction`
--

/*!50001 DROP VIEW IF EXISTS `transaction`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb3 */;
/*!50001 SET character_set_results     = utf8mb3 */;
/*!50001 SET collation_connection      = utf8mb4_0900_ai_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `transaction` AS select `tx1`.`transaction_pk` AS `transaction_pk`,`tx1`.`connector_pk` AS `connector_pk`,`tx1`.`id_tag` AS `id_tag`,`tx1`.`event_timestamp` AS `start_event_timestamp`,`tx1`.`start_timestamp` AS `start_timestamp`,`tx1`.`start_value` AS `start_value`,`tx2`.`event_actor` AS `stop_event_actor`,`tx2`.`event_timestamp` AS `stop_event_timestamp`,`tx2`.`stop_timestamp` AS `stop_timestamp`,`tx2`.`stop_value` AS `stop_value`,`tx2`.`stop_reason` AS `stop_reason` from (`transaction_start` `tx1` left join `transaction_stop` `tx2` on(((`tx1`.`transaction_pk` = `tx2`.`transaction_pk`) and (`tx2`.`event_timestamp` = (select max(`s2`.`event_timestamp`) from `transaction_stop` `s2` where (`tx2`.`transaction_pk` = `s2`.`transaction_pk`)))))) */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `view_charger_issues`
--

/*!50001 DROP VIEW IF EXISTS `view_charger_issues`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_unicode_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `view_charger_issues` AS select `ci`.`issue_id` AS `issue_id`,`ci`.`charge_box_pk` AS `charge_box_pk`,`cb`.`charge_box_id` AS `charge_box_id`,`ci`.`report_time` AS `report_time`,`ci`.`reporter_user_pk` AS `reporter_user_pk`,`wu`.`username` AS `reporter_username`,`ci`.`fault_description` AS `fault_description`,`ci`.`status` AS `status`,`ci`.`ocpp_error_code` AS `ocpp_error_code`,`ci`.`is_auto_reported` AS `is_auto_reported`,`ci`.`resolve_time` AS `resolve_time`,`ci`.`resolve_description` AS `resolve_description`,`cb`.`charge_point_vendor` AS `charge_point_vendor`,`cb`.`charge_point_model` AS `charge_point_model`,(select group_concat(`ucb`.`web_user_pk` separator ',') from `user_charge_box` `ucb` where (`ucb`.`charge_box_pk` = `ci`.`charge_box_pk`)) AS `owner_user_pks` from ((`charger_issue` `ci` join `charge_box` `cb` on((`ci`.`charge_box_pk` = `cb`.`charge_box_pk`))) left join `web_user` `wu` on((`ci`.`reporter_user_pk` = `wu`.`web_user_pk`))) */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `view_issue_complete`
--

/*!50001 DROP VIEW IF EXISTS `view_issue_complete`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_unicode_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `view_issue_complete` AS select `ci`.`issue_id` AS `issue_id`,`ci`.`charge_box_pk` AS `charge_box_pk`,`cb`.`charge_box_id` AS `charge_box_id`,`ci`.`report_time` AS `report_time`,`ci`.`reporter_user_pk` AS `reporter_user_pk`,`wu_reporter`.`username` AS `reporter_username`,`ci`.`fault_description` AS `fault_description`,`ci`.`status` AS `status`,`ci`.`ocpp_error_code` AS `ocpp_error_code`,`ci`.`is_auto_reported` AS `is_auto_reported`,`ci`.`resolve_time` AS `resolve_time`,`ci`.`resolve_description` AS `resolve_description`,`cb`.`charge_point_vendor` AS `charge_point_vendor`,`cb`.`charge_point_model` AS `charge_point_model`,(select count(0) from `issue_maintenance_record` `imr` where (`imr`.`issue_id` = `ci`.`issue_id`)) AS `maintenance_count`,(select count(0) from `issue_image` `ii` where (`ii`.`issue_id` = `ci`.`issue_id`)) AS `image_count`,(select group_concat(`ucb`.`web_user_pk` separator ',') from `user_charge_box` `ucb` where (`ucb`.`charge_box_pk` = `ci`.`charge_box_pk`)) AS `owner_user_pks`,(select group_concat(distinct `wu_owner`.`username` separator ',') from (`user_charge_box` `ucb` join `web_user` `wu_owner` on((`ucb`.`web_user_pk` = `wu_owner`.`web_user_pk`))) where (`ucb`.`charge_box_pk` = `ci`.`charge_box_pk`)) AS `owner_usernames` from ((`charger_issue` `ci` join `charge_box` `cb` on((`ci`.`charge_box_pk` = `cb`.`charge_box_pk`))) left join `web_user` `wu_reporter` on((`ci`.`reporter_user_pk` = `wu_reporter`.`web_user_pk`))) */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `view_issue_image_count`
--

/*!50001 DROP VIEW IF EXISTS `view_issue_image_count`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_unicode_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `view_issue_image_count` AS select `ci`.`issue_id` AS `issue_id`,`cb`.`charge_box_id` AS `charge_box_id`,`ci`.`status` AS `status`,`ci`.`report_time` AS `report_time`,count(`ii`.`image_id`) AS `image_count` from ((`charger_issue` `ci` join `charge_box` `cb` on((`ci`.`charge_box_pk` = `cb`.`charge_box_pk`))) left join `issue_image` `ii` on((`ci`.`issue_id` = `ii`.`issue_id`))) group by `ci`.`issue_id` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `view_maintenance_records`
--

/*!50001 DROP VIEW IF EXISTS `view_maintenance_records`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_unicode_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `view_maintenance_records` AS select `imr`.`record_id` AS `record_id`,`imr`.`issue_id` AS `issue_id`,`imr`.`maintenance_time` AS `maintenance_time`,`imr`.`maintainer_user_pk` AS `maintainer_user_pk`,`wu`.`username` AS `maintainer_username`,`imr`.`maintenance_description` AS `maintenance_description`,`ci`.`charge_box_pk` AS `charge_box_pk`,`cb`.`charge_box_id` AS `charge_box_id`,`ci`.`status` AS `issue_status`,`ci`.`fault_description` AS `fault_description` from (((`issue_maintenance_record` `imr` join `charger_issue` `ci` on((`imr`.`issue_id` = `ci`.`issue_id`))) join `charge_box` `cb` on((`ci`.`charge_box_pk` = `cb`.`charge_box_pk`))) left join `web_user` `wu` on((`imr`.`maintainer_user_pk` = `wu`.`web_user_pk`))) */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `view_owner_charge_boxes`
--

/*!50001 DROP VIEW IF EXISTS `view_owner_charge_boxes`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_0900_ai_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `view_owner_charge_boxes` AS select `cb`.`charge_box_pk` AS `charge_box_pk`,`cb`.`charge_box_id` AS `charge_box_id`,`cb`.`endpoint_address` AS `endpoint_address`,`cb`.`ocpp_protocol` AS `ocpp_protocol`,`cb`.`registration_status` AS `registration_status`,`cb`.`charge_point_vendor` AS `charge_point_vendor`,`cb`.`charge_point_model` AS `charge_point_model`,`cb`.`charge_point_serial_number` AS `charge_point_serial_number`,`cb`.`charge_box_serial_number` AS `charge_box_serial_number`,`cb`.`fw_version` AS `fw_version`,`cb`.`fw_update_status` AS `fw_update_status`,`cb`.`fw_update_timestamp` AS `fw_update_timestamp`,`cb`.`iccid` AS `iccid`,`cb`.`imsi` AS `imsi`,`cb`.`meter_type` AS `meter_type`,`cb`.`meter_serial_number` AS `meter_serial_number`,`cb`.`diagnostics_status` AS `diagnostics_status`,`cb`.`diagnostics_timestamp` AS `diagnostics_timestamp`,`cb`.`last_heartbeat_timestamp` AS `last_heartbeat_timestamp`,`cb`.`description` AS `description`,`cb`.`note` AS `note`,`cb`.`location_latitude` AS `location_latitude`,`cb`.`location_longitude` AS `location_longitude`,`cb`.`address_pk` AS `address_pk`,`cb`.`admin_address` AS `admin_address`,`cb`.`insert_connector_status_after_transaction_msg` AS `insert_connector_status_after_transaction_msg`,`ucb`.`web_user_pk` AS `web_user_pk` from (`charge_box` `cb` join `user_charge_box` `ucb` on((`cb`.`charge_box_pk` = `ucb`.`charge_box_pk`))) */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `view_owner_transactions`
--

/*!50001 DROP VIEW IF EXISTS `view_owner_transactions`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_0900_ai_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `view_owner_transactions` AS select `t`.`transaction_pk` AS `transaction_pk`,`t`.`connector_pk` AS `connector_pk`,`t`.`id_tag` AS `id_tag`,`t`.`start_event_timestamp` AS `start_event_timestamp`,`t`.`start_timestamp` AS `start_timestamp`,`t`.`start_value` AS `start_value`,`t`.`stop_event_actor` AS `stop_event_actor`,`t`.`stop_event_timestamp` AS `stop_event_timestamp`,`t`.`stop_timestamp` AS `stop_timestamp`,`t`.`stop_value` AS `stop_value`,`t`.`stop_reason` AS `stop_reason`,`c`.`charge_box_id` AS `charge_box_id`,`ucb`.`web_user_pk` AS `web_user_pk` from (((`transaction` `t` join `connector` `c` on((`t`.`connector_pk` = `c`.`connector_pk`))) join `charge_box` `cb` on((`c`.`charge_box_id` = `cb`.`charge_box_id`))) join `user_charge_box` `ucb` on((`cb`.`charge_box_pk` = `ucb`.`charge_box_pk`))) */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-04-22 16:36:33
