/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve;

import de.rwth.idsg.steve.ocpp.ws.custom.WsSessionSelectStrategy;
import de.rwth.idsg.steve.ocpp.ws.custom.WsSessionSelectStrategyEnum;
import de.rwth.idsg.steve.utils.PropertiesFileLoader;
import lombok.Builder;
import lombok.Getter;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;

/**
 * <AUTHOR> Goekay <<EMAIL>>
 * @since 19.08.2014
 */
@Getter
public enum SteveConfiguration {
    CONFIG;

    // Root mapping for Spring
    private final String springMapping = "/";
    // Web frontend
    private final String springManagerMapping = "/manager";
    // Mapping for CXF SOAP services
    private final String cxfMapping = "/services";
    // Mapping for Web APIs
    private final String apiMapping = "/api";
    // Dummy service path
    private final String routerEndpointPath = "/CentralSystemService";
    // Time zone for the application and database connections
    private final String timeZoneId = "Asia/Shanghai";  // 修改为中国标准时间

    // -------------------------------------------------------------------------
    // main.properties
    // -------------------------------------------------------------------------

    private final String contextPath;
    private final String steveVersion;
    private final String gitDescribe;
    private final ApplicationProfile profile;
    private final Ocpp ocpp;
    private final Auth auth;
    private final WebApi webApi;
    private final DB db;
    private final Jetty jetty;

    // New fields for firmware and FTP configuration
    private final String physicalFirmwareUploadPath;
    private final String physicalDiagnosticsUploadPath;
    private final boolean ftpEnabled;
    private final String ftpUsername;
    private final String ftpPassword;
    private final String ftpIp;
    private final int ftpPort;
    private final String ftpBasePath;
    private final String ftpLogPath;
    private final String ftpPassivePorts;
    private final String ftpPassiveExternalAddress;

    SteveConfiguration() {
        PropertiesFileLoader p = new PropertiesFileLoader("main.properties");

        contextPath = sanitizeContextPath(p.getOptionalString("context.path"));
        steveVersion = p.getString("steve.version");
        gitDescribe = useFallbackIfNotSet(p.getOptionalString("git.describe"), null);

        profile = ApplicationProfile.fromName(p.getString("profile"));
        System.setProperty("spring.profiles.active", profile.name().toLowerCase());

        jetty = Jetty.builder()
                     .serverHost(p.getString("server.host"))
                     .gzipEnabled(p.getBoolean("server.gzip.enabled"))
                     .httpEnabled(p.getBoolean("http.enabled"))
                     .httpPort(p.getInt("http.port"))
                     .httpsEnabled(p.getBoolean("https.enabled"))
                     .httpsPort(p.getInt("https.port"))
                     .keyStorePath(p.getOptionalString("keystore.path"))
                     .keyStorePassword(p.getOptionalString("keystore.password"))
                     .build();

        db = DB.builder()
               .ip(p.getString("db.ip"))
               .port(p.getInt("db.port"))
               .schema(p.getString("db.schema"))
               .userName(p.getString("db.user"))
               .password(p.getString("db.password"))
               .sqlLogging(p.getBoolean("db.sql.logging"))
               .build();

        PasswordEncoder encoder = new BCryptPasswordEncoder();

        auth = Auth.builder()
                   .passwordEncoder(encoder)
                   .userName(p.getString("auth.user"))
                   .encodedPassword(encoder.encode(p.getString("auth.password")))
                   .build();

        webApi = WebApi.builder()
                       .headerKey(p.getOptionalString("webapi.key"))
                       .headerValue(p.getOptionalString("webapi.value"))
                       .build();

        ocpp = Ocpp.builder()
                   .autoRegisterUnknownStations(p.getOptionalBoolean("auto.register.unknown.stations"))
                   .chargeBoxIdValidationRegex(p.getOptionalString("charge-box-id.validation.regex"))
                   .wsSessionSelectStrategy(
                           WsSessionSelectStrategyEnum.fromName(p.getString("ws.session.select.strategy")))
                   .build();

        // Load new firmware and FTP properties from main.properties
        physicalFirmwareUploadPath = p.getString("steve.firmware.physical-upload-path");

        String tempDiagnosticsUploadPath = p.getOptionalString("steve.diagnostics.physical-upload-path");
        if (tempDiagnosticsUploadPath == null || tempDiagnosticsUploadPath.trim().isEmpty()) {
            physicalDiagnosticsUploadPath = "ftp_files/logs/"; // Default to logs directory
        } else {
            physicalDiagnosticsUploadPath = tempDiagnosticsUploadPath;
        }

        String ftpEnabledStr = p.getOptionalString("steve.firmware.ftp.enabled");
        ftpEnabled = "true".equalsIgnoreCase(ftpEnabledStr);

        ftpUsername = p.getOptionalString("steve.firmware.ftp.username");
        ftpPassword = p.getOptionalString("steve.firmware.ftp.password");
        ftpIp = p.getOptionalString("steve.firmware.ftp.ip"); // Can be "auto", specific IP, or empty

        String ftpPortStr = p.getOptionalString("steve.firmware.ftp.port");
        int tempFtpPort; // Temporary variable for ftpPort
        try {
            tempFtpPort = (ftpPortStr != null && !ftpPortStr.trim().isEmpty()) ? Integer.parseInt(ftpPortStr.trim()) : 9988;
        } catch (NumberFormatException e) {
            System.err.println("Warning: Could not parse steve.firmware.ftp.port value: '" + ftpPortStr + "'. Using default 9988.");
            tempFtpPort = 9988;
        }
        ftpPort = tempFtpPort; // Assign to final field once
        
        String tempFtpBasePath = p.getOptionalString("steve.firmware.ftp.base.path");
        if (tempFtpBasePath == null || tempFtpBasePath.trim().isEmpty()) {
            ftpBasePath = "/bin/"; // Default base path if not specified or empty
        } else {
            ftpBasePath = tempFtpBasePath;
        }

        ftpLogPath = p.getOptionalString("steve.firmware.ftp.log-path");

        // Load passive FTP settings
        ftpPassivePorts = p.getOptionalString("steve.firmware.ftp.passive.ports");
        ftpPassiveExternalAddress = p.getOptionalString("steve.firmware.ftp.passive.external-address");

        validate();
    }

    public String getSteveCompositeVersion() {
        if (gitDescribe == null) {
            return steveVersion;
        } else {
            return steveVersion + "-g" + gitDescribe;
        }
    }

    // Add getters for these new fields:
    public String getPhysicalFirmwareUploadPath() { return physicalFirmwareUploadPath; }
    public String getPhysicalDiagnosticsUploadPath() { return physicalDiagnosticsUploadPath; }
    public boolean isFtpEnabled() { return ftpEnabled; }
    public String getFtpUsername() { return ftpUsername; }
    public String getFtpPassword() { return ftpPassword; }
    public String getFtpIp() { return ftpIp; }
    public int getFtpPort() { return ftpPort; }
    public String getFtpBasePath() { return ftpBasePath; }
    public String getFtpLogPath() { return ftpLogPath; }

    public String getFtpPassivePorts() {
        return ftpPassivePorts;
    }

    public String getFtpPassiveExternalAddress() {
        return ftpPassiveExternalAddress;
    }

    private static String useFallbackIfNotSet(String value, String fallback) {
        if (value == null) {
            // if the property is optional, value will be null
            return fallback;
        } else if (value.startsWith("${")) {
            // property value variables start with "${" (if maven is not used, the value will not be set)
            return fallback;
        } else {
            return value;
        }
    }

    private String sanitizeContextPath(String s) {
        if (s == null || "/".equals(s)) {
            return "";

        } else if (s.startsWith("/")) {
            return s;

        } else {
            return "/" + s;
        }
    }

    private void validate() {
        if (!(jetty.httpEnabled || jetty.httpsEnabled)) {
            throw new IllegalArgumentException(
                    "HTTP and HTTPS are both disabled. Well, how do you want to access the server, then?");
        }
    }

    // -------------------------------------------------------------------------
    // Class declarations
    // -------------------------------------------------------------------------

    // Jetty configuration
    @Builder @Getter
    public static class Jetty {
        private final String serverHost;
        private final boolean gzipEnabled;

        // HTTP
        private final boolean httpEnabled;
        private final int httpPort;

        // HTTPS
        private final boolean httpsEnabled;
        private final int httpsPort;
        private final String keyStorePath;
        private final String keyStorePassword;
    }

    // Database configuration
    @Builder @Getter
    public static class DB {
        private final String ip;
        private final int port;
        private final String schema;
        private final String userName;
        private final String password;
        private final boolean sqlLogging;
    }

    // Credentials for Web interface access
    @Builder @Getter
    public static class Auth {
        private final PasswordEncoder passwordEncoder;
        private final String userName;
        private final String encodedPassword;
    }

    @Builder @Getter
    public static class WebApi {
        private final String headerKey;
        private final String headerValue;
    }

    // OCPP-related configuration
    @Builder @Getter
    public static class Ocpp {
        private final boolean autoRegisterUnknownStations;
        private final String chargeBoxIdValidationRegex;
        private final WsSessionSelectStrategy wsSessionSelectStrategy;
    }

}
