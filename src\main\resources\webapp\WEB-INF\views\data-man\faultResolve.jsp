<%@ include file="../00-header.jsp" %>
<style>
    .content {
        max-width: 800px;
        margin: 0 auto;
    }
    .resolve-container {
        background-color: #f9f9f9;
        padding: 25px;
        border-radius: 5px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        margin-top: 20px;
    }
    .issue-info {
        background-color: white;
        padding: 15px;
        border-radius: 4px;
        margin-bottom: 20px;
        border-left: 4px solid #5bc0de;
    }
    .issue-info h3 {
        margin-top: 0;
        color: #333;
    }
    .issue-detail {
        display: flex;
        margin-bottom: 8px;
    }
    .issue-label {
        width: 120px;
        font-weight: bold;
        color: #555;
    }
    .issue-value {
        flex: 1;
    }
    .form-group {
        margin-bottom: 20px;
    }
    .form-label {
        display: block;
        font-weight: bold;
        margin-bottom: 8px;
        color: #333;
    }
    .form-textarea {
        width: 100%;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        min-height: 120px;
        font-family: inherit;
        font-size: 14px;
        resize: vertical;
    }
    .form-textarea:focus {
        border-color: #5bc0de;
        outline: none;
        box-shadow: 0 0 3px rgba(91, 192, 222, 0.5);
    }
    .required {
        color: #d9534f;
        margin-left: 3px;
    }
    .btn-group {
        margin-top: 25px;
    }
    .btn {
        padding: 10px 20px;
        border-radius: 4px;
        text-decoration: none;
        display: inline-block;
        margin-right: 10px;
        font-weight: bold;
        cursor: pointer;
        border: none;
        font-size: 14px;
    }
    .btn-primary {
        background-color: #5cb85c;
        color: white;
    }
    .btn-primary:hover {
        background-color: #4cae4c;
    }
    .btn-secondary {
        background-color: #f0f0f0;
        color: #333;
        border: 1px solid #ddd;
    }
    .btn-secondary:hover {
        background-color: #e0e0e0;
    }
    .status-badge {
        display: inline-block;
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 0.85em;
        font-weight: bold;
    }
    .status-new {
        background-color: #f8d7da;
        color: #721c24;
    }
    .status-in-progress {
        background-color: #fff3cd;
        color: #856404;
    }
</style>

<div class="content">
    <section><span>解决故障</span></section>
    
    <div class="resolve-container">
        <div class="issue-info">
            <h3>故障信息</h3>
            <div class="issue-detail">
                <div class="issue-label">故障ID:</div>
                <div class="issue-value">${issue.issueId}</div>
            </div>
            <div class="issue-detail">
                <div class="issue-label">充电桩:</div>
                <div class="issue-value">${issue.chargeBoxId}</div>
            </div>
            <div class="issue-detail">
                <div class="issue-label">状态:</div>
                <div class="issue-value">
                    <span class="status-badge ${issue.status eq 'NEW' ? 'status-new' : 'status-in-progress'}">
                        ${issue.status.value()}
                    </span>
                </div>
            </div>
            <div class="issue-detail">
                <div class="issue-label">故障描述:</div>
                <div class="issue-value">${issue.faultDescription}</div>
            </div>
        </div>
        
        <form:form action="${ctxPath}/manager/faults/resolve/${issue.issueId}" method="post" modelAttribute="resolveForm">
            <div class="form-group">
                <label for="resolveDescription" class="form-label">解决方案描述<span class="required">*</span></label>
                <form:textarea path="resolveDescription" class="form-textarea" required="required"/>
                <form:errors path="resolveDescription" cssClass="error"/>
            </div>
            
            <div class="btn-group">
                <button type="submit" class="btn btn-primary">确认解决</button>
                <a href="${ctxPath}/manager/faults/details/${issue.issueId}" class="btn btn-secondary">返回详情</a>
            </div>
        </form:form>
    </div>
</div>

<%@ include file="../00-footer.jsp" %> 