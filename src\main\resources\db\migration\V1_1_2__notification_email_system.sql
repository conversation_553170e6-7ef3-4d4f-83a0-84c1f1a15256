-- 创建故障通知邮箱表
CREATE TABLE IF NOT EXISTS fault_notification_email (
    id INT NOT NULL AUTO_INCREMENT,
    email VARCHAR(255) NOT NULL,
    enabled BOOLEAN NOT NULL DEFAULT TRUE,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_notified_time TIMESTAMP NULL,
    description VARCHAR(255) NULL COMMENT '备注',
    PRIMARY KEY (id),
    UNIQUE KEY (email),
    INDEX idx_enabled (enabled),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci; 