-- 首先确保所有用户的用户角色与权限一致
-- 更新角色为ADMIN的用户
UPDATE web_user SET user_role = 'ADMIN' WHERE JSON_CONTAINS(authorities, '"ADMIN"');

-- 更新角色为OPERATOR_FACTORY的用户
UPDATE web_user SET user_role = 'OPERATOR_FACTORY' WHERE JSON_CONTAINS(authorities, '"OPERATOR_FACTORY"');

-- 更新角色为OPERATOR_OWNER的用户
UPDATE web_user SET user_role = 'OPERATOR_OWNER' WHERE JSON_CONTAINS(authorities, '"OPERATOR_OWNER"');

-- 更新角色为ROLE_USER的用户（默认设置为OPERATOR_OWNER角色）
UPDATE web_user SET user_role = 'OPERATOR_OWNER' WHERE JSON_CONTAINS(authorities, '"ROLE_USER"');

-- 删除authorities列，因为我们现在完全使用user_role
ALTER TABLE web_user DROP COLUMN authorities;

-- 删除之前V1_0_6__update.sql中创建的authorities检查约束
-- 因为该列已被删除，该约束也不再需要
-- (SQL执行时会自动删除约束)