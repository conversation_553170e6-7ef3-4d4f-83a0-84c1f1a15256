/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.service;

import de.rwth.idsg.steve.NotificationFeature;
import de.rwth.idsg.steve.repository.SettingsRepository;
import de.rwth.idsg.steve.service.notification.OcppStationStatusFailure;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import static de.rwth.idsg.steve.NotificationFeature.OcppStationStatusFailure;

/**
 * 自动故障监听服务，用于监听充电桩故障事件并自动创建故障记录
 * 注意：该服务已被禁用，故障监听功能由FaultEventListener提供，避免重复创建故障记录
 */
@Slf4j
@Service
public class AutoFaultListenerService {

    @Autowired
    private FaultService faultService;
    
    @Autowired
    private SettingsRepository settingsRepository;

    /**
     * 监听充电桩故障事件，自动创建故障记录
     * 注意：该方法已被禁用，以避免重复创建故障记录
     */
    //@EventListener
    public void handleOcppStationStatusFailure(OcppStationStatusFailure notification) {
        // 已禁用，改由FaultEventListener处理
        log.debug("AutoFaultListenerService.handleOcppStationStatusFailure已禁用，故障记录由FaultEventListener创建");
    }
    
    /**
     * 检查通知功能是否启用
     */
    private boolean isNotificationFeatureEnabled(NotificationFeature feature) {
        return settingsRepository.getNotificationSettings().isEnabled(feature);
    }
} 