<%--

    SteV<PERSON> - SteckdosenVerwaltung - https://github.com/steve-community/steve
    Copyright (C) 2013-2025 SteVe Community Team
    All Rights Reserved.

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.

--%>
<%@ include file="../00-header.jsp" %>
<%@ include file="../00-op-bind-errors.jsp" %>
<script type="text/javascript">
	$(document).ready(function() {
		<%@ include file="../snippets/dateTimePicker.js" %>
		<%@ include file="../snippets/periodTypeSelect.js" %>
		<%@ include file="../snippets/sortable.js" %>
	});
</script>
<div class="content-container">
	<div class="page-title">Reservations</div>
	<section><span>
		Reservations
			<a class="tooltip" href="#"><img src="${ctxPath}/static/images/info.png" style="vertical-align:middle">
				<span>
				Status definitions:
				WAITING (Waiting for charge point to respond to a reservation request),
				ACCEPTED (Charge point accepted - The only status for active, usable reservations),
				USED (Reservation used by the user for a transaction),
				CANCELLED (Reservation cancelled by the user)
				</span>
			</a>
	</span></section>
	<div id="overview">
		<div class="search-panel mb-4">
			<form:form action="${ctxPath}/manager/reservations/query" method="get" modelAttribute="params">
				<div class="row mb-3">
					<div class="col-md-6 mb-3">
						<div class="form-group">
							<label for="chargeBoxId">ChargeBox ID:</label>
							<form:select path="chargeBoxId" class="form-control">
								<option value="" selected>All</option>
								<form:options items="${cpList}"/>
							</form:select>
						</div>
					</div>
					<div class="col-md-6 mb-3">
						<div class="form-group">
							<label for="ocppIdTag">OCPP ID Tag:</label>
							<form:select path="ocppIdTag" class="form-control">
								<option value="" selected>All</option>
								<form:options items="${idTagList}"/>
							</form:select>
						</div>
					</div>
				</div>
				<div class="row mb-3">
					<div class="col-md-6 mb-3">
						<div class="form-group">
							<label for="status">Reservation Status:</label>
							<form:select path="status" class="form-control">
								<option value="" selected>All</option>
								<form:options items="${statusList}"/>
							</form:select>
						</div>
					</div>
					<div class="col-md-6 mb-3">
						<div class="form-group">
							<label for="periodType">Period Type:</label>
							<form:select path="periodType" id="periodTypeSelect" class="form-control">
								<form:options items="${periodType}" itemLabel="value"/>
							</form:select>
						</div>
					</div>
				</div>
				<div class="row mb-3">
					<div class="col-md-6 mb-3">
						<div class="form-group">
							<label for="from">From:</label>
							<form:input path="from" id="intervalPeriodTypeFrom" cssClass="dateTimePicker form-control"/>
						</div>
					</div>
					<div class="col-md-6 mb-3">
						<div class="form-group">
							<label for="to">To:</label>
							<form:input path="to" id="intervalPeriodTypeTo" cssClass="dateTimePicker form-control"/>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-md-12 text-right">
						<input type="submit" class="btn btn-primary" value="Search">
					</div>
				</div>
			</form:form>
		</div>

		<div class="table-responsive">
			<table class="table table-striped table-hover table-bordered">
				<thead>
					<tr>
						<th class="sorting" data-sort="int">Reservation ID</th>
						<th class="sorting" data-sort="int">Transaction ID</th>
						<th class="sorting" data-sort="string">OCPP ID Tag</th>
						<th class="sorting" data-sort="string">ChargeBox ID</th>
						<th class="sorting" data-sort="int">Connector ID</th>
						<th class="sorting" data-sort="date">Start Date/Time</th>
						<th class="sorting" data-sort="date">Expiry Date/Time</th>
						<th class="sorting" data-sort="string">Status</th>
					</tr>
				</thead>
				<tbody>
				<c:forEach items="${reservList}" var="res">
					<tr>
						<td>${res.id}</td>
						<td>
							<c:if test="${not empty res.transactionId}">
								<a href="${ctxPath}/manager/transactions/query?type=ALL&transactionPk=${res.transactionId}">${res.transactionId}</a>
							</c:if>
						</td>
						<td><a href="${ctxPath}/manager/ocppTags/details/${res.ocppTagPk}">${res.ocppIdTag}</a></td>
						<td><a href="${ctxPath}/manager/chargepoints/details/${res.chargeBoxPk}">${res.chargeBoxId}</a></td>
						<td>${res.connectorId}</td>
						<td data-sort-value="${res.startDatetimeDT.millis}">${res.startDatetime}</td>
						<td data-sort-value="${res.expiryDatetimeDT.millis}">${res.expiryDatetime}</td>
						<td>${res.status}</td>
					</tr>
				</c:forEach>
				</tbody>
			</table>
		</div>
	</div>
</div>
<%@ include file="../00-footer.jsp" %>