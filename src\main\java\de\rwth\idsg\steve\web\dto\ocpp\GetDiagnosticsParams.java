/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.web.dto.ocpp;

import lombok.Getter;
import lombok.Setter;
import org.joda.time.LocalDate;
import org.joda.time.LocalDateTime;

import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.Min;

/**
 * <AUTHOR> <PERSON>ekay <<EMAIL>>
 * @since 30.12.2014
 */
@Getter
@Setter
public class GetDiagnosticsParams extends MultipleChargePointSelect {

    // Location will be auto-generated, no validation needed
    private String location;

    @Min(value = 1, message = "Retries must be at least {value}")
    private Integer retries;

    @Min(value = 1, message = "Retry Interval must be at least {value}")
    private Integer retryInterval;

    // Start and Stop dates for GetDiagnostics
    // If not provided, defaults to yesterday
    private LocalDate startDate;

    private LocalDate stopDate;

    // Internal fields for OCPP request (converted from dates)
    private LocalDateTime start;
    private LocalDateTime stop;

    @AssertTrue(message = "Start Date/Time must be in the past if provided")
    public boolean isStartDateValid() {
        // If start is null or in the past, validation passes
        return start == null || start.isBefore(org.joda.time.LocalDateTime.now());
    }

    @AssertTrue(message = "Stop Date/Time must be in the past if provided")
    public boolean isStopDateValid() {
        // If stop is null or in the past, validation passes
        return stop == null || stop.isBefore(org.joda.time.LocalDateTime.now());
    }

    @AssertTrue(message = "Stop Date/Time must be after Start Date/Time")
    public boolean isDateRangeValid() {
        // If both start and stop are provided, stop must be after start
        // If either is null, validation passes
        return !(start != null && stop != null) || stop.isAfter(start);
    }
}
