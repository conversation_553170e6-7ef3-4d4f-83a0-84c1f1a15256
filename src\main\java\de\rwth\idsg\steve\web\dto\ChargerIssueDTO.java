/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.web.dto;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * 充电桩故障数据传输对象
 */
@Getter
@Setter
@Builder
public class ChargerIssueDTO {
    
    private Integer issueId;
    private Integer chargeBoxPk;
    private String chargeBoxId;
    private Date reportTime;
    private Integer reporterUserPk;
    private String reporterUsername;
    private String faultDescription;
    private ChargerIssueStatus status;
    private String ocppErrorCode;
    private Boolean isAutoReported;
    private Date resolveTime;
    private String resolveDescription;
    private List<String> imagePaths;
    private String chargeBoxOwner;
    private String vendorErrorCode;
    private String vendorId;
    
    // 辅助方法
    public boolean isResolved() {
        return ChargerIssueStatus.RESOLVED.equals(status);
    }
    
    public boolean isInProgress() {
        return ChargerIssueStatus.IN_PROGRESS.equals(status);
    }
} 