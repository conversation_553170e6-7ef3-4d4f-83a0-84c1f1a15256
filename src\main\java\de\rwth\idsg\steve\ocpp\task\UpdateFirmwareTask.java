/*
 * SteV<PERSON> - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2023 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.ocpp.task;

import de.rwth.idsg.steve.ocpp.CommunicationTask;
import de.rwth.idsg.steve.ocpp.OcppCallback;
import de.rwth.idsg.steve.repository.UpdateFirmwareLogRepository;
import de.rwth.idsg.steve.utils.DateTimeUtils;
import de.rwth.idsg.steve.web.dto.UpdateFirmwareLog;
import de.rwth.idsg.steve.web.dto.ocpp.UpdateFirmwareParams;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.xml.ws.AsyncHandler;

import static de.rwth.idsg.steve.utils.DateTimeUtils.toDateTime;

import java.util.List;

/**
 * <AUTHOR> Goekay <<EMAIL>>
 * @since 09.03.2018
 */
@Slf4j
@Component
public class UpdateFirmwareTask extends CommunicationTask<UpdateFirmwareParams, String> {

    @Autowired
    private UpdateFirmwareLogRepository updateFirmwareLogRepository;

    public UpdateFirmwareTask(UpdateFirmwareParams params) {
        super(params);
    }
    
    /**
     * 设置固件更新日志仓库
     * 当通过new操作符创建实例时，Spring不会自动注入，需要手动设置
     */
    public void setUpdateFirmwareLogRepository(UpdateFirmwareLogRepository updateFirmwareLogRepository) {
        this.updateFirmwareLogRepository = updateFirmwareLogRepository;
    }

    @Override
    public OcppCallback<String> defaultCallback() {
        return new StringOcppCallback();
    }

    @Override
    public ocpp.cp._2010._08.UpdateFirmwareRequest getOcpp12Request() {
        ocpp.cp._2010._08.UpdateFirmwareRequest request = new ocpp.cp._2010._08.UpdateFirmwareRequest()
                .withLocation(params.getLocation())
                .withRetrieveDate(toDateTime(params.getRetrieve()))
                .withRetries(params.getRetries())
                .withRetryInterval(params.getRetryInterval());
        
        log.info("Created UpdateFirmware request for OCPP 1.2: {}", request);
        return request;
    }

    @Override
    public ocpp.cp._2012._06.UpdateFirmwareRequest getOcpp15Request() {
        ocpp.cp._2012._06.UpdateFirmwareRequest request = new ocpp.cp._2012._06.UpdateFirmwareRequest()
                .withLocation(params.getLocation())
                .withRetrieveDate(toDateTime(params.getRetrieve()))
                .withRetries(params.getRetries())
                .withRetryInterval(params.getRetryInterval());

        
        log.info("Created UpdateFirmware request for OCPP 1.5: {}", request);
        return request;
    }

    @Override
    public ocpp.cp._2015._10.UpdateFirmwareRequest getOcpp16Request() {
        ocpp.cp._2015._10.UpdateFirmwareRequest request = new ocpp.cp._2015._10.UpdateFirmwareRequest()
                .withLocation(params.getLocation())
                .withRetrieveDate(toDateTime(params.getRetrieve()))
                .withRetries(params.getRetries())
                .withRetryInterval(params.getRetryInterval());
        
        log.info("Created UpdateFirmware request for OCPP 1.6: {}", request);
        return request;
    }

    @Override
    public AsyncHandler<ocpp.cp._2010._08.UpdateFirmwareResponse> getOcpp12Handler(String chargeBoxId) {
        return res -> {
            try {
                log.info("Received UpdateFirmware response for OCPP 1.2 from {}: {}", chargeBoxId, res.get());
                success(chargeBoxId, "OK");
                
                // 为OCPP 1.2版本记录日志，确保所有版本都记录日志
                logUpdateFirmware(params.getLocation(), params.getRetries(), params.getRetryInterval(), 
                                 DateTimeUtils.toDateTime(params.getRetrieve()), chargeBoxId, "Success", "OK");
                
            } catch (Exception e) {
                log.error("Error in UpdateFirmware for {}: {}", chargeBoxId, e.getMessage());
                failed(chargeBoxId, e);
                
                // 记录失败日志
                logUpdateFirmware(params.getLocation(), params.getRetries(), params.getRetryInterval(), 
                                 DateTimeUtils.toDateTime(params.getRetrieve()), chargeBoxId, "Failed", e.getMessage());
            }
        };
    }

    @Override
    public AsyncHandler<ocpp.cp._2012._06.UpdateFirmwareResponse> getOcpp15Handler(String chargeBoxId) {
        return res -> {
            try {
                log.info("Received UpdateFirmware response for OCPP 1.5 from {}: {}", chargeBoxId, res.get());
                success(chargeBoxId, "OK");
                
                // 为OCPP 1.5版本记录日志，确保所有版本都记录日志
                logUpdateFirmware(params.getLocation(), params.getRetries(), params.getRetryInterval(), 
                                 DateTimeUtils.toDateTime(params.getRetrieve()), chargeBoxId, "Success", "OK");
                
            } catch (Exception e) {
                log.error("Error in UpdateFirmware for {}: {}", chargeBoxId, e.getMessage());
                failed(chargeBoxId, e);
                
                // 记录失败日志
                logUpdateFirmware(params.getLocation(), params.getRetries(), params.getRetryInterval(), 
                                 DateTimeUtils.toDateTime(params.getRetrieve()), chargeBoxId, "Failed", e.getMessage());
            }
        };
    }

    @Override
    public AsyncHandler<ocpp.cp._2015._10.UpdateFirmwareResponse> getOcpp16Handler(String chargeBoxId) {
        return res -> {
            try {
                log.info("Received UpdateFirmware response for OCPP 1.6 from {}: {}", chargeBoxId, res.get());
                success(chargeBoxId, "OK");
                
                // 记录日志 - 只对OCPP 1.6记录成功状态的日志
                logUpdateFirmware(params.getLocation(), params.getRetries(), params.getRetryInterval(), 
                                 DateTimeUtils.toDateTime(params.getRetrieve()), chargeBoxId, "Success", "OK");
                
            } catch (Exception e) {
                log.error("Error in UpdateFirmware for {}: {}", chargeBoxId, e.getMessage());
                failed(chargeBoxId, e);
                
                // 不再重复记录失败日志，因为logUpdateFirmware方法已经包含了重复检测逻辑
                // 如果确实需要区分成功和失败状态，则可以保留下面的代码
                // logUpdateFirmware(params.getLocation(), params.getRetries(), params.getRetryInterval(), 
                //                  DateTimeUtils.toDateTime(params.getRetrieve()), chargeBoxId, "Failed", e.getMessage());
            }
        };
    }
    
    /**
     * 记录固件更新日志
     */
    private void logUpdateFirmware(String location, Integer retries, Integer retryInterval, 
                                   DateTime retrieveDateTime, String chargeBoxId, 
                                   String status, String response) {
        try {
            if (updateFirmwareLogRepository != null) {
                // 在更长时间内（60秒内）检查是否已经存在相同参数的记录，避免重复记录
                List<UpdateFirmwareLog> recentLogs = updateFirmwareLogRepository.getRecentLogs(chargeBoxId, 60);
                
                boolean isDuplicate = false;
                if (recentLogs != null && !recentLogs.isEmpty()) {
                    for (UpdateFirmwareLog logEntry : recentLogs) {
                        // 检查是否有相同参数的记录
                        if (isSameLocation(location, logEntry.getLocation()) && 
                            isSameRetrieveTime(retrieveDateTime, logEntry.getRetrieveDatetime())) {
                            isDuplicate = true;
                            // 修改为debug级别，避免误导性的日志信息
                            log.debug("Firmware update command already exists for chargeBoxId={}, location={}, skipping duplicate record", 
                                    chargeBoxId, location);
                            break;
                        }
                    }
                }
                
                if (!isDuplicate) {
                    int logId = updateFirmwareLogRepository.insert(location, retries, retryInterval, 
                                                                  retrieveDateTime, chargeBoxId, 
                                                                  status, response);
                    if (logId > 0) {
                        log.info("Successfully recorded update firmware {} command for chargeBoxId={}, logId={}", 
                                status, chargeBoxId, logId);
                    } else {
                        // 修改为debug级别，避免误导性的警告信息
                        log.debug("Unable to record firmware update normally, will try alternative method");
                        // 再尝试一次
                        logId = updateFirmwareLogRepository.insert(location, retries, retryInterval, 
                                                                  retrieveDateTime, chargeBoxId, 
                                                                  status, response);
                        log.debug("Alternative recording method result: {}", logId > 0 ? "Success" : "Failed but operation continues");
                    }
                }
            }
        } catch (Exception e) {
            // 修改为debug级别，因为这不影响主要操作
            log.debug("Note: Unable to log UpdateFirmware operation: {}, but firmware update continues", e.getMessage());
        }
    }

    /**
     * 判断两个固件位置是否相同
     */
    private boolean isSameLocation(String location1, String location2) {
        if (location1 == null && location2 == null) {
            return true;
        }
        if (location1 == null || location2 == null) {
            return false;
        }
        return location1.equals(location2);
    }
    
    /**
     * 判断两个检索时间是否相同（允许几秒钟的误差）
     */
    private boolean isSameRetrieveTime(DateTime time1, DateTime time2) {
        if (time1 == null && time2 == null) {
            return true;
        }
        if (time1 == null || time2 == null) {
            return false;
        }
        // 允许5秒内的误差
        return Math.abs(time1.getMillis() - time2.getMillis()) < 5000;
    }
}