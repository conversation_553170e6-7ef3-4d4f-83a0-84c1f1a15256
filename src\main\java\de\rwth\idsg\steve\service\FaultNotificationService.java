/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.service;

import de.rwth.idsg.steve.web.dto.ChargerIssueDTO;
import de.rwth.idsg.steve.web.dto.FaultNotificationEmailDTO;
import de.rwth.idsg.steve.web.dto.FaultNotificationEmailForm;

import java.util.List;

/**
 * 故障通知服务接口
 */
public interface FaultNotificationService {
    
    /**
     * 获取所有通知邮箱
     */
    List<FaultNotificationEmailDTO> getAllEmails();
    
    /**
     * 获取已启用的通知邮箱
     */
    List<FaultNotificationEmailDTO> getEnabledEmails();
    
    /**
     * 添加通知邮箱
     */
    int addEmail(FaultNotificationEmailForm form);
    
    /**
     * 更新通知邮箱
     */
    void updateEmail(FaultNotificationEmailForm form);
    
    /**
     * 删除通知邮箱
     */
    void deleteEmail(Integer id);
    
    /**
     * 发送新故障通知
     */
    void sendNewIssueNotification(ChargerIssueDTO issue);
} 