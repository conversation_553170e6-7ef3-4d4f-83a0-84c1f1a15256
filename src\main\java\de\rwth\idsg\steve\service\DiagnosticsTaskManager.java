/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.service;

import de.rwth.idsg.steve.repository.TaskStore;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.concurrent.ConcurrentHashMap;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;

/**
 * 诊断任务管理器
 * 负责管理每个充电桩的诊断任务生命周期，避免重复任务和资源浪费
 * 
 * <AUTHOR> Community Team
 * @since 2025.01
 */
@Slf4j
@Service
public class DiagnosticsTaskManager {
    
    @Autowired
    private TaskStore taskStore;
    
    @Autowired
    private DiagnosticFileDownloadService downloadService;
    
    /**
     * 诊断任务过期时间（分钟）
     */
    @Value("${steve.diagnostics.task.expiry-minutes:10}")
    private int taskExpiryMinutes;
    
    /**
     * 充电桩诊断任务映射表
     * Key: 充电桩ID, Value: 诊断任务信息
     */
    private final ConcurrentHashMap<String, DiagnosticsTaskInfo> chargeBoxTasks = new ConcurrentHashMap<>();
    
    /**
     * 检查指定充电桩是否有有效的诊断任务
     * 
     * @param chargeBoxId 充电桩ID
     * @return 如果有有效任务返回任务信息，否则返回null
     */
    public DiagnosticsTaskInfo getValidTask(String chargeBoxId) {
        DiagnosticsTaskInfo taskInfo = chargeBoxTasks.get(chargeBoxId);
        
        if (taskInfo == null) {
            log.debug("No existing task found for charge box: {}", chargeBoxId);
            return null;
        }
        
        if (taskInfo.isValid()) {
            log.info("📋 Found valid existing task for {}: {} (remaining: {} min)", 
                    chargeBoxId, taskInfo.getTaskId(), taskInfo.getRemainingMinutes());
            return taskInfo;
        } else {
            log.info("⏰ Existing task for {} has expired, will be cleaned up", chargeBoxId);
            // 过期任务会在定时清理中处理
            return null;
        }
    }
    
    /**
     * 注册新的诊断任务
     *
     * @param chargeBoxId 充电桩ID
     * @param taskId 任务ID
     * @return 创建的任务信息
     */
    public DiagnosticsTaskInfo registerTask(String chargeBoxId, Integer taskId) {
        log.info("🔧 DiagnosticsTaskManager.registerTask called for {}: taskId={}", chargeBoxId, taskId);

        // 先清理该充电桩的旧任务（如果存在）
        cleanupTaskForChargeBox(chargeBoxId);

        DiagnosticsTaskInfo taskInfo = new DiagnosticsTaskInfo(taskId, chargeBoxId, taskExpiryMinutes);
        chargeBoxTasks.put(chargeBoxId, taskInfo);

        log.info("📝 Registered new diagnostics task for {}: taskId={}, expiresIn={}min, totalTasks={}",
                chargeBoxId, taskId, taskExpiryMinutes, chargeBoxTasks.size());

        return taskInfo;
    }
    
    /**
     * 标记任务的文件已准备好
     *
     * @param chargeBoxId 充电桩ID
     * @param fileName 文件名
     */
    public void markFileReady(String chargeBoxId, String fileName) {
        log.info("🔧 DiagnosticsTaskManager.markFileReady called for {}: fileName={}, totalTasks={}",
                chargeBoxId, fileName, chargeBoxTasks.size());

        DiagnosticsTaskInfo taskInfo = chargeBoxTasks.get(chargeBoxId);
        if (taskInfo != null && taskInfo.isValid()) {
            taskInfo.markFileReady(fileName);
            log.info("✅ Marked file ready for {}: {}", chargeBoxId, fileName);
        } else {
            log.warn("⚠️ Cannot mark file ready for {}: no valid task found (taskInfo={}, totalTasks={})",
                    chargeBoxId, taskInfo, chargeBoxTasks.size());

            // 打印所有现有任务用于调试
            if (!chargeBoxTasks.isEmpty()) {
                log.info("🔍 Current tasks in manager:");
                chargeBoxTasks.forEach((id, info) ->
                    log.info("  - {}: taskId={}, status={}, valid={}",
                            id, info.getTaskId(), info.getStatus(), info.isValid()));
            }
        }
    }
    
    /**
     * 标记任务的文件已下载
     * 
     * @param chargeBoxId 充电桩ID
     */
    public void markFileDownloaded(String chargeBoxId) {
        DiagnosticsTaskInfo taskInfo = chargeBoxTasks.get(chargeBoxId);
        if (taskInfo != null) {
            taskInfo.markDownloaded();
            log.info("📥 Marked file downloaded for {}", chargeBoxId);
        }
    }
    
    /**
     * 清理指定充电桩的任务
     * 
     * @param chargeBoxId 充电桩ID
     */
    public void cleanupTaskForChargeBox(String chargeBoxId) {
        DiagnosticsTaskInfo taskInfo = chargeBoxTasks.remove(chargeBoxId);
        if (taskInfo != null) {
            log.info("🗑️ Cleaned up task for {}: taskId={}", chargeBoxId, taskInfo.getTaskId());
            
            // 清理相关文件
            if (downloadService.hasDownloadableFile(chargeBoxId)) {
                downloadService.cleanupAfterDownload(chargeBoxId);
            }
        }
    }
    
    /**
     * 清理所有过期的任务
     *
     * @return 清理的任务数量
     */
    public int cleanupExpiredTasks() {
        List<String> expiredChargeBoxes = new ArrayList<>();

        log.debug("🔍 Checking for expired tasks. Current total tasks: {}", chargeBoxTasks.size());

        // 找出所有过期的任务
        for (Map.Entry<String, DiagnosticsTaskInfo> entry : chargeBoxTasks.entrySet()) {
            DiagnosticsTaskInfo taskInfo = entry.getValue();
            log.debug("🔍 Checking task for {}: taskId={}, isExpired={}, remainingMinutes={}",
                    entry.getKey(), taskInfo.getTaskId(), taskInfo.isExpired(), taskInfo.getRemainingMinutes());

            if (taskInfo.isExpired()) {
                expiredChargeBoxes.add(entry.getKey());
            }
        }

        log.info("🔍 Found {} expired tasks to clean up", expiredChargeBoxes.size());

        // 清理过期任务
        for (String chargeBoxId : expiredChargeBoxes) {
            DiagnosticsTaskInfo taskInfo = chargeBoxTasks.remove(chargeBoxId);
            if (taskInfo != null) {
                log.info("⏰ Cleaning up expired task for {}: taskId={}",
                        chargeBoxId, taskInfo.getTaskId());

                try {
                    // 清理TaskStore中的任务
                    log.info("🗑️ Attempting to clean up TaskStore entry for taskId: {}", taskInfo.getTaskId());

                    // 获取任务并标记为完成（如果还在运行）
                    try {
                        de.rwth.idsg.steve.ocpp.CommunicationTask task = taskStore.get(taskInfo.getTaskId());
                        if (task != null && !task.isFinished()) {
                            // 强制完成任务，这样TaskStore的定期清理会移除它
                            log.info("🔚 Forcing completion of expired task: {}", taskInfo.getTaskId());
                            // 注意：我们不能直接修改任务状态，但可以记录这个情况
                            // TaskStore的清理会在下次运行时处理已完成的任务
                        }
                    } catch (Exception taskStoreException) {
                        // 任务可能已经不存在了，这是正常的
                        log.debug("Task {} not found in TaskStore (already cleaned?): {}",
                                taskInfo.getTaskId(), taskStoreException.getMessage());
                    }

                    // 清理下载文件
                    if (downloadService.hasDownloadableFile(chargeBoxId)) {
                        log.info("🗑️ Cleaning up diagnostic file for {}", chargeBoxId);
                        downloadService.cleanupAfterDownload(chargeBoxId);
                    } else {
                        // 强制删除文件（如果存在）
                        boolean fileDeleted = downloadService.forceDeleteFile(chargeBoxId);
                        if (fileDeleted) {
                            log.info("🗑️ Force deleted diagnostic file for {}", chargeBoxId);
                        }
                    }
                } catch (Exception e) {
                    log.warn("❌ Error cleaning up task {}: {}", taskInfo.getTaskId(), e.getMessage());
                }
            }
        }

        if (!expiredChargeBoxes.isEmpty()) {
            log.info("🧹 Successfully cleaned up {} expired diagnostics tasks", expiredChargeBoxes.size());
        } else {
            log.debug("✅ No expired tasks found during cleanup");
        }

        return expiredChargeBoxes.size();
    }
    
    /**
     * 获取所有活跃任务的统计信息
     * 
     * @return 统计信息字符串
     */
    public String getTaskStatistics() {
        int totalTasks = chargeBoxTasks.size();
        long validTasks = chargeBoxTasks.values().stream()
                .mapToLong(task -> task.isValid() ? 1 : 0)
                .sum();
        
        return String.format("Active diagnostics tasks: %d (valid: %d)", totalTasks, validTasks);
    }
    
    /**
     * 获取任务过期时间配置
     * 
     * @return 过期时间（分钟）
     */
    public int getTaskExpiryMinutes() {
        return taskExpiryMinutes;
    }
}
