/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.repository;

import de.rwth.idsg.steve.repository.dto.OcppTag;
import de.rwth.idsg.steve.web.dto.OcppTagForm;
import de.rwth.idsg.steve.web.dto.OcppTagQueryForm;
import jooq.steve.db.tables.records.OcppTagActivityRecord;
import org.jooq.Result;

import java.util.List;

/**
 * <AUTHOR> Goekay <<EMAIL>>
 * @since 19.08.2014
 */
public interface OcppTagRepository {
    List<OcppTag.OcppTagOverview> getOverview(OcppTagQueryForm form);

    Result<OcppTagActivityRecord> getRecords();
    Result<OcppTagActivityRecord> getRecords(List<String> idTagList);

    OcppTagActivityRecord getRecord(String idTag);
    OcppTagActivityRecord getRecord(int ocppTagPk);

    List<String> getIdTags();
    List<String> getActiveIdTags();

    List<String> getParentIdTags();
    String getParentIdtag(String idTag);

    void addOcppTagList(List<String> idTagList);
    int addOcppTag(OcppTagForm form);
    void updateOcppTag(OcppTagForm form);
    void deleteOcppTag(int ocppTagPk);
}
