<%--

    SteV<PERSON> - SteckdosenVerwaltung - https://github.com/steve-community/steve
    Copyright (C) 2013-2025 SteVe Community Team
    All Rights Reserved.

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.

--%>
<%@ include file="00-header.jsp" %>
<div class="content">

<!-- 显示成功消息 -->
<c:if test="${not empty sessionScope.successMessage}">
    <div class="success-message" style="background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
        <strong>Success!</strong> ${sessionScope.successMessage}
    </div>
    <!-- 显示后清除session中的消息 -->
    <c:remove var="successMessage" scope="session"/>
</c:if>

<!-- GetDiagnostics特定信息 -->
<c:if test="${task.operationName == 'GetDiagnostics'}">
    <div class="diagnostics-info" style="background-color: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
        <h4>GetDiagnostics Progress Monitor</h4>
        <p>You can monitor the real-time progress of diagnostic file uploads:</p>
        <p><a href="../../../diagnostics_progress_monitor.html" target="_blank" style="color: #007cba; text-decoration: none;">
            Open Progress Monitor (New Window)
        </a></p>
        <p><strong>Auto-download:</strong> Diagnostic files will be automatically downloaded to your computer when ready.</p>
        <div class="download-reminder" style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 10px 0; transition: all 0.3s ease;">
            <strong>📥 Download Reminder:</strong> Once the diagnostic file is ready, you can download it multiple times within <strong>10 minutes</strong>.
            After 10 minutes, this task and the diagnostic file will be automatically cleaned up to save server space.
        </div>

        <style>
            @keyframes pulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.02); }
                100% { transform: scale(1); }
            }

            .download-reminder {
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                font-size: 14px;
                line-height: 1.4;
            }

            .download-reminder strong {
                color: #856404;
            }
        </style>
        <div id="downloadStatus" style="margin-top: 10px; padding: 10px; background-color: #f8f9fa; border-radius: 3px;">
            <span id="downloadStatusText">Checking for downloadable files...</span>
        </div>
    </div>

    <script>
        // Auto-download functionality for GetDiagnostics
        let autoDownloadedFiles = new Set();
        let downloadCheckInterval;

        function checkForDownloadableFiles() {
            console.log('Checking for downloadable files...');

            fetch('/steve/api/diagnostics/progress')
                .then(response => response.json())
                .then(data => {
                    console.log('Progress data:', data);

                    let hasReadyFiles = false;
                    let statusText = 'Monitoring for diagnostic files...';

                    for (const [chargeBoxId, progressData] of Object.entries(data)) {
                        console.log(chargeBoxId + ': ' + progressData.status + ' (' + progressData.percentage + '%)');

                        if (progressData.status === 'File Ready' && !autoDownloadedFiles.has(chargeBoxId)) {
                            hasReadyFiles = true;
                            statusText = 'Auto-downloading file for ' + chargeBoxId + '...';
                            autoDownloadedFiles.add(chargeBoxId);

                            // Trigger download
                            setTimeout(() => {
                                downloadFileFromTask(chargeBoxId);
                            }, 1000);

                        } else if (progressData.status === 'Downloading') {
                            statusText = 'Downloading file for ' + chargeBoxId + '...';
                            // 显示下载中的提醒
                            showDownloadReminder('downloading');
                        } else if (progressData.status === 'Download Completed') {
                            statusText = 'File downloaded successfully for ' + chargeBoxId;
                            autoDownloadedFiles.delete(chargeBoxId);

                            // 显示下载完成的提醒
                            showDownloadReminder('completed');

                            // Stop checking after successful download
                            setTimeout(() => {
                                if (downloadCheckInterval) {
                                    clearInterval(downloadCheckInterval);
                                    document.getElementById('downloadStatusText').textContent = 'All diagnostic files downloaded successfully';
                                }
                            }, 5000);
                        } else if (progressData.percentage > 0) {
                            statusText = chargeBoxId + ': ' + progressData.status + ' (' + progressData.percentage + '%)';
                        }
                    }

                    document.getElementById('downloadStatusText').textContent = statusText;
                })
                .catch(error => {
                    console.error('Error checking for downloadable files:', error);
                    document.getElementById('downloadStatusText').textContent = 'Error checking for files';
                });
        }

        function downloadFile(chargeBoxId) {
            console.log('Starting download for: ' + chargeBoxId);

            const downloadUrl = '/steve/api/diagnostics/download/' + chargeBoxId;
            const link = document.createElement('a');
            link.href = downloadUrl;
            link.download = '';
            link.style.display = 'none';

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            console.log('Download initiated for: ' + chargeBoxId);
            document.getElementById('downloadStatusText').textContent = 'Downloading file for ' + chargeBoxId + '...';

            // 显示手动下载的提醒
            showDownloadReminder('manual');
        }

        // 显示下载提醒的函数
        function showDownloadReminder(status) {
            const reminderDiv = document.querySelector('.download-reminder');
            if (!reminderDiv) return;

            let message = '';
            let bgColor = '';

            switch(status) {
                case 'downloading':
                    message = '📥 <strong>Downloading...</strong> You can download this diagnostic file multiple times within <strong>10 minutes</strong>. The task and file will be automatically cleaned up after 10 minutes to save server space.';
                    bgColor = '#e7f3ff'; // 蓝色背景
                    break;
                case 'completed':
                    message = '✅ <strong>Download Completed!</strong> You can download this diagnostic file again within the remaining time. The task and file will be automatically cleaned up after <strong>10 minutes</strong> from task creation.';
                    bgColor = '#d4edda'; // 绿色背景
                    break;
                case 'manual':
                    message = '📥 <strong>Manual Download Started!</strong> You can download this diagnostic file multiple times within <strong>10 minutes</strong>. The task and file will be automatically cleaned up after 10 minutes to save server space.';
                    bgColor = '#fff3cd'; // 黄色背景
                    break;
                default:
                    message = '📥 <strong>Download Reminder:</strong> Once the diagnostic file is ready, you can download it multiple times within <strong>10 minutes</strong>. After 10 minutes, this task and the diagnostic file will be automatically cleaned up to save server space.';
                    bgColor = '#fff3cd'; // 默认黄色背景
            }

            reminderDiv.innerHTML = message;
            reminderDiv.style.backgroundColor = bgColor;

            // 添加闪烁效果来吸引注意
            reminderDiv.style.animation = 'pulse 2s ease-in-out 3';
        }

        // 从任务结果中下载文件的新函数
        function downloadFileFromTask(chargeBoxId) {
            console.log('Attempting to download file from task result for: ' + chargeBoxId);

            // 首先尝试从任务结果表格中获取文件名
            const responseCell = document.querySelector('table.res tbody tr td:nth-child(2)');
            if (responseCell && responseCell.textContent.trim().endsWith('.zip')) {
                const fileName = responseCell.textContent.trim();
                console.log('Found file name in task result: ' + fileName);

                // 直接构造下载URL，使用文件名作为路径
                const downloadUrl = '/steve/manager/operations/download/' + fileName;
                const link = document.createElement('a');
                link.href = downloadUrl;
                link.download = fileName;
                link.style.display = 'none';

                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                console.log('Download initiated from task result for: ' + chargeBoxId);

                // 显示从任务结果下载的提醒
                showDownloadReminder('manual');

                return true;
            }

            console.log('No file name found in task result');
            return false;
        }

        // Start checking for downloadable files
        $(document).ready(function() {
            console.log('GetDiagnostics auto-download initialized');
            checkForDownloadableFiles();
            downloadCheckInterval = setInterval(checkForDownloadableFiles, 5000);

            // Stop checking after 10 minutes
            setTimeout(() => {
                if (downloadCheckInterval) {
                    clearInterval(downloadCheckInterval);
                    console.log('⏰ Auto-download monitoring stopped after 10 minutes');
                }
            }, 600000);
        });
    </script>
</c:if>



<!-- Auto download functionality for completed tasks -->
<c:if test="${task.finished && task.operationName eq 'Get Diagnostics'}">
    <script>
        $(document).ready(function() {
            console.log('GetDiagnostics task completed, checking for downloadable files...');

            // Wait a moment to ensure file is fully ready
            setTimeout(function() {
                checkAndDownloadCompletedFiles();
            }, 2000);
        });

        function checkAndDownloadCompletedFiles() {
            console.log('Checking for completed diagnostic files...');

            // 直接从任务结果表格中获取文件名
            const responseCell = document.querySelector('table.res tbody tr td:nth-child(2)');
            if (responseCell && responseCell.textContent.trim().endsWith('.zip')) {
                const fileName = responseCell.textContent.trim();
                console.log('Found file name in task result: ' + fileName);

                // Show download notification
                showDownloadNotification(fileName);

                // Auto trigger download
                setTimeout(() => {
                    downloadCompletedFile(fileName);
                }, 1000);
            } else {
                console.log('No file name found in task result');
            }
        }

        function showDownloadNotification(fileName) {
            // Create download notification
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background-color: #4CAF50;
                color: white;
                padding: 15px 20px;
                border-radius: 5px;
                box-shadow: 0 4px 8px rgba(0,0,0,0.2);
                z-index: 1000;
                font-family: Arial, sans-serif;
                font-size: 14px;
            `;
            notification.innerHTML = `
                <strong>Diagnostic File Download</strong><br>
                Downloading file: ${fileName}
            `;

            document.body.appendChild(notification);

            // Remove notification after 5 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 5000);
        }

        function downloadCompletedFile(fileName) {
            console.log('Starting download for completed task: ' + fileName);

            const downloadUrl = '/steve/manager/operations/download/' + fileName;
            const link = document.createElement('a');
            link.href = downloadUrl;
            link.download = fileName;
            link.style.display = 'none';

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            console.log('Download initiated for completed task: ' + fileName);

            // Update page display
            setTimeout(() => {
                const notification = document.createElement('div');
                notification.style.cssText = `
                    position: fixed;
                    top: 80px;
                    right: 20px;
                    background-color: #2196F3;
                    color: white;
                    padding: 15px 20px;
                    border-radius: 5px;
                    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
                    z-index: 1000;
                    font-family: Arial, sans-serif;
                    font-size: 14px;
                `;
                notification.innerHTML = `
                    <strong>Download Complete</strong><br>
                    Diagnostic file for charge point ${chargeBoxId} has started downloading
                `;

                document.body.appendChild(notification);

                // Remove notification after 8 seconds
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 8000);
            }, 500);
        }
    </script>
    <div class="info" style="background-color: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px; margin-bottom: 15px;">
        <b>Task Completed!</b> Diagnostic files will start downloading automatically. If download doesn't start automatically, please click the download button below.
        <br><br>
        <div id="downloadButtonContainer" style="text-align: center;">
            <button id="manualDownloadBtn" onclick="manualDownload()"
                    style="background-color: #4CAF50; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-size: 14px; margin-right: 10px;">
                Download Diagnostic File
            </button>
            <span id="downloadStatus" style="margin-left: 10px; color: #666;"></span>
        </div>
    </div>

    <script>
        // Manual download functionality
        function manualDownload() {
            console.log('Manual download button clicked');
            document.getElementById('downloadStatus').textContent = 'Preparing download...';
            document.getElementById('manualDownloadBtn').disabled = true;

            // Get the file name from the task result table
            const responseCell = document.querySelector('table.res tbody tr td:nth-child(2)');
            if (responseCell && responseCell.textContent.trim().endsWith('.zip')) {
                const fileName = responseCell.textContent.trim();
                console.log('Found file name in response: ' + fileName);

                // Extract chargeBoxId from the file name (format: CP006_20250722_232650.zip)
                const chargeBoxId = fileName.split('_')[0];
                console.log('Extracted chargeBoxId: ' + chargeBoxId);

                document.getElementById('downloadStatus').textContent = 'Downloading ' + fileName + '...';
                document.getElementById('downloadStatus').style.color = '#4CAF50';

                const downloadUrl = '/steve/manager/operations/download/' + fileName;
                const link = document.createElement('a');
                link.href = downloadUrl;
                link.download = fileName;
                link.style.display = 'none';

                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                setTimeout(() => {
                    document.getElementById('downloadStatus').textContent = 'Download started!';
                    document.getElementById('downloadStatus').style.color = '#4CAF50';
                    document.getElementById('manualDownloadBtn').disabled = false;
                }, 1000);

                return;
            }

            // No file name found in response
            document.getElementById('downloadStatus').textContent = 'No file available for download';
            document.getElementById('downloadStatus').style.color = '#f44336';
        }

        // Check for downloadable files when page loads, show download button if available
        $(document).ready(function() {
            setTimeout(function() {
                // 直接检查任务结果中的文件名
                const responseCell = document.querySelector('table.res tbody tr td:nth-child(2)');
                if (responseCell && responseCell.textContent.trim().endsWith('.zip')) {
                    const fileName = responseCell.textContent.trim();
                    document.getElementById('downloadStatus').textContent = 'File ready: ' + fileName;
                    document.getElementById('downloadStatus').style.color = '#4CAF50';
                    document.getElementById('manualDownloadBtn').disabled = false;
                } else {
                    document.getElementById('downloadStatus').textContent = 'No file available for download';
                    document.getElementById('downloadStatus').style.color = '#ff9800';
                    document.getElementById('manualDownloadBtn').disabled = true;
                }
            }, 1000);
        });
    </script>
</c:if>

<c:if test="${not task.finished}">
    <script>
        $(document).ready(
                setTimeout(function(){
                    window.location.reload(1);
                }, 5000));
    </script>
    <div class="info"><b>Info:</b> This page automatically reloads every 5 seconds until the task is finished.</div>
</c:if>
    <center>
        <table id='details' class='cpd'>
            <thead><tr><th>Task Details</th><th></th></tr></thead>
            <tr><td>Operation name</td><td>${task.operationName}</td></tr>
            <tr><td>Origin</td><td>${task.origin} (${task.caller})</td></tr>
            <tr><td>Start timestamp</td><td>${task.startTimestamp}</td></tr>
            <tr><td>End timestamp</td><td>${task.endTimestamp}</td></tr>
            <tr><td># of charge point requests</td><td>${task.resultMap.size()}</td></tr>
            <tr><td># of responses</td><td>${task.responseCount}</td></tr>
            <tr><td># of errors</td><td>${task.errorCount}</td></tr>
        </table>
    </center>
    <br>
    <section><span>Task Result</span></section>
    <table class="res">
        <thead><tr><th>ChargeBox ID</th><th>Response</th><th>Error</th></tr></thead>
        <tbody>
        <c:forEach items="${task.resultMap}" var="result">
            <tr>
                <td>${result.key}</td>
                <td>
                    <c:choose>
                        <c:when test="${result.value.details == null}">
                            <encode:forHtml value="${result.value.response}" />
                        </c:when>
                        <c:otherwise>
                            <encode:forHtml value="${result.value.response}" /> (<a href="${ctxPath}/manager/operations/tasks/${taskId}/details/${result.key}/">Details</a>)
                        </c:otherwise>
                    </c:choose>
                </td>
                <td>${result.value.errorMessage}</td>
            </tr>
        </c:forEach>
        </tbody>
    </table>
</div>
<%@ include file="00-footer.jsp" %>
