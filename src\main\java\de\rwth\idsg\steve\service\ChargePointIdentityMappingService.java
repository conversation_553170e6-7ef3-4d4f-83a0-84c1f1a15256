package de.rwth.idsg.steve.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 充电桩标识映射服务
 * 用于处理充电桩OCPP标识和维护标识之间的映射关系
 * 
 * 解决问题：充电桩有两个连接部分
 * 1. OCPP部分：使用 getChargeBoxIdentity() 获取标识（如CP001）
 * 2. 维护部分：使用 getDevOpsIdentity() 获取标识（如CP002）
 * 
 * 当充电桩连接使用维护标识（CP002），但文件名包含OCPP标识（CP001）时，
 * 此服务提供映射功能来正确匹配。
 */
@Slf4j
@Service
public class ChargePointIdentityMappingService {

    // OCPP标识 -> 维护标识的映射
    private final Map<String, String> ocppToDevOpsMapping = new ConcurrentHashMap<>();
    
    // 维护标识 -> OCPP标识的映射（反向映射）
    private final Map<String, String> devOpsToOcppMapping = new ConcurrentHashMap<>();

    public ChargePointIdentityMappingService() {
        // 不再初始化默认映射关系，改为动态学习
        log.info("🔧 ChargePointIdentityMappingService initialized with dynamic learning mode");
    }

    /**
     * 添加OCPP标识到维护标识的映射
     * 
     * @param ocppIdentity OCPP标识（如CP001）
     * @param devOpsIdentity 维护标识（如CP002）
     */
    public void addMapping(String ocppIdentity, String devOpsIdentity) {
        if (ocppIdentity == null || devOpsIdentity == null) {
            log.warn("⚠️ Cannot add mapping with null identity: ocpp={}, devOps={}", ocppIdentity, devOpsIdentity);
            return;
        }

        ocppToDevOpsMapping.put(ocppIdentity, devOpsIdentity);
        devOpsToOcppMapping.put(devOpsIdentity, ocppIdentity);
        
        log.info("✅ Added identity mapping: {} (OCPP) <-> {} (DevOps)", ocppIdentity, devOpsIdentity);
    }

    /**
     * 移除映射关系
     * 
     * @param ocppIdentity OCPP标识
     */
    public void removeMapping(String ocppIdentity) {
        String devOpsIdentity = ocppToDevOpsMapping.remove(ocppIdentity);
        if (devOpsIdentity != null) {
            devOpsToOcppMapping.remove(devOpsIdentity);
            log.info("🗑️ Removed identity mapping: {} <-> {}", ocppIdentity, devOpsIdentity);
        }
    }

    /**
     * 根据OCPP标识获取对应的维护标识
     * 
     * @param ocppIdentity OCPP标识（如CP001）
     * @return 对应的维护标识（如CP002），如果没有映射则返回原标识
     */
    public String getDevOpsIdentity(String ocppIdentity) {
        if (ocppIdentity == null) {
            return null;
        }

        String devOpsIdentity = ocppToDevOpsMapping.get(ocppIdentity);
        if (devOpsIdentity != null) {
            log.debug("🔄 Mapped OCPP identity {} to DevOps identity {}", ocppIdentity, devOpsIdentity);
            return devOpsIdentity;
        }

        // 如果没有映射，返回原标识
        log.debug("🔍 No mapping found for OCPP identity {}, returning original", ocppIdentity);
        return ocppIdentity;
    }

    /**
     * 根据维护标识获取对应的OCPP标识
     * 
     * @param devOpsIdentity 维护标识（如CP002）
     * @return 对应的OCPP标识（如CP001），如果没有映射则返回原标识
     */
    public String getOcppIdentity(String devOpsIdentity) {
        if (devOpsIdentity == null) {
            return null;
        }

        String ocppIdentity = devOpsToOcppMapping.get(devOpsIdentity);
        if (ocppIdentity != null) {
            log.debug("🔄 Mapped DevOps identity {} to OCPP identity {}", devOpsIdentity, ocppIdentity);
            return ocppIdentity;
        }

        // 如果没有映射，返回原标识
        log.debug("🔍 No mapping found for DevOps identity {}, returning original", devOpsIdentity);
        return devOpsIdentity;
    }

    /**
     * 检查是否存在指定OCPP标识的映射
     *
     * @param ocppIdentity OCPP标识
     * @return 如果存在映射返回true，否则返回false
     */
    public boolean hasOcppMapping(String ocppIdentity) {
        if (ocppIdentity == null) {
            return false;
        }
        return ocppToDevOpsMapping.containsKey(ocppIdentity);
    }

    /**
     * 检查是否存在指定维护标识的映射
     *
     * @param devOpsIdentity 维护标识
     * @return 如果存在映射返回true，否则返回false
     */
    public boolean hasDevOpsMapping(String devOpsIdentity) {
        if (devOpsIdentity == null) {
            return false;
        }
        return devOpsToOcppMapping.containsKey(devOpsIdentity);
    }

    /**
     * 智能解析充电桩标识
     * 动态学习和处理标识不匹配问题，无需预配置映射关系
     *
     * @param fileName 文件名或标识字符串
     * @param connectedChargeBoxId 当前连接的充电桩ID（维护标识）
     * @return 解析后的正确标识
     */
    public String resolveChargePointIdentity(String fileName, String connectedChargeBoxId) {
        if (fileName == null || connectedChargeBoxId == null) {
            return connectedChargeBoxId;
        }

        log.info("🔍 Resolving charge point identity from file: {}, connected as: {}", fileName, connectedChargeBoxId);

        // 1. 首先检查文件名中是否包含连接的充电桩ID
        if (fileName.contains(connectedChargeBoxId)) {
            log.info("✅ File name contains connected charge box ID: {}", connectedChargeBoxId);
            return connectedChargeBoxId;
        }

        // 2. 从文件名中提取可能的充电桩标识
        String extractedId = extractChargePointIdFromFileName(fileName);
        if (extractedId != null && !extractedId.equals(connectedChargeBoxId)) {
            log.info("🔍 Extracted ID {} from file name, but connected as {}", extractedId, connectedChargeBoxId);

            // 3. 检查是否已有映射关系
            if (ocppToDevOpsMapping.containsKey(extractedId)) {
                String mappedId = ocppToDevOpsMapping.get(extractedId);
                log.info("✅ Found existing mapping: {} -> {}", extractedId, mappedId);
                return mappedId;
            }

            // 4. 动态学习：创建新的映射关系
            log.info("🎓 Learning new mapping: {} (from file) -> {} (connected)", extractedId, connectedChargeBoxId);
            addMapping(extractedId, connectedChargeBoxId);
            return connectedChargeBoxId;
        }

        // 5. 检查已有的映射关系
        for (String ocppId : ocppToDevOpsMapping.keySet()) {
            if (fileName.contains(ocppId)) {
                String devOpsId = ocppToDevOpsMapping.get(ocppId);
                log.info("✅ Found OCPP identity {} in file name, mapped to DevOps identity {}", ocppId, devOpsId);
                return devOpsId;
            }
        }

        // 6. 如果都没有匹配，返回连接的充电桩ID
        log.info("⚠️ No identity match found in file name, using connected charge box ID: {}", connectedChargeBoxId);
        return connectedChargeBoxId;
    }

    /**
     * 从文件名中提取充电桩标识
     * 支持多种常见的充电桩标识格式
     *
     * @param fileName 文件名
     * @return 提取的充电桩标识，如果无法提取则返回null
     */
    private String extractChargePointIdFromFileName(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return null;
        }

        // 移除文件扩展名
        String nameWithoutExt = fileName.replaceAll("\\.[^.]+$", "");

        // 1. 匹配 CP + 数字格式 (如 CP001, CP002)
        java.util.regex.Pattern cpPattern = java.util.regex.Pattern.compile("(CP\\d{3,})");
        java.util.regex.Matcher cpMatcher = cpPattern.matcher(nameWithoutExt);
        if (cpMatcher.find()) {
            String cpId = cpMatcher.group(1);
            log.debug("🔍 Extracted CP format ID: {}", cpId);
            return cpId;
        }

        // 2. 匹配 MAC 地址格式 (如 A25A58A962D9)
        java.util.regex.Pattern macPattern = java.util.regex.Pattern.compile("([A-F0-9]{12})");
        java.util.regex.Matcher macMatcher = macPattern.matcher(nameWithoutExt.toUpperCase());
        if (macMatcher.find()) {
            String macId = macMatcher.group(1);
            log.debug("🔍 Extracted MAC format ID: {}", macId);
            return macId;
        }

        // 3. 匹配其他可能的格式 (如 CHARGER001, STATION_01)
        java.util.regex.Pattern generalPattern = java.util.regex.Pattern.compile("(CHARGER\\d+|STATION[_-]?\\d+|BOX\\d+)");
        java.util.regex.Matcher generalMatcher = generalPattern.matcher(nameWithoutExt.toUpperCase());
        if (generalMatcher.find()) {
            String generalId = generalMatcher.group(1);
            log.debug("🔍 Extracted general format ID: {}", generalId);
            return generalId;
        }

        // 4. 尝试从文件名开头提取标识（假设标识在文件名开头）
        String[] parts = nameWithoutExt.split("[_-]");
        if (parts.length > 0 && parts[0].length() >= 3) {
            String firstPart = parts[0];
            // 检查是否看起来像充电桩标识
            if (firstPart.matches(".*\\d.*") || firstPart.length() <= 10) {
                log.debug("🔍 Extracted first part as ID: {}", firstPart);
                return firstPart;
            }
        }

        log.debug("🔍 Could not extract charge point ID from file name: {}", fileName);
        return null;
    }

    /**
     * 获取所有映射关系
     * 
     * @return OCPP标识到维护标识的映射表
     */
    public Map<String, String> getAllMappings() {
        return new ConcurrentHashMap<>(ocppToDevOpsMapping);
    }

    /**
     * 清除所有映射关系
     */
    public void clearAllMappings() {
        int count = ocppToDevOpsMapping.size();
        ocppToDevOpsMapping.clear();
        devOpsToOcppMapping.clear();
        log.info("🧹 Cleared all identity mappings ({} mappings removed)", count);
    }

    /**
     * 获取映射统计信息
     * 
     * @return 映射数量
     */
    public int getMappingCount() {
        return ocppToDevOpsMapping.size();
    }

    /**
     * 批量添加映射关系
     * 
     * @param mappings OCPP标识到维护标识的映射表
     */
    public void addMappings(Map<String, String> mappings) {
        if (mappings == null || mappings.isEmpty()) {
            return;
        }

        for (Map.Entry<String, String> entry : mappings.entrySet()) {
            addMapping(entry.getKey(), entry.getValue());
        }

        log.info("✅ Batch added {} identity mappings", mappings.size());
    }
}
