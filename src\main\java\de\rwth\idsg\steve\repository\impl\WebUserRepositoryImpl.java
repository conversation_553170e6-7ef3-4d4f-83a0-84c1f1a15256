/*
 * SteV<PERSON> - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.repository.impl;

import de.rwth.idsg.steve.repository.WebUserRepository;
import de.rwth.idsg.steve.web.dto.UserRole;
import jooq.steve.db.tables.records.WebUserRecord;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.jooq.JSON;
import org.springframework.stereotype.Repository;

import java.util.List;

import static jooq.steve.db.Tables.WEB_USER;
import static org.jooq.impl.DSL.condition;
import static org.jooq.impl.DSL.count;

/**
 * <AUTHOR> Goekay <<EMAIL>>
 * @since 10.08.2024
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class WebUserRepositoryImpl implements WebUserRepository {

    private final DSLContext ctx;

    /**
     * 将我们的UserRole枚举转换为JOOQ生成的WebUserUserRole枚举
     */
    private jooq.steve.db.enums.WebUserUserRole convertUserRole(UserRole role) {
        return jooq.steve.db.enums.WebUserUserRole.valueOf(role.name());
    }

    @Override
    public void createUser(WebUserRecord user) {
        ctx.insertInto(WEB_USER)
            .set(WEB_USER.USERNAME, user.getUsername())
            .set(WEB_USER.PASSWORD, user.getPassword())
            .set(WEB_USER.API_PASSWORD, user.getApiPassword())
            .set(WEB_USER.ENABLED, user.getEnabled())
            .set(WEB_USER.USER_ROLE, user.getUserRole())
            .execute();
    }

    @Override
    public void updateUser(WebUserRecord user) {
        ctx.update(WEB_USER)
            .set(WEB_USER.PASSWORD, user.getPassword())
            .set(WEB_USER.API_PASSWORD, user.getApiPassword())
            .set(WEB_USER.ENABLED, user.getEnabled())
            .set(WEB_USER.USER_ROLE, user.getUserRole())
            .where(WEB_USER.USERNAME.eq(user.getUsername()))
            .execute();
    }

    @Override
    public void deleteUser(String username) {
        ctx.delete(WEB_USER)
            .where(WEB_USER.USERNAME.eq(username))
            .execute();
    }

    @Override
    public void deleteUser(int webUserPk) {
        ctx.delete(WEB_USER)
            .where(WEB_USER.WEB_USER_PK.eq(webUserPk))
            .execute();
    }

    @Override
    public void changeStatusOfUser(String username, boolean enabled) {
        ctx.update(WEB_USER)
            .set(WEB_USER.ENABLED, enabled)
            .where(WEB_USER.USERNAME.eq(username))
            .execute();
    }

    @Override
    public Integer getUserCountWithRole(UserRole role) {
        return ctx.selectCount()
            .from(WEB_USER)
            .where(WEB_USER.USER_ROLE.eq(convertUserRole(role)))
            .fetchOne(count());
    }

    @Override
    public void changePassword(String username, String newPassword) {
        ctx.update(WEB_USER)
            .set(WEB_USER.PASSWORD, newPassword)
            .where(WEB_USER.USERNAME.eq(username))
            .execute();
    }

    @Override
    public boolean userExists(String username) {
        return ctx.selectOne()
            .from(WEB_USER)
            .where(WEB_USER.USERNAME.eq(username))
            .fetchOptional()
            .isPresent();
    }

    @Override
    public WebUserRecord loadUserByUsername(String username) {
        return ctx.selectFrom(WEB_USER)
            .where(WEB_USER.USERNAME.eq(username))
            .fetchOne();
    }

    @Override
    public List<WebUserRecord> getUsersByRole(UserRole role) {
        return ctx.selectFrom(WEB_USER)
            .where(WEB_USER.USER_ROLE.eq(convertUserRole(role)))
            .fetch();
    }

    @Override
    public void updateUserRole(String username, UserRole role) {
        ctx.update(WEB_USER)
            .set(WEB_USER.USER_ROLE, convertUserRole(role))
            .where(WEB_USER.USERNAME.eq(username))
            .execute();
    }

    @Override
    public void updateUserRole(int webUserPk, UserRole role) {
        ctx.update(WEB_USER)
            .set(WEB_USER.USER_ROLE, convertUserRole(role))
            .where(WEB_USER.WEB_USER_PK.eq(webUserPk))
            .execute();
    }

    @Override
    public UserRole getUserRole(String username) {
        String role = ctx.select(WEB_USER.USER_ROLE)
            .from(WEB_USER)
            .where(WEB_USER.USERNAME.eq(username))
            .fetchOneInto(String.class);
        
        return role != null ? UserRole.valueOf(role) : null;
    }

    @Override
    public UserRole getUserRole(int webUserPk) {
        String role = ctx.select(WEB_USER.USER_ROLE)
            .from(WEB_USER)
            .where(WEB_USER.WEB_USER_PK.eq(webUserPk))
            .fetchOneInto(String.class);
        
        return role != null ? UserRole.valueOf(role) : null;
    }

    @Override
    public WebUserRecord loadUserByPk(int webUserPk) {
        try {
            log.debug("Loading user by PK: {}", webUserPk);
            WebUserRecord record = ctx.selectFrom(WEB_USER)
                .where(WEB_USER.WEB_USER_PK.eq(webUserPk))
                .fetchOne();
            
            if (record != null) {
                log.debug("User found with PK {}: username={}, role={}", 
                         webUserPk, record.getUsername(), record.getUserRole());
            } else {
                log.warn("No user found with PK: {}", webUserPk);
            }
            
            return record;
        } catch (Exception e) {
            log.error("Error loading user by PK {}: {}", webUserPk, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<WebUserRecord> findWebUsersByNamePattern(String namePattern) {
        try {
            log.debug("Finding web users with name pattern: {}", namePattern);
            
            // 由于web_user表中没有与user表相关的字段，我们先返回所有的web_user记录
            // 上层应用可以根据其他信息（如用户名或关联字段）进行筛选
            List<WebUserRecord> records = ctx.selectFrom(WEB_USER)
                .fetch();
            
            log.debug("Found {} web users in total", records.size());
            return records;
        } catch (Exception e) {
            log.error("Error finding web users by name pattern {}: {}", namePattern, e.getMessage(), e);
            return List.of(); // 返回空列表而不是抛出异常
        }
    }

    @Override
    public List<WebUserRecord> getAllUsers() {
        try {
            log.debug("获取所有web用户");
            return ctx.selectFrom(WEB_USER).fetch();
        } catch (Exception e) {
            log.error("获取所有web用户时出错: {}", e.getMessage(), e);
            return List.of(); // 返回空列表而不是抛出异常
        }
    }

    @Override
    public void addUser(String username, String encodedPassword, UserRole userRole) {
        try {
            log.debug("Adding new web user: {}, role: {}", username, userRole);
            
            ctx.insertInto(WEB_USER)
                .set(WEB_USER.USERNAME, username)
                .set(WEB_USER.PASSWORD, encodedPassword)
                .set(WEB_USER.ENABLED, true)
                .set(WEB_USER.USER_ROLE, convertUserRole(userRole))
                .execute();
            
            log.info("Web user added successfully: {}", username);
        } catch (Exception e) {
            log.error("Error adding web user {}: {}", username, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public void updateUserPassword(String username, String encodedPassword) {
        try {
            log.debug("Updating password for web user: {}", username);
            
            int count = ctx.update(WEB_USER)
                .set(WEB_USER.PASSWORD, encodedPassword)
                .where(WEB_USER.USERNAME.eq(username))
                .execute();
                
            if (count > 0) {
                log.info("Password updated successfully for web user: {}", username);
            } else {
                log.warn("No web user found with username: {}", username);
            }
        } catch (Exception e) {
            log.error("Error updating password for web user {}: {}", username, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 根据用户名获取web_user主键
     *
     * @param username 用户名
     * @return web_user主键，如果未找到则返回null
     */
    public Integer getUserPkByUsername(String username) {
        try {
            log.debug("Getting web_user PK for username: {}", username);

            Integer webUserPk = ctx.select(WEB_USER.WEB_USER_PK)
                .from(WEB_USER)
                .where(WEB_USER.USERNAME.eq(username))
                .fetchOneInto(Integer.class);

            if (webUserPk != null) {
                log.debug("Found web_user PK {} for username: {}", webUserPk, username);
            } else {
                log.warn("No web_user found with username: {}", username);
            }

            return webUserPk;
        } catch (Exception e) {
            log.error("Error getting web_user PK for username {}: {}", username, e.getMessage(), e);
            return null;
        }
    }

    // ========== 新增：user_pk关联相关方法的实现 ==========

    @Override
    public WebUserRecord findByUserPk(int userPk) {
        try {
            log.debug("Finding web_user by user_pk: {}", userPk);

            WebUserRecord record = ctx.selectFrom(WEB_USER)
                .where(WEB_USER.USER_PK.eq(userPk))
                .fetchOne();

            if (record != null) {
                log.debug("Found web_user: {} for user_pk: {}", record.getUsername(), userPk);
            } else {
                log.debug("No web_user found for user_pk: {}", userPk);
            }

            return record;
        } catch (Exception e) {
            log.error("Error finding web_user by user_pk {}: {}", userPk, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public Integer getAssociatedUserPk(int webUserPk) {
        try {
            log.debug("Getting associated user_pk for web_user_pk: {}", webUserPk);

            Integer userPk = ctx.select(WEB_USER.USER_PK)
                .from(WEB_USER)
                .where(WEB_USER.WEB_USER_PK.eq(webUserPk))
                .fetchOneInto(Integer.class);

            if (userPk != null) {
                log.debug("Found associated user_pk {} for web_user_pk: {}", userPk, webUserPk);
            } else {
                log.debug("No associated user_pk found for web_user_pk: {}", webUserPk);
            }

            return userPk;
        } catch (Exception e) {
            log.error("Error getting associated user_pk for web_user_pk {}: {}", webUserPk, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public void associateWithUser(int webUserPk, int userPk) {
        try {
            log.debug("Associating web_user_pk {} with user_pk {}", webUserPk, userPk);

            int count = ctx.update(WEB_USER)
                .set(WEB_USER.USER_PK, userPk)
                .where(WEB_USER.WEB_USER_PK.eq(webUserPk))
                .execute();

            if (count > 0) {
                log.info("Successfully associated web_user_pk {} with user_pk {}", webUserPk, userPk);
            } else {
                log.warn("No web_user found with web_user_pk: {}", webUserPk);
            }
        } catch (Exception e) {
            log.error("Error associating web_user_pk {} with user_pk {}: {}", webUserPk, userPk, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public void disassociateFromUser(int webUserPk) {
        try {
            log.debug("Disassociating web_user_pk {} from user", webUserPk);

            int count = ctx.update(WEB_USER)
                .setNull(WEB_USER.USER_PK)
                .where(WEB_USER.WEB_USER_PK.eq(webUserPk))
                .execute();

            if (count > 0) {
                log.info("Successfully disassociated web_user_pk {} from user", webUserPk);
            } else {
                log.warn("No web_user found with web_user_pk: {}", webUserPk);
            }
        } catch (Exception e) {
            log.error("Error disassociating web_user_pk {} from user: {}", webUserPk, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public void disassociateByUserPk(int userPk) {
        try {
            log.debug("Disassociating all web_users from user_pk: {}", userPk);

            int count = ctx.update(WEB_USER)
                .setNull(WEB_USER.USER_PK)
                .where(WEB_USER.USER_PK.eq(userPk))
                .execute();

            if (count > 0) {
                log.info("Successfully disassociated {} web_user(s) from user_pk: {}", count, userPk);
            } else {
                log.debug("No web_users were associated with user_pk: {}", userPk);
            }
        } catch (Exception e) {
            log.error("Error disassociating web_users from user_pk {}: {}", userPk, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<WebUserRecord> getAllUsersWithAssociation() {
        try {
            log.debug("Getting all web_users with user association");

            List<WebUserRecord> records = ctx.selectFrom(WEB_USER)
                .where(WEB_USER.USER_PK.isNotNull())
                .fetch();

            log.debug("Found {} web_users with user association", records.size());
            return records;
        } catch (Exception e) {
            log.error("Error getting web_users with association: {}", e.getMessage(), e);
            return List.of();
        }
    }

    @Override
    public List<WebUserRecord> getAllUsersWithoutAssociation() {
        try {
            log.debug("Getting all web_users without user association");

            List<WebUserRecord> records = ctx.selectFrom(WEB_USER)
                .where(WEB_USER.USER_PK.isNull())
                .fetch();

            log.debug("Found {} web_users without user association", records.size());
            return records;
        } catch (Exception e) {
            log.error("Error getting web_users without association: {}", e.getMessage(), e);
            return List.of();
        }
    }
}