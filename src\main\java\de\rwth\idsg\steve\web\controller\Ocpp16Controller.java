/*
 * SteV<PERSON> - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.web.controller;

import de.rwth.idsg.steve.SteveConfiguration;
import de.rwth.idsg.steve.ocpp.OcppVersion;
import de.rwth.idsg.steve.repository.ChargingProfileRepository;
import de.rwth.idsg.steve.service.DiagnosticsProgressTracker;
import de.rwth.idsg.steve.service.DiagnosticsTaskManager;
import de.rwth.idsg.steve.service.DiagnosticsTaskInfo;
import de.rwth.idsg.steve.web.dto.ocpp.ChangeConfigurationParams;
import de.rwth.idsg.steve.web.dto.ocpp.ClearChargingProfileParams;
import de.rwth.idsg.steve.web.dto.ocpp.ConfigurationKeyEnum;
import de.rwth.idsg.steve.web.dto.ocpp.ConfigurationKeyReadWriteEnum;
import de.rwth.idsg.steve.web.dto.ocpp.GetCompositeScheduleParams;
import de.rwth.idsg.steve.web.dto.ocpp.GetConfigurationParams;
import de.rwth.idsg.steve.web.dto.ocpp.GetDiagnosticsParams;
import de.rwth.idsg.steve.web.dto.ocpp.SetChargingProfileParams;
import de.rwth.idsg.steve.web.dto.ocpp.TriggerMessageParams;
import ocpp.cs._2015._10.RegistrationStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.Arrays;
import java.util.Collections;
import java.util.Enumeration;
import java.util.List;
import java.util.Map;

import static de.rwth.idsg.steve.web.dto.ocpp.ConfigurationKeyReadWriteEnum.R;
import static de.rwth.idsg.steve.web.dto.ocpp.ConfigurationKeyReadWriteEnum.RW;

/**
 * <AUTHOR> Goekay <<EMAIL>>
 * @since 15.03.2018
 */
@Slf4j
@Controller
@RequestMapping(value = "/manager/operations/v1.6")
@PreAuthorize("hasAuthority('ADMIN')")
public class Ocpp16Controller extends Ocpp15Controller {

    @Autowired private ChargingProfileRepository chargingProfileRepository;
    @Autowired private DiagnosticsProgressTracker progressTracker;
    @Autowired private DiagnosticsTaskManager diagnosticsTaskManager;

    // -------------------------------------------------------------------------
    // Paths
    // -------------------------------------------------------------------------

    private static final String GET_COMPOSITE_PATH = "/GetCompositeSchedule";
    private static final String CLEAR_CHARGING_PATH = "/ClearChargingProfile";
    private static final String SET_CHARGING_PATH = "/SetChargingProfile";
    private static final String TRIGGER_MESSAGE_PATH = "/TriggerMessage";
    private static final String GET_DIAG_PATH = "/GetDiagnostics";

    // -------------------------------------------------------------------------
    // Helpers
    // -------------------------------------------------------------------------

    @Override
    protected void setCommonAttributesForTx(Model model) {
        model.addAttribute("cpList", chargePointHelperService.getChargePoints(OcppVersion.V_16));
        model.addAttribute("opVersion", "v1.6");
    }

    /**
     * From OCPP 1.6 spec: "While in pending state, the following Central
     * System initiated messages are not allowed: RemoteStartTransaction.req
     * and RemoteStopTransaction.req"
     *
     * Conversely, it means all other operations are allowed for pending state.
     */
    @Override
    protected void setCommonAttributes(Model model) {
        List<RegistrationStatus> inStatusFilter = Arrays.asList(RegistrationStatus.ACCEPTED, RegistrationStatus.PENDING);
        model.addAttribute("cpList", chargePointHelperService.getChargePoints(OcppVersion.V_16, inStatusFilter));
        model.addAttribute("opVersion", "v1.6");
    }

    @Override
    protected Map<String, String> getConfigurationKeys(ConfigurationKeyReadWriteEnum confEnum) {
        switch (confEnum) {
            case R:
                return ConfigurationKeyEnum.OCPP_16_MAP_R;
            case RW:
                return ConfigurationKeyEnum.OCPP_16_MAP_RW;
            default:
                return Collections.emptyMap();
        }
    }

    @Override
    protected String getRedirectPath() {
        return "redirect:/manager/operations/v1.6/ChangeAvailability";
    }

    @Override
    protected String getPrefix() {
        return "op16";
    }

    // -------------------------------------------------------------------------
    // Override method from parent class
    // -------------------------------------------------------------------------

    @Override
    @RequestMapping(method = RequestMethod.GET)
    public String getBase() {
        return getRedirectPath();
    }

    // -------------------------------------------------------------------------
    // Old Http methods with changed logic
    // -------------------------------------------------------------------------

    @RequestMapping(value = GET_CONF_PATH, method = RequestMethod.GET)
    public String getGetConf(Model model) {
        setCommonAttributes(model);
        model.addAttribute(PARAMS, new GetConfigurationParams());
        model.addAttribute("ocppConfKeys", getConfigurationKeys(R));
        return getPrefix() + GET_CONF_PATH;
    }

    @RequestMapping(value = CHANGE_CONF_PATH, method = RequestMethod.GET)
    public String getChangeConf(Model model) {
        setCommonAttributes(model);
        model.addAttribute(PARAMS, new ChangeConfigurationParams());
        model.addAttribute("ocppConfKeys", getConfigurationKeys(RW));
        return getPrefix() + CHANGE_CONF_PATH;
    }

    @RequestMapping(value = GET_CONF_PATH, method = RequestMethod.POST)
    public String postGetConf(@Valid @ModelAttribute(PARAMS) GetConfigurationParams params,
                              BindingResult result, Model model) {
        if (result.hasErrors()) {
            setCommonAttributes(model);
            model.addAttribute("ocppConfKeys", getConfigurationKeys(R));
            return getPrefix() + GET_CONF_PATH;
        }
        return REDIRECT_TASKS_PATH + chargePointServiceClient.getConfiguration(params);
    }

    // -------------------------------------------------------------------------
    // New Http methods (GET)
    // -------------------------------------------------------------------------

    @RequestMapping(value = GET_COMPOSITE_PATH, method = RequestMethod.GET)
    public String getGetCompositeSchedule(Model model) {
        setCommonAttributes(model);
        model.addAttribute(PARAMS, new GetCompositeScheduleParams());
        return getPrefix() + GET_COMPOSITE_PATH;
    }

    @RequestMapping(value = CLEAR_CHARGING_PATH, method = RequestMethod.GET)
    public String getClearChargingProfile(Model model) {
        setCommonAttributes(model);
        model.addAttribute("profileList", chargingProfileRepository.getBasicInfo());
        model.addAttribute(PARAMS, new ClearChargingProfileParams());
        return getPrefix() + CLEAR_CHARGING_PATH;
    }

    @RequestMapping(value = SET_CHARGING_PATH, method = RequestMethod.GET)
    public String getSetChargingProfile(Model model) {
        setCommonAttributes(model);
        model.addAttribute("profileList", chargingProfileRepository.getBasicInfo());
        model.addAttribute(PARAMS, new SetChargingProfileParams());
        return getPrefix() + SET_CHARGING_PATH;
    }

    @RequestMapping(value = TRIGGER_MESSAGE_PATH, method = RequestMethod.GET)
    public String getTriggerMessage(Model model) {
        setCommonAttributes(model);
        model.addAttribute(PARAMS, new TriggerMessageParams());
        return getPrefix() + TRIGGER_MESSAGE_PATH;
    }

    @RequestMapping(value = GET_DIAG_PATH, method = RequestMethod.GET)
    public String getGetDiag(Model model) {
        setCommonAttributes(model);
        model.addAttribute(PARAMS, new GetDiagnosticsParams());

        // 添加FTP服务器信息到模型中
        if (SteveConfiguration.CONFIG.isFtpEnabled()) {
            String ftpInfo = String.format("ftp://%s@%s:%d/logs/",
                SteveConfiguration.CONFIG.getFtpUsername(),
                SteveConfiguration.CONFIG.getFtpIp(),
                SteveConfiguration.CONFIG.getFtpPort());
            model.addAttribute("ftpServerInfo", ftpInfo);
        } else {
            model.addAttribute("ftpServerInfo", "FTP server is not enabled");
        }

        return getPrefix() + GET_DIAG_PATH;
    }

    @Override
    @RequestMapping(value = GET_DIAG_PATH, method = RequestMethod.POST)
    public String postGetDiag(@Valid @ModelAttribute(PARAMS) GetDiagnosticsParams params,
                              BindingResult result, Model model, HttpServletRequest request) {

        // 调试：打印所有请求参数
        log.info("=== OCPP 1.6 GetDiagnostics Form Submission Debug ===");
        log.info("Request parameters:");
        request.getParameterMap().forEach((key, values) -> {
            log.info("  {}: {}", key, java.util.Arrays.toString(values));
        });

        // 手动处理充电桩选择（修复Spring绑定问题）
        String[] chargePointParams = request.getParameterValues("chargePointSelectList");
        if (chargePointParams != null && chargePointParams.length > 0) {
            log.info("Found chargePointSelectList parameters: {}", java.util.Arrays.toString(chargePointParams));
            java.util.List<de.rwth.idsg.steve.repository.dto.ChargePointSelect> chargePoints = new java.util.ArrayList<>();
            for (String param : chargePointParams) {
                if (param != null && !param.trim().isEmpty()) {
                    String[] parts = param.split(";");
                    if (parts.length >= 3) {
                        de.rwth.idsg.steve.repository.dto.ChargePointSelect cp =
                            new de.rwth.idsg.steve.repository.dto.ChargePointSelect(
                                de.rwth.idsg.steve.ocpp.OcppProtocol.valueOf(parts[0]),
                                parts[1],
                                parts[2]
                            );
                        chargePoints.add(cp);
                        log.info("Added charge point: {}", parts[1]);
                    }
                }
            }
            params.setChargePointSelectList(chargePoints);
            log.info("Manually set chargePointSelectList size: {}", chargePoints.size());
        }

        // 处理日期范围：将日期转换为日期时间范围
        org.joda.time.LocalDate today = org.joda.time.LocalDate.now();

        if (params.getStartDate() == null) {
            params.setStartDate(today.minusDays(1));
            log.info("Set default start date to yesterday: {}", params.getStartDate());
        }

        if (params.getStopDate() == null) {
            params.setStopDate(today.minusDays(1));
            log.info("Set default stop date to yesterday: {}", params.getStopDate());
        }

        // 将日期转换为日期时间范围
        params.setStart(params.getStartDate().toLocalDateTime(org.joda.time.LocalTime.MIDNIGHT));
        params.setStop(params.getStopDate().toLocalDateTime(new org.joda.time.LocalTime(23, 59, 59)));

        log.info("GetDiagnostics date range: {} to {} (converted to time range: {} to {})",
                params.getStartDate(), params.getStopDate(), params.getStart(), params.getStop());

        // 自动生成FTP Location路径
        try {
            String ftpLocation = generateDiagnosticsFtpLocation();
            params.setLocation(ftpLocation);
            log.info("Auto-generated GetDiagnostics FTP location: {}", ftpLocation);
        } catch (Exception e) {
            log.error("Failed to generate FTP location for GetDiagnostics: {}", e.getMessage());
            result.rejectValue("location", "error.ftp.location", "Failed to generate FTP path: " + e.getMessage());
        }

        // 检查充电桩选择
        if (params.getChargePointSelectList() == null || params.getChargePointSelectList().isEmpty()) {
            log.warn("No charge points selected for GetDiagnostics operation");
            result.rejectValue("chargePointSelectList", "error.chargepoint.required", "Please select at least one charge point");
        } else {
            log.info("Selected {} charge points for GetDiagnostics: {}",
                    params.getChargePointSelectList().size(),
                    params.getChargePointSelectList().stream()
                            .map(cp -> cp.getChargeBoxId())
                            .toList());
        }

        if (result.hasErrors()) {
            log.warn("GetDiagnostics form has validation errors: {}", result.getAllErrors());
            setCommonAttributes(model);
            // 重新添加FTP服务器信息
            if (SteveConfiguration.CONFIG.isFtpEnabled()) {
                String ftpInfo = String.format("ftp://%s@%s:%d/logs/",
                    SteveConfiguration.CONFIG.getFtpUsername(),
                    SteveConfiguration.CONFIG.getFtpIp(),
                    SteveConfiguration.CONFIG.getFtpPort());
                model.addAttribute("ftpServerInfo", ftpInfo);
            } else {
                model.addAttribute("ftpServerInfo", "FTP server is not enabled");
            }
            return getPrefix() + GET_DIAG_PATH;
        }

        // 检查是否有单个充电桩的请求，如果是，尝试复用现有任务
        log.info("🔧 Ocpp16Controller.postGetDiag: checking for task reuse, chargePointCount={}",
                params.getChargePointSelectList().size());

        if (params.getChargePointSelectList().size() == 1) {
            String chargeBoxId = params.getChargePointSelectList().get(0).getChargeBoxId();
            log.info("🔧 Single charge point request for: {}", chargeBoxId);

            DiagnosticsTaskInfo existingTask = diagnosticsTaskManager.getValidTask(chargeBoxId);

            if (existingTask != null) {
                log.info("🔄 Reusing existing diagnostics task for {}: taskId={}, remaining={}min",
                        chargeBoxId, existingTask.getTaskId(), existingTask.getRemainingMinutes());

                String reuseMessage = String.format(
                    "Found existing diagnostics task for charge point %s. Task ID: %d. " +
                    "Remaining time: %d minutes. Redirecting to existing task.",
                    chargeBoxId, existingTask.getTaskId(), existingTask.getRemainingMinutes());

                request.getSession().setAttribute("successMessage", reuseMessage);

                String redirectPath = "redirect:/manager/operations/tasks/" + existingTask.getTaskId();
                log.info("Redirecting to existing task: {}", redirectPath);
                return redirectPath;
            } else {
                log.info("🔧 No existing valid task found for {}, will create new task", chargeBoxId);
            }
        } else {
            log.info("🔧 Multiple charge points selected, skipping task reuse logic");
        }

        log.info("Calling chargePointServiceClient.getDiagnostics with params: location={}, chargePoints={}",
                params.getLocation(),
                params.getChargePointSelectList().stream()
                        .map(cp -> cp.getChargeBoxId())
                        .toList());

        // 启动进度跟踪
        for (de.rwth.idsg.steve.repository.dto.ChargePointSelect cp : params.getChargePointSelectList()) {
            progressTracker.startTracking(cp.getChargeBoxId());
            // 模拟进度更新（在实际实现中，这应该由OCPP消息处理器调用）
            progressTracker.simulateProgress(cp.getChargeBoxId());
        }

        int taskId = chargePointServiceClient.getDiagnostics(params);
        log.info("GetDiagnostics task created with ID: {}", taskId);

        // 注册新任务到任务管理器（仅对单个充电桩）
        if (params.getChargePointSelectList().size() == 1) {
            String chargeBoxId = params.getChargePointSelectList().get(0).getChargeBoxId();
            log.info("🔧 Registering new task for {}: taskId={}", chargeBoxId, taskId);
            diagnosticsTaskManager.registerTask(chargeBoxId, taskId);
        } else {
            log.info("🔧 Multiple charge points, not registering task in manager");
        }

        // 添加成功消息到session
        String successMessage = String.format(
            "GetDiagnostics request sent successfully to %d charge point(s). Task ID: %d. " +
            "Diagnostic files will be uploaded to the FTP server. You can monitor progress in real-time.",
            params.getChargePointSelectList().size(), taskId);

        // 使用session传递成功消息
        request.getSession().setAttribute("successMessage", successMessage);
        log.info("Success: {}", successMessage);

        // 正确的重定向路径 - 使用任务详情页面
        String redirectPath = "redirect:/manager/operations/tasks/" + taskId;
        log.info("Redirecting to task details page: {}", redirectPath);

        return redirectPath;
    }

    // -------------------------------------------------------------------------
    // Http methods (POST)
    // -------------------------------------------------------------------------

    @RequestMapping(value = TRIGGER_MESSAGE_PATH, method = RequestMethod.POST)
    public String postTriggerMessage(@Valid @ModelAttribute(PARAMS) TriggerMessageParams params,
                                     BindingResult result, Model model) {
        if (result.hasErrors()) {
            setCommonAttributes(model);
            return getPrefix() + TRIGGER_MESSAGE_PATH;
        }
        return REDIRECT_TASKS_PATH + chargePointServiceClient.triggerMessage(params);
    }

    @RequestMapping(value = SET_CHARGING_PATH, method = RequestMethod.POST)
    public String postSetChargingProfile(@Valid @ModelAttribute(PARAMS) SetChargingProfileParams params,
                                         BindingResult result, Model model) {
        if (result.hasErrors()) {
            setCommonAttributes(model);
            return getPrefix() + SET_CHARGING_PATH;
        }
        return REDIRECT_TASKS_PATH + chargePointServiceClient.setChargingProfile(params);
    }

    @RequestMapping(value = CLEAR_CHARGING_PATH, method = RequestMethod.POST)
    public String postClearChargingProfile(@Valid @ModelAttribute(PARAMS) ClearChargingProfileParams params,
                                           BindingResult result, Model model) {
        if (result.hasErrors()) {
            setCommonAttributes(model);
            return getPrefix() + CLEAR_CHARGING_PATH;
        }
        return REDIRECT_TASKS_PATH + chargePointServiceClient.clearChargingProfile(params);
    }

    @RequestMapping(value = GET_COMPOSITE_PATH, method = RequestMethod.POST)
    public String postGetCompositeSchedule(@Valid @ModelAttribute(PARAMS) GetCompositeScheduleParams params,
                                           BindingResult result, Model model) {
        if (result.hasErrors()) {
            setCommonAttributes(model);
            return getPrefix() + GET_COMPOSITE_PATH;
        }
        return REDIRECT_TASKS_PATH + chargePointServiceClient.getCompositeSchedule(params);
    }

    /**
     * 生成GetDiagnostics的FTP Location路径
     */
    private String generateDiagnosticsFtpLocation() throws IOException {
        if (!SteveConfiguration.CONFIG.isFtpEnabled()) {
            throw new IOException("FTP server is not enabled");
        }

        String ftpIp = SteveConfiguration.CONFIG.getFtpIp();
        if ("auto".equalsIgnoreCase(ftpIp) || !org.springframework.util.StringUtils.hasText(ftpIp)) {
            log.info("Attempting to dynamically determine internal IP for GetDiagnostics FTP as ftpIp property is 'auto'.");
            ftpIp = getInternalIpAddress();
            if (ftpIp == null) {
                throw new IOException("Could not automatically determine a site-local IP address for FTP.");
            }
            log.info("Dynamically determined internal IP for GetDiagnostics FTP: {}", ftpIp);
        }

        int ftpPort = SteveConfiguration.CONFIG.getFtpPort();
        String ftpUser = SteveConfiguration.CONFIG.getFtpUsername();
        String ftpPassword = SteveConfiguration.CONFIG.getFtpPassword();

        // 使用 /logs/ 路径用于诊断日志
        String ftpLogsPath = "/logs/";

        String location = String.format("ftp://%s:%s@%s:%d%s", ftpUser, ftpPassword, ftpIp, ftpPort, ftpLogsPath);
        log.info("Generated GetDiagnostics FTP URL: {}", location);
        return location;
    }

    /**
     * 获取内部IP地址
     */
    private String getInternalIpAddress() throws IOException {
        try {
            Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
            while (networkInterfaces.hasMoreElements()) {
                NetworkInterface networkInterface = networkInterfaces.nextElement();
                if (networkInterface.isLoopback() || !networkInterface.isUp()) {
                    continue;
                }
                Enumeration<InetAddress> addresses = networkInterface.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    InetAddress address = addresses.nextElement();
                    if (address.isSiteLocalAddress() && address instanceof Inet4Address) {
                        return address.getHostAddress();
                    }
                }
            }
        } catch (SocketException e) {
            throw new IOException("Failed to determine internal IP address", e);
        }
        return null;
    }
}