<%--
    SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
    Copyright (C) 2013-2025 SteVe Community Team
    All Rights Reserved.

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.
--%>
<%@ include file="00-header.jsp" %>
<%@ include file="00-op-bind-errors.jsp" %>

<script type="text/javascript">
    $(document).ready(function() {
        // Initialize date picker
        $('#reportDatePicker').datetimepicker({
            format: 'yyyy-mm-dd hh:ii',
            autoclose: true,
            todayBtn: true,
            minuteStep: 5
        }).on('changeDate', function(ev) {
            // 确保日期格式正确 - 将选定的日期格式化为yyyy-MM-dd HH:mm格式
            var date = ev.date;
            if (date) {
                var year = date.getFullYear();
                var month = (date.getMonth() + 1).toString().padStart(2, '0');
                var day = date.getDate().toString().padStart(2, '0');
                var hours = date.getHours().toString().padStart(2, '0');
                var minutes = date.getMinutes().toString().padStart(2, '0');
                
                // 设置为格式化后的值
                $(this).val(year + '-' + month + '-' + day + ' ' + hours + ':' + minutes);
            }
        });
        
        // Image preview functionality
        $('#imageInput').change(function() {
            $('#imagePreviewContainer').empty();
            var files = this.files;
            
            if (files && files.length > 0) {
                for (var i = 0; i < files.length; i++) {
                    if (files[i].type.match('image.*')) {
                        var reader = new FileReader();
                        reader.onload = (function(file) {
                            return function(e) {
                                $('#imagePreviewContainer').append(
                                    '<div style="display: inline-block; margin: 10px; text-align: center;">' +
                                    '<img src="' + e.target.result + '" style="max-height: 100px; max-width: 150px; border: 1px solid #ddd; padding: 3px;" />' +
                                    '<p style="font-size: 0.8em; margin-top: 5px;">' + file.name + '</p>' +
                                    '</div>'
                                );
                            };
                        })(files[i]);
                        reader.readAsDataURL(files[i]);
                    }
                }
            }
        });
    });
</script>

<style>
    .fault-form-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0 12px;
    }
    
    .fault-form-table td {
        padding: 8px;
        vertical-align: top;
    }
    
    .fault-form-table td:first-child {
        width: 180px;
        text-align: right;
        padding-right: 15px;
    }
    
    .fault-form-table input[type="text"],
    .fault-form-table select,
    .fault-form-table textarea {
        width: 90%;
        padding: 8px;
        border-radius: 4px;
        border: 1px solid #ccc;
    }
    
    .fault-form-table textarea {
        min-height: 120px;
    }
    
    .note {
        color: #666;
        font-size: 0.9em;
        margin-top: 5px;
        display: block;
    }
    
    .image-preview {
        margin-top: 10px;
        padding: 10px;
        border: 1px dashed #ccc;
        min-height: 50px;
        background-color: #f9f9f9;
    }
    
    .form-buttons {
        margin-top: 20px;
    }
    
    .form-buttons input[type="submit"] {
        margin-right: 10px;
        padding: 8px 16px;
    }
</style>

<div class="content">
    <div>
        <section><span>Report Charge Point Fault</span></section>
        <form:form action="${ctxPath}/manager/operations/report-fault" method="post" 
                  modelAttribute="faultForm" enctype="multipart/form-data">
            
            <table class="fault-form-table">
                <tr>
                    <td><form:label path="chargeBoxId">Charge Point: <span class="required-field">*</span></form:label></td>
                    <td>
                        <form:select path="chargeBoxId" required="required">
                            <option value="" selected>-- Select a charge point --</option>
                            <c:forEach items="${chargePoints}" var="cp">
                                <option value="${cp.chargeBoxId}" ${cp.chargeBoxId eq faultForm.chargeBoxId ? 'selected' : ''}>
                                    ${cp.chargeBoxId} ${cp.description != null ? '(' : ''}${cp.description}${cp.description != null ? ')' : ''}
                                </option>
                            </c:forEach>
                        </form:select>
                        <form:errors path="chargeBoxId" cssClass="error" />
                    </td>
                </tr>
                <tr>
                    <td><form:label path="reportTime">Report Time:</form:label></td>
                    <td>
                        <form:input path="reportTime" id="reportDatePicker" placeholder="Select date and time" />
                        <form:errors path="reportTime" cssClass="error" />
                        <span class="note">If not provided, current time will be used</span>
                    </td>
                </tr>
                <tr>
                    <td><form:label path="faultDescription">Fault Description: <span class="required-field">*</span></form:label></td>
                    <td>
                        <form:textarea path="faultDescription" required="required" rows="5" cols="50" 
                                     placeholder="Please describe the fault in detail, including when it occurred and symptoms" />
                        <form:errors path="faultDescription" cssClass="error" />
                    </td>
                </tr>
                <tr>
                    <td><form:label path="ocppErrorCode">OCPP Error Code:</form:label></td>
                    <td>
                        <form:input path="ocppErrorCode" placeholder="If you know the OCPP error code, please enter it" />
                        <form:errors path="ocppErrorCode" cssClass="error" />
                    </td>
                </tr>
                <tr>
                    <td><label for="imageInput">Fault Images:</label></td>
                    <td>
                        <div class="file-input-container">
                            <input type="file" name="images" id="imageInput" multiple accept="image/*" />
                            <p class="note">You can select multiple images (hold Ctrl key to select multiple)</p>
                        </div>
                        <div id="imagePreviewContainer" class="image-preview"></div>
                    </td>
                </tr>
                <tr>
                    <td></td>
                    <td class="form-buttons">
                        <input type="submit" value="Submit Fault Report" class="btn btn-primary">
                        <a href="${ctxPath}/manager/operations/faults" class="btn">Cancel</a>
                    </td>
                </tr>
            </table>
        </form:form>
    </div>
</div>

<%@ include file="00-footer.jsp" %> 