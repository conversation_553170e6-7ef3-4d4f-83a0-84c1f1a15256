-- 创建充电会话表，用于跟踪每次充电会话的状态变化
CREATE TABLE IF NOT EXISTS charging_session (
    session_id INT AUTO_INCREMENT PRIMARY KEY,
    charge_box_id VARCHAR(255) NOT NULL,
    connector_id INT NOT NULL,
    transaction_id INT,
    session_start_time TIMESTAMP NULL,
    session_end_time TIMESTAMP NULL,
    status ENUM('PREPARING', 'CHARGING', 'FINISHING', 'COMPLETED', 'FAILED') DEFAULT 'PREPARING',
    is_successful BOOLEAN DEFAULT FALSE,
    has_preparing BOOLEAN DEFAULT FALSE,
    has_charging BOOLEAN DEFAULT FALSE,
    has_valid_meter_values BOOLEAN DEFAULT FALSE,
    has_finishing BOOLEAN DEFAULT FALSE,
    meter_value_count INT DEFAULT 0,
    valid_meter_value_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_charge_box_connector (charge_box_id, connector_id),
    INDEX idx_transaction (transaction_id),
    INDEX idx_session_time (session_start_time, session_end_time),
    INDEX idx_status (status),
    INDEX idx_is_successful (is_successful)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建充电成功率统计表，用于存储每个充电桩的成功率统计数据
CREATE TABLE IF NOT EXISTS charging_success_stats (
    stats_id INT AUTO_INCREMENT PRIMARY KEY,
    charge_box_id VARCHAR(255) NOT NULL UNIQUE,
    total_sessions INT DEFAULT 0,
    successful_sessions INT DEFAULT 0,
    success_rate DECIMAL(5,2) DEFAULT 0.00,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_charge_box (charge_box_id),
    INDEX idx_success_rate (success_rate),
    FOREIGN KEY (charge_box_id) REFERENCES charge_box(charge_box_id) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 为现有的充电桩初始化统计数据
INSERT INTO charging_success_stats (charge_box_id, total_sessions, successful_sessions, success_rate)
SELECT
    cb.charge_box_id,
    0 as total_sessions,
    0 as successful_sessions,
    0.00 as success_rate
FROM charge_box cb
ON DUPLICATE KEY UPDATE charge_box_id = VALUES(charge_box_id);
