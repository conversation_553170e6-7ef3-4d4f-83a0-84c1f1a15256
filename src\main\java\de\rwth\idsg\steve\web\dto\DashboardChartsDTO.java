package de.rwth.idsg.steve.web.dto;

import lombok.Builder;
import lombok.Getter;

/**
 * 仪表板图表数据传输对象
 * 
 * <AUTHOR> Assistant
 */
@Getter
@Builder
public class DashboardChartsDTO {
    
    /**
     * 故障状态分布图表数据
     */
    private final ChartDataDTO faultStatusChart;
    
    /**
     * 在线离线状态图表数据
     */
    private final ChartDataDTO onlineStatusChart;
    
    /**
     * 充电成功率图表数据
     */
    private final ChartDataDTO chargingSuccessChart;
}
