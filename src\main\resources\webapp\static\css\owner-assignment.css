/* Owner Assignment Page Styles */

/* 整体容器样式 */
.content-container {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 0 20px 0 rgba(76,87,125,.02);
    padding: 20px;
}

/* 用户信息表格样式 */
.userInput {
    width: 100%;
    margin-bottom: 20px;
    border-collapse: collapse;
}

.userInput thead th {
    text-align: left;
    padding: 10px;
    background-color: #f7f7f7;
    color: #181c32;
    font-weight: 600;
    border-bottom: 1px solid #ebedf2;
}

.userInput tbody td {
    padding: 8px 10px;
    border-bottom: 1px solid #ebedf2;
    color: #6c7293;
}

.userInput tbody tr:hover {
    background-color: #f8f9fa;
}

/* 表格样式统一 */
#assignedTable, #unassignedTable {
    width: 100%;
    margin-bottom: 20px;
    border-collapse: collapse;
}

#assignedTable th, #unassignedTable th {
    padding: 10px 8px;
    background-color: #f7f7f7;
    text-align: center;
    font-weight: 600;
    border-bottom: 1px solid #ebedf2;
    color: #181c32;
}

#assignedTable td, #unassignedTable td {
    padding: 8px;
    vertical-align: middle;
    text-align: center;
    border-bottom: 1px solid #ebedf2;
    color: #6c7293;
}

/* 链接样式统一 */
#assignedTable a, #unassignedTable a {
    color: #3699ff;
    text-decoration: none;
}

#assignedTable a:hover, #unassignedTable a:hover {
    color: #187de4;
    text-decoration: underline;
}

/* 表格行悬停效果 */
#assignedTable tr:hover, #unassignedTable tr:hover {
    background-color: #f8f9fa;
}

/* 表格隔行变色 */
#assignedTable tr:nth-child(even), #unassignedTable tr:nth-child(even) {
    background-color: #fcfcfc;
}

/* 复选框样式优化 */
#assignedTable input[type="checkbox"],
#unassignedTable input[type="checkbox"] {
    margin: 0 auto;
    display: block;
    cursor: pointer;
}

/* 区块标题样式 */
section {
    margin-bottom: 15px;
    background: #fff;
    padding: 5px 0;
    border-bottom: 1px solid #ebedf2;
}

section span {
    color: #181c32;
    font-size: 16px;
    font-weight: 500;
    background: #fff;
    padding-right: 10px;
}

/* 信息区块样式 */
.info {
    background-color: #f8f9fa;
    border: 1px solid #ebedf2;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
}

/* 按钮样式统一 */
.info .blueSubmit {
    background: #3699ff;
    color: #fff;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    transition: background-color 0.3s;
}

.info .blueSubmit:hover {
    background: #187de4;
}

.info .redSubmit {
    background: #f64e60;
    color: #fff;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    transition: background-color 0.3s;
}

.info .redSubmit:hover {
    background: #ee2d41;
}

/* 返回按钮容器 */
.add-margin-bottom {
    margin-top: 20px;
    margin-bottom: 20px;
    text-align: left;
}

.add-margin-bottom input[type="submit"] {
    background: #3699ff;
    color: #fff;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    transition: background-color 0.3s;
}

.add-margin-bottom input[type="submit"]:hover {
    background: #187de4;
}

/* 错误信息样式 */
.error {
    color: #f64e60;
    background-color: #fff5f8;
    border: 1px solid #ffe2e5;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
}

/* 表格为空提示信息 */
#assignedTable tr td[colspan="4"],
#unassignedTable tr td[colspan="4"] {
    padding: 15px;
    text-align: center;
    color: #b5b5c3;
    font-style: italic;
}

/* 工具提示样式 */
a.tooltip {
    position: relative;
    display: inline-block;
    margin-left: 5px;
    color: #6c7293;
}

a.tooltip:hover {
    color: #3699ff;
}

a.tooltip img {
    width: 16px;
    height: 16px;
    vertical-align: middle;
}

a.tooltip span {
    display: none;
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    background: #1e1e2d;
    color: #fff;
    padding: 8px 12px;
    border-radius: 4px;
    width: 200px;
    font-size: 12px;
    z-index: 100;
}

a.tooltip:hover span {
    display: block;
}

a.tooltip span:after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: #1e1e2d transparent transparent transparent;
}

/* 响应式样式调整 */
@media (max-width: 768px) {
    #assignedTable, #unassignedTable {
        display: block;
        width: 100%;
        overflow-x: auto;
    }
    
    .userInput {
        width: 100%;
    }
} 