/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.repository;

import de.rwth.idsg.steve.repository.dto.User;
import de.rwth.idsg.steve.web.dto.UserDTO;
import de.rwth.idsg.steve.web.dto.UserForm;
import de.rwth.idsg.steve.web.dto.UserQueryForm;

import java.util.List;

/**
 * <AUTHOR> Goekay <<EMAIL>>
 * @since 25.11.2015
 */
public interface UserRepository {
    List<User.Overview> getOverview(UserQueryForm form);
    User.Details getDetails(int userPk);

    void add(UserForm form);
    void update(UserForm form);
    void delete(int userPk);
    
    List<UserDTO> getUsers();
    UserForm getUser(int userPk);
    void addEnterpriseUser(UserForm form);
    void addCarUser(UserForm form);
    void updateUser(UserForm form);
    void deleteUser(int userPk);
    List<Integer> getUserIdsByName(String userName);
    Integer getWebUserId(String userName);
    String getUserName(int userPk);
}
