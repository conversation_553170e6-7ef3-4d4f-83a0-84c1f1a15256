/**
 * Style overrides to ensure our new styles take precedence over the original
 */

/* Override the original content area style */
.full-content-container {
  width: 100% !important;
  max-width: 100% !important;
  padding: 0 !important;
  margin: 0 !important;
  display: flex !important;
  justify-content: center !important;
  background-color: #f3f3f3 !important;
}

.content {
  position: relative !important;
  width: 100% !important;
  max-width: 100% !important;
  border: 1px solid #CCC !important;
  border-radius: 0 !important;
  background: #fff !important;
  margin: 0 !important;
  padding: 20px !important;
  flex: 1 !important;
}

/* Center the tile wrapper */
.tileWrapper {
  text-align: center !important;
  display: flex !important;
  flex-wrap: wrap !important;
  justify-content: center !important;
  gap: 25px !important;
  margin: 0 auto !important;
  padding: 20px 0 !important;
  width: 100% !important;
  max-width: 100% !important;
}

/* Style for individual tiles */
.tileRow1 {
  flex: 0 0 calc(25% - 30px) !important;
  min-width: 220px !important;
  max-width: none !important;
  height: auto !important;
  min-height: 140px !important;
  margin: 0 !important;
  width: auto !important;
}

/* Fix top navigation menu */
ul.navigation {
  color: #ccc !important;
  margin: 0 auto !important;
  position: relative !important;
  float: none !important;
  display: flex !important;
  justify-content: center !important;
  width: 100% !important;
  max-width: 1200px !important;
}

.top-menu {
  height: 50px !important;
  border-radius: 0 !important;
  background: #2c3e50 !important;
  display: flex !important;
  align-items: center !important;
}

.container {
  width: 100% !important;
  max-width: 100% !important;
  margin-right: auto !important;
  margin-left: auto !important;
  padding: 0 !important;
}

/* Fix navigation items */
ul.navigation li a {
  padding: 0 15px !important;
  line-height: 50px !important;
  text-decoration: none !important;
  display: inline-block !important;
  cursor: pointer !important;
  font-size: 14px !important;
  font-weight: 500 !important;
}

/* Remove specific border styling that may interfere */
.tileWrapper a:link, .tileWrapper a:visited {
  margin: 0 !important;
  border: 1px solid #ebeef5 !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: center !important;
  align-items: center !important;
  height: 100% !important;
  text-decoration: none !important;
  background-color: #fff !important;
  box-shadow: 0 2px 12px rgba(0, 21, 41, 0.08) !important;
  transition: all .3s !important;
  padding: 20px 15px !important;
}

.tileWrapper a:hover, .tileWrapper a:active {
  transform: translateY(-4px) !important;
  box-shadow: 0 4px 12px rgba(0, 21, 41, 0.12) !important;
  background-color: #fff !important;
}

span.base {
  margin-top: 15px !important;
  margin-bottom: 10px !important;
  display: block !important;
  font-weight: bold !important;
  font-size: 24px !important;
  color: #409EFF !important;
}

span.formatNumber {
  font-size: 22px !important;
  color: #409EFF !important;
  font-weight: bold !important;
}

/* Better styling for table cells in tiles */
span.baseCell:first-child {
  width: auto !important;
  text-align: left !important;
}

span.baseCell:nth-child(2) {
  text-align: right !important;
  font-weight: bold !important;
  color: #409EFF !important;
  padding-left: 15px !important;
}

/* Main wrapper adjustments */
.main {
  display: flex !important;
  flex-direction: column !important;
  min-height: 100vh !important;
  width: 100% !important;
  max-width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
}

.main-wrapper {
  display: flex !important;
  flex: 1 !important;
  width: 100% !important;
  max-width: 100% !important;
  padding-top: 50px !important;
}

/* Ensure body takes full width */
body {
  width: 100% !important;
  max-width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
  overflow-x: hidden !important;
}