<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>下拉菜单溢出修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        /* 模拟原始的容器样式 - 有overflow: hidden */
        .content {
            background-color: white;
            border-radius: 4px;
            box-shadow: 0 0 20px 0 rgba(76,87,125,.02);
            padding: 20px;
            margin-bottom: 20px;
            overflow: hidden; /* 这会裁剪下拉菜单 */
        }
        
        .content-container {
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 0 20px 0 rgba(76,87,125,.02);
            padding: 25px;
            min-height: 200px;
            overflow: hidden; /* 这也会裁剪下拉菜单 */
        }
        
        .tab-content {
            border: 1px solid #ddd;
            border-top: none;
            padding: 15px;
            overflow: hidden; /* 这也会裁剪下拉菜单 */
        }
        
        /* 下拉菜单样式 */
        .multi-select-container {
            position: relative;
            width: 100%;
            margin-bottom: 10px;
        }
        
        .multi-select-header {
            padding: 8px 12px;
            border: 1px solid #ccc;
            background-color: #fff;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .multi-select-menu {
            position: absolute;
            width: 100%;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ccc;
            background-color: #fff;
            z-index: 9999;
            display: none;
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        
        .dropdown-item {
            padding: 8px 12px;
            cursor: pointer;
            border-bottom: 1px solid #eee;
        }
        
        .dropdown-item:hover {
            background-color: #f5f5f5;
        }
        
        /* 修复样式 - 应用后下拉菜单应该不被裁剪 */
        .fixed .content,
        .fixed .content-container,
        .fixed .tab-content,
        .fixed .multi-select-container {
            overflow: visible !important;
        }
        
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        
        .test-description {
            margin-bottom: 10px;
            color: #666;
        }
    </style>
</head>
<body>
    <h1>下拉菜单溢出修复测试</h1>
    
    <div class="test-title">测试1: 未修复的情况（下拉菜单会被裁剪）</div>
    <div class="test-description">这个容器设置了 overflow: hidden，下拉菜单会被裁剪</div>
    <div class="content">
        <div class="content-container">
            <div class="tab-content" style="display: block;">
                <div class="multi-select-container">
                    <div class="multi-select-header" onclick="toggleDropdown(this, 'test1')">
                        <span>点击选择充电桩（会被裁剪）</span>
                        <span>▼</span>
                    </div>
                    <div class="multi-select-menu" id="test1">
                        <div class="dropdown-item">CP001 - 充电桩001</div>
                        <div class="dropdown-item">CP002 - 充电桩002</div>
                        <div class="dropdown-item">CBI-146b9c8b1e52 - 充电桩003</div>
                        <div class="dropdown-item">510004 - 充电桩004</div>
                        <div class="dropdown-item">CBI-0c8c24238798 - 充电桩005</div>
                        <div class="dropdown-item">CBI-1597302927231 - 充电桩006</div>
                        <div class="dropdown-item">CBI-1597302917231 - 充电桩007</div>
                        <div class="dropdown-item">CBI-203233a00a8f - 充电桩008</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="test-title">测试2: 修复后的情况（下拉菜单不会被裁剪）</div>
    <div class="test-description">这个容器应用了修复样式，下拉菜单不会被裁剪</div>
    <div class="content fixed">
        <div class="content-container">
            <div class="tab-content" style="display: block;">
                <div class="multi-select-container">
                    <div class="multi-select-header" onclick="toggleDropdown(this, 'test2')">
                        <span>点击选择充电桩（不会被裁剪）</span>
                        <span>▼</span>
                    </div>
                    <div class="multi-select-menu" id="test2">
                        <div class="dropdown-item">CP001 - 充电桩001</div>
                        <div class="dropdown-item">CP002 - 充电桩002</div>
                        <div class="dropdown-item">CBI-146b9c8b1e52 - 充电桩003</div>
                        <div class="dropdown-item">510004 - 充电桩004</div>
                        <div class="dropdown-item">CBI-0c8c24238798 - 充电桩005</div>
                        <div class="dropdown-item">CBI-1597302927231 - 充电桩006</div>
                        <div class="dropdown-item">CBI-1597302917231 - 充电桩007</div>
                        <div class="dropdown-item">CBI-203233a00a8f - 充电桩008</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function toggleDropdown(element, id) {
            // 关闭所有其他下拉菜单
            document.querySelectorAll('.multi-select-menu').forEach(function(menu) {
                if (menu.id !== id) {
                    menu.style.display = 'none';
                }
            });
            
            // 切换当前下拉菜单
            var menu = document.getElementById(id);
            menu.style.display = menu.style.display === 'none' ? 'block' : 'none';
        }
        
        // 点击外部关闭下拉菜单
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.multi-select-container')) {
                document.querySelectorAll('.multi-select-menu').forEach(function(menu) {
                    menu.style.display = 'none';
                });
            }
        });
    </script>
</body>
</html>
