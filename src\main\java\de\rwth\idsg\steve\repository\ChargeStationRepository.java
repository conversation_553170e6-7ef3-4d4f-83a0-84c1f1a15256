/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.repository;

import de.rwth.idsg.steve.repository.dto.ChargeStation;
import de.rwth.idsg.steve.repository.dto.ChargeStationSelect;
import de.rwth.idsg.steve.web.dto.ChargeStationQueryForm;

import java.util.List;

/**
 * <AUTHOR>
public interface ChargeStationRepository {

    /**
     * Get a specific charge station.
     *
     * @param chargingStationPk database primary key
     * @return The charge station details
     */
    ChargeStation.Details getDetails(int chargingStationPk);

    /**
     * Get all charge stations.
     *
     * @return List of charge stations
     */
    List<ChargeStation.Overview> getOverview(ChargeStationQueryForm form);

    /**
     * Get charge stations that are assigned to a specific user
     *
     * @param userPk database primary key of user
     * @param form query form to filter results
     * @return List of charge stations
     */
    List<ChargeStation.Overview> getOwnerChargeStations(int userPk, ChargeStationQueryForm form);

    /**
     * Adds a new charge station.
     *
     * @param form Details about the charge station to be added
     */
    void addChargeStation(ChargeStation.Form form);

    /**
     * Updates a charge station.
     *
     * @param form Details about the charge station to be updated
     */
    void updateChargeStation(ChargeStation.Form form);

    /**
     * Deletes a charge station.
     *
     * @param chargingStationPk database primary key
     */
    void deleteChargeStation(int chargingStationPk);

    /**
     * Get all charge stations for selection dropdown
     *
     * @return List of charge stations for selection
     */
    List<ChargeStationSelect> getChargeStationSelectList();

    /**
     * Get charge stations for selection dropdown filtered by user permissions
     *
     * @param webUserPk User primary key, null for admin/factory operator (no filter)
     * @return List of charge stations for selection
     */
    List<ChargeStationSelect> getChargeStationSelectListByUser(Integer webUserPk);

    /**
     * Check if a charge station is owned by a specific user
     *
     * @param chargingStationPk Charge station primary key
     * @param webUserPk User primary key
     * @return true if the charge station is owned by the user
     */
    boolean isChargeStationOwnedByUser(int chargingStationPk, int webUserPk);

}