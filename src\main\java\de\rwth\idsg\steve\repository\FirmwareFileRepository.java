package de.rwth.idsg.steve.repository;

import de.rwth.idsg.steve.repository.dto.FirmwareFile;
import java.util.List;

/**
 * 固件文件管理Repository接口
 */
public interface FirmwareFileRepository {
    
    /**
     * 获取所有上传成功的固件文件列表
     * @return 成功上传的固件文件列表
     */
    List<FirmwareFile> getSuccessfulFirmwareFiles();
    
    /**
     * Get all firmware files with detailed information
     * @return All firmware files with status and metadata
     */
    List<FirmwareFile> getAllFirmwareFiles();

    /**
     * Get all firmware filenames (regardless of status)
     * @return All firmware filenames
     */
    List<String> getAllFirmwareFilenames();

    /**
     * Get all successfully installed firmware filenames
     * @return Successfully installed firmware filenames
     */
    List<String> getSuccessfulFirmwareFilenames();
    
    /**
     * 根据文件名查询固件文件信息
     * @param filename 文件名
     * @return 固件文件信息，如果不存在返回null
     */
    FirmwareFile getFirmwareFileByFilename(String filename);
    
    /**
     * 添加固件文件记录
     * @param firmwareFile 固件文件信息
     * @return 插入的记录ID
     */
    Integer addFirmwareFile(FirmwareFile firmwareFile);
    
    /**
     * Update firmware file status
     * @param filename File name
     * @param status New status
     * @return Number of updated records
     */
    int updateFirmwareFileStatus(String filename, FirmwareFile.UploadStatus status);

    /**
     * Update firmware file upload status with safety check
     * @param filename File name
     * @param fromStatus Current status (for safety check)
     * @param toStatus New status
     * @param description Updated description
     * @return Number of updated records
     */
    int updateUploadStatus(String filename, FirmwareFile.UploadStatus fromStatus,
                          FirmwareFile.UploadStatus toStatus, String description);
    
    /**
     * 删除固件文件记录
     * @param filename 文件名
     * @return 删除的记录数
     */
    int deleteFirmwareFile(String filename);
    
    /**
     * 检查文件名是否已存在
     * @param filename 文件名
     * @return 如果存在返回true
     */
    boolean existsByFilename(String filename);
    

    
    /**
     * 根据上传用户获取固件文件列表
     * @param userPk 用户ID
     * @return 该用户上传的固件文件列表
     */
    List<FirmwareFile> getFirmwareFilesByUser(Integer userPk);
}
