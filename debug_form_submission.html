<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Form Submission</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-info {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .form-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .multi-select-container {
            position: relative;
            width: 100%;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        .selected-options {
            padding: 10px;
            cursor: pointer;
            min-height: 20px;
            line-height: 1.5;
            background-color: #fff;
        }
        .options-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            border: 1px solid #ccc;
            border-top: none;
            background-color: white;
            z-index: 1000;
            max-height: 200px;
            overflow-y: auto;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .options-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .options-list li {
            padding: 8px 10px;
            cursor: pointer;
        }
        .options-list li:hover {
            background-color: #f0f0f0;
        }
        .options-list li input[type="checkbox"] {
            margin-right: 8px;
        }
        .submit-button {
            text-align: center;
            margin-top: 15px;
        }
        .submit-button input[type="submit"] {
            background-color: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        .debug-output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Debug Form Submission for GetDiagnostics</h1>
        
        <div class="debug-info">
            <h3>Debug Purpose</h3>
            <p>This page helps debug why the GetDiagnostics form submission is not working properly.</p>
            <p>It will show exactly what data is being submitted to the server.</p>
        </div>

        <form id="debugForm" method="post" action="javascript:void(0);">
            <div class="form-section">
                <h3>Charge Points Selection</h3>
                
                <div class="multi-select-container">
                    <div class="selected-options" tabindex="0">Click to select Charge Points</div>
                    <div class="options-dropdown" style="display: none;">
                        <ul class="options-list">
                            <li>
                                <input type="checkbox" id="cp-CP001" name="selectedChargePoints" value="V_16_SOAP;CP001;http://example.com/cp001">
                                <label for="cp-CP001">CP001 (OCPP 1.6 SOAP)</label>
                            </li>
                            <li>
                                <input type="checkbox" id="cp-CP002" name="selectedChargePoints" value="V_16_SOAP;CP002;http://example.com/cp002">
                                <label for="cp-CP002">CP002 (OCPP 1.6 SOAP)</label>
                            </li>
                            <li>
                                <input type="checkbox" id="cp-CP005" name="selectedChargePoints" value="V_16_JSON;CP005;ws://example.com/cp005">
                                <label for="cp-CP005">CP005 (OCPP 1.6 JSON)</label>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- Hidden select for form submission -->
                    <select name="chargePointSelectList" multiple="true" style="display:none;">
                        <option value="V_16_SOAP;CP001;http://example.com/cp001">CP001</option>
                        <option value="V_16_SOAP;CP002;http://example.com/cp002">CP002</option>
                        <option value="V_16_JSON;CP005;ws://example.com/cp005">CP005</option>
                    </select>
                </div>
            </div>
            
            <div class="form-section">
                <h3>Parameters</h3>
                <p><strong>Diagnostic logs will be automatically saved to the built-in FTP server logs directory</strong></p>
                <p>FTP Server Address: ftp://user@************:9988/logs/</p>
                
                <table>
                    <tr>
                        <td>Retries (integer):</td>
                        <td><input type="text" name="retries" placeholder="optional" /></td>
                    </tr>
                    <tr>
                        <td>Retry Interval (integer):</td>
                        <td><input type="text" name="retryInterval" placeholder="optional" /></td>
                    </tr>
                </table>
                
                <div class="submit-button">
                    <input type="submit" value="Debug Submit" onclick="debugSubmit(event)">
                </div>
            </div>
        </form>
        
        <div id="debugOutput" class="debug-output" style="display: none;"></div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.querySelector('.multi-select-container');
            const selectedOptionsDisplay = container.querySelector('.selected-options');
            const dropdown = container.querySelector('.options-dropdown');
            const optionsList = container.querySelector('.options-list');
            const hiddenSelect = document.querySelector('select[name="chargePointSelectList"]');

            // Toggle dropdown visibility
            selectedOptionsDisplay.addEventListener('click', function() {
                dropdown.style.display = dropdown.style.display === 'none' ? 'block' : 'none';
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(event) {
                if (!container.contains(event.target)) {
                    dropdown.style.display = 'none';
                }
            });

            // Prevent dropdown from closing when clicking inside it
            dropdown.addEventListener('click', function(event) {
                event.stopPropagation();
            });
            
            // Update selection function
            function updateSelection() {
                const selectedValues = [];
                const displayTexts = [];
                optionsList.querySelectorAll('input[type="checkbox"]:checked').forEach(function(checkbox) {
                    selectedValues.push(checkbox.value);
                    const labelElement = checkbox.parentElement.querySelector('label[for="' + checkbox.id + '"]');
                    if (labelElement) {
                         displayTexts.push(labelElement.textContent);
                    }
                });

                // Update hidden select
                Array.from(hiddenSelect.options).forEach(function(option) {
                    option.selected = false;
                });
                selectedValues.forEach(function(value) {
                    const optionToSelect = Array.from(hiddenSelect.options).find(opt => opt.value === value);
                    if (optionToSelect) {
                        optionToSelect.selected = true;
                    }
                });
                
                var event = new Event('change', { bubbles: true });
                hiddenSelect.dispatchEvent(event);

                if (displayTexts.length > 0) {
                    selectedOptionsDisplay.textContent = displayTexts.join(', ');
                } else {
                    selectedOptionsDisplay.textContent = 'Click to select Charge Points';
                }
                
                // Debug output
                console.log('Selected values:', selectedValues);
                console.log('Hidden select options selected:', Array.from(hiddenSelect.selectedOptions).map(opt => opt.value));
            }

            // Event listeners for checkboxes
            optionsList.querySelectorAll('input[type="checkbox"]').forEach(function(checkbox) {
                checkbox.addEventListener('change', updateSelection);
            });
        });
        
        function debugSubmit(event) {
            event.preventDefault();
            
            const form = document.getElementById('debugForm');
            const formData = new FormData(form);
            
            let debugInfo = 'Form Data Debug Information:\n\n';
            
            // Show all form data
            for (let [key, value] of formData.entries()) {
                debugInfo += `${key}: ${value}\n`;
            }
            
            // Show hidden select specifically
            const hiddenSelect = document.querySelector('select[name="chargePointSelectList"]');
            const selectedOptions = Array.from(hiddenSelect.selectedOptions);
            
            debugInfo += '\nHidden Select Debug:\n';
            debugInfo += `Total options: ${hiddenSelect.options.length}\n`;
            debugInfo += `Selected options: ${selectedOptions.length}\n`;
            debugInfo += 'Selected values:\n';
            selectedOptions.forEach(opt => {
                debugInfo += `  - ${opt.value}\n`;
            });
            
            // Show what would be sent to server
            debugInfo += '\nWhat would be sent to server:\n';
            const params = {
                chargePointSelectList: selectedOptions.map(opt => opt.value),
                retries: formData.get('retries') || null,
                retryInterval: formData.get('retryInterval') || null
            };
            debugInfo += JSON.stringify(params, null, 2);
            
            const debugOutput = document.getElementById('debugOutput');
            debugOutput.textContent = debugInfo;
            debugOutput.style.display = 'block';
            
            // Also log to console
            console.log('Debug info:', debugInfo);
            console.log('Params object:', params);
        }
    </script>
</body>
</html>
