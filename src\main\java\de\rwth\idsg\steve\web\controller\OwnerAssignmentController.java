/*
 * SteVe - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.web.controller;

import de.rwth.idsg.steve.repository.ChargePointRepository;
import de.rwth.idsg.steve.repository.UserChargeBoxRepository;
import de.rwth.idsg.steve.repository.dto.ChargePoint;
import de.rwth.idsg.steve.service.WebUserService;
import de.rwth.idsg.steve.web.dto.OwnerAssignmentForm;
import de.rwth.idsg.steve.web.dto.OwnerAssignmentQueryForm;
import de.rwth.idsg.steve.web.dto.UserRole;
import de.rwth.idsg.steve.web.dto.WebUserDTO;
import jooq.steve.db.tables.records.WebUserRecord;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Controller for managing charge box owner (桩主) assignments to charge points
 *
 * @since 1.0.8
 */
@Slf4j
@Controller
@RequiredArgsConstructor
@RequestMapping(value = "/manager/ownerAssignments")
@PreAuthorize("hasAnyAuthority('ADMIN', 'OPERATOR_FACTORY')")
public class OwnerAssignmentController {

    private final WebUserService webUserService;
    private final ChargePointRepository chargePointRepository;
    private final UserChargeBoxRepository userChargeBoxRepository;

    private static final String PARAMS = "params";

    // -------------------------------------------------------------------------
    // Paths
    // -------------------------------------------------------------------------

    private static final String QUERY_PATH = "/query";
    private static final String ASSIGN_PATH = "/assign";
    private static final String UNASSIGN_PATH = "/unassign";
    private static final String DETAILS_PATH = "/details/{userPk}";

    // -------------------------------------------------------------------------
    // HTTP methods
    // -------------------------------------------------------------------------

    @RequestMapping(method = RequestMethod.GET)
    public String getOverview(Model model) {
        initList(model, new OwnerAssignmentQueryForm());
        return "data-man/ownerAssignment";
    }

    @RequestMapping(value = QUERY_PATH, method = RequestMethod.GET)
    public String getQuery(@ModelAttribute(PARAMS) OwnerAssignmentQueryForm params, Model model) {
        initList(model, params);
        return "data-man/ownerAssignment";
    }

    @RequestMapping(value = DETAILS_PATH, method = RequestMethod.GET)
    public String getDetails(@PathVariable("userPk") int userPk, Model model) {
        try {
            log.debug("Getting details for user PK: {}", userPk);
            
            if (userPk <= 0) {
                String msg = "Invalid user PK: " + userPk;
                log.warn(msg);
                model.addAttribute("errorMessage", msg);
                return "data-man/ownerAssignmentDetails";
            }
            
            // 获取用户信息
            WebUserRecord user = webUserService.loadUserByPk(userPk);
            if (user == null) {
                String msg = "User with PK " + userPk + " not found";
                log.warn(msg);
                model.addAttribute("errorMessage", msg);
                return "data-man/ownerAssignmentDetails";
            }
            
            log.debug("User found: {}, role: {}", user.getUsername(), user.getUserRole());
            
            // 获取充电桩分配信息
            if (!prepareAssignmentForm(model, userPk)) {
                // prepareAssignmentForm已添加错误消息
                log.warn("Failed to prepare assignment form for user {}", userPk);
            }
            
            return "data-man/ownerAssignmentDetails";
        } catch (Exception e) {
            String msg = "Error while loading user assignments: " + e.getMessage();
            log.error(msg, e);
            model.addAttribute("errorMessage", msg);
            return "data-man/ownerAssignmentDetails";
        }
    }

    @RequestMapping(value = ASSIGN_PATH, method = RequestMethod.POST)
    public String assign(@ModelAttribute("assignmentForm") OwnerAssignmentForm form, 
                         BindingResult result, Model model) {
        if (result.hasErrors()) {
            log.error("Form validation errors: {}", result.getAllErrors());
            prepareAssignmentForm(model, form.getUserPk());
            model.addAttribute("errorMessage", "Form validation failed: Please check your input.");
            return "data-man/ownerAssignmentDetails";
        }

        try {
            if (form.getUserPk() <= 0) {
                throw new IllegalArgumentException("Invalid user ID");
            }
            
            if (form.getUnassignedChargeBoxIds() == null || form.getUnassignedChargeBoxIds().isEmpty()) {
                log.warn("No charge boxes selected for assignment");
                model.addAttribute("errorMessage", "Please select at least one charge box to assign.");
                prepareAssignmentForm(model, form.getUserPk());
                return "data-man/ownerAssignmentDetails";
            }
            
            processAssignments(form, true);
            return toOverview();
        } catch (Exception e) {
            log.error("Error while assigning charge boxes to user", e);
            prepareAssignmentForm(model, form.getUserPk());
            model.addAttribute("errorMessage", "Failed to assign charge boxes: " + e.getMessage());
            return "data-man/ownerAssignmentDetails";
        }
    }

    @RequestMapping(value = UNASSIGN_PATH, method = RequestMethod.POST)
    public String unassign(@ModelAttribute("assignmentForm") OwnerAssignmentForm form, 
                           BindingResult result, Model model) {
        if (result.hasErrors()) {
            log.error("Form validation errors: {}", result.getAllErrors());
            prepareAssignmentForm(model, form.getUserPk());
            model.addAttribute("errorMessage", "Form validation failed: Please check your input.");
            return "data-man/ownerAssignmentDetails";
        }

        try {
            if (form.getUserPk() <= 0) {
                throw new IllegalArgumentException("Invalid user ID");
            }
            
            if (form.getAssignedChargeBoxIds() == null || form.getAssignedChargeBoxIds().isEmpty()) {
                log.warn("No charge boxes selected for unassignment");
                model.addAttribute("errorMessage", "Please select at least one charge box to unassign.");
                prepareAssignmentForm(model, form.getUserPk());
                return "data-man/ownerAssignmentDetails";
            }
            
            processAssignments(form, false);
            return toOverview();
        } catch (Exception e) {
            log.error("Error while unassigning charge boxes from user", e);
            prepareAssignmentForm(model, form.getUserPk());
            model.addAttribute("errorMessage", "Failed to unassign charge boxes: " + e.getMessage());
            return "data-man/ownerAssignmentDetails";
        }
    }

    /**
     * 取消操作，返回到概述页面
     */
    @RequestMapping(value = "/cancel", method = RequestMethod.POST)
    public String cancel() {
        return toOverview();
    }

    // -------------------------------------------------------------------------
    // Helpers
    // -------------------------------------------------------------------------

    private void initList(Model model, OwnerAssignmentQueryForm params) {
        try {
            // 获取所有OPERATOR_OWNER角色的用户
            List<WebUserRecord> ownerList = new ArrayList<>(webUserService.getUsersByRole(UserRole.OPERATOR_OWNER));
            
            // 根据用户名筛选
            if (params.isSetUsername()) {
                final String username = params.getUsername().toLowerCase();
                ownerList = ownerList.stream()
                    .filter(u -> u.getUsername().toLowerCase().contains(username))
                    .collect(Collectors.toList());
            }
            
            // 将WebUserRecord转换为WebUserDTO并构建桩主列表数据
            List<Map<String, Object>> owners = ownerList.stream()
                .map(WebUserDTO::fromRecord)
                .filter(dto -> dto != null)  // 过滤掉转换失败的记录
                .map(dto -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("userPk", dto.getUserPk());
                    map.put("username", dto.getUsername());
                    
                    try {
                        // 获取该用户关联的充电桩ID列表
                        List<String> chargeBoxIds = webUserService.getChargeBoxIdsByUser(dto.getUserPk());
                        map.put("chargeBoxCount", chargeBoxIds != null ? chargeBoxIds.size() : 0);
                    } catch (Exception e) {
                        log.error("Error getting charge boxes for user {}: {}", dto.getUsername(), e.getMessage());
                        map.put("chargeBoxCount", 0);
                    }
                    
                    return map;
                })
                .collect(Collectors.toList());
            
            model.addAttribute(PARAMS, params);
            model.addAttribute("owners", owners);
        } catch (Exception e) {
            log.error("Error while initializing owner list", e);
            model.addAttribute("errorMessage", "Error loading owner list: " + e.getMessage());
            model.addAttribute(PARAMS, params);
            model.addAttribute("owners", new ArrayList<>());
        }
    }

    private boolean prepareAssignmentForm(Model model, int userPk) {
        try {
            // 获取指定用户
            WebUserRecord owner = webUserService.loadUserByPk(userPk);
            
            if (owner == null) {
                log.warn("User with ID {} not found", userPk);
                model.addAttribute("errorMessage", "User not found");
                return false;
            }
            
            // 将WebUserRecord转换为WebUserDTO
            WebUserDTO ownerDTO = WebUserDTO.fromRecord(owner);
            if (ownerDTO == null) {
                log.error("Failed to convert user record to DTO for user {}", userPk);
                model.addAttribute("errorMessage", "Error processing user data");
                return false;
            }
            
            log.debug("User found: {}, role: {}", ownerDTO.getUsername(), ownerDTO.getUserRole());
            
            // 获取该桩主已分配的充电桩ID列表
            List<String> assignedIds;
            try {
                assignedIds = userChargeBoxRepository.getChargeBoxIdsByUser(userPk);
                if (assignedIds == null) {
                    assignedIds = new ArrayList<>();
                }
                log.debug("User {} has {} assigned chargeBoxIds", userPk, assignedIds.size());
                // 增加日志输出分配的充电桩ID，便于调试
                if (!assignedIds.isEmpty()) {
                    log.debug("Assigned chargeBoxIds: {}", String.join(", ", assignedIds));
                }
            } catch (Exception e) {
                log.error("Error getting assigned chargeBoxIds for user {}: {}", userPk, e.getMessage(), e);
                assignedIds = new ArrayList<>();
            }
            
            // 获取所有充电桩
            List<ChargePoint.Overview> allChargePoints = chargePointRepository.getOverview(null);
            if (allChargePoints == null) {
                allChargePoints = new ArrayList<>();
            }
            
            // 获取已分配给任何用户的充电桩ID列表
            List<String> allAssignedChargeBoxIds = userChargeBoxRepository.getAllAssignedChargeBoxIds();
            log.debug("Total {} charge boxes are assigned to any user", allAssignedChargeBoxIds.size());
            
            // 分离已分配和未分配的充电桩
            List<ChargePoint.Overview> assignedChargePoints = new ArrayList<>();
            List<ChargePoint.Overview> unassignedChargePoints = new ArrayList<>();
            
            for (ChargePoint.Overview cp : allChargePoints) {
                if (assignedIds.contains(cp.getChargeBoxId())) {
                    // 这个充电桩分配给了当前用户
                    assignedChargePoints.add(cp);
                } else if (!allAssignedChargeBoxIds.contains(cp.getChargeBoxId())) {
                    // 这个充电桩未分配给任何用户
                    unassignedChargePoints.add(cp);
                }
                // 已分配给其他用户的充电桩不会出现在已分配或未分配列表中
            }
            
            log.debug("Total charge points: {}, Assigned to this user: {}, Truly unassigned: {}", 
                     allChargePoints.size(), assignedChargePoints.size(), unassignedChargePoints.size());
            
            // 准备表单
            OwnerAssignmentForm form = new OwnerAssignmentForm();
            form.setUserPk(userPk);
            form.setAssignedChargeBoxIds(new ArrayList<>()); // 初始化为空列表
            form.setUnassignedChargeBoxIds(new ArrayList<>()); // 初始化为空列表
            
            // 添加到模型
            model.addAttribute("owner", ownerDTO);
            model.addAttribute("assignmentForm", form);
            model.addAttribute("assignedChargePoints", assignedChargePoints);
            model.addAttribute("unassignedChargePoints", unassignedChargePoints);
            
            return true;
        } catch (Exception e) {
            log.error("Error preparing assignment form for user {}: {}", userPk, e.getMessage(), e);
            model.addAttribute("errorMessage", "Error preparing assignment form: " + e.getMessage());
            return false;
        }
    }

    private void processAssignments(OwnerAssignmentForm form, boolean isAssign) {
        int userPk = form.getUserPk();
        log.debug("Processing {} for user PK: {}", isAssign ? "assignments" : "unassignments", userPk);
        
        try {
            // 获取用户信息以进行日志记录
            WebUserRecord user = webUserService.loadUserByPk(userPk);
            if (user != null) {
                log.debug("Processing for user: {}", user.getUsername());
            }
            
            // 获取充电桩ID - PK映射
            List<String> chargeBoxIds;
            if (isAssign) {
                chargeBoxIds = form.getUnassignedChargeBoxIds();
                log.debug("Assigning {} charge boxes to user {}", 
                         chargeBoxIds != null ? chargeBoxIds.size() : 0, 
                         userPk);
            } else {
                chargeBoxIds = form.getAssignedChargeBoxIds();
                log.debug("Unassigning {} charge boxes from user {}", 
                         chargeBoxIds != null ? chargeBoxIds.size() : 0, 
                         userPk);
            }
            
            if (chargeBoxIds == null || chargeBoxIds.isEmpty()) {
                log.warn("No charge box IDs selected for {}", isAssign ? "assignment" : "unassignment");
                return;
            }
            
            // 记录充电桩ID列表
            log.debug("ChargeBox IDs: {}", String.join(", ", chargeBoxIds));
            
            // 获取充电桩ID到主键的映射
            Map<String, Integer> idPkMap = chargePointRepository.getChargeBoxIdPkPair(chargeBoxIds);
            if (idPkMap == null || idPkMap.isEmpty()) {
                log.warn("No charge box PKs found for the given IDs");
                return;
            }
            
            // 记录映射信息
            idPkMap.forEach((id, pk) -> 
                log.debug("ChargeBox mapping - ID: {}, PK: {}", id, pk));
            
            // 批量处理分配/取消分配
            for (String chargeBoxId : chargeBoxIds) {
                Integer chargeBoxPk = idPkMap.get(chargeBoxId);
                if (chargeBoxPk == null) {
                    log.warn("No PK found for chargeBoxId: {}", chargeBoxId);
                    continue;
                }
                
                try {
                    if (isAssign) {
                        log.debug("Assigning chargeBox PK {} to user PK {}", chargeBoxPk, userPk);
                        userChargeBoxRepository.assignChargeBoxToUser(userPk, chargeBoxPk);
                    } else {
                        log.debug("Removing chargeBox PK {} from user PK {}", chargeBoxPk, userPk);
                        userChargeBoxRepository.removeChargeBoxFromUser(userPk, chargeBoxPk);
                    }
                } catch (Exception e) {
                    log.error("Error processing chargeBoxId {}: {}", chargeBoxId, e.getMessage(), e);
                }
            }
            
            // 验证处理结果
            List<String> assignedAfter = userChargeBoxRepository.getChargeBoxIdsByUser(userPk);
            log.debug("After processing: User {} has {} assigned charge boxes", 
                     userPk, assignedAfter.size());
            if (!assignedAfter.isEmpty()) {
                log.debug("Assigned charge boxes: {}", String.join(", ", assignedAfter));
            }
            
        } catch (Exception e) {
            log.error("Error processing assignments for user {}: {}", userPk, e.getMessage(), e);
            throw e; // 重新抛出以便调用者处理
        }
    }

    /**
     * 重定向到概述页面
     */
    private String toOverview() {
        return "redirect:/manager/ownerAssignments";
    }
}