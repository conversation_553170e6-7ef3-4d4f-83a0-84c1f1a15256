/*
 * SteV<PERSON> - SteckdosenVerwaltung - https://github.com/steve-community/steve
 * Copyright (C) 2013-2025 SteVe Community Team
 * All Rights Reserved.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 */
package de.rwth.idsg.steve.service;

import de.rwth.idsg.steve.repository.ChargeStationRepository;
import de.rwth.idsg.steve.repository.UserChargeBoxRepository;
import de.rwth.idsg.steve.repository.WebUserRepository;
import de.rwth.idsg.steve.web.dto.CustomUserDetails;
import de.rwth.idsg.steve.web.dto.UserRole;
import jooq.steve.db.tables.records.WebUserRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;

import java.util.Collection;

/**
 * Service to handle permission checks.
 *
 * @since 1.0.8
 */
@Slf4j
@Service
public class PermissionService {

    private final WebUserRepository webUserRepository;
    private final UserChargeBoxRepository userChargeBoxRepository;
    private final ChargeStationRepository chargeStationRepository;
    
    @Lazy
    @Autowired
    private WebUserService webUserService;

    @Autowired
    public PermissionService(WebUserRepository webUserRepository,
                           UserChargeBoxRepository userChargeBoxRepository,
                           ChargeStationRepository chargeStationRepository) {
        this.webUserRepository = webUserRepository;
        this.userChargeBoxRepository = userChargeBoxRepository;
        this.chargeStationRepository = chargeStationRepository;
    }

    /**
     * 检查当前用户是否是管理员
     *
     * @return 如果是管理员返回true
     */
    public boolean isAdmin() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth == null) {
            return false;
        }

        String username = auth.getName();
        UserRole role = webUserRepository.getUserRole(username);
        return role == UserRole.ADMIN;
    }

    /**
     * 检查当前用户是否是工厂运营商
     *
     * @return 如果是工厂运营商返回true
     */
    public boolean isOperatorFactory() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth == null) {
            return false;
        }

        String username = auth.getName();
        UserRole role = webUserRepository.getUserRole(username);
        return role == UserRole.OPERATOR_FACTORY;
    }

    /**
     * 检查当前用户是否是充电桩所有者
     *
     * @return 如果是充电桩所有者返回true
     */
    public boolean isOperatorOwner() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth == null) {
            return false;
        }

        String username = auth.getName();
        UserRole role = webUserRepository.getUserRole(username);
        return role == UserRole.OPERATOR_OWNER;
    }

    /**
     * 检查当前用户是否可以查看指定的充电桩
     *
     * @param chargeBoxId 充电桩ID
     * @return 如果可以查看返回true
     */
    public boolean canViewChargeBox(String chargeBoxId) {
        // 管理员和工厂运营商可以查看所有充电桩
        if (isAdmin() || isOperatorFactory()) {
            return true;
        }

        // 充电桩所有者只能查看分配给他们的充电桩
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth == null) {
            return false;
        }

        String username = auth.getName();
        WebUserRecord user = webUserRepository.loadUserByUsername(username);
        if (user == null) {
            return false;
        }

        return userChargeBoxRepository.isChargeBoxAssignedToUser(user.getWebUserPk(), chargeBoxId);
    }

    /**
     * 检查当前用户是否可以查看指定的充电桩（通过主键）
     *
     * @param chargeBoxPk 充电桩主键
     * @return 如果可以查看返回true
     */
    public boolean canViewChargeBoxByPk(int chargeBoxPk) {
        log.info("=== canViewChargeBoxByPk Debug for chargeBoxPk: {} ===", chargeBoxPk);

        try {
            // 管理员和工厂运营商可以查看所有充电桩
            boolean isAdminUser = isAdmin();
            boolean isFactoryOperator = isOperatorFactory();
            boolean isOwnerOperator = isOperatorOwner();

            log.info("User roles - isAdmin: {}, isOperatorFactory: {}, isOperatorOwner: {}",
                     isAdminUser, isFactoryOperator, isOwnerOperator);

            if (isAdminUser || isFactoryOperator) {
                log.info("User is admin or factory operator, granting access");
                return true;
            }

            // 充电桩所有者只能查看分配给他们的充电桩
            if (isOwnerOperator) {
                try {
                    WebUserRecord currentUser = getCurrentUser();
                    if (currentUser == null) {
                        log.warn("Current user is null when checking view permission for charge box pk: {}", chargeBoxPk);
                        return false;
                    }

                    log.info("Current user: webUserPk={}, username={}",
                             currentUser.getWebUserPk(), currentUser.getUsername());

                    boolean isAssigned = userChargeBoxRepository.isChargeBoxAssignedToUserByPk(currentUser.getWebUserPk(), chargeBoxPk);
                    log.info("isChargeBoxAssignedToUserByPk result: {}", isAssigned);

                    return isAssigned;
                } catch (Exception e) {
                    log.warn("Failed to check charge box ownership for viewing, chargeBoxPk: {}", chargeBoxPk, e);
                    return false;
                }
            }

            log.info("User does not have any recognized role, denying access");
            return false;
        } catch (Exception e) {
            log.error("Unexpected error in canViewChargeBoxByPk for chargeBoxPk: {}", chargeBoxPk, e);
            return false;
        }
    }

    /**
     * 检查当前用户是否具有指定角色中的任意一个
     *
     * @param roles 要检查的角色列表
     * @return 如果用户具有任一角色返回true
     */
    public boolean hasAnyRole(UserRole... roles) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth == null) {
            return false;
        }

        String username = auth.getName();
        UserRole userRole = webUserRepository.getUserRole(username);
        
        if (userRole == null) {
            return false;
        }
        
        for (UserRole role : roles) {
            if (userRole == role) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查当前用户是否具有指定角色
     *
     * @param role 要检查的角色
     * @return 如果用户具有该角色返回true
     */
    public boolean hasRole(UserRole role) {
        return hasAnyRole(role);
    }

    /**
     * 检查指定的充电桩是否分配给当前用户
     *
     * @param chargeBoxId 充电桩ID
     * @return 如果充电桩分配给当前用户返回true
     */
    public boolean isChargeBoxAssignedToCurrentUser(String chargeBoxId) {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth == null) {
            return false;
        }

        String username = auth.getName();
        WebUserRecord user = webUserRepository.loadUserByUsername(username);
        if (user == null) {
            return false;
        }

        return userChargeBoxRepository.isChargeBoxAssignedToUser(user.getWebUserPk(), chargeBoxId);
    }

    /**
     * 获取当前认证用户的WebUserRecord
     *
     * @return 当前用户的WebUserRecord
     * @throws AccessDeniedException 如果没有用户认证或找不到用户
     */
    public WebUserRecord getCurrentUser() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        if (auth == null) {
            throw new AccessDeniedException("No user is authenticated");
        }

        // 优先从CustomUserDetails中获取，避免重复数据库查询
        if (auth.getPrincipal() instanceof CustomUserDetails) {
            CustomUserDetails userDetails = (CustomUserDetails) auth.getPrincipal();
            return userDetails.getUserRecord();
        }

        // 回退到数据库查询（用于API认证等场景）
        String username = auth.getName();
        WebUserRecord user = webUserRepository.loadUserByUsername(username);
        if (user == null) {
            throw new AccessDeniedException("User not found");
        }

        return user;
    }

    /**
     * 检查指定的UserDetails是否有权限访问充电桩
     *
     * @param user UserDetails对象
     * @param chargeBoxId 充电桩ID
     * @return 如果用户有权限访问返回true
     */
    public boolean canUserAccessChargeBox(UserDetails user, String chargeBoxId) {
        // 获取用户角色
        WebUserRecord webUser = webUserRepository.loadUserByUsername(user.getUsername());
        if (webUser == null) {
            return false;
        }

        UserRole role = UserRole.valueOf(webUser.getUserRole().name());
        
        // 管理员和工厂运营商可以访问所有充电桩
        if (role == UserRole.ADMIN || role == UserRole.OPERATOR_FACTORY) {
            return true;
        }
        
        // 充电桩所有者只能访问分配给他们的充电桩
        if (role == UserRole.OPERATOR_OWNER) {
            return userChargeBoxRepository.isChargeBoxAssignedToUser(webUser.getWebUserPk(), chargeBoxId);
        }
        
        return false;
    }

    /**
     * 检查当前用户是否可以删除指定的充电桩
     *
     * @param chargeBoxPk 充电桩主键
     * @return 如果可以删除返回true
     */
    public boolean canDeleteChargeBox(int chargeBoxPk) {
        try {
            // 管理员和工厂运营商可以删除所有充电桩
            if (isAdmin() || isOperatorFactory()) {
                return true;
            }

            // 充电桩所有者只能删除分配给他们的充电桩
            if (isOperatorOwner()) {
                try {
                    WebUserRecord currentUser = getCurrentUser();
                    if (currentUser == null) {
                        log.warn("Current user is null when checking delete permission for charge box pk: {}", chargeBoxPk);
                        return false;
                    }
                    return userChargeBoxRepository.isChargeBoxAssignedToUserByPk(currentUser.getWebUserPk(), chargeBoxPk);
                } catch (Exception e) {
                    log.warn("Failed to check charge box ownership for deletion, chargeBoxPk: {}", chargeBoxPk, e);
                    return false;
                }
            }

            return false;
        } catch (Exception e) {
            log.error("Unexpected error in canDeleteChargeBox for chargeBoxPk: {}", chargeBoxPk, e);
            return false;
        }
    }

    /**
     * 检查当前用户是否可以删除指定的充电站
     *
     * @param chargingStationPk 充电站主键
     * @return 如果可以删除返回true
     */
    public boolean canDeleteChargeStation(int chargingStationPk) {
        // 管理员和工厂运营商可以删除所有充电站
        if (isAdmin() || isOperatorFactory()) {
            return true;
        }

        // 充电桩所有者只能删除分配给他们的充电站
        if (isOperatorOwner()) {
            try {
                WebUserRecord currentUser = getCurrentUser();
                // 需要检查充电站是否属于当前用户
                // 这里需要调用ChargeStationRepository来检查所有权
                return chargeStationRepository.isChargeStationOwnedByUser(chargingStationPk, currentUser.getWebUserPk());
            } catch (Exception e) {
                log.warn("Failed to check charge station ownership for deletion", e);
                return false;
            }
        }

        return false;
    }
}