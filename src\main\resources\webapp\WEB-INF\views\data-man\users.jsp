<%--

    St<PERSON><PERSON><PERSON> - SteckdosenVerwaltung - https://github.com/steve-community/steve
    Copyright (C) 2013-2025 SteVe Community Team
    All Rights Reserved.

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.

--%>
<%@ include file="../00-header.jsp" %>
<script type="text/javascript">
    $(document).ready(function() {
        <%@ include file="../snippets/sortable.js" %>
    });

    // Delete confirmation function
    function confirmDelete(userName, userPk) {
        var message = "Are you sure you want to delete user \"" + userName + "\" (ID: " + userPk + ")?\n\n" +
                     "WARNING: This operation will:\n" +
                     "- Delete user's basic information\n" +
                     "- Delete associated web login account\n" +
                     "- Release all charge boxes owned by this user\n" +
                     "- This operation cannot be undone!\n\n" +
                     "Do you want to continue?";

        return confirm(message);
    }
</script>
<div class="content-container">
    <div class="page-title">User Management</div>
    <div>
    <section><span>User Overview</span></section>
    <div class="search-panel mb-4">
        <form:form action="${ctxPath}/manager/users/query" method="get" modelAttribute="params">
            <div class="row mb-3">
                <div class="col-md-6 mb-3">
                    <div class="form-group">
                        <label for="userPk">User ID:</label>
                        <form:input path="userPk" class="form-control"/>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="form-group">
                        <label for="ocppIdTag">Ocpp ID Tag:</label>
                        <form:input path="ocppIdTag" class="form-control"/>
                    </div>
                </div>
            </div>
            <div class="row mb-3">
                <div class="col-md-6 mb-3">
                    <div class="form-group">
                        <label for="name">Name:</label>
                        <form:input path="name" class="form-control"/>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="form-group">
                        <label for="email">E-Mail:</label>
                        <form:input path="email" class="form-control"/>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12 text-right">
                    <input type="submit" class="btn btn-primary" value="Search">
                </div>
            </div>
        </form:form>
    </div>
    
    <div class="table-responsive">
        <table class="table table-striped table-hover table-bordered">
            <thead>
                <tr>
                    <th class="sorting" data-sort="int">User ID</th>
                    <th class="sorting" data-sort="string">Ocpp ID Tag</th>
                    <th class="sorting" data-sort="string">Name</th>
                    <th class="sorting" data-sort="string">Role</th>
                    <th class="sorting" data-sort="string">Phone</th>
                    <th class="sorting" data-sort="string">E-Mail</th>
                    <th class="text-center" style="min-width: 150px;">
                        Actions
                        <br>
                        <form:form action="${ctxPath}/manager/users/add" method="get" style="margin-top: 5px;">
                            <input type="submit" class="btn btn-success btn-sm" value="Add New User">
                        </form:form>
                    </th>
                </tr>
            </thead>
            <tbody>
            <c:forEach items="${userList}" var="cr">
                <tr>
                    <td><a href="${ctxPath}/manager/users/details/${cr.userPk}">${cr.userPk}</a></td>
                    <td>
                        <c:if test="${not empty cr.ocppIdTag}">
                            <a href="${ctxPath}/manager/ocppTags/details/${cr.ocppTagPk}">${cr.ocppIdTag}</a>
                        </c:if>
                    </td>
                    <td>${cr.name}</td>
                    <td>${cr.role}</td>
                    <td>${cr.phone}</td>
                    <td>${cr.email}</td>
                    <td class="text-center">
                        <!-- View Details Button -->
                        <a href="${ctxPath}/manager/users/details/${cr.userPk}" class="btn btn-info btn-sm" title="View Details">
                            <i class="fa fa-eye"></i> Details
                        </a>

                        <!-- Delete Button -->
                        <form:form action="${ctxPath}/manager/users/delete/${cr.userPk}" style="display: inline-block; margin-left: 5px;">
                            <input type="submit" class="btn btn-danger btn-sm" value="Delete"
                                   onclick="return confirmDelete('${cr.name}', '${cr.userPk}')">
                        </form:form>
                    </td>
                </tr>
            </c:forEach>
            </tbody>
        </table>
    </div>
</div>
</div>
<%@ include file="../00-footer.jsp" %>