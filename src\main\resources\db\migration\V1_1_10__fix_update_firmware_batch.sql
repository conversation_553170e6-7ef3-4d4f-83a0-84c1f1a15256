-- 检查并确保update_firmware_batch表存在并正确设置
CREATE TABLE IF NOT EXISTS `update_firmware_batch` (
    `batch_id` INT(11) NOT NULL AUTO_INCREMENT,
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `total_count` INT(11) NOT NULL DEFAULT 0,
    `success_count` INT(11) NOT NULL DEFAULT 0,
    `pending_count` INT(11) NOT NULL DEFAULT 0,
    `error_count` INT(11) NOT NULL DEFAULT 0,
    `status` VARCHAR(50) NOT NULL DEFAULT 'INPROGRESS',
    `created_by` VARCHAR(255) NULL,
    PRIMARY KEY (`batch_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='固件升级批处理';

-- 删除外键约束(如果存在)
SET @constraintExists = (
    SELECT COUNT(1) 
    FROM information_schema.TABLE_CONSTRAINTS 
    WHERE TABLE_NAME = 'update_firmware_log' 
    AND CONSTRAINT_TYPE = 'FOREIGN KEY' 
    AND CONSTRAINT_NAME = 'fk_update_firmware_log_batch'
);

SET @query = IF(@constraintExists > 0, 
               'ALTER TABLE `update_firmware_log` DROP FOREIGN KEY `fk_update_firmware_log_batch`', 
               'SELECT 1');

PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 重新添加外键关系
ALTER TABLE `update_firmware_log` 
    ADD CONSTRAINT `fk_update_firmware_log_batch`
    FOREIGN KEY (`batch_id`)
    REFERENCES `update_firmware_batch` (`batch_id`)
    ON DELETE SET NULL
    ON UPDATE NO ACTION; 