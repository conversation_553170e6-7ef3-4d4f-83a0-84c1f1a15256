package de.rwth.idsg.steve.repository.dto;

import lombok.Builder;
import lombok.Getter;
import org.joda.time.DateTime;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 充电成功率统计数据传输对象
 * 
 * <AUTHOR> Assistant
 */
@Getter
@Builder
public class ChargingSuccessStats {
    
    private final Integer statsId;
    private final String chargeBoxId;
    private final int totalSessions;
    private final int successfulSessions;
    private final BigDecimal successRate;
    private final DateTime lastUpdated;
    
    /**
     * 计算成功率
     */
    public static BigDecimal calculateSuccessRate(int successful, int total) {
        if (total == 0) {
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(successful)
                .multiply(BigDecimal.valueOf(100))
                .divide(BigDecimal.valueOf(total), 2, RoundingMode.HALF_UP);
    }
    
    /**
     * 获取格式化的成功率字符串
     */
    public String getFormattedSuccessRate() {
        return successRate.toString() + "%";
    }
    
    /**
     * 创建更新后的统计数据
     */
    public ChargingSuccessStats withUpdatedStats(int newTotal, int newSuccessful) {
        BigDecimal newSuccessRate = calculateSuccessRate(newSuccessful, newTotal);
        
        return ChargingSuccessStats.builder()
                .statsId(this.statsId)
                .chargeBoxId(this.chargeBoxId)
                .totalSessions(newTotal)
                .successfulSessions(newSuccessful)
                .successRate(newSuccessRate)
                .lastUpdated(DateTime.now())
                .build();
    }
}
