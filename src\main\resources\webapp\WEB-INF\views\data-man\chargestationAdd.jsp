<%--

    SteV<PERSON> - SteckdosenVerwaltung - https://github.com/steve-community/steve
    Copyright (C) 2013-2025 SteVe Community Team
    All Rights Reserved.

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.

--%>
<%@ include file="../00-header.jsp" %>
<spring:hasBindErrors name="chargeStationForm">
    <div class="error">
        Error while trying to add a charge station:
        <ul>
            <c:forEach var="error" items="${errors.allErrors}">
                <li>${error.defaultMessage}</li>
            </c:forEach>
        </ul>
    </div>
</spring:hasBindErrors>
<div class="content"><div>
    <section><span>Add Charge Station</span></section>
    <form:form action="${ctxPath}/manager/chargestations/add" modelAttribute="chargeStationForm">
        <table class="userInput">
            <tr>
                <td>Station Name:</td>
                <td>
                    <form:input path="stationName"/>
                </td>
            </tr>
            <tr>
                <td>Operator Name:</td>
                <td>
                    <form:input path="operatorName"/>
                </td>
            </tr>
            <tr>
                <td>Construction Date:</td>
                <td>
                    <form:input path="constructionDate" type="date"/>
                </td>
            </tr>
            <tr>
                <td>Operation Date:</td>
                <td>
                    <form:input path="operationDate" type="date"/>
                </td>
            </tr>
            <tr>
                <td>Location:</td>
                <td>
                    <form:input path="location"/>
                </td>
            </tr>
            <tr>
                <td>Note:</td>
                <td>
                    <form:textarea path="note"/>
                </td>
            </tr>
            <tr><td></td><td><div class="submit-button"><input type="submit" value="Add"></div></td></tr>
        </table>
    </form:form>
</div></div>
<%@ include file="../00-footer.jsp" %> 